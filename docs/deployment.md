# M&A Platform Multi-Tenant Deployment Guide

This guide covers deploying the M&A Platform in various environments with full multi-tenant support.

## Overview

The M&A Platform uses a **shared database, shared schema** multi-tenancy approach with:
- Tenant isolation at the application level
- Subdomain-based tenant routing
- Nginx reverse proxy for multi-tenant request routing
- Horizontal scaling capabilities
- Comprehensive monitoring and logging

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │      Nginx      │    │   Frontend      │
│                 │────│   Reverse Proxy │────│   (React)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │     Backend     │
                       │   (Node.js)     │
                       └─────────────────┘
                                │
                    ┌───────────┼───────────┐
                    ▼           ▼           ▼
            ┌─────────────┐ ┌─────────┐ ┌─────────┐
            │ PostgreSQL  │ │  Redis  │ │  Files  │
            │  Database   │ │  Cache  │ │ Storage │
            └─────────────┘ └─────────┘ └─────────┘
```

## Deployment Options

### 1. Docker Compose (Development/Testing)

**Prerequisites:**
- Docker and Docker Compose
- Node.js 18+ (for local development)

**Quick Start:**
```bash
# Clone and setup
git clone <repository>
cd ma-platform

# Copy environment file
cp .env.example .env.development

# Deploy with Docker Compose
./scripts/deploy.sh development

# Access the application
open http://localhost:3000
```

**Environment Configuration:**
```bash
# .env.development
NODE_ENV=development
DB_PASSWORD=postgres
JWT_SECRET=your-super-secret-jwt-key
ENCRYPTION_KEY=your-32-char-encryption-key
CORS_ORIGIN=http://localhost:3000
MAIN_DOMAIN=localhost
API_DOMAIN=api.localhost
```

**Multi-tenant Testing:**
```bash
# Add entries to /etc/hosts for local testing
echo "127.0.0.1 demo.localhost" >> /etc/hosts
echo "127.0.0.1 acme.localhost" >> /etc/hosts
echo "127.0.0.1 api.localhost" >> /etc/hosts

# Create test tenants
docker-compose exec backend npm run migrate init --tenant-slug=demo
docker-compose exec backend npm run migrate init --tenant-slug=acme

# Access tenant-specific URLs
open http://demo.localhost:3000
open http://acme.localhost:3000
```

### 2. Kubernetes (Production)

**Prerequisites:**
- Kubernetes cluster (1.20+)
- kubectl configured
- Docker registry access
- SSL certificates

**Deployment Steps:**

1. **Prepare Environment:**
```bash
# Set environment variables
export DOCKER_REGISTRY=your-registry.com
export IMAGE_TAG=v1.0.0
export ENVIRONMENT=production

# Update secrets in k8s/secrets.yaml
kubectl create secret generic ma-platform-secrets \
  --from-literal=DATABASE_URL="********************************/db" \
  --from-literal=JWT_SECRET="your-jwt-secret" \
  --from-literal=ENCRYPTION_KEY="your-encryption-key" \
  --from-literal=REDIS_URL="redis://redis-service:6379" \
  -n ma-platform-production
```

2. **Deploy to Kubernetes:**
```bash
# Deploy with script
./scripts/k8s-deploy.sh production

# Or deploy manually
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/secrets.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/postgres.yaml
kubectl apply -f k8s/redis.yaml
kubectl apply -f k8s/backend.yaml
kubectl apply -f k8s/frontend.yaml
kubectl apply -f k8s/nginx.yaml
```

3. **Verify Deployment:**
```bash
# Check pod status
kubectl get pods -n ma-platform-production

# Check services
kubectl get services -n ma-platform-production

# Check ingress
kubectl get ingress -n ma-platform-production

# View logs
kubectl logs -f deployment/ma-platform-backend -n ma-platform-production
```

## Multi-Tenant Configuration

### Nginx Routing

The Nginx configuration handles multi-tenant routing:

```nginx
# Extract tenant information from hostname
map $host $subdomain {
    ~^([^.]+)\.(.+)$ $1;
    default "";
}

# Set tenant ID based on subdomain
map $subdomain $tenant_id {
    default $subdomain;
    "" "main";
    "www" "main";
    "api" "main";
}

# Add tenant headers to backend requests
proxy_set_header X-Tenant-ID $tenant_id;
proxy_set_header X-Subdomain $subdomain;
```

### Backend Tenant Middleware

The backend automatically resolves tenants:

```typescript
// Tenant resolution middleware
app.use(resolveTenant);

// Routes automatically include tenant context
app.get('/api/deals', authenticateToken, (req: TenantRequest, res) => {
  // req.tenantContext contains tenant information
  const deals = await Deal.findMany({
    where: { tenantId: req.tenantContext.id }
  });
});
```

### Frontend Tenant Context

The frontend provides tenant-aware components:

```typescript
// Wrap app with tenant provider
<TenantProvider autoResolve={true}>
  <App />
</TenantProvider>

// Use tenant context in components
const { tenant, hasFeature } = useTenant();
```

## Environment-Specific Configurations

### Development
- Single-node setup
- File-based storage
- Debug logging enabled
- Hot reloading
- Local SSL certificates

### Staging
- Multi-node setup
- Cloud storage integration
- Production-like data
- Performance testing
- SSL certificates from Let's Encrypt

### Production
- High availability setup
- Auto-scaling enabled
- Monitoring and alerting
- Backup and disaster recovery
- Commercial SSL certificates

## Scaling Considerations

### Horizontal Scaling

**Backend API:**
```yaml
# Kubernetes HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ma-platform-backend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ma-platform-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

**Database Scaling:**
- Read replicas for reporting queries
- Connection pooling
- Query optimization
- Tenant-specific indexing

**Cache Scaling:**
- Redis Cluster for high availability
- Tenant-specific cache keys
- Cache warming strategies

### Vertical Scaling

**Resource Allocation:**
```yaml
resources:
  requests:
    memory: "512Mi"
    cpu: "250m"
  limits:
    memory: "2Gi"
    cpu: "1"
```

## Monitoring and Observability

### Metrics Collection
- Application metrics (Prometheus)
- Infrastructure metrics (Node Exporter)
- Tenant-specific metrics
- Business metrics

### Logging
- Structured logging with tenant context
- Centralized log aggregation (ELK Stack)
- Log retention policies
- Security audit logs

### Alerting
- System health alerts
- Performance degradation alerts
- Tenant-specific alerts
- Security incident alerts

## Security Considerations

### Network Security
- TLS encryption for all communications
- Network policies in Kubernetes
- VPC isolation
- WAF protection

### Application Security
- JWT token validation
- Rate limiting per tenant
- Input validation and sanitization
- SQL injection prevention

### Data Security
- Encryption at rest
- Encryption in transit
- Tenant data isolation
- Regular security audits

## Backup and Disaster Recovery

### Database Backups
```bash
# Automated daily backups
kubectl create cronjob postgres-backup \
  --image=postgres:15-alpine \
  --schedule="0 2 * * *" \
  -- pg_dump $DATABASE_URL > /backups/$(date +%Y%m%d).sql
```

### Tenant Data Export
```bash
# Export specific tenant data
npm run migrate export --tenant-id=<tenant-id> --output=/backups/tenant-data.json
```

### Recovery Procedures
1. Database point-in-time recovery
2. Tenant-specific data restoration
3. Application state recovery
4. DNS and routing recovery

## Troubleshooting

### Common Issues

**Tenant Resolution Fails:**
```bash
# Check nginx configuration
kubectl logs deployment/nginx -n ma-platform-production

# Verify tenant exists in database
kubectl exec deployment/ma-platform-backend -n ma-platform-production -- \
  npm run migrate status --tenant-slug=<tenant-slug>
```

**Database Connection Issues:**
```bash
# Check database connectivity
kubectl exec deployment/ma-platform-backend -n ma-platform-production -- \
  pg_isready -h postgres-service -p 5432

# Check database logs
kubectl logs deployment/postgres -n ma-platform-production
```

**Performance Issues:**
```bash
# Check resource usage
kubectl top pods -n ma-platform-production

# Check HPA status
kubectl get hpa -n ma-platform-production

# Analyze slow queries
kubectl exec deployment/postgres -n ma-platform-production -- \
  psql -c "SELECT * FROM pg_stat_activity WHERE state = 'active';"
```

### Debug Commands

```bash
# Get all resources
kubectl get all -n ma-platform-production

# Describe problematic pod
kubectl describe pod <pod-name> -n ma-platform-production

# Get events
kubectl get events -n ma-platform-production --sort-by='.lastTimestamp'

# Port forward for local debugging
kubectl port-forward service/ma-platform-backend 3001:3001 -n ma-platform-production
```

## Maintenance

### Regular Tasks
- Security updates
- Database maintenance
- Log rotation
- Certificate renewal
- Performance optimization

### Tenant Management
- Tenant provisioning
- Plan upgrades/downgrades
- Data migration
- Deprovisioning

### Monitoring Health
- Daily health checks
- Performance reviews
- Capacity planning
- Security audits

This deployment guide ensures a robust, scalable, and secure multi-tenant M&A platform deployment across different environments.
