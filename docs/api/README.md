# API Documentation

The M&A Enterprise Platform provides a comprehensive RESTful API for all platform functionality.

## Base URL

- **Development**: `http://localhost:3001/api`
- **Staging**: `https://staging-api.ma-platform.com/api`
- **Production**: `https://api.ma-platform.com/api`

## Authentication

All API endpoints require authentication unless otherwise specified. The API uses JWT (JSON Web Tokens) for authentication.

### Authentication Header
```http
Authorization: Bearer <jwt-token>
```

### Tenant Context
Multi-tenant requests require a tenant identifier:
```http
X-Tenant-ID: <tenant-id>
```

## API Endpoints

### Authentication
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Refresh JWT token
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password

### Users & Tenants
- `GET /users` - List users
- `POST /users` - Create user
- `GET /users/:id` - Get user details
- `PUT /users/:id` - Update user
- `DELETE /users/:id` - Delete user
- `GET /tenants` - List tenants
- `POST /tenants` - Create tenant
- `GET /tenants/:id` - Get tenant details

### Deals Management
- `GET /deals` - List deals
- `POST /deals` - Create deal
- `GET /deals/:id` - Get deal details
- `PUT /deals/:id` - Update deal
- `DELETE /deals/:id` - Delete deal
- `POST /deals/:id/stage` - Update deal stage

### Document Management (VDR)
- `GET /documents` - List documents
- `POST /documents` - Upload document
- `GET /documents/:id` - Get document details
- `PUT /documents/:id` - Update document
- `DELETE /documents/:id` - Delete document
- `GET /documents/:id/download` - Download document

### Due Diligence
- `GET /due-diligence` - List DD items
- `POST /due-diligence` - Create DD item
- `GET /due-diligence/:id` - Get DD item details
- `PUT /due-diligence/:id` - Update DD item
- `DELETE /due-diligence/:id` - Delete DD item

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "req_123456789"
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "req_123456789"
  }
}
```

## HTTP Status Codes

- `200` - OK: Request successful
- `201` - Created: Resource created successfully
- `400` - Bad Request: Invalid request data
- `401` - Unauthorized: Authentication required
- `403` - Forbidden: Insufficient permissions
- `404` - Not Found: Resource not found
- `409` - Conflict: Resource already exists
- `422` - Unprocessable Entity: Validation error
- `429` - Too Many Requests: Rate limit exceeded
- `500` - Internal Server Error: Server error

## Pagination

List endpoints support pagination using query parameters:

```http
GET /deals?page=1&limit=20&sort=createdAt&order=desc
```

### Pagination Response
```json
{
  "success": true,
  "data": [
    // Array of items
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## Filtering and Sorting

### Filtering
```http
GET /deals?status=PIPELINE&priority=HIGH&createdAt[gte]=2024-01-01
```

### Sorting
```http
GET /deals?sort=createdAt&order=desc
```

### Search
```http
GET /deals?search=acquisition&searchFields=title,description
```

## Rate Limiting

API requests are rate limited to prevent abuse:

- **Authenticated requests**: 1000 requests per hour
- **Unauthenticated requests**: 100 requests per hour

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Error Codes

### Authentication Errors
- `AUTH_REQUIRED` - Authentication required
- `INVALID_TOKEN` - Invalid or expired token
- `INSUFFICIENT_PERMISSIONS` - User lacks required permissions

### Validation Errors
- `VALIDATION_ERROR` - Request validation failed
- `INVALID_FORMAT` - Invalid data format
- `REQUIRED_FIELD` - Required field missing

### Business Logic Errors
- `RESOURCE_NOT_FOUND` - Requested resource not found
- `DUPLICATE_RESOURCE` - Resource already exists
- `BUSINESS_RULE_VIOLATION` - Business rule violated

### System Errors
- `INTERNAL_ERROR` - Internal server error
- `SERVICE_UNAVAILABLE` - Service temporarily unavailable
- `RATE_LIMIT_EXCEEDED` - Rate limit exceeded

## API Versioning

The API uses URL versioning:
- Current version: `v1`
- Base URL: `/api/v1`

Future versions will be available at `/api/v2`, etc.

## SDKs and Libraries

### JavaScript/TypeScript
```bash
npm install @ma-platform/api-client
```

```typescript
import { MAPlatformClient } from '@ma-platform/api-client'

const client = new MAPlatformClient({
  baseURL: 'https://api.ma-platform.com',
  apiKey: 'your-api-key'
})

const deals = await client.deals.list()
```

### Python
```bash
pip install ma-platform-python
```

```python
from ma_platform import Client

client = Client(
    base_url='https://api.ma-platform.com',
    api_key='your-api-key'
)

deals = client.deals.list()
```

## Webhooks

The platform supports webhooks for real-time notifications:

### Webhook Events
- `deal.created` - New deal created
- `deal.updated` - Deal updated
- `deal.stage_changed` - Deal stage changed
- `document.uploaded` - Document uploaded
- `user.created` - User created

### Webhook Payload
```json
{
  "event": "deal.created",
  "data": {
    "id": "deal_123",
    "title": "New Acquisition",
    // ... deal data
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "tenantId": "tenant_123"
}
```

## Testing

### Postman Collection
Import our Postman collection for easy API testing:
[Download Collection](./postman/ma-platform-api.json)

### API Testing Environment
Use our testing environment for development:
- **Base URL**: `https://api-test.ma-platform.com`
- **Test Credentials**: Available in development documentation

## Support

For API support:
- **Documentation**: This documentation
- **GitHub Issues**: [Create an issue](../../issues)
- **Email**: <EMAIL>
- **Slack**: #api-support

## Changelog

### v1.0.0 (Current)
- Initial API release
- Authentication and user management
- Deal management
- Document management
- Due diligence management

See [CHANGELOG.md](./CHANGELOG.md) for detailed version history.
