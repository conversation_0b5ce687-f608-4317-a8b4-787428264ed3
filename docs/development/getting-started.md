# Getting Started - Development Guide

This guide will help you set up the M&A Enterprise Platform for local development.

## Prerequisites

Before you begin, ensure you have the following installed:

### Required Software
- **Node.js 18+**: [Download](https://nodejs.org/)
- **PNPM 8+**: `npm install -g pnpm`
- **Docker**: [Download](https://www.docker.com/get-started)
- **Docker Compose**: Included with Docker Desktop
- **Git**: [Download](https://git-scm.com/)

### Optional but Recommended
- **VS Code**: [Download](https://code.visualstudio.com/)
- **PostgreSQL Client**: pgAdmin, DBeaver, or similar
- **Redis Client**: RedisInsight or similar

## Quick Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ma-enterprise-platform
```

### 2. Run Setup Script
```bash
# Make script executable
chmod +x scripts/setup-env.sh

# Run setup (this will install dependencies, setup database, etc.)
./scripts/setup-env.sh
```

### 3. Start Development Environment
```bash
# Start all services
pnpm dev

# Or start services individually
pnpm --filter backend dev    # Backend only
pnpm --filter frontend dev   # Frontend only
```

### 4. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/docs
- **Prisma Studio**: `pnpm --filter backend db:studio`

## Manual Setup (Alternative)

If you prefer to set up manually or the script doesn't work:

### 1. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
# Generate secure secrets for JWT_SECRET and ENCRYPTION_KEY
```

### 2. Install Dependencies
```bash
# Install all dependencies
pnpm install
```

### 3. Database Setup
```bash
# Start PostgreSQL and Redis
docker-compose up -d postgres redis

# Generate Prisma client
pnpm --filter backend prisma generate

# Run database migrations
pnpm --filter backend prisma migrate dev

# Seed database with sample data
pnpm --filter backend db:seed
```

### 4. Start Development Servers
```bash
# Terminal 1: Backend
pnpm --filter backend dev

# Terminal 2: Frontend
pnpm --filter frontend dev
```

## Development Workflow

### Daily Development
1. **Pull latest changes**
   ```bash
   git pull origin main
   pnpm install  # Install any new dependencies
   ```

2. **Start development environment**
   ```bash
   pnpm dev
   ```

3. **Make your changes**
   - Frontend code in `packages/frontend/src/`
   - Backend code in `packages/backend/src/`

4. **Run tests**
   ```bash
   pnpm test                    # All tests
   pnpm --filter frontend test  # Frontend tests only
   pnpm --filter backend test   # Backend tests only
   ```

5. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: your feature description"
   git push origin your-branch
   ```

### Code Quality
The project includes several tools to maintain code quality:

- **ESLint**: Linting for TypeScript/JavaScript
- **Prettier**: Code formatting
- **Husky**: Git hooks for pre-commit checks
- **TypeScript**: Type checking

These run automatically on commit, but you can run them manually:
```bash
pnpm lint        # Run linting
pnpm type-check  # Run type checking
```

## Project Structure

```
ma-enterprise-platform/
├── packages/
│   ├── frontend/              # React frontend application
│   │   ├── src/
│   │   │   ├── components/    # Reusable UI components
│   │   │   ├── pages/         # Page components
│   │   │   ├── hooks/         # Custom React hooks
│   │   │   ├── lib/           # Utility functions
│   │   │   ├── api/           # API client functions
│   │   │   └── types/         # TypeScript type definitions
│   │   ├── public/            # Static assets
│   │   └── package.json
│   │
│   ├── backend/               # Express.js backend API
│   │   ├── src/
│   │   │   ├── domain/        # Domain models and business logic
│   │   │   ├── application/   # Application services
│   │   │   ├── infrastructure/# Infrastructure layer (DB, cache, etc.)
│   │   │   ├── interfaces/    # API routes and controllers
│   │   │   └── shared/        # Shared utilities and config
│   │   ├── prisma/            # Database schema and migrations
│   │   └── package.json
│   │
│   └── shared/                # Shared types and utilities
│       ├── types/             # Shared TypeScript types
│       └── utils/             # Shared utility functions
│
├── docs/                      # Documentation
├── scripts/                   # Deployment and utility scripts
├── config/                    # Environment configurations
├── .github/                   # GitHub Actions workflows
└── .taskmaster/               # Task management
```

## Available Scripts

### Root Level
- `pnpm dev` - Start all development servers
- `pnpm build` - Build all packages
- `pnpm test` - Run all tests
- `pnpm lint` - Lint all packages
- `pnpm type-check` - Type check all packages

### Frontend Package
- `pnpm --filter frontend dev` - Start frontend dev server
- `pnpm --filter frontend build` - Build frontend for production
- `pnpm --filter frontend test` - Run frontend tests
- `pnpm --filter frontend test:ui` - Run tests with UI
- `pnpm --filter frontend preview` - Preview production build

### Backend Package
- `pnpm --filter backend dev` - Start backend dev server
- `pnpm --filter backend build` - Build backend for production
- `pnpm --filter backend test` - Run backend tests
- `pnpm --filter backend db:studio` - Open Prisma Studio
- `pnpm --filter backend db:migrate` - Run database migrations
- `pnpm --filter backend db:seed` - Seed database with sample data

## Environment Variables

Key environment variables you may need to configure:

### Required
- `DATABASE_URL` - PostgreSQL connection string
- `REDIS_URL` - Redis connection string
- `JWT_SECRET` - Secret for JWT token signing
- `ENCRYPTION_KEY` - Key for data encryption

### Optional
- `FRONTEND_URL` - Frontend URL (default: http://localhost:3000)
- `SMTP_*` - Email configuration
- `AWS_*` - AWS S3 configuration for file storage

## Database Management

### Prisma Commands
```bash
# Generate Prisma client
pnpm --filter backend prisma generate

# Create and apply migration
pnpm --filter backend prisma migrate dev --name migration-name

# Reset database (WARNING: deletes all data)
pnpm --filter backend prisma migrate reset

# Open Prisma Studio
pnpm --filter backend prisma studio

# Seed database
pnpm --filter backend db:seed
```

### Database Access
- **Connection**: Use the `DATABASE_URL` from your `.env` file
- **GUI Tools**: pgAdmin, DBeaver, or Prisma Studio
- **Command Line**: `psql` with connection string

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill process using port 3000 or 3001
   lsof -ti:3000 | xargs kill -9
   lsof -ti:3001 | xargs kill -9
   ```

2. **Database connection issues**
   ```bash
   # Restart database containers
   docker-compose restart postgres redis
   
   # Check container status
   docker-compose ps
   ```

3. **Node modules issues**
   ```bash
   # Clean install
   rm -rf node_modules packages/*/node_modules
   pnpm install
   ```

4. **Prisma client issues**
   ```bash
   # Regenerate Prisma client
   pnpm --filter backend prisma generate
   ```

### Getting Help
- Check the [FAQ](./faq.md)
- Search existing [GitHub Issues](../../issues)
- Ask in the team Slack channel
- Create a new issue with detailed information

## Next Steps

Once you have the development environment running:

1. **Explore the codebase**: Start with the main entry points
2. **Read the architecture docs**: Understand the system design
3. **Run the tests**: Familiarize yourself with the test suite
4. **Make a small change**: Try adding a simple feature
5. **Read the contributing guide**: Learn about our development process

## Additional Resources

- [Architecture Overview](../architecture/overview.md)
- [API Documentation](../api/README.md)
- [Testing Guide](./testing.md)
- [Deployment Guide](../deployment/deployment-guide.md)
- [Contributing Guidelines](./contributing.md)
