{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.next": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.next": true, "**/pnpm-lock.yaml": true}, "eslint.workingDirectories": ["packages/frontend", "packages/backend"], "typescript.preferences.includePackageJsonAutoImports": "on", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}}