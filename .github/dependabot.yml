version: 2
updates:
  # Enable version updates for npm (root)
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    reviewers:
      - "ma-platform-team"
    assignees:
      - "ma-platform-team"

  # Enable version updates for npm (frontend)
  - package-ecosystem: "npm"
    directory: "/packages/frontend"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5

  # Enable version updates for npm (backend)
  - package-ecosystem: "npm"
    directory: "/packages/backend"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5

  # Enable version updates for Docker
  - package-ecosystem: "docker"
    directory: "/packages/backend"
    schedule:
      interval: "weekly"

  - package-ecosystem: "docker"
    directory: "/packages/frontend"
    schedule:
      interval: "weekly"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
