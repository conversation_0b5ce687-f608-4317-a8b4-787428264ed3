name: Deploy

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8.15.0'

jobs:
  # Job 1: Build and Test Before Deployment
  pre-deployment-tests:
    name: Pre-Deployment Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ma_platform_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma client
        run: pnpm --filter backend prisma generate

      - name: Run database migrations
        run: pnpm --filter backend prisma migrate deploy
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ma_platform_test

      - name: Run comprehensive test suite
        run: |
          pnpm --filter backend test:unit
          pnpm --filter backend test:integration
          pnpm --filter frontend test
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ma_platform_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-jwt-secret-key-at-least-32-characters-long
          ENCRYPTION_KEY: test-encryption-key-32-characters

      - name: Build applications
        run: pnpm build
        env:
          NODE_ENV: production

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: pre-deployment-tests
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma client
        run: pnpm --filter backend prisma generate

      - name: Build applications
        run: pnpm build
        env:
          VITE_API_URL: ${{ secrets.STAGING_API_URL }}

      - name: Build Docker images
        run: |
          docker build -f packages/backend/Dockerfile -t ma-platform-backend:staging .
          docker build -f packages/frontend/Dockerfile -t ma-platform-frontend:staging .

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Add your staging deployment commands here
          # Example: kubectl apply -f k8s/staging/
          # Example: docker-compose -f docker-compose.staging.yml up -d

      - name: Wait for deployment
        run: |
          echo "Waiting for services to be ready..."
          sleep 30

      - name: Run smoke tests
        run: |
          # Basic health checks
          echo "Running smoke tests..."

          # Test backend health endpoint
          curl -f ${{ secrets.STAGING_API_URL }}/health || exit 1

          # Test API endpoints
          curl -f ${{ secrets.STAGING_API_URL }}/api/health || exit 1

          echo "Smoke tests passed!"

      - name: Run staging integration tests
        run: pnpm --filter backend test:staging
        env:
          STAGING_API_URL: ${{ secrets.STAGING_API_URL }}
          TEST_USER_EMAIL: ${{ secrets.TEST_USER_EMAIL }}
          TEST_USER_PASSWORD: ${{ secrets.TEST_USER_PASSWORD }}

      - name: Notify staging deployment success
        uses: 8398a7/action-slack@v3
        if: success()
        with:
          status: success
          channel: '#deployments'
          message: |
            🚀 Successfully deployed to staging!
            Environment: ${{ secrets.STAGING_API_URL }}
            Commit: ${{ github.sha }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

      - name: Notify staging deployment failure
        uses: 8398a7/action-slack@v3
        if: failure()
        with:
          status: failure
          channel: '#deployments'
          message: |
            ❌ Staging deployment failed!
            Check the logs: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    if: github.event.inputs.environment == 'production'
    environment: production
    needs: [deploy-staging]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma client
        run: pnpm --filter backend prisma generate

      - name: Build applications
        run: pnpm build
        env:
          VITE_API_URL: ${{ secrets.PRODUCTION_API_URL }}

      - name: Run database migrations
        run: pnpm --filter backend prisma migrate deploy
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}

      - name: Build Docker images
        run: |
          docker build -f packages/backend/Dockerfile -t ma-platform-backend:production .
          docker build -f packages/frontend/Dockerfile -t ma-platform-frontend:production .

      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # Add your production deployment commands here
          # Example: kubectl apply -f k8s/production/
          # Example: docker-compose -f docker-compose.prod.yml up -d

      - name: Wait for production deployment
        run: |
          echo "Waiting for production services to be ready..."
          sleep 60

      - name: Run production smoke tests
        run: |
          # Comprehensive health checks for production
          echo "Running production smoke tests..."

          # Test backend health endpoint
          curl -f ${{ secrets.PRODUCTION_API_URL }}/health || exit 1

          # Test API endpoints
          curl -f ${{ secrets.PRODUCTION_API_URL }}/api/health || exit 1

          # Test database connectivity
          curl -f ${{ secrets.PRODUCTION_API_URL }}/api/health/database || exit 1

          echo "Production smoke tests passed!"

      - name: Run production integration tests
        run: pnpm --filter backend test:production
        env:
          PRODUCTION_API_URL: ${{ secrets.PRODUCTION_API_URL }}
          TEST_USER_EMAIL: ${{ secrets.PROD_TEST_USER_EMAIL }}
          TEST_USER_PASSWORD: ${{ secrets.PROD_TEST_USER_PASSWORD }}

      - name: Notify production deployment success
        uses: 8398a7/action-slack@v3
        if: success()
        with:
          status: success
          channel: '#deployments'
          message: |
            🎉 Successfully deployed to PRODUCTION! 🎉
            Environment: ${{ secrets.PRODUCTION_API_URL }}
            Commit: ${{ github.sha }}

            Please monitor the application closely.
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

      - name: Notify production deployment failure
        uses: 8398a7/action-slack@v3
        if: failure()
        with:
          status: failure
          channel: '#deployments'
          message: |
            🚨 PRODUCTION DEPLOYMENT FAILED! 🚨
            Immediate attention required!
            Check the logs: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
      - name: Notify deployment status
        run: |
          echo "Deployment completed"
          # Add notification logic here (Slack, email, etc.)
