name: Deploy

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8.15.0'

jobs:
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma client
        run: pnpm --filter backend prisma generate

      - name: Build applications
        run: pnpm build
        env:
          VITE_API_URL: ${{ secrets.STAGING_API_URL }}

      - name: Build Docker images
        run: |
          docker build -f packages/backend/Dockerfile -t ma-platform-backend:staging .
          docker build -f packages/frontend/Dockerfile -t ma-platform-frontend:staging .

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Add your staging deployment commands here
          # Example: kubectl apply -f k8s/staging/
          # Example: docker-compose -f docker-compose.staging.yml up -d

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    if: github.event.inputs.environment == 'production'
    environment: production
    needs: [deploy-staging]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma client
        run: pnpm --filter backend prisma generate

      - name: Build applications
        run: pnpm build
        env:
          VITE_API_URL: ${{ secrets.PRODUCTION_API_URL }}

      - name: Run database migrations
        run: pnpm --filter backend prisma migrate deploy
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}

      - name: Build Docker images
        run: |
          docker build -f packages/backend/Dockerfile -t ma-platform-backend:production .
          docker build -f packages/frontend/Dockerfile -t ma-platform-frontend:production .

      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # Add your production deployment commands here
          # Example: kubectl apply -f k8s/production/
          # Example: docker-compose -f docker-compose.prod.yml up -d

  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
      - name: Notify deployment status
        run: |
          echo "Deployment completed"
          # Add notification logic here (Slack, email, etc.)
