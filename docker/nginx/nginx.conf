user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'tenant="$tenant_id" subdomain="$subdomain"';

    access_log /var/log/nginx/access.log main;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

    # Upstream servers
    upstream backend {
        server backend:3001;
        keepalive 32;
    }

    upstream frontend {
        server frontend:3000;
        keepalive 32;
    }

    # Map to extract tenant information
    map $host $subdomain {
        ~^([^.]+)\.(.+)$ $1;
        default "";
    }

    map $host $main_domain {
        ~^([^.]+)\.(.+)$ $2;
        default $host;
    }

    # Set tenant ID based on subdomain
    map $subdomain $tenant_id {
        default $subdomain;
        "" "main";
        "www" "main";
        "api" "main";
        "admin" "main";
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Main application server
    server {
        listen 80;
        server_name localhost *.localhost;

        # Security headers
        add_header X-Tenant-ID $tenant_id always;
        add_header X-Subdomain $subdomain always;

        # API routes
        location /api/ {
            # Rate limiting
            limit_req zone=api burst=20 nodelay;

            # Add tenant headers
            proxy_set_header X-Tenant-ID $tenant_id;
            proxy_set_header X-Subdomain $subdomain;
            proxy_set_header X-Main-Domain $main_domain;
            proxy_set_header X-Original-Host $host;
            
            # Standard proxy headers
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # Proxy settings
            proxy_pass http://backend;
            proxy_redirect off;
            proxy_buffering off;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Auth routes with stricter rate limiting
        location /api/auth/login {
            limit_req zone=login burst=5 nodelay;
            
            proxy_set_header X-Tenant-ID $tenant_id;
            proxy_set_header X-Subdomain $subdomain;
            proxy_set_header X-Main-Domain $main_domain;
            proxy_set_header X-Original-Host $host;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_pass http://backend;
            proxy_redirect off;
            proxy_buffering off;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

        # Health check
        location /health {
            proxy_pass http://backend/health;
            proxy_set_header Host $host;
            access_log off;
        }

        # Frontend application
        location / {
            # Add tenant headers for frontend
            proxy_set_header X-Tenant-ID $tenant_id;
            proxy_set_header X-Subdomain $subdomain;
            proxy_set_header X-Main-Domain $main_domain;
            proxy_set_header X-Original-Host $host;
            
            # Standard proxy headers
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Proxy to frontend
            proxy_pass http://frontend;
            proxy_redirect off;
            proxy_buffering off;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # WebSocket support for development
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Static assets caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Tenant-ID $tenant_id always;
            
            proxy_pass http://frontend;
            proxy_set_header Host $host;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # Admin interface (optional)
    server {
        listen 80;
        server_name admin.localhost;

        location / {
            proxy_set_header X-Admin-Interface "true";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_pass http://frontend;
            proxy_redirect off;
        }
    }

    # API-only subdomain
    server {
        listen 80;
        server_name api.localhost;

        location / {
            limit_req zone=api burst=20 nodelay;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_pass http://backend;
            proxy_redirect off;
            proxy_buffering off;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
    }
}

# Stream block for TCP/UDP load balancing (if needed)
stream {
    # Example for database connections (if needed)
    # upstream postgres {
    #     server postgres:5432;
    # }
    
    # server {
    #     listen 5433;
    #     proxy_pass postgres;
    # }
}
