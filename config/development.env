# Development Environment Configuration
NODE_ENV=development
PORT=3001

# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/ma_platform_dev"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="dev-jwt-secret-key-at-least-32-characters-long-for-development"
JWT_EXPIRES_IN="7d"

# Frontend
FRONTEND_URL="http://localhost:3000"

# Email (Development - use Ethereal or MailHog)
SMTP_HOST="localhost"
SMTP_PORT="1025"
SMTP_USER=""
SMTP_PASS=""

# File Storage (Local for development)
STORAGE_PROVIDER="local"
LOCAL_STORAGE_PATH="./uploads"

# Encryption
ENCRYPTION_KEY="dev-encryption-key-32-characters-long"

# Rate Limiting (More lenient for development)
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="1000"

# Logging
LOG_LEVEL="debug"

# Feature Flags
ENABLE_SWAGGER_DOCS="true"
ENABLE_DEBUG_ROUTES="true"
ENABLE_SEED_DATA="true"
