# Production Environment Configuration
NODE_ENV=production
PORT=3001

# Database (Use production database with connection pooling)
DATABASE_URL="*****************************************************/ma_platform_prod?connection_limit=20&pool_timeout=20"

# Redis (Use Redis cluster for production)
REDIS_URL="redis://prod-redis-cluster:6379"

# JWT (Use strong production secrets - CHANGE THESE!)
JWT_SECRET="CHANGE-THIS-production-jwt-secret-key-at-least-32-characters-long"
JWT_EXPIRES_IN="24h"

# Frontend
FRONTEND_URL="https://app.ma-platform.com"

# Email (Use production email service)
SMTP_HOST="smtp.sendgrid.net"
SMTP_PORT="587"
SMTP_USER="apikey"
SMTP_PASS="production-sendgrid-api-key"

# File Storage (Use production S3 bucket with CDN)
STORAGE_PROVIDER="s3"
AWS_ACCESS_KEY_ID="production-aws-access-key"
AWS_SECRET_ACCESS_KEY="production-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="ma-platform-production-files"
CDN_URL="https://cdn.ma-platform.com"

# Encryption (Use strong production key - CHANGE THIS!)
ENCRYPTION_KEY="CHANGE-THIS-production-encryption-key"

# Rate Limiting (Strict for production)
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="100"

# Logging
LOG_LEVEL="warn"

# Feature Flags
ENABLE_SWAGGER_DOCS="false"
ENABLE_DEBUG_ROUTES="false"
ENABLE_SEED_DATA="false"

# Security
CORS_ORIGIN="https://app.ma-platform.com"
SECURE_COOKIES="true"
TRUST_PROXY="true"

# Monitoring
SENTRY_DSN="https://<EMAIL>/project"
NEW_RELIC_LICENSE_KEY="production-newrelic-license-key"

# Performance
CLUSTER_MODE="true"
MAX_WORKERS="4"

# Backup
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS="30"
