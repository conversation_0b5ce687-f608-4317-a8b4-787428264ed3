# Staging Environment Configuration
NODE_ENV=staging
PORT=3001

# Database (Use staging database)
DATABASE_URL="******************************************************/ma_platform_staging"

# Redis
REDIS_URL="redis://staging-redis:6379"

# JWT (Use staging-specific secrets)
JWT_SECRET="staging-jwt-secret-key-at-least-32-characters-long-change-in-production"
JWT_EXPIRES_IN="7d"

# Frontend
FRONTEND_URL="https://staging.ma-platform.com"

# Email (Use staging email service)
SMTP_HOST="smtp.staging-email-service.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="staging-email-password"

# File Storage (Use staging S3 bucket)
STORAGE_PROVIDER="s3"
AWS_ACCESS_KEY_ID="staging-aws-access-key"
AWS_SECRET_ACCESS_KEY="staging-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="ma-platform-staging-files"

# Encryption
ENCRYPTION_KEY="staging-encryption-key-32-characters"

# Rate Limiting
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="500"

# Logging
LOG_LEVEL="info"

# Feature Flags
ENABLE_SWAGGER_DOCS="true"
ENABLE_DEBUG_ROUTES="false"
ENABLE_SEED_DATA="false"

# Monitoring
SENTRY_DSN="https://<EMAIL>/project"
NEW_RELIC_LICENSE_KEY="staging-newrelic-license-key"
