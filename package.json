{"name": "ma-enterprise-platform", "version": "1.0.0", "description": "Enterprise M&A Management Platform", "private": true, "workspaces": ["packages/*"], "packageManager": "pnpm@8.15.0", "scripts": {"dev": "concurrently \"pnpm --filter backend dev\" \"pnpm --filter frontend dev\"", "build": "pnpm --filter backend build && pnpm --filter frontend build", "test": "pnpm --filter \"*\" test", "lint": "pnpm --filter \"*\" lint", "type-check": "pnpm --filter \"*\" type-check", "clean": "pnpm --filter \"*\" clean", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "keywords": ["m&a", "enterprise", "saas", "mergers", "acquisitions", "due-diligence", "virtual-data-room"], "author": "M&A Platform Team", "license": "UNLICENSED"}