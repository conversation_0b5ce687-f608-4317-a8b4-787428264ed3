{"name": "@ma-platform/frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist .vite", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "@tanstack/react-query": "^5.81.5", "@tanstack/react-form": "^1.12.4", "zod": "^3.25.67", "@hookform/resolvers": "^5.1.1", "tailwindcss": "^4.1.11", "clsx": "^2.1.1", "class-variance-authority": "^0.7.1", "lucide-react": "^0.525.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-avatar": "^1.1.10"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "typescript": "^5.8.3", "vite": "^7.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "@testing-library/react": "^16.3.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "vitest": "^3.2.4", "jsdom": "^26.1.0", "@vitest/ui": "^3.2.4", "msw": "^2.10.2"}}