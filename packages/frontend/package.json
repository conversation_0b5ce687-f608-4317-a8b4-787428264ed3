{"name": "@ma-platform/frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist .vite", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "@tanstack/react-form": "^0.26.4", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2", "tailwindcss": "^3.3.6", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.294.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-badge": "^1.0.4"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "vitest": "^1.0.4", "jsdom": "^23.0.1", "@vitest/ui": "^1.0.4", "msw": "^2.0.8"}}