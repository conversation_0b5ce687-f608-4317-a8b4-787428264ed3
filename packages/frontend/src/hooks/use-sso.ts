import { useState, useEffect, useCallback } from 'react'
import { SsoService, SsoProvider } from '@/services/sso.service'

interface UseSsoOptions {
  tenantId?: string
  autoLoad?: boolean
}

interface UseSsoReturn {
  providers: SsoProvider[]
  loading: boolean
  error: string | null
  loadProviders: (tenantId: string) => Promise<void>
  initiateLogin: (providerId: string, tenantId: string) => Promise<void>
  isLoading: (providerId: string) => boolean
}

export function useSso(options: UseSsoOptions = {}): UseSsoReturn {
  const { tenantId, autoLoad = false } = options
  
  const [providers, setProviders] = useState<SsoProvider[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [loadingProviders, setLoadingProviders] = useState<Set<string>>(new Set())

  const loadProviders = useCallback(async (targetTenantId: string) => {
    if (!targetTenantId) {
      setError('Tenant ID is required')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const providersData = await SsoService.getProviders(targetTenantId)
      setProviders(providersData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load SSO providers'
      setError(errorMessage)
      console.error('Failed to load SSO providers:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const initiateLogin = useCallback(async (providerId: string, targetTenantId: string) => {
    if (!providerId || !targetTenantId) {
      setError('Provider ID and Tenant ID are required')
      return
    }

    // Check if provider exists and is enabled
    const provider = providers.find(p => p.id === providerId)
    if (!provider) {
      setError('Provider not found')
      return
    }

    if (!provider.enabled) {
      setError('Provider is not enabled')
      return
    }

    setLoadingProviders(prev => new Set(prev).add(providerId))
    setError(null)

    try {
      // Store current location for redirect after login
      const currentPath = window.location.pathname + window.location.search
      if (currentPath !== '/login' && currentPath !== '/register') {
        sessionStorage.setItem('auth_redirect', currentPath)
      }

      await SsoService.initiateLogin(providerId, targetTenantId)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initiate SSO login'
      setError(errorMessage)
      console.error('Failed to initiate SSO login:', err)
      
      setLoadingProviders(prev => {
        const newSet = new Set(prev)
        newSet.delete(providerId)
        return newSet
      })
    }
  }, [providers])

  const isLoading = useCallback((providerId: string): boolean => {
    return loadingProviders.has(providerId)
  }, [loadingProviders])

  // Auto-load providers if tenantId is provided and autoLoad is true
  useEffect(() => {
    if (autoLoad && tenantId) {
      loadProviders(tenantId)
    }
  }, [autoLoad, tenantId, loadProviders])

  // Clean up loading states on unmount
  useEffect(() => {
    return () => {
      setLoadingProviders(new Set())
    }
  }, [])

  return {
    providers,
    loading,
    error,
    loadProviders,
    initiateLogin,
    isLoading
  }
}
