import { useState, useEffect, useCallback } from 'react'
import { MfaService, MfaStatus, MfaSetupData } from '@/services/mfa.service'

interface UseMfaReturn {
  // Status
  status: MfaStatus | null
  loading: boolean
  error: string | null
  
  // Setup
  setupData: MfaSetupData | null
  setupLoading: boolean
  setupError: string | null
  
  // Actions
  loadStatus: () => Promise<void>
  startSetup: () => Promise<void>
  verifySetup: (code: string) => Promise<boolean>
  verifyCode: (code: string) => Promise<boolean>
  disableMfa: (code: string) => Promise<void>
  generateBackupCodes: (code: string) => Promise<string[]>
  
  // Utilities
  clearErrors: () => void
  clearSetup: () => void
}

export function useMfa(): UseMfaReturn {
  // Status state
  const [status, setStatus] = useState<MfaStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Setup state
  const [setupData, setSetupData] = useState<MfaSetupData | null>(null)
  const [setupLoading, setSetupLoading] = useState(false)
  const [setupError, setSetupError] = useState<string | null>(null)

  // Load MFA status
  const loadStatus = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const statusData = await MfaService.getStatus()
      setStatus(statusData)
    } catch (err) {
      const errorMessage = MfaService.parseMfaError(err)
      setError(errorMessage)
      console.error('Failed to load MFA status:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // Start MFA setup
  const startSetup = useCallback(async () => {
    setSetupLoading(true)
    setSetupError(null)

    try {
      const data = await MfaService.setupMfa()
      setSetupData(data)
    } catch (err) {
      const errorMessage = MfaService.parseMfaError(err)
      setSetupError(errorMessage)
      console.error('Failed to start MFA setup:', err)
    } finally {
      setSetupLoading(false)
    }
  }, [])

  // Verify MFA setup
  const verifySetup = useCallback(async (code: string): Promise<boolean> => {
    setSetupLoading(true)
    setSetupError(null)

    try {
      const verified = await MfaService.verifySetup(code)
      
      if (verified) {
        // Reload status to reflect changes
        await loadStatus()
        setSetupData(null) // Clear setup data after successful verification
      }
      
      return verified
    } catch (err) {
      const errorMessage = MfaService.parseMfaError(err)
      setSetupError(errorMessage)
      console.error('Failed to verify MFA setup:', err)
      return false
    } finally {
      setSetupLoading(false)
    }
  }, [loadStatus])

  // Verify MFA code
  const verifyCode = useCallback(async (code: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const result = await MfaService.verifyCode(code)
      return result.valid
    } catch (err) {
      const errorMessage = MfaService.parseMfaError(err)
      setError(errorMessage)
      console.error('Failed to verify MFA code:', err)
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  // Disable MFA
  const disableMfa = useCallback(async (code: string): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      await MfaService.disableMfa(code)
      // Reload status to reflect changes
      await loadStatus()
    } catch (err) {
      const errorMessage = MfaService.parseMfaError(err)
      setError(errorMessage)
      console.error('Failed to disable MFA:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [loadStatus])

  // Generate new backup codes
  const generateBackupCodes = useCallback(async (code: string): Promise<string[]> => {
    setLoading(true)
    setError(null)

    try {
      const newCodes = await MfaService.generateNewBackupCodes(code)
      // Reload status to reflect changes
      await loadStatus()
      return newCodes
    } catch (err) {
      const errorMessage = MfaService.parseMfaError(err)
      setError(errorMessage)
      console.error('Failed to generate backup codes:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [loadStatus])

  // Clear errors
  const clearErrors = useCallback(() => {
    setError(null)
    setSetupError(null)
  }, [])

  // Clear setup data
  const clearSetup = useCallback(() => {
    setSetupData(null)
    setSetupError(null)
  }, [])

  // Load status on mount
  useEffect(() => {
    loadStatus()
  }, [loadStatus])

  return {
    // Status
    status,
    loading,
    error,
    
    // Setup
    setupData,
    setupLoading,
    setupError,
    
    // Actions
    loadStatus,
    startSetup,
    verifySetup,
    verifyCode,
    disableMfa,
    generateBackupCodes,
    
    // Utilities
    clearErrors,
    clearSetup
  }
}
