import { useTenant } from '@/contexts/tenant.context'
import { TenantService } from '@/services/tenant.service'

interface UseTenantFeaturesReturn {
  // Feature checks
  hasFeature: (feature: string) => boolean
  hasApiAccess: boolean
  hasCustomBranding: boolean
  hasAdvancedReporting: boolean
  hasSso: boolean
  hasCustomIntegrations: boolean
  
  // Limit checks
  isWithinLimit: (limitType: string, currentValue: number) => boolean
  canAddUsers: (currentCount: number) => boolean
  canAddDeals: (currentCount: number) => boolean
  canUploadFile: (fileSize: number, currentUsage: number) => boolean
  
  // Plan info
  plan: string
  planFeatures: Record<string, any>
  
  // Usage info
  maxUsers: number
  maxDeals: number
  maxStorage: number
  
  // Utilities
  formatStorageSize: (bytes: number) => string
  getUsagePercentage: (current: number, max: number) => number
}

export function useTenantFeatures(): UseTenantFeaturesReturn {
  const { tenant, hasFeature, isWithinLimit } = useTenant()

  // Get plan features
  const plan = tenant?.plan || 'STARTER'
  const planFeatures = TenantService.getPlanFeatures(plan)

  // Feature checks
  const hasApiAccess = hasFeature('apiAccess')
  const hasCustomBranding = hasFeature('customBranding')
  const hasAdvancedReporting = hasFeature('advancedReporting')
  const hasSso = hasFeature('sso')
  const hasCustomIntegrations = hasFeature('customIntegrations')

  // Get limits
  const maxUsers = tenant?.limits?.maxUsers ?? planFeatures.maxUsers ?? 5
  const maxDeals = tenant?.limits?.maxDeals ?? planFeatures.maxDeals ?? 10
  const maxStorage = tenant?.limits?.maxStorage ?? planFeatures.maxStorage ?? (1024 * 1024 * 1024)

  // Limit checks
  const canAddUsers = (currentCount: number): boolean => {
    return isWithinLimit('maxUsers', currentCount)
  }

  const canAddDeals = (currentCount: number): boolean => {
    return isWithinLimit('maxDeals', currentCount)
  }

  const canUploadFile = (fileSize: number, currentUsage: number): boolean => {
    return isWithinLimit('maxStorage', currentUsage + fileSize)
  }

  // Utilities
  const formatStorageSize = (bytes: number): string => {
    return TenantService.formatStorageSize(bytes)
  }

  const getUsagePercentage = (current: number, max: number): number => {
    if (max === -1) return 0 // Unlimited
    if (max === 0) return 100
    return Math.min(100, (current / max) * 100)
  }

  return {
    // Feature checks
    hasFeature,
    hasApiAccess,
    hasCustomBranding,
    hasAdvancedReporting,
    hasSso,
    hasCustomIntegrations,
    
    // Limit checks
    isWithinLimit,
    canAddUsers,
    canAddDeals,
    canUploadFile,
    
    // Plan info
    plan,
    planFeatures,
    
    // Usage info
    maxUsers,
    maxDeals,
    maxStorage,
    
    // Utilities
    formatStorageSize,
    getUsagePercentage
  }
}

// Hook for tenant trial information
export function useTenantTrial() {
  const { tenant, isInTrial, trialDaysRemaining } = useTenant()

  const isTrialExpired = isInTrial && trialDaysRemaining <= 0
  const isTrialExpiringSoon = isInTrial && trialDaysRemaining <= 7
  const trialEndDate = tenant?.trialEndsAt ? new Date(tenant.trialEndsAt) : null

  const getTrialStatus = (): 'active' | 'expiring_soon' | 'expired' | 'not_trial' => {
    if (!isInTrial) return 'not_trial'
    if (isTrialExpired) return 'expired'
    if (isTrialExpiringSoon) return 'expiring_soon'
    return 'active'
  }

  const getTrialMessage = (): string => {
    const status = getTrialStatus()
    
    switch (status) {
      case 'expired':
        return 'Your trial has expired. Please upgrade to continue using the platform.'
      case 'expiring_soon':
        return `Your trial expires in ${trialDaysRemaining} day${trialDaysRemaining === 1 ? '' : 's'}. Upgrade now to avoid interruption.`
      case 'active':
        return `You have ${trialDaysRemaining} day${trialDaysRemaining === 1 ? '' : 's'} remaining in your trial.`
      default:
        return ''
    }
  }

  return {
    isInTrial,
    isTrialExpired,
    isTrialExpiringSoon,
    trialDaysRemaining,
    trialEndDate,
    getTrialStatus,
    getTrialMessage
  }
}

// Hook for tenant branding
export function useTenantBranding() {
  const { tenant } = useTenant()

  const branding = tenant?.branding || {}
  
  const primaryColor = branding.primaryColor || '#3b82f6'
  const secondaryColor = branding.secondaryColor || '#64748b'
  const logo = branding.logo || tenant?.logo
  const favicon = branding.favicon
  const customCss = branding.customCss

  const getBrandingStyles = () => ({
    '--primary-color': primaryColor,
    '--secondary-color': secondaryColor
  })

  const getLogoUrl = (fallback?: string): string => {
    return logo || fallback || '/logo.png'
  }

  return {
    branding,
    primaryColor,
    secondaryColor,
    logo,
    favicon,
    customCss,
    getBrandingStyles,
    getLogoUrl
  }
}

// Hook for tenant settings
export function useTenantSettings() {
  const { tenant, updateTenant } = useTenant()

  const settings = tenant?.settings || {}

  const getSetting = (key: string, defaultValue?: any): any => {
    return settings[key] ?? defaultValue
  }

  const updateSetting = async (key: string, value: any): Promise<void> => {
    const newSettings = {
      ...settings,
      [key]: value
    }

    await updateTenant({ settings: newSettings })
  }

  const updateSettings = async (newSettings: Record<string, any>): Promise<void> => {
    const mergedSettings = {
      ...settings,
      ...newSettings
    }

    await updateTenant({ settings: mergedSettings })
  }

  return {
    settings,
    getSetting,
    updateSetting,
    updateSettings
  }
}
