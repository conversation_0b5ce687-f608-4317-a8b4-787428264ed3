import { useState, useEffect, useCallback, useRef } from 'react'
import { SessionService, SessionData, SessionStats } from '@/services/session.service'

interface UseSessionOptions {
  autoRefresh?: boolean
  refreshMinutes?: number
  monitorInterval?: number
  warningMinutes?: number
}

interface UseSessionReturn {
  // Current session
  currentSession: SessionData | null
  
  // All sessions
  allSessions: SessionData[]
  
  // Stats
  stats: SessionStats | null
  
  // State
  loading: boolean
  error: string | null
  
  // Session status
  isExpiringSoon: boolean
  timeUntilExpiration: {
    totalMinutes: number
    hours: number
    minutes: number
    isExpired: boolean
  } | null
  
  // Actions
  loadCurrentSession: () => Promise<void>
  loadAllSessions: () => Promise<void>
  loadStats: () => Promise<void>
  extendSession: (additionalTime?: number) => Promise<void>
  destroySession: (sessionId: string) => Promise<void>
  destroyAllOtherSessions: () => Promise<void>
  validateSession: () => Promise<boolean>
  
  // Utilities
  clearError: () => void
}

export function useSession(options: UseSessionOptions = {}): UseSessionReturn {
  const {
    autoRefresh = true,
    refreshMinutes = 10,
    monitorInterval = 5,
    warningMinutes = 5
  } = options

  // State
  const [currentSession, setCurrentSession] = useState<SessionData | null>(null)
  const [allSessions, setAllSessions] = useState<SessionData[]>([])
  const [stats, setStats] = useState<SessionStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Refs for timers
  const refreshTimerRef = useRef<NodeJS.Timeout>()
  const monitorTimerRef = useRef<NodeJS.Timeout>()

  // Load current session
  const loadCurrentSession = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const session = await SessionService.getCurrentSession()
      setCurrentSession(session)

      // Set up auto-refresh if enabled
      if (autoRefresh && session.expiresAt) {
        if (refreshTimerRef.current) {
          SessionService.clearSessionRefreshTimer(refreshTimerRef.current)
        }
        refreshTimerRef.current = SessionService.startSessionRefreshTimer(
          session.expiresAt,
          refreshMinutes
        )
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load session'
      setError(errorMessage)
      console.error('Failed to load current session:', err)
    } finally {
      setLoading(false)
    }
  }, [autoRefresh, refreshMinutes])

  // Load all sessions
  const loadAllSessions = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const sessions = await SessionService.getAllSessions()
      setAllSessions(sessions)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load sessions'
      setError(errorMessage)
      console.error('Failed to load all sessions:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // Load session stats
  const loadStats = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const sessionStats = await SessionService.getSessionStats()
      setStats(sessionStats)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load session stats'
      setError(errorMessage)
      console.error('Failed to load session stats:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // Extend session
  const extendSession = useCallback(async (additionalTime?: number) => {
    setLoading(true)
    setError(null)

    try {
      await SessionService.extendSession(additionalTime)
      // Reload current session to get updated expiration
      await loadCurrentSession()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to extend session'
      setError(errorMessage)
      console.error('Failed to extend session:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [loadCurrentSession])

  // Destroy specific session
  const destroySession = useCallback(async (sessionId: string) => {
    setLoading(true)
    setError(null)

    try {
      await SessionService.destroySession(sessionId)
      
      // If destroying current session, handle logout
      if (SessionService.isCurrentSession(sessionId)) {
        SessionService.handleSessionExpiration()
      } else {
        // Reload sessions to reflect changes
        await loadAllSessions()
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to destroy session'
      setError(errorMessage)
      console.error('Failed to destroy session:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [loadAllSessions])

  // Destroy all other sessions
  const destroyAllOtherSessions = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      await SessionService.destroyAllOtherSessions()
      // Reload sessions to reflect changes
      await loadAllSessions()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to destroy sessions'
      setError(errorMessage)
      console.error('Failed to destroy other sessions:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [loadAllSessions])

  // Validate session
  const validateSession = useCallback(async (): Promise<boolean> => {
    try {
      const validation = await SessionService.validateSession()
      if (!validation.isValid) {
        SessionService.handleSessionExpiration()
      }
      return validation.isValid
    } catch (err) {
      console.error('Session validation failed:', err)
      SessionService.handleSessionExpiration()
      return false
    }
  }, [])

  // Clear error
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Check if session is expiring soon
  const isExpiringSoon = currentSession?.expiresAt 
    ? SessionService.isSessionExpiringSoon(currentSession.expiresAt, warningMinutes)
    : false

  // Get time until expiration
  const timeUntilExpiration = currentSession?.expiresAt
    ? SessionService.getTimeUntilExpiration(currentSession.expiresAt)
    : null

  // Set up session monitoring
  useEffect(() => {
    if (monitorInterval > 0) {
      monitorTimerRef.current = SessionService.startSessionMonitoring(monitorInterval)
    }

    return () => {
      if (monitorTimerRef.current) {
        SessionService.stopSessionMonitoring(monitorTimerRef.current)
      }
    }
  }, [monitorInterval])

  // Load initial session data
  useEffect(() => {
    loadCurrentSession()
  }, [loadCurrentSession])

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (refreshTimerRef.current) {
        SessionService.clearSessionRefreshTimer(refreshTimerRef.current)
      }
      if (monitorTimerRef.current) {
        SessionService.stopSessionMonitoring(monitorTimerRef.current)
      }
    }
  }, [])

  return {
    // Current session
    currentSession,
    
    // All sessions
    allSessions,
    
    // Stats
    stats,
    
    // State
    loading,
    error,
    
    // Session status
    isExpiringSoon,
    timeUntilExpiration,
    
    // Actions
    loadCurrentSession,
    loadAllSessions,
    loadStats,
    extendSession,
    destroySession,
    destroyAllOtherSessions,
    validateSession,
    
    // Utilities
    clearError
  }
}
