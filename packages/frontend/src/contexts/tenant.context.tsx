import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { TenantService, Tenant } from '@/services/tenant.service'
import { apiClient } from '@/services/api.client'

interface TenantState {
  // Current tenant
  tenant: Tenant | null
  
  // Loading states
  loading: boolean
  error: string | null
  
  // Tenant resolution
  isResolved: boolean
  resolutionMethod: 'subdomain' | 'domain' | 'manual' | null
  
  // Actions
  setTenant: (tenant: Tenant | null) => void
  resolveTenant: () => Promise<void>
  updateTenant: (data: any) => Promise<void>
  
  // Feature checks
  hasFeature: (feature: string) => boolean
  isWithinLimit: (limitType: string, currentValue: number) => boolean
  
  // Branding
  applyBranding: () => void
  removeBranding: () => void
  
  // Trial info
  isInTrial: boolean
  trialDaysRemaining: number
  
  // Utilities
  clearError: () => void
}

interface TenantProviderProps {
  children: ReactNode
  autoResolve?: boolean
  tenantId?: string // For manual tenant setting
}

const TenantContext = createContext<TenantState | undefined>(undefined)

export function TenantProvider({ 
  children, 
  autoResolve = true,
  tenantId 
}: TenantProviderProps) {
  const [tenant, setTenantState] = useState<Tenant | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isResolved, setIsResolved] = useState(false)
  const [resolutionMethod, setResolutionMethod] = useState<'subdomain' | 'domain' | 'manual' | null>(null)

  // Set tenant and update API client
  const setTenant = (newTenant: Tenant | null) => {
    setTenantState(newTenant)
    
    if (newTenant) {
      // Set tenant ID in API client for automatic inclusion in requests
      apiClient.setTenantId(newTenant.id)
      
      // Apply branding
      TenantService.applyTenantBranding(newTenant)
    } else {
      // Clear tenant ID from API client
      apiClient.removeTenantId()
      
      // Remove branding
      TenantService.removeTenantBranding()
    }
  }

  // Resolve tenant from URL or manual ID
  const resolveTenant = async () => {
    setLoading(true)
    setError(null)

    try {
      let resolvedTenant: Tenant | null = null
      let method: 'subdomain' | 'domain' | 'manual' | null = null

      // Manual tenant ID takes precedence
      if (tenantId) {
        resolvedTenant = await TenantService.getTenantById(tenantId)
        method = 'manual'
      } else if (autoResolve) {
        // Try to resolve from URL
        resolvedTenant = await TenantService.resolveTenantFromUrl()
        const urlInfo = TenantService.getCurrentTenantFromUrl()
        method = urlInfo.type
      }

      setTenant(resolvedTenant)
      setResolutionMethod(method)
      setIsResolved(true)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to resolve tenant'
      setError(errorMessage)
      console.error('Tenant resolution error:', err)
    } finally {
      setLoading(false)
    }
  }

  // Update tenant
  const updateTenant = async (data: any) => {
    if (!tenant) {
      throw new Error('No tenant to update')
    }

    setLoading(true)
    setError(null)

    try {
      const updatedTenant = await TenantService.updateTenant(tenant.id, data)
      setTenant(updatedTenant)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update tenant'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // Feature checks
  const hasFeature = (feature: string): boolean => {
    if (!tenant) return false
    return TenantService.hasFeature(tenant, feature)
  }

  const isWithinLimit = (limitType: string, currentValue: number): boolean => {
    if (!tenant) return false
    return TenantService.isWithinLimit(tenant, limitType, currentValue)
  }

  // Branding
  const applyBranding = () => {
    if (tenant) {
      TenantService.applyTenantBranding(tenant)
    }
  }

  const removeBranding = () => {
    TenantService.removeTenantBranding()
  }

  // Trial info
  const isInTrial = tenant ? TenantService.isInTrial(tenant) : false
  const trialDaysRemaining = tenant ? TenantService.getTrialDaysRemaining(tenant) : 0

  // Clear error
  const clearError = () => {
    setError(null)
  }

  // Auto-resolve tenant on mount
  useEffect(() => {
    if (autoResolve || tenantId) {
      resolveTenant()
    }
  }, [autoResolve, tenantId])

  // Listen for tenant changes in localStorage (for cross-tab sync)
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'tenant_id') {
        if (event.newValue) {
          // Tenant changed, resolve new tenant
          resolveTenant()
        } else {
          // Tenant cleared
          setTenant(null)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  // Apply branding when tenant changes
  useEffect(() => {
    if (tenant) {
      applyBranding()
    } else {
      removeBranding()
    }
  }, [tenant])

  const value: TenantState = {
    // Current tenant
    tenant,
    
    // Loading states
    loading,
    error,
    
    // Tenant resolution
    isResolved,
    resolutionMethod,
    
    // Actions
    setTenant,
    resolveTenant,
    updateTenant,
    
    // Feature checks
    hasFeature,
    isWithinLimit,
    
    // Branding
    applyBranding,
    removeBranding,
    
    // Trial info
    isInTrial,
    trialDaysRemaining,
    
    // Utilities
    clearError
  }

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  )
}

export function useTenant() {
  const context = useContext(TenantContext)
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider')
  }
  return context
}

// HOC for components that require a tenant
export function withTenant<P extends object>(
  Component: React.ComponentType<P>
) {
  return function TenantWrappedComponent(props: P) {
    const { tenant, loading, error } = useTenant()
    
    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      )
    }
    
    if (error) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Tenant Error</h1>
            <p className="text-gray-600">{error}</p>
          </div>
        </div>
      )
    }
    
    if (!tenant) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">No Tenant Found</h1>
            <p className="text-gray-600">Unable to identify the tenant for this request.</p>
          </div>
        </div>
      )
    }

    return <Component {...props} />
  }
}

// Hook for tenant-aware routing
export function useTenantRouting() {
  const { tenant } = useTenant()
  
  const getTenantUrl = (path: string = ''): string => {
    if (!tenant) return path
    
    const baseUrl = window.location.origin
    
    // If using subdomain
    if (tenant.subdomain) {
      const hostname = window.location.hostname
      const parts = hostname.split('.')
      
      if (parts.length >= 2) {
        const domain = parts.slice(1).join('.')
        return `${window.location.protocol}//${tenant.subdomain}.${domain}${path}`
      }
    }
    
    // Fallback to current URL with path
    return `${baseUrl}${path}`
  }
  
  const navigateToTenant = (path: string = '') => {
    const url = getTenantUrl(path)
    window.location.href = url
  }
  
  return {
    getTenantUrl,
    navigateToTenant
  }
}
