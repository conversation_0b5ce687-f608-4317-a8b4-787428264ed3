import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { SecurityHeaderUtils, DeviceUtils, RateLimitUtils } from '@/utils/security'

interface SecurityState {
  // Security status
  isSecureContext: boolean
  hasSecurityHeaders: boolean
  deviceFingerprint: string
  isIncognitoMode: boolean
  isDevToolsOpen: boolean
  
  // Security warnings
  warnings: string[]
  
  // Rate limiting
  isRateLimited: (key: string, maxAttempts: number, windowMs: number) => boolean
  resetRateLimit: (key: string) => void
  getRemainingAttempts: (key: string, maxAttempts: number) => number
  
  // Security actions
  reportSecurityEvent: (event: SecurityEvent) => void
  checkSecurityStatus: () => void
}

interface SecurityEvent {
  type: 'suspicious_activity' | 'rate_limit_exceeded' | 'unauthorized_access' | 'security_warning'
  details: any
  timestamp: Date
}

interface SecurityProviderProps {
  children: ReactNode
  enableMonitoring?: boolean
  reportingEndpoint?: string
}

const SecurityContext = createContext<SecurityState | undefined>(undefined)

export function SecurityProvider({ 
  children, 
  enableMonitoring = true,
  reportingEndpoint 
}: SecurityProviderProps) {
  const [isSecureContext, setIsSecureContext] = useState(false)
  const [hasSecurityHeaders, setHasSecurityHeaders] = useState(false)
  const [deviceFingerprint, setDeviceFingerprint] = useState('')
  const [isIncognitoMode, setIsIncognitoMode] = useState(false)
  const [isDevToolsOpen, setIsDevToolsOpen] = useState(false)
  const [warnings, setWarnings] = useState<string[]>([])
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([])

  // Check security status
  const checkSecurityStatus = () => {
    // Check secure context
    setIsSecureContext(SecurityHeaderUtils.isSecureContext())
    
    // Check security headers
    const headerCheck = SecurityHeaderUtils.validateSecurityHeaders()
    setHasSecurityHeaders(headerCheck.warnings.length === 0)
    setWarnings(headerCheck.warnings)
    
    // Get device fingerprint
    setDeviceFingerprint(DeviceUtils.getDeviceFingerprint())
    
    // Check incognito mode
    DeviceUtils.isIncognitoMode().then(setIsIncognitoMode)
    
    // Check dev tools
    setIsDevToolsOpen(DeviceUtils.isDevToolsOpen())
  }

  // Report security event
  const reportSecurityEvent = (event: SecurityEvent) => {
    const eventWithTimestamp = {
      ...event,
      timestamp: new Date(),
      deviceFingerprint,
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // Add to local events
    setSecurityEvents(prev => [...prev.slice(-99), eventWithTimestamp]) // Keep last 100 events

    // Report to server if endpoint provided
    if (reportingEndpoint) {
      fetch(reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(eventWithTimestamp)
      }).catch(error => {
        console.error('Failed to report security event:', error)
      })
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.warn('Security Event:', eventWithTimestamp)
    }
  }

  // Rate limiting functions
  const isRateLimited = (key: string, maxAttempts: number, windowMs: number): boolean => {
    const limited = RateLimitUtils.isRateLimited(key, maxAttempts, windowMs)
    
    if (limited) {
      reportSecurityEvent({
        type: 'rate_limit_exceeded',
        details: { key, maxAttempts, windowMs }
      })
    }
    
    return limited
  }

  const resetRateLimit = (key: string): void => {
    RateLimitUtils.resetRateLimit(key)
  }

  const getRemainingAttempts = (key: string, maxAttempts: number): number => {
    return RateLimitUtils.getRemainingAttempts(key, maxAttempts)
  }

  // Security monitoring
  useEffect(() => {
    if (!enableMonitoring) return

    // Initial security check
    checkSecurityStatus()

    // Monitor for security changes
    const securityInterval = setInterval(() => {
      const newDevToolsState = DeviceUtils.isDevToolsOpen()
      if (newDevToolsState !== isDevToolsOpen) {
        setIsDevToolsOpen(newDevToolsState)
        if (newDevToolsState) {
          reportSecurityEvent({
            type: 'security_warning',
            details: { event: 'dev_tools_opened' }
          })
        }
      }
    }, 5000)

    // Monitor for suspicious activity
    const activityMonitor = () => {
      // Check for rapid clicks (potential bot activity)
      let clickCount = 0
      const clickHandler = () => {
        clickCount++
        if (clickCount > 10) {
          reportSecurityEvent({
            type: 'suspicious_activity',
            details: { event: 'rapid_clicking', count: clickCount }
          })
          clickCount = 0
        }
      }

      document.addEventListener('click', clickHandler)
      
      // Reset click count every second
      const clickReset = setInterval(() => {
        clickCount = 0
      }, 1000)

      return () => {
        document.removeEventListener('click', clickHandler)
        clearInterval(clickReset)
      }
    }

    const cleanupActivityMonitor = activityMonitor()

    // Monitor for page visibility changes
    const visibilityHandler = () => {
      if (document.hidden) {
        reportSecurityEvent({
          type: 'security_warning',
          details: { event: 'page_hidden' }
        })
      }
    }

    document.addEventListener('visibilitychange', visibilityHandler)

    // Monitor for focus changes
    const focusHandler = () => {
      reportSecurityEvent({
        type: 'security_warning',
        details: { event: 'window_focus_lost' }
      })
    }

    window.addEventListener('blur', focusHandler)

    // Cleanup
    return () => {
      clearInterval(securityInterval)
      cleanupActivityMonitor()
      document.removeEventListener('visibilitychange', visibilityHandler)
      window.removeEventListener('blur', focusHandler)
    }
  }, [enableMonitoring, isDevToolsOpen])

  // Monitor for console access (potential security risk)
  useEffect(() => {
    if (!enableMonitoring) return

    // Override console methods to detect usage
    const originalLog = console.log
    const originalWarn = console.warn
    const originalError = console.error

    console.log = (...args) => {
      reportSecurityEvent({
        type: 'security_warning',
        details: { event: 'console_access', method: 'log' }
      })
      originalLog.apply(console, args)
    }

    console.warn = (...args) => {
      reportSecurityEvent({
        type: 'security_warning',
        details: { event: 'console_access', method: 'warn' }
      })
      originalWarn.apply(console, args)
    }

    console.error = (...args) => {
      reportSecurityEvent({
        type: 'security_warning',
        details: { event: 'console_access', method: 'error' }
      })
      originalError.apply(console, args)
    }

    return () => {
      console.log = originalLog
      console.warn = originalWarn
      console.error = originalError
    }
  }, [enableMonitoring])

  const value: SecurityState = {
    // Security status
    isSecureContext,
    hasSecurityHeaders,
    deviceFingerprint,
    isIncognitoMode,
    isDevToolsOpen,
    
    // Security warnings
    warnings,
    
    // Rate limiting
    isRateLimited,
    resetRateLimit,
    getRemainingAttempts,
    
    // Security actions
    reportSecurityEvent,
    checkSecurityStatus
  }

  return (
    <SecurityContext.Provider value={value}>
      {children}
    </SecurityContext.Provider>
  )
}

export function useSecurity() {
  const context = useContext(SecurityContext)
  if (context === undefined) {
    throw new Error('useSecurity must be used within a SecurityProvider')
  }
  return context
}

// HOC for components that need security monitoring
export function withSecurity<P extends object>(
  Component: React.ComponentType<P>
) {
  return function SecurityWrappedComponent(props: P) {
    const security = useSecurity()
    
    useEffect(() => {
      security.reportSecurityEvent({
        type: 'security_warning',
        details: { 
          event: 'component_mounted',
          component: Component.displayName || Component.name 
        }
      })
    }, [security])

    return <Component {...props} />
  }
}
