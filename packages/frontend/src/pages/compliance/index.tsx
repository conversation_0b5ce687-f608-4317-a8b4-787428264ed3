import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Shield, 
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  BarChart3
} from 'lucide-react'

// Import compliance components
import { ComplianceDashboard } from '@/components/compliance/compliance-dashboard'

interface Deal {
  id: string
  name: string
  type: string
  value: number
  currency: string
  status: string
  complianceStatus: 'NOT_INITIALIZED' | 'ACTIVE' | 'COMPLETED' | 'OVERDUE'
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  completionPercentage: number
  criticalAlerts: number
  lastUpdated: string
}

export default function CompliancePage() {
  const router = useRouter()
  const { dealId } = router.query
  const [deals, setDeals] = useState<Deal[]>([])
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockDeals: Deal[] = [
      {
        id: 'deal-1',
        name: 'Technology M&A - CloudTech Acquisition',
        type: 'ACQUISITION',
        value: 250000000,
        currency: 'USD',
        status: 'IN_PROGRESS',
        complianceStatus: 'ACTIVE',
        riskLevel: 'MEDIUM',
        completionPercentage: 67,
        criticalAlerts: 2,
        lastUpdated: '2024-02-15T10:30:00Z'
      },
      {
        id: 'deal-2',
        name: 'Healthcare Merger - MedCorp Integration',
        type: 'MERGER',
        value: 500000000,
        currency: 'USD',
        status: 'IN_PROGRESS',
        complianceStatus: 'OVERDUE',
        riskLevel: 'HIGH',
        completionPercentage: 45,
        criticalAlerts: 5,
        lastUpdated: '2024-02-14T16:45:00Z'
      },
      {
        id: 'deal-3',
        name: 'Financial Services Acquisition',
        type: 'ACQUISITION',
        value: 150000000,
        currency: 'USD',
        status: 'COMPLETED',
        complianceStatus: 'COMPLETED',
        riskLevel: 'LOW',
        completionPercentage: 100,
        criticalAlerts: 0,
        lastUpdated: '2024-02-10T14:20:00Z'
      },
      {
        id: 'deal-4',
        name: 'Energy Sector Joint Venture',
        type: 'JOINT_VENTURE',
        value: 75000000,
        currency: 'USD',
        status: 'PLANNING',
        complianceStatus: 'NOT_INITIALIZED',
        riskLevel: 'MEDIUM',
        completionPercentage: 0,
        criticalAlerts: 0,
        lastUpdated: '2024-02-12T11:15:00Z'
      }
    ]

    setDeals(mockDeals)

    // If dealId is provided in URL, select that deal
    if (dealId && typeof dealId === 'string') {
      const deal = mockDeals.find(d => d.id === dealId)
      if (deal) {
        setSelectedDeal(deal)
      }
    }

    setLoading(false)
  }, [dealId])

  // Filter deals
  const filteredDeals = deals.filter(deal => {
    const matchesSearch = deal.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || deal.complianceStatus === statusFilter
    return matchesSearch && matchesStatus
  })

  const getComplianceStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'ACTIVE':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'OVERDUE':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'NOT_INITIALIZED':
        return 'text-gray-600 bg-gray-50 border-gray-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'CRITICAL':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'HIGH':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'LOW':
        return 'text-green-600 bg-green-50 border-green-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const formatCurrency = (value: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const handleDealSelect = (deal: Deal) => {
    setSelectedDeal(deal)
    router.push(`/compliance?dealId=${deal.id}`, undefined, { shallow: true })
  }

  const handleBackToList = () => {
    setSelectedDeal(null)
    router.push('/compliance', undefined, { shallow: true })
  }

  const handleInitializeCompliance = () => {
    console.log('Initialize compliance for deal:', selectedDeal?.id)
    // Implementation would open initialization dialog
  }

  const handleConfigureSystem = () => {
    console.log('Configure compliance system for deal:', selectedDeal?.id)
    // Implementation would open configuration dialog
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Show deal-specific compliance dashboard
  if (selectedDeal) {
    return (
      <div className="space-y-6">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <button onClick={handleBackToList} className="hover:text-blue-600">
            Compliance Management
          </button>
          <span>/</span>
          <span className="font-medium">{selectedDeal.name}</span>
        </div>

        <ComplianceDashboard
          dealId={selectedDeal.id}
          onInitializeCompliance={handleInitializeCompliance}
          onConfigureSystem={handleConfigureSystem}
        />
      </div>
    )
  }

  // Show deals list
  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Compliance Management
              </CardTitle>
              <CardDescription>
                Manage regulatory compliance across all M&A transactions
              </CardDescription>
            </div>
            
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Compliance Project
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Deals</p>
                <p className="text-3xl font-bold">{deals.filter(d => d.complianceStatus === 'ACTIVE').length}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical Alerts</p>
                <p className="text-3xl font-bold text-red-600">
                  {deals.reduce((sum, deal) => sum + deal.criticalAlerts, 0)}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-3xl font-bold text-green-600">
                  {deals.filter(d => d.complianceStatus === 'COMPLETED').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Overdue</p>
                <p className="text-3xl font-bold text-orange-600">
                  {deals.filter(d => d.complianceStatus === 'OVERDUE').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search deals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Status</option>
              <option value="ACTIVE">Active</option>
              <option value="COMPLETED">Completed</option>
              <option value="OVERDUE">Overdue</option>
              <option value="NOT_INITIALIZED">Not Initialized</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Deals List */}
      <Card>
        <CardContent className="p-0">
          {filteredDeals.length === 0 ? (
            <div className="text-center py-12">
              <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No deals found</h3>
              <p className="text-gray-600">
                {searchTerm ? 'No deals match your search criteria.' : 'No compliance projects have been created yet.'}
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {filteredDeals.map((deal) => (
                <div key={deal.id} className="p-6 hover:bg-gray-50 cursor-pointer" onClick={() => handleDealSelect(deal)}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold">{deal.name}</h3>
                        <Badge className={`text-xs ${getComplianceStatusColor(deal.complianceStatus)}`}>
                          {deal.complianceStatus.replace('_', ' ')}
                        </Badge>
                        <Badge className={`text-xs ${getRiskLevelColor(deal.riskLevel)}`}>
                          {deal.riskLevel} Risk
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Type:</span> {deal.type}
                        </div>
                        <div>
                          <span className="font-medium">Value:</span> {formatCurrency(deal.value, deal.currency)}
                        </div>
                        <div>
                          <span className="font-medium">Completion:</span> {deal.completionPercentage}%
                        </div>
                        <div>
                          <span className="font-medium">Last Updated:</span> {formatDate(deal.lastUpdated)}
                        </div>
                      </div>
                      
                      {deal.criticalAlerts > 0 && (
                        <div className="flex items-center gap-2 mt-2">
                          <AlertTriangle className="h-4 w-4 text-red-600" />
                          <span className="text-sm text-red-600">
                            {deal.criticalAlerts} critical alert{deal.criticalAlerts > 1 ? 's' : ''}
                          </span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
