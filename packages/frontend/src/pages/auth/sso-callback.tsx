import { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { SsoService } from '@/services/sso.service'
import { apiClient } from '@/services/api.client'
import { AuthLayout } from '@/components/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Loader2, CheckCircle, XCircle, ArrowLeft } from 'lucide-react'

export function SsoCallbackPage() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [error, setError] = useState<string>('')
  const [userInfo, setUserInfo] = useState<any>(null)

  useEffect(() => {
    handleCallback()
  }, [])

  const handleCallback = async () => {
    try {
      // Check for error in URL params
      const errorParam = searchParams.get('error')
      if (errorParam) {
        const errorDescription = searchParams.get('error_description')
        const ssoError = SsoService.handleSsoError(errorParam, errorDescription || undefined)
        throw ssoError
      }

      // Get code and state from URL
      const code = searchParams.get('code')
      const state = searchParams.get('state')

      if (!code || !state) {
        throw new Error('Missing authorization code or state parameter')
      }

      // Handle the callback
      const result = await SsoService.handleCallback(code, state)

      // Store auth data
      apiClient.setAuthToken(result.token)
      apiClient.setUserData(result.user)
      apiClient.setTenantId(result.user.tenantId)

      setUserInfo(result.user)
      setStatus('success')

      // Redirect after a short delay
      setTimeout(() => {
        const redirectTo = sessionStorage.getItem('auth_redirect') || '/dashboard'
        sessionStorage.removeItem('auth_redirect')
        navigate(redirectTo, { replace: true })
      }, 2000)

    } catch (error) {
      console.error('SSO callback error:', error)
      setError(error instanceof Error ? error.message : 'An unexpected error occurred')
      setStatus('error')
      
      // Clear any stored SSO data
      SsoService.clearSsoData()
    }
  }

  const handleRetry = () => {
    setStatus('loading')
    setError('')
    handleCallback()
  }

  const handleBackToLogin = () => {
    navigate('/login', { replace: true })
  }

  return (
    <AuthLayout showLogo={false}>
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {status === 'loading' && (
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
            )}
            {status === 'success' && (
              <CheckCircle className="h-12 w-12 text-green-500" />
            )}
            {status === 'error' && (
              <XCircle className="h-12 w-12 text-red-500" />
            )}
          </div>
          
          <CardTitle className="text-xl">
            {status === 'loading' && 'Completing Sign In...'}
            {status === 'success' && 'Sign In Successful!'}
            {status === 'error' && 'Sign In Failed'}
          </CardTitle>
          
          <CardDescription>
            {status === 'loading' && 'Please wait while we complete your sign in process.'}
            {status === 'success' && 'You will be redirected to your dashboard shortly.'}
            {status === 'error' && 'There was a problem completing your sign in.'}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {status === 'success' && userInfo && (
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                Welcome back, {userInfo.firstName} {userInfo.lastName}!
              </p>
              <p className="text-xs text-muted-foreground">
                Signed in as {userInfo.email}
              </p>
            </div>
          )}

          {status === 'error' && (
            <div className="space-y-4">
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={handleBackToLogin}
                  className="flex-1"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Login
                </Button>
                <Button
                  onClick={handleRetry}
                  className="flex-1"
                >
                  Try Again
                </Button>
              </div>
            </div>
          )}

          {status === 'loading' && (
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                This may take a few moments...
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </AuthLayout>
  )
}
