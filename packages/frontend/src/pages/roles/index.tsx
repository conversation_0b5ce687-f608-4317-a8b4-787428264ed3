import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Shield, 
  Settings,
  TreePine,
  Grid,
  BarChart3,
  Plus,
  Search,
  Filter
} from 'lucide-react'
import { RoleList } from '@/components/roles/role-list'
import { RoleDetails } from '@/components/roles/role-details'
import { RoleHierarchy } from '@/components/roles/role-hierarchy'
import { RolePermissionsMatrix } from '@/components/roles/role-permissions-matrix'
import { Role } from '@/types/rbac'
import { useRoles } from '@/hooks/use-roles'

export default function RolesPage() {
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [activeTab, setActiveTab] = useState('list')
  
  const { 
    roles, 
    loading, 
    error, 
    refetch,
    updateRole,
    deleteRole,
    assignRole,
    revokeRole 
  } = useRoles()

  const handleRoleSelect = (role: Role) => {
    setSelectedRole(role)
    if (activeTab === 'list') {
      // Auto-switch to details when a role is selected from list
      setActiveTab('details')
    }
  }

  const handleRoleUpdate = async (roleData: any) => {
    if (!selectedRole) return
    
    try {
      await updateRole(selectedRole.id, roleData)
      await refetch()
      
      // Update selected role with new data
      const updatedRole = { ...selectedRole, ...roleData }
      setSelectedRole(updatedRole)
    } catch (error) {
      console.error('Failed to update role:', error)
      throw error
    }
  }

  const handleRoleDelete = async () => {
    if (!selectedRole) return
    
    try {
      await deleteRole(selectedRole.id)
      await refetch()
      setSelectedRole(null)
      setActiveTab('list')
    } catch (error) {
      console.error('Failed to delete role:', error)
      throw error
    }
  }

  const handleUserAssign = async (userId: string) => {
    if (!selectedRole) return
    
    try {
      await assignRole(userId, selectedRole.id)
      await refetch()
    } catch (error) {
      console.error('Failed to assign role:', error)
      throw error
    }
  }

  const handleUserRevoke = async (userId: string) => {
    if (!selectedRole) return
    
    try {
      await revokeRole(userId, selectedRole.id)
      await refetch()
    } catch (error) {
      console.error('Failed to revoke role:', error)
      throw error
    }
  }

  const getRoleStats = () => {
    if (!roles) return { total: 0, system: 0, custom: 0, totalUsers: 0 }
    
    return {
      total: roles.length,
      system: roles.filter(r => r.isSystem).length,
      custom: roles.filter(r => !r.isSystem).length,
      totalUsers: roles.reduce((sum, role) => sum + (role.userCount || 0), 0)
    }
  }

  const stats = getRoleStats()

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Role Management</h1>
          <p className="text-muted-foreground">
            Manage user roles, permissions, and access control
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Active roles in system
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Roles</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.system}</div>
            <p className="text-xs text-muted-foreground">
              Built-in system roles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Custom Roles</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.custom}</div>
            <p className="text-xs text-muted-foreground">
              User-defined roles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              Users with roles assigned
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="list" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Roles
          </TabsTrigger>
          <TabsTrigger value="details" className="flex items-center gap-2" disabled={!selectedRole}>
            <Settings className="h-4 w-4" />
            Details
          </TabsTrigger>
          <TabsTrigger value="hierarchy" className="flex items-center gap-2">
            <TreePine className="h-4 w-4" />
            Hierarchy
          </TabsTrigger>
          <TabsTrigger value="matrix" className="flex items-center gap-2">
            <Grid className="h-4 w-4" />
            Matrix
          </TabsTrigger>
        </TabsList>

        {/* Role List Tab */}
        <TabsContent value="list" className="space-y-4">
          <RoleList
            onRoleSelect={handleRoleSelect}
            selectedRoleId={selectedRole?.id}
          />
        </TabsContent>

        {/* Role Details Tab */}
        <TabsContent value="details" className="space-y-4">
          {selectedRole ? (
            <RoleDetails
              role={selectedRole}
              onUpdate={handleRoleUpdate}
              onDelete={handleRoleDelete}
              onUserAssign={handleUserAssign}
              onUserRevoke={handleUserRevoke}
            />
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Role Selected</h3>
                <p className="text-gray-600 mb-4">
                  Select a role from the list to view its details and manage settings.
                </p>
                <Button onClick={() => setActiveTab('list')}>
                  <Users className="h-4 w-4 mr-2" />
                  Browse Roles
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Role Hierarchy Tab */}
        <TabsContent value="hierarchy" className="space-y-4">
          <RoleHierarchy
            onRoleSelect={handleRoleSelect}
            selectedRoleId={selectedRole?.id}
          />
        </TabsContent>

        {/* Permissions Matrix Tab */}
        <TabsContent value="matrix" className="space-y-4">
          <RolePermissionsMatrix
            onRoleSelect={handleRoleSelect}
            selectedRoleId={selectedRole?.id}
          />
        </TabsContent>
      </Tabs>

      {/* Selected Role Indicator */}
      {selectedRole && (
        <div className="fixed bottom-4 right-4 z-50">
          <Card className="shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  {selectedRole.isSystem ? (
                    <Shield className="h-4 w-4 text-blue-600" />
                  ) : (
                    <Users className="h-4 w-4 text-gray-600" />
                  )}
                  <span className="font-medium">{selectedRole.name}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setActiveTab('details')}
                  >
                    View Details
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedRole(null)}
                  >
                    ×
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
