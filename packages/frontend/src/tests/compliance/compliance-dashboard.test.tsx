import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { jest } from '@jest/globals'
import '@testing-library/jest-dom'
import { ComplianceDashboard } from '@/components/compliance/compliance-dashboard'

// Mock the sub-components
jest.mock('@/components/compliance/compliance-monitoring-dashboard', () => ({
  ComplianceMonitoringDashboard: ({ dealId, onAlertAction, onConfigureMonitoring }: any) => (
    <div data-testid="monitoring-dashboard">
      <span>Monitoring Dashboard for {dealId}</span>
      <button onClick={() => onAlertAction('alert-1', 'acknowledge')}>
        Acknowledge Alert
      </button>
      <button onClick={onConfigureMonitoring}>Configure Monitoring</button>
    </div>
  )
}))

jest.mock('@/components/compliance/compliance-document-manager', () => ({
  ComplianceDocumentManager: ({ dealId, onDocumentUploaded, onDocumentReviewed }: any) => (
    <div data-testid="document-manager">
      <span>Document Manager for {dealId}</span>
      <button onClick={() => onDocumentUploaded({ id: 'doc-1', name: 'Test Doc' })}>
        Upload Document
      </button>
      <button onClick={() => onDocumentReviewed('doc-1', true)}>
        Review Document
      </button>
    </div>
  )
}))

jest.mock('@/components/compliance/compliance-reporting-dashboard', () => ({
  ComplianceReportingDashboard: ({ dealId, onCreateReport, onEditReport, onDeleteReport, onScheduleReport }: any) => (
    <div data-testid="reporting-dashboard">
      <span>Reporting Dashboard for {dealId}</span>
      <button onClick={onCreateReport}>Create Report</button>
      <button onClick={() => onEditReport({ id: 'report-1', name: 'Test Report' })}>
        Edit Report
      </button>
      <button onClick={() => onDeleteReport('report-1')}>Delete Report</button>
      <button onClick={() => onScheduleReport({ id: 'report-1' })}>Schedule Report</button>
    </div>
  )
}))

describe('ComplianceDashboard', () => {
  const defaultProps = {
    dealId: 'deal-1',
    onInitializeCompliance: jest.fn(),
    onConfigureSystem: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders loading state initially', () => {
    render(<ComplianceDashboard {...defaultProps} />)
    
    expect(screen.getByRole('status')).toBeInTheDocument()
    expect(screen.getByText(/loading/i)).toBeInTheDocument()
  })

  it('renders compliance system not initialized state', async () => {
    // Mock the useEffect to return no data
    const mockUseEffect = jest.spyOn(React, 'useEffect')
    mockUseEffect.mockImplementation((effect, deps) => {
      if (deps && deps.includes(defaultProps.dealId)) {
        // Simulate no data state
        setTimeout(() => {
          // This would set loading to false and overview/systemStatus to null
        }, 0)
      }
    })

    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('Compliance System Not Initialized')).toBeInTheDocument()
      expect(screen.getByText('Initialize Compliance System')).toBeInTheDocument()
    })
  })

  it('renders dashboard with data', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('Compliance Management')).toBeInTheDocument()
      expect(screen.getByText('Technology M&A Transaction')).toBeInTheDocument()
      expect(screen.getByText('System Status')).toBeInTheDocument()
    })
  })

  it('displays correct summary metrics', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('67%')).toBeInTheDocument() // Completion percentage
      expect(screen.getByText('45')).toBeInTheDocument() // Risk score
      expect(screen.getByText('2')).toBeInTheDocument() // Critical alerts
      expect(screen.getByText('3')).toBeInTheDocument() // Overdue requirements
    })
  })

  it('shows system status with all services healthy', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('Active')).toBeInTheDocument()
      expect(screen.getAllByText('HEALTHY')).toHaveLength(5) // 5 services
    })
  })

  it('displays recent activity correctly', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('HSR Filing Completed')).toBeInTheDocument()
      expect(screen.getByText('Financial Statements Uploaded')).toBeInTheDocument()
      expect(screen.getByText('Critical Deadline Alert')).toBeInTheDocument()
      expect(screen.getByText('Weekly Compliance Report')).toBeInTheDocument()
    })
  })

  it('displays upcoming deadlines with correct priorities', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('SEC 8-K Filing')).toBeInTheDocument()
      expect(screen.getByText('Proxy Statement Filing')).toBeInTheDocument()
      expect(screen.getByText('CFIUS Declaration')).toBeInTheDocument()
      expect(screen.getByText('CRITICAL')).toBeInTheDocument()
      expect(screen.getByText('HIGH')).toBeInTheDocument()
      expect(screen.getByText('MEDIUM')).toBeInTheDocument()
    })
  })

  it('handles tab navigation correctly', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      // Check that overview tab is active by default
      expect(screen.getByRole('tab', { name: 'Overview' })).toHaveAttribute('data-state', 'active')
    })

    // Click on monitoring tab
    fireEvent.click(screen.getByRole('tab', { name: 'Monitoring' }))
    
    await waitFor(() => {
      expect(screen.getByTestId('monitoring-dashboard')).toBeInTheDocument()
      expect(screen.getByText('Monitoring Dashboard for deal-1')).toBeInTheDocument()
    })

    // Click on documents tab
    fireEvent.click(screen.getByRole('tab', { name: 'Documents' }))
    
    await waitFor(() => {
      expect(screen.getByTestId('document-manager')).toBeInTheDocument()
      expect(screen.getByText('Document Manager for deal-1')).toBeInTheDocument()
    })

    // Click on reports tab
    fireEvent.click(screen.getByRole('tab', { name: 'Reports' }))
    
    await waitFor(() => {
      expect(screen.getByTestId('reporting-dashboard')).toBeInTheDocument()
      expect(screen.getByText('Reporting Dashboard for deal-1')).toBeInTheDocument()
    })
  })

  it('calls refresh handler when refresh button is clicked', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      const refreshButton = screen.getByRole('button', { name: /refresh/i })
      fireEvent.click(refreshButton)
      
      // Should show refreshing state
      expect(refreshButton).toBeDisabled()
    })
  })

  it('calls configure handler when configure button is clicked', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      const configureButton = screen.getByRole('button', { name: /configure/i })
      fireEvent.click(configureButton)
      
      expect(defaultProps.onConfigureSystem).toHaveBeenCalledTimes(1)
    })
  })

  it('calls initialize compliance when system is not initialized', async () => {
    // Mock to return uninitialized state
    const mockUseEffect = jest.spyOn(React, 'useEffect')
    mockUseEffect.mockImplementation((effect, deps) => {
      if (deps && deps.includes(defaultProps.dealId)) {
        // Simulate uninitialized state
        setTimeout(() => {
          // This would set overview and systemStatus to null
        }, 0)
      }
    })

    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      const initializeButton = screen.getByRole('button', { name: /initialize compliance system/i })
      fireEvent.click(initializeButton)
      
      expect(defaultProps.onInitializeCompliance).toHaveBeenCalledTimes(1)
    })
  })

  it('handles sub-component callbacks correctly', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    // Navigate to monitoring tab
    fireEvent.click(screen.getByRole('tab', { name: 'Monitoring' }))
    
    await waitFor(() => {
      // Test monitoring dashboard callbacks
      fireEvent.click(screen.getByText('Acknowledge Alert'))
      fireEvent.click(screen.getByText('Configure Monitoring'))
    })

    // Navigate to documents tab
    fireEvent.click(screen.getByRole('tab', { name: 'Documents' }))
    
    await waitFor(() => {
      // Test document manager callbacks
      fireEvent.click(screen.getByText('Upload Document'))
      fireEvent.click(screen.getByText('Review Document'))
    })

    // Navigate to reports tab
    fireEvent.click(screen.getByRole('tab', { name: 'Reports' }))
    
    await waitFor(() => {
      // Test reporting dashboard callbacks
      fireEvent.click(screen.getByText('Create Report'))
      fireEvent.click(screen.getByText('Edit Report'))
      fireEvent.click(screen.getByText('Delete Report'))
      fireEvent.click(screen.getByText('Schedule Report'))
    })
  })

  it('displays progress bars correctly', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      const progressBars = screen.getAllByRole('progressbar')
      expect(progressBars).toHaveLength(1) // Completion progress bar
      
      // Check that progress bar has correct value
      expect(progressBars[0]).toHaveAttribute('aria-valuenow', '67')
    })
  })

  it('shows correct risk level indicators', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      // Check for trending indicators
      const trendingIcons = screen.getAllByTestId(/trending/i)
      expect(trendingIcons.length).toBeGreaterThan(0)
    })
  })

  it('formats dates correctly', async () => {
    render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      // Check that dates are formatted properly
      expect(screen.getByText(/2\/15\/2024/)).toBeInTheDocument() // Recent activity date
      expect(screen.getByText(/2\/17\/2024/)).toBeInTheDocument() // Deadline date
    })
  })

  it('handles error states gracefully', async () => {
    // Mock console.error to avoid test output noise
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    // Mock useEffect to simulate error
    const mockUseEffect = jest.spyOn(React, 'useEffect')
    mockUseEffect.mockImplementation((effect, deps) => {
      if (deps && deps.includes(defaultProps.dealId)) {
        throw new Error('Failed to load data')
      }
    })

    render(<ComplianceDashboard {...defaultProps} />)

    // Component should handle error gracefully and not crash
    expect(screen.getByText('Compliance Management')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('updates data when dealId changes', async () => {
    const { rerender } = render(<ComplianceDashboard {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('Technology M&A Transaction')).toBeInTheDocument()
    })

    // Change dealId
    rerender(<ComplianceDashboard {...defaultProps} dealId="deal-2" />)

    // Should trigger new data load
    await waitFor(() => {
      // In a real implementation, this would show different data
      expect(screen.getByText('Compliance Management')).toBeInTheDocument()
    })
  })
})
