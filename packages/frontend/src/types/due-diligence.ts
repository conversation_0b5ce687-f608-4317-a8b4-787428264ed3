/**
 * Due Diligence Management System Type Definitions
 */

// Enums
export enum DDChecklistStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  ARCHIVED = 'ARCHIVED'
}

export enum DDItemStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING_REVIEW = 'PENDING_REVIEW',
  COMPLETED = 'COMPLETED',
  NOT_APPLICABLE = 'NOT_APPLICABLE',
  BLOCKED = 'BLOCKED'
}

export enum DDItemPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum DDFieldType {
  TEXT = 'TEXT',
  TEXTAREA = 'TEXTAREA',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  BOOLEAN = 'BOOLEAN',
  SELECT = 'SELECT',
  MULTI_SELECT = 'MULTI_SELECT',
  FILE_UPLOAD = 'FILE_UPLOAD',
  DOCUMENT_LINK = 'DOCUMENT_LINK',
  URL = 'URL',
  CURRENCY = 'CURRENCY',
  PERCENTAGE = 'PERCENTAGE'
}

export enum DDCategoryType {
  FINANCIAL = 'FINANCIAL',
  LEGAL = 'LEGAL',
  COMMERCIAL = 'COMMERCIAL',
  TECHNICAL = 'TECHNICAL',
  HR = 'HR',
  OPERATIONAL = 'OPERATIONAL',
  REGULATORY = 'REGULATORY',
  ENVIRONMENTAL = 'ENVIRONMENTAL',
  INTELLECTUAL_PROPERTY = 'INTELLECTUAL_PROPERTY',
  INSURANCE = 'INSURANCE',
  TAX = 'TAX',
  CUSTOM = 'CUSTOM'
}

export enum DDIndustryType {
  TECHNOLOGY = 'TECHNOLOGY',
  HEALTHCARE = 'HEALTHCARE',
  FINANCIAL_SERVICES = 'FINANCIAL_SERVICES',
  MANUFACTURING = 'MANUFACTURING',
  RETAIL = 'RETAIL',
  ENERGY = 'ENERGY',
  REAL_ESTATE = 'REAL_ESTATE',
  TELECOMMUNICATIONS = 'TELECOMMUNICATIONS',
  MEDIA = 'MEDIA',
  TRANSPORTATION = 'TRANSPORTATION',
  AGRICULTURE = 'AGRICULTURE',
  EDUCATION = 'EDUCATION',
  GOVERNMENT = 'GOVERNMENT',
  NON_PROFIT = 'NON_PROFIT',
  OTHER = 'OTHER'
}

// Core Interfaces
export interface DDTemplate {
  id: string
  name: string
  description?: string
  version: string
  industryType: DDIndustryType
  isPublic: boolean
  isDefault: boolean
  
  // Template metadata
  createdBy: string
  createdAt: string
  updatedAt: string
  tenantId: string
  
  // Template structure
  categories: DDCategory[]
  totalItems: number
  estimatedHours: number
  
  // Usage statistics
  usageCount: number
  lastUsedAt?: string
  
  // Template settings
  allowCustomization: boolean
  requiresApproval: boolean
  tags: string[]
}

export interface DDCategory {
  id: string
  name: string
  description?: string
  categoryType: DDCategoryType
  order: number
  isRequired: boolean
  
  // Category settings
  allowCustomItems: boolean
  estimatedHours: number
  
  // Visual settings
  color?: string
  icon?: string
  
  // Category items
  items: DDItem[]
  
  // Progress tracking
  completedItems: number
  totalItems: number
  progressPercentage: number
}

export interface DDItem {
  id: string
  title: string
  description?: string
  categoryId: string
  
  // Item properties
  priority: DDItemPriority
  isRequired: boolean
  order: number
  estimatedHours: number
  
  // Status and assignment
  status: DDItemStatus
  assignedTo?: string
  assignedAt?: string
  dueDate?: string
  completedAt?: string
  completedBy?: string
  
  // Item configuration
  fields: DDField[]
  dependencies: string[] // Other item IDs
  
  // Documentation
  instructions?: string
  examples?: string[]
  references?: string[]
  
  // Linked resources
  documents: DDDocumentLink[]
  comments: DDComment[]
  
  // Validation
  validationRules: DDValidationRule[]
  
  // Progress tracking
  progressPercentage: number
  timeSpent: number
  
  // Metadata
  createdAt: string
  updatedAt: string
}

export interface DDField {
  id: string
  name: string
  label: string
  type: DDFieldType
  isRequired: boolean
  order: number
  
  // Field configuration
  placeholder?: string
  helpText?: string
  defaultValue?: any
  
  // Field options (for select types)
  options?: DDFieldOption[]
  
  // Validation
  validation?: {
    min?: number
    max?: number
    pattern?: string
    customRule?: string
  }
  
  // Conditional logic
  showWhen?: {
    fieldId: string
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than'
    value: any
  }
  
  // Current value
  value?: any
  lastUpdatedAt?: string
  lastUpdatedBy?: string
}

export interface DDFieldOption {
  id: string
  label: string
  value: string
  order: number
  isDefault?: boolean
  color?: string
}

export interface DDValidationRule {
  id: string
  type: 'required' | 'format' | 'range' | 'custom'
  message: string
  configuration: any
}

export interface DDDocumentLink {
  id: string
  documentId: string
  documentName: string
  documentType: string
  vdrPath?: string
  uploadedAt: string
  uploadedBy: string
  isRequired: boolean
  status: 'pending' | 'uploaded' | 'approved' | 'rejected'
  reviewNotes?: string
}

export interface DDComment {
  id: string
  content: string
  authorId: string
  authorName: string
  createdAt: string
  updatedAt?: string
  isInternal: boolean
  mentions: string[]
  attachments: string[]
  parentId?: string // For replies
  reactions: DDReaction[]
}

export interface DDReaction {
  emoji: string
  userId: string
  userName: string
  createdAt: string
}

// Checklist Management
export interface DDChecklist {
  id: string
  name: string
  description?: string
  templateId: string
  dealId: string
  
  // Checklist status
  status: DDChecklistStatus
  startDate: string
  targetCompletionDate?: string
  actualCompletionDate?: string
  
  // Progress tracking
  totalItems: number
  completedItems: number
  progressPercentage: number
  
  // Time tracking
  estimatedHours: number
  actualHours: number
  
  // Assignment and ownership
  ownerId: string
  teamMembers: DDTeamMember[]
  
  // Checklist data
  categories: DDCategory[]
  
  // Settings
  allowParallelExecution: boolean
  requiresApproval: boolean
  autoProgressDependencies: boolean
  
  // Metadata
  createdBy: string
  createdAt: string
  updatedAt: string
  tenantId: string
  
  // Integration
  vdrId?: string
  linkedDocuments: number
  
  // Analytics
  lastActivityAt: string
  viewCount: number
}

export interface DDTeamMember {
  id: string
  userId: string
  email: string
  firstName: string
  lastName: string
  role: 'owner' | 'manager' | 'contributor' | 'reviewer'
  permissions: DDPermission[]
  assignedItems: string[]
  joinedAt: string
  lastActiveAt?: string
}

export interface DDPermission {
  action: 'view' | 'edit' | 'assign' | 'approve' | 'delete' | 'export'
  scope: 'all' | 'assigned' | 'category' | 'specific'
  resourceIds?: string[]
}

// Progress and Analytics
export interface DDProgress {
  checklistId: string
  categoryProgress: Array<{
    categoryId: string
    categoryName: string
    completed: number
    total: number
    percentage: number
  }>
  itemProgress: Array<{
    itemId: string
    status: DDItemStatus
    assignedTo?: string
    dueDate?: string
    isOverdue: boolean
  }>
  timelineProgress: Array<{
    date: string
    completedItems: number
    totalHours: number
  }>
  teamProgress: Array<{
    userId: string
    userName: string
    assignedItems: number
    completedItems: number
    hoursSpent: number
  }>
}

export interface DDAnalytics {
  checklistId: string
  summary: {
    totalItems: number
    completedItems: number
    overdue: number
    blocked: number
    averageCompletionTime: number
    estimatedCompletion: string
  }
  categoryBreakdown: Array<{
    categoryName: string
    items: number
    completed: number
    avgTime: number
  }>
  teamPerformance: Array<{
    userId: string
    userName: string
    efficiency: number
    qualityScore: number
    itemsCompleted: number
  }>
  bottlenecks: Array<{
    itemId: string
    itemTitle: string
    daysStuck: number
    reason: string
  }>
  trends: {
    completionRate: Array<{ date: string; rate: number }>
    velocityTrend: Array<{ week: string; itemsCompleted: number }>
  }
}

// API Request/Response Types
export interface CreateDDTemplateRequest {
  name: string
  description?: string
  industryType: DDIndustryType
  isPublic?: boolean
  categories: Omit<DDCategory, 'id' | 'completedItems' | 'totalItems' | 'progressPercentage'>[]
  tags?: string[]
}

export interface CreateDDChecklistRequest {
  name: string
  description?: string
  templateId: string
  dealId: string
  targetCompletionDate?: string
  teamMembers: Array<{
    userId: string
    role: DDTeamMember['role']
  }>
  customizations?: {
    addedItems?: Omit<DDItem, 'id' | 'createdAt' | 'updatedAt'>[]
    removedItems?: string[]
    modifiedItems?: Array<{
      itemId: string
      changes: Partial<DDItem>
    }>
  }
}

export interface UpdateDDItemRequest {
  status?: DDItemStatus
  assignedTo?: string
  dueDate?: string
  fieldValues?: Array<{
    fieldId: string
    value: any
  }>
  documents?: DDDocumentLink[]
  comment?: string
  timeSpent?: number
}

// UI Helper Types
export interface DDFilterOptions {
  status?: DDItemStatus[]
  priority?: DDItemPriority[]
  assignedTo?: string[]
  category?: string[]
  dueDate?: {
    from?: string
    to?: string
  }
  search?: string
}

export interface DDSortOptions {
  field: 'title' | 'priority' | 'status' | 'dueDate' | 'assignedTo' | 'progress'
  direction: 'asc' | 'desc'
}

// Constants
export const DD_STATUS_COLORS = {
  [DDItemStatus.NOT_STARTED]: '#6B7280',
  [DDItemStatus.IN_PROGRESS]: '#3B82F6',
  [DDItemStatus.PENDING_REVIEW]: '#F59E0B',
  [DDItemStatus.COMPLETED]: '#10B981',
  [DDItemStatus.NOT_APPLICABLE]: '#9CA3AF',
  [DDItemStatus.BLOCKED]: '#EF4444'
} as const

export const DD_PRIORITY_COLORS = {
  [DDItemPriority.LOW]: '#10B981',
  [DDItemPriority.MEDIUM]: '#F59E0B',
  [DDItemPriority.HIGH]: '#F97316',
  [DDItemPriority.CRITICAL]: '#EF4444'
} as const

export const DD_CATEGORY_ICONS = {
  [DDCategoryType.FINANCIAL]: '💰',
  [DDCategoryType.LEGAL]: '⚖️',
  [DDCategoryType.COMMERCIAL]: '📊',
  [DDCategoryType.TECHNICAL]: '🔧',
  [DDCategoryType.HR]: '👥',
  [DDCategoryType.OPERATIONAL]: '⚙️',
  [DDCategoryType.REGULATORY]: '📋',
  [DDCategoryType.ENVIRONMENTAL]: '🌱',
  [DDCategoryType.INTELLECTUAL_PROPERTY]: '💡',
  [DDCategoryType.INSURANCE]: '🛡️',
  [DDCategoryType.TAX]: '🧾',
  [DDCategoryType.CUSTOM]: '📝'
} as const
