/**
 * Deal Pipeline Management Type Definitions for Frontend
 */

// Enums
export enum DealStatus {
  PIPELINE = 'PIPELINE',
  DUE_DILIGENCE = 'DUE_DILIGENCE',
  NEGOTIATION = 'NEGOTIATION',
  CLOSING = 'CLOSING',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED'
}

export enum DealPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum DealType {
  ACQUISITION = 'ACQUISITION',
  MERGER = 'MERGER',
  ASSET_PURCHASE = 'ASSET_PURCHASE',
  STOCK_PURCHASE = 'STOCK_PURCHASE',
  JOINT_VENTURE = 'JOINT_VENTURE',
  STRATEGIC_PARTNERSHIP = 'STRATEGIC_PARTNERSHIP',
  DIVESTITURE = 'DIVESTITURE',
  SPIN_OFF = 'SPIN_OFF',
  CARVE_OUT = 'CARVE_OUT',
  RECAPITALIZATION = 'RECAPITALIZATION'
}

export enum DealSource {
  DIRECT = 'DIRECT',
  INVESTMENT_BANK = 'INVESTMENT_BANK',
  BROKER = 'BROKER',
  REFERRAL = 'REFERRAL',
  COLD_OUTREACH = 'COLD_OUTREACH',
  INBOUND = 'INBOUND',
  CONFERENCE = 'CONFERENCE',
  NETWORK = 'NETWORK',
  EXISTING_RELATIONSHIP = 'EXISTING_RELATIONSHIP',
  AUCTION = 'AUCTION'
}

export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum ForecastCategory {
  PIPELINE = 'PIPELINE',
  BEST_CASE = 'BEST_CASE',
  COMMIT = 'COMMIT',
  CLOSED = 'CLOSED'
}

export enum ActivityType {
  CALL = 'CALL',
  EMAIL = 'EMAIL',
  MEETING = 'MEETING',
  PRESENTATION = 'PRESENTATION',
  SITE_VISIT = 'SITE_VISIT',
  DUE_DILIGENCE = 'DUE_DILIGENCE',
  NEGOTIATION = 'NEGOTIATION',
  DOCUMENT_REVIEW = 'DOCUMENT_REVIEW',
  VALUATION = 'VALUATION',
  LEGAL_REVIEW = 'LEGAL_REVIEW',
  REGULATORY = 'REGULATORY',
  CLOSING = 'CLOSING',
  OTHER = 'OTHER'
}

export enum TaskStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  OVERDUE = 'OVERDUE'
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Core interfaces
export interface Deal {
  id: string
  title: string
  description?: string
  status: DealStatus
  stage: string
  dealValue?: number
  currency: string
  priority: DealPriority
  
  // Enhanced deal information
  dealType: DealType
  dealSource: DealSource
  confidentiality?: string
  tags: string[]
  
  // Target company information
  targetCompany: string
  targetIndustry?: string
  targetRevenue?: number
  targetEmployees?: number
  targetLocation?: string
  targetWebsite?: string
  targetDescription?: string

  // Financial information
  enterpriseValue?: number
  equityValue?: number
  ebitda?: number
  revenue?: number
  multiple?: number
  
  // Timeline and milestones
  expectedCloseDate?: string
  actualCloseDate?: string
  firstContactDate?: string
  loiSignedDate?: string
  ddStartDate?: string
  ddEndDate?: string

  // Pipeline tracking
  currentStageId?: string
  stageEnteredAt?: string
  daysInCurrentStage?: number
  totalDaysInPipeline?: number
  
  // Probability and forecasting
  probability?: number
  weightedValue?: number
  forecastCategory: ForecastCategory
  
  // Deal health and scoring
  healthScore?: number
  riskLevel: RiskLevel
  lastActivityDate?: string
  
  // Competitive information
  competitiveProcess: boolean
  competitors: string[]
  
  // Internal tracking
  internalNotes?: string
  nextSteps?: string
  keyRisks: string[]
  keyOpportunities: string[]

  // Metadata
  tenantId: string
  createdBy: string
  assignedTo?: string
  createdAt: string
  updatedAt: string

  // Relations
  currentStage?: DealStage
  stageHistory?: DealStageHistory[]
  activities?: DealActivity[]
  contacts?: DealContact[]
  team?: DealTeamMember[]
  tasks?: DealTask[]
  notes?: DealNote[]
  milestones?: DealMilestone[]
  creator?: User
  assignee?: User
}

export interface DealStage {
  id: string
  name: string
  description?: string
  order: number
  isActive: boolean
  color?: string
  isDefault: boolean
  isClosing: boolean
  autoAdvance: boolean
  requiredFields: string[]
  tenantId: string
  createdAt: string
  updatedAt: string
}

export interface DealStageHistory {
  id: string
  dealId: string
  stageId: string
  enteredAt: string
  exitedAt?: string
  daysInStage?: number
  changedBy: string
  reason?: string
  notes?: string
  deal?: Deal
  stage?: DealStage
}

export interface DealActivity {
  id: string
  dealId: string
  type: ActivityType
  subject: string
  description?: string
  startTime: string
  endTime?: string
  location?: string
  isCompleted: boolean
  createdBy: string
  attendees: string[]
  externalAttendees?: any[]
  followUpDate?: string
  followUpNotes?: string
  createdAt: string
  updatedAt: string
  deal?: Deal
}

export interface DealContact {
  id: string
  dealId: string
  firstName: string
  lastName: string
  email?: string
  phone?: string
  title?: string
  company?: string
  role?: string
  isPrimary: boolean
  isDecisionMaker: boolean
  notes?: string
  linkedInUrl?: string
  createdAt: string
  updatedAt: string
}

export interface DealTeamMember {
  id: string
  dealId: string
  userId: string
  role: string
  canEdit: boolean
  canView: boolean
  createdAt: string
  user?: User
}

export interface DealTask {
  id: string
  dealId: string
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  dueDate?: string
  completedAt?: string
  estimatedHours?: number
  actualHours?: number
  assignedTo?: string
  createdBy: string
  dependsOn: string[]
  blockedBy: string[]
  createdAt: string
  updatedAt: string
  deal?: Deal
  assignee?: User
  creator?: User
}

export interface DealNote {
  id: string
  dealId: string
  content: string
  isPrivate: boolean
  tags: string[]
  createdBy: string
  createdAt: string
  updatedAt: string
  author?: User
}

export interface DealMilestone {
  id: string
  dealId: string
  name: string
  description?: string
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'MISSED' | 'CANCELLED'
  targetDate: string
  actualDate?: string
  isRequired: boolean
  order: number
  createdAt: string
  updatedAt: string
}

export interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  avatar?: string
}

// API Request/Response Types
export interface CreateDealRequest {
  title: string
  description?: string
  dealType: DealType
  dealSource: DealSource
  targetCompany: string
  targetIndustry?: string
  dealValue?: number
  currency?: string
  priority?: DealPriority
  expectedCloseDate?: string
  assignedTo?: string
  tags?: string[]
  confidentiality?: string
}

export interface UpdateDealRequest {
  title?: string
  description?: string
  status?: DealStatus
  stage?: string
  dealValue?: number
  priority?: DealPriority
  expectedCloseDate?: string
  assignedTo?: string
  tags?: string[]
  probability?: number
  healthScore?: number
  riskLevel?: RiskLevel
  nextSteps?: string
  internalNotes?: string
}

export interface DealFilters {
  status?: DealStatus[]
  stage?: string[]
  assignedTo?: string[]
  priority?: DealPriority[]
  dealType?: DealType[]
  dealSource?: DealSource[]
  riskLevel?: RiskLevel[]
  minValue?: number
  maxValue?: number
  expectedCloseDateFrom?: string
  expectedCloseDateTo?: string
  tags?: string[]
  search?: string
}

export interface DealSortOptions {
  field: 'title' | 'dealValue' | 'expectedCloseDate' | 'createdAt' | 'updatedAt' | 'priority' | 'healthScore'
  direction: 'asc' | 'desc'
}

// Analytics Types
export interface DealAnalytics {
  totalDeals: number
  totalValue: number
  averageDealSize: number
  averageDaysInPipeline: number
  conversionRate: number
  
  // By status
  dealsByStatus: Record<DealStatus, number>
  valueByStatus: Record<DealStatus, number>
  
  // By stage
  dealsByStage: Record<string, number>
  valueByStage: Record<string, number>
  
  // By time period
  dealsCreatedThisMonth: number
  dealsClosedThisMonth: number
  valueClosedThisMonth: number
  
  // Forecasting
  forecastedValue: number
  weightedPipelineValue: number
  
  // Performance metrics
  averageHealthScore: number
  dealsAtRisk: number
  overdueDeals: number
}

export interface PipelineMetrics {
  stageId: string
  stageName: string
  dealCount: number
  totalValue: number
  averageDaysInStage: number
  conversionRate: number
  dropOffRate: number
}

export interface DealForecast {
  period: string
  bestCase: number
  commit: number
  pipeline: number
  closed: number
  confidence: number
}

// UI Helper Types
export interface DealCardProps {
  deal: Deal
  onClick?: (deal: Deal) => void
  onEdit?: (deal: Deal) => void
  onDelete?: (deal: Deal) => void
  showActions?: boolean
  compact?: boolean
}

export interface DealListProps {
  deals: Deal[]
  loading?: boolean
  onDealClick?: (deal: Deal) => void
  onDealEdit?: (deal: Deal) => void
  onDealDelete?: (deal: Deal) => void
  showFilters?: boolean
  showPagination?: boolean
}

export interface DealFiltersProps {
  filters: DealFilters
  onFiltersChange: (filters: DealFilters) => void
  stages?: DealStage[]
  users?: User[]
}

// Constants
export const DEAL_STATUS_COLORS = {
  [DealStatus.PIPELINE]: '#6B7280',
  [DealStatus.DUE_DILIGENCE]: '#3B82F6',
  [DealStatus.NEGOTIATION]: '#F59E0B',
  [DealStatus.CLOSING]: '#10B981',
  [DealStatus.CLOSED]: '#059669',
  [DealStatus.CANCELLED]: '#EF4444'
} as const

export const DEAL_PRIORITY_COLORS = {
  [DealPriority.LOW]: '#10B981',
  [DealPriority.MEDIUM]: '#F59E0B',
  [DealPriority.HIGH]: '#EF4444',
  [DealPriority.CRITICAL]: '#DC2626'
} as const

export const RISK_LEVEL_COLORS = {
  [RiskLevel.LOW]: '#10B981',
  [RiskLevel.MEDIUM]: '#F59E0B',
  [RiskLevel.HIGH]: '#EF4444',
  [RiskLevel.CRITICAL]: '#DC2626'
} as const

export const DEAL_HEALTH_THRESHOLDS = {
  EXCELLENT: 80,
  GOOD: 60,
  FAIR: 40,
  POOR: 20
} as const
