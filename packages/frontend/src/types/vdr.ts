/**
 * Virtual Data Room (VDR) Type Definitions for Frontend
 */

// Enums
export enum VDRRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  CONTRIBUTOR = 'CONTRIBUTOR',
  VIEWER = 'VIEWER'
}

export enum VDRAccessLevel {
  PUBLIC = 'PUBLIC',
  INTERNAL = 'INTERNAL',
  RESTRICTED = 'RESTRICTED',
  CONFIDENTIAL = 'CONFIDENTIAL',
  ADMIN_ONLY = 'ADMIN_ONLY'
}

export enum VDRUserStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  EXPIRED = 'EXPIRED',
  REVOKED = 'REVOKED'
}

export enum VDRActivityType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  VIEW_DOCUMENT = 'VIEW_DOCUMENT',
  DOWNLOAD_DOCUMENT = 'DOWNLOAD_DOCUMENT',
  UPLOAD_DOCUMENT = 'UPLOAD_DOCUMENT',
  DELETE_DOCUMENT = 'DELETE_DOCUMENT',
  CREATE_FOLDER = 'CREATE_FOLDER',
  DELETE_FOLDER = 'DELETE_FOLDER',
  SHARE_DOCUMENT = 'SHARE_DOCUMENT',
  COMMENT_DOCUMENT = 'COMMENT_DOCUMENT',
  SEARCH = 'SEARCH',
  EXPORT_DATA = 'EXPORT_DATA',
  PRINT_DOCUMENT = 'PRINT_DOCUMENT',
  COPY_DOCUMENT = 'COPY_DOCUMENT'
}

export enum DocumentStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
  DELETED = 'DELETED'
}

// Core interfaces
export interface VirtualDataRoom {
  id: string
  name: string
  description?: string
  dealId?: string
  
  // VDR Configuration
  isActive: boolean
  expiresAt?: string
  maxUsers?: number
  
  // Security Settings
  requiresApproval: boolean
  allowDownload: boolean
  allowPrint: boolean
  allowCopy: boolean
  watermarkEnabled: boolean
  sessionTimeout?: number
  
  // Branding
  logoUrl?: string
  primaryColor?: string
  customDomain?: string
  
  // Analytics
  totalViews: number
  totalDownloads: number
  uniqueVisitors: number
  
  createdAt: string
  updatedAt: string

  // Relations
  tenantId: string
  createdBy: string
  deal?: Deal
  folders?: VDRFolder[]
  documents?: VDRDocument[]
  users?: VDRUser[]
  creator?: User
}

export interface VDRFolder {
  id: string
  name: string
  description?: string
  path: string
  parentId?: string
  vdrId: string
  
  // Folder settings
  isProtected: boolean
  order: number
  
  // Access control
  accessLevel: VDRAccessLevel
  
  createdAt: string
  updatedAt: string

  // Relations
  parent?: VDRFolder
  children?: VDRFolder[]
  documents?: VDRDocument[]
  _count?: {
    documents: number
    children: number
  }
}

export interface VDRDocument {
  id: string
  name: string
  originalName: string
  mimeType: string
  size: number
  
  // File storage
  storageProvider: string
  storagePath: string
  storageKey?: string
  
  // Document properties
  version: number
  status: DocumentStatus
  checksum?: string
  
  // VDR specific
  vdrId: string
  folderId?: string
  
  // Security
  isEncrypted: boolean
  accessLevel: VDRAccessLevel
  
  // Document metadata
  tags: string[]
  description?: string
  
  // Analytics
  viewCount: number
  downloadCount: number
  lastViewedAt?: string
  
  createdAt: string
  updatedAt: string

  // Relations
  tenantId: string
  uploadedBy: string
  uploader?: User
  vdr?: VirtualDataRoom
  folder?: VDRFolder
  
  // Version control
  parentId?: string
  parent?: VDRDocument
  versions?: VDRDocument[]
}

export interface VDRUser {
  id: string
  vdrId: string
  userId?: string
  email: string
  
  // User details for external users
  firstName?: string
  lastName?: string
  company?: string
  title?: string
  
  // Access control
  role: VDRRole
  accessLevel: VDRAccessLevel
  status: VDRUserStatus
  
  // Access settings
  canDownload: boolean
  canPrint: boolean
  canComment: boolean
  
  // Session management
  lastLoginAt?: string
  expiresAt?: string
  isActive: boolean
  
  // Invitation
  invitedBy?: string
  invitedAt?: string
  acceptedAt?: string
  
  createdAt: string
  updatedAt: string

  // Relations
  user?: User
  inviter?: User
}

export interface VDRActivity {
  id: string
  vdrId: string
  userId: string
  action: VDRActivityType
  
  // Activity details
  documentId?: string
  folderId?: string
  details?: any
  
  // Request information
  ipAddress?: string
  userAgent?: string
  location?: string
  
  // Timing
  duration?: number
  
  createdAt: string

  // Relations
  vdrUser?: VDRUser
  document?: VDRDocument
  folder?: VDRFolder
}

export interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  avatar?: string
}

export interface Deal {
  id: string
  title: string
  targetCompany: string
}

// API Request/Response Types
export interface CreateVDRRequest {
  name: string
  description?: string
  dealId?: string
  expiresAt?: string
  maxUsers?: number
  requiresApproval?: boolean
  allowDownload?: boolean
  allowPrint?: boolean
  allowCopy?: boolean
  watermarkEnabled?: boolean
  sessionTimeout?: number
  logoUrl?: string
  primaryColor?: string
  customDomain?: string
}

export interface UpdateVDRRequest {
  name?: string
  description?: string
  expiresAt?: string
  maxUsers?: number
  requiresApproval?: boolean
  allowDownload?: boolean
  allowPrint?: boolean
  allowCopy?: boolean
  watermarkEnabled?: boolean
  sessionTimeout?: number
  logoUrl?: string
  primaryColor?: string
  customDomain?: string
}

export interface VDRFilters {
  search?: string
  dealId?: string
  isActive?: boolean
  createdBy?: string
}

export interface CreateFolderRequest {
  name: string
  description?: string
  parentId?: string
  accessLevel?: VDRAccessLevel
}

export interface UploadDocumentRequest {
  folderId?: string
  files: File[]
  tags?: string[]
  description?: string
  accessLevel?: VDRAccessLevel
}

export interface InviteUserRequest {
  email: string
  firstName?: string
  lastName?: string
  company?: string
  title?: string
  role: VDRRole
  accessLevel: VDRAccessLevel
  canDownload?: boolean
  canPrint?: boolean
  canComment?: boolean
  expiresAt?: string
  message?: string
}

// UI Helper Types
export interface VDRBreadcrumb {
  id: string
  name: string
  path: string
}

export interface FileUploadProgress {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  error?: string
}

export interface VDRPermissions {
  canView: boolean
  canDownload: boolean
  canUpload: boolean
  canDelete: boolean
  canShare: boolean
  canComment: boolean
  canAdmin: boolean
}

export interface VDRAnalytics {
  totalViews: number
  totalDownloads: number
  uniqueVisitors: number
  topDocuments: Array<{
    id: string
    name: string
    viewCount: number
    downloadCount: number
  }>
  userActivity: Array<{
    userId: string
    email: string
    lastLogin: string
    totalViews: number
    totalDownloads: number
  }>
  activityTimeline: Array<{
    date: string
    views: number
    downloads: number
    uploads: number
  }>
}

// Constants
export const VDR_ROLE_COLORS = {
  [VDRRole.ADMIN]: '#DC2626',
  [VDRRole.MANAGER]: '#EA580C',
  [VDRRole.CONTRIBUTOR]: '#2563EB',
  [VDRRole.VIEWER]: '#059669'
} as const

export const VDR_STATUS_COLORS = {
  [VDRUserStatus.PENDING]: '#F59E0B',
  [VDRUserStatus.ACTIVE]: '#10B981',
  [VDRUserStatus.SUSPENDED]: '#EF4444',
  [VDRUserStatus.EXPIRED]: '#6B7280',
  [VDRUserStatus.REVOKED]: '#DC2626'
} as const

export const ACCESS_LEVEL_COLORS = {
  [VDRAccessLevel.PUBLIC]: '#10B981',
  [VDRAccessLevel.INTERNAL]: '#3B82F6',
  [VDRAccessLevel.RESTRICTED]: '#F59E0B',
  [VDRAccessLevel.CONFIDENTIAL]: '#EF4444',
  [VDRAccessLevel.ADMIN_ONLY]: '#DC2626'
} as const

export const FILE_TYPE_ICONS = {
  'application/pdf': '📄',
  'application/msword': '📝',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '📝',
  'application/vnd.ms-excel': '📊',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '📊',
  'application/vnd.ms-powerpoint': '📊',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': '📊',
  'image/jpeg': '🖼️',
  'image/png': '🖼️',
  'image/gif': '🖼️',
  'text/plain': '📄',
  'text/csv': '📊',
  'application/zip': '🗜️',
  'application/x-rar-compressed': '🗜️',
  'default': '📄'
} as const

export const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB
export const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'image/jpeg',
  'image/png',
  'image/gif',
  'text/plain',
  'text/csv',
  'application/zip',
  'application/x-rar-compressed'
]
