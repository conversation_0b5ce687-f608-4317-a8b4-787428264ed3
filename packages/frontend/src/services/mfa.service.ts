import { apiClient } from './api.client'

export interface MfaStatus {
  enabled: boolean
  backupCodesRemaining: number
  setupDate?: string
}

export interface MfaSetupData {
  qrCodeUrl: string
  manualEntryKey: string
  backupCodes: string[]
}

export interface MfaVerificationResult {
  valid: boolean
  backupCodeUsed?: boolean
}

export class MfaService {
  // Get MFA status for current user
  static async getStatus(): Promise<MfaStatus> {
    const response = await apiClient.get('/mfa/status')
    return response.data
  }

  // Check if MFA is enabled for current user
  static async isEnabled(): Promise<boolean> {
    const response = await apiClient.get('/mfa/enabled')
    return response.data.enabled
  }

  // Setup MFA for current user
  static async setupMfa(): Promise<MfaSetupData> {
    const response = await apiClient.post('/mfa/setup')
    return response.data
  }

  // Verify MFA setup with initial code
  static async verifySetup(verificationCode: string): Promise<boolean> {
    const response = await apiClient.post('/mfa/verify-setup', {
      verificationCode
    })
    return response.data.verified
  }

  // Verify MFA code (for login or general verification)
  static async verifyCode(code: string): Promise<MfaVerificationResult> {
    const response = await apiClient.post('/mfa/verify', {
      code
    })
    return {
      valid: response.data.valid,
      backupCodeUsed: response.data.backupCodeUsed
    }
  }

  // Disable MFA for current user
  static async disableMfa(verificationCode: string): Promise<void> {
    await apiClient.post('/mfa/disable', {
      verificationCode
    })
  }

  // Generate new backup codes
  static async generateNewBackupCodes(verificationCode: string): Promise<string[]> {
    const response = await apiClient.post('/mfa/backup-codes/regenerate', {
      verificationCode
    })
    return response.data.backupCodes
  }

  // Validate TOTP code format
  static validateTotpCode(code: string): boolean {
    return /^\d{6}$/.test(code)
  }

  // Validate backup code format
  static validateBackupCode(code: string): boolean {
    return /^[A-Z0-9]{8}$/.test(code.toUpperCase())
  }

  // Format backup codes for display
  static formatBackupCodes(codes: string[]): string[] {
    return codes.map(code => code.toUpperCase())
  }

  // Download backup codes as text file
  static downloadBackupCodes(codes: string[]): void {
    const content = `M&A Platform - Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\n\nBackup Codes:\n${codes.join('\n')}\n\nImportant:\n- Keep these codes safe and secure\n- Each code can only be used once\n- Use these codes if you lose access to your authenticator app\n- Generate new codes if you suspect these have been compromised`
    
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'ma-platform-backup-codes.txt'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Copy backup codes to clipboard
  static async copyBackupCodes(codes: string[]): Promise<boolean> {
    try {
      const text = codes.join('\n')
      await navigator.clipboard.writeText(text)
      return true
    } catch (error) {
      console.error('Failed to copy backup codes:', error)
      return false
    }
  }

  // Copy manual entry key to clipboard
  static async copyManualEntryKey(key: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(key)
      return true
    } catch (error) {
      console.error('Failed to copy manual entry key:', error)
      return false
    }
  }

  // Get QR code data URL from setup data
  static getQrCodeDataUrl(setupData: MfaSetupData): string {
    return setupData.qrCodeUrl
  }

  // Parse MFA error messages
  static parseMfaError(error: any): string {
    if (error?.code === 'VALIDATION_ERROR') {
      return error.message || 'Invalid input provided'
    }

    if (error?.code === 'MFA_SETUP_EXPIRED') {
      return 'MFA setup session expired. Please start setup again.'
    }

    if (error?.code === 'INVALID_MFA_CODE') {
      return 'Invalid verification code. Please try again.'
    }

    if (error?.code === 'MFA_NOT_ENABLED') {
      return 'MFA is not enabled for this account.'
    }

    if (error?.code === 'BACKUP_CODE_USED') {
      return 'This backup code has already been used.'
    }

    if (error?.message) {
      return error.message
    }

    return 'An unexpected error occurred. Please try again.'
  }

  // Check if code looks like a backup code vs TOTP
  static isBackupCode(code: string): boolean {
    // Backup codes are typically 8 characters, alphanumeric
    // TOTP codes are 6 digits
    return /^[A-Z0-9]{8}$/i.test(code) && !/^\d{6}$/.test(code)
  }

  // Format code for display (add spaces for readability)
  static formatCodeForDisplay(code: string): string {
    if (this.isBackupCode(code)) {
      // Format backup code: ABCD-EFGH
      return code.toUpperCase().replace(/(.{4})(.{4})/, '$1-$2')
    }
    
    // Format TOTP code: 123 456
    return code.replace(/(.{3})(.{3})/, '$1 $2')
  }

  // Remove formatting from code
  static cleanCode(code: string): string {
    return code.replace(/[\s-]/g, '').toUpperCase()
  }
}
