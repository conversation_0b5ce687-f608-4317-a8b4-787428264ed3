import { apiClient } from './api.client'

export interface SessionData {
  id: string
  createdAt: string
  lastAccessedAt: string
  expiresAt: string
  ipAddress?: string
  deviceInfo?: {
    browser?: string
    os?: string
    device?: string
  }
  location?: {
    country?: string
    city?: string
    region?: string
  }
  isCurrent: boolean
}

export interface SessionStats {
  totalActiveSessions: number
  userActiveSessions?: number
  averageSessionDuration?: number
}

export class SessionService {
  // Get current session details
  static async getCurrentSession(): Promise<SessionData> {
    const response = await apiClient.get('/sessions/current')
    return response.data.session
  }

  // Get all user sessions
  static async getAllSessions(): Promise<SessionData[]> {
    const response = await apiClient.get('/sessions/all')
    return response.data.sessions
  }

  // Extend current session
  static async extendSession(additionalTime?: number): Promise<void> {
    const sessionId = apiClient.getSessionId()
    if (!sessionId) {
      throw new Error('No active session')
    }

    await apiClient.post('/sessions/extend', {
      sessionId,
      additionalTime
    })
  }

  // Destroy a specific session
  static async destroySession(sessionId: string): Promise<void> {
    await apiClient.delete(`/sessions/${sessionId}`)
  }

  // Destroy all other sessions (keep current)
  static async destroyAllOtherSessions(): Promise<{ destroyedCount: number }> {
    const response = await apiClient.delete('/sessions/all')
    return response.data
  }

  // Validate current session
  static async validateSession(): Promise<{
    isValid: boolean
    user?: any
  }> {
    try {
      const response = await apiClient.post('/sessions/validate')
      return response.data
    } catch (error) {
      return { isValid: false }
    }
  }

  // Get session statistics
  static async getSessionStats(): Promise<SessionStats> {
    const response = await apiClient.get('/sessions/stats')
    return response.data
  }

  // Check if session is about to expire
  static isSessionExpiringSoon(expiresAt: string, warningMinutes: number = 5): boolean {
    const expirationTime = new Date(expiresAt).getTime()
    const warningTime = Date.now() + (warningMinutes * 60 * 1000)
    return expirationTime <= warningTime
  }

  // Get time until session expires
  static getTimeUntilExpiration(expiresAt: string): {
    totalMinutes: number
    hours: number
    minutes: number
    isExpired: boolean
  } {
    const expirationTime = new Date(expiresAt).getTime()
    const currentTime = Date.now()
    const timeDiff = expirationTime - currentTime

    if (timeDiff <= 0) {
      return {
        totalMinutes: 0,
        hours: 0,
        minutes: 0,
        isExpired: true
      }
    }

    const totalMinutes = Math.floor(timeDiff / (1000 * 60))
    const hours = Math.floor(totalMinutes / 60)
    const minutes = totalMinutes % 60

    return {
      totalMinutes,
      hours,
      minutes,
      isExpired: false
    }
  }

  // Format session duration
  static formatSessionDuration(createdAt: string, lastAccessedAt?: string): string {
    const startTime = new Date(createdAt).getTime()
    const endTime = lastAccessedAt ? new Date(lastAccessedAt).getTime() : Date.now()
    const duration = endTime - startTime

    const hours = Math.floor(duration / (1000 * 60 * 60))
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  // Parse device info from user agent
  static parseDeviceInfo(userAgent?: string): {
    browser?: string
    os?: string
    device?: string
  } {
    if (!userAgent) return {}

    // Simple user agent parsing (in production, use a proper library)
    const browser = userAgent.includes('Chrome') ? 'Chrome' :
                   userAgent.includes('Firefox') ? 'Firefox' :
                   userAgent.includes('Safari') ? 'Safari' :
                   userAgent.includes('Edge') ? 'Edge' : 'Unknown'

    const os = userAgent.includes('Windows') ? 'Windows' :
              userAgent.includes('Mac') ? 'macOS' :
              userAgent.includes('Linux') ? 'Linux' :
              userAgent.includes('Android') ? 'Android' :
              userAgent.includes('iOS') ? 'iOS' : 'Unknown'

    const device = userAgent.includes('Mobile') ? 'Mobile' :
                  userAgent.includes('Tablet') ? 'Tablet' : 'Desktop'

    return { browser, os, device }
  }

  // Get device icon based on device type
  static getDeviceIcon(deviceInfo?: { device?: string }): string {
    switch (deviceInfo?.device) {
      case 'Mobile':
        return '📱'
      case 'Tablet':
        return '📱'
      case 'Desktop':
        return '💻'
      default:
        return '🖥️'
    }
  }

  // Format IP address for display
  static formatIpAddress(ipAddress?: string): string {
    if (!ipAddress) return 'Unknown'
    
    // Hide part of IP for privacy
    const parts = ipAddress.split('.')
    if (parts.length === 4) {
      return `${parts[0]}.${parts[1]}.*.***`
    }
    
    return ipAddress
  }

  // Check if session is current
  static isCurrentSession(sessionId: string): boolean {
    return apiClient.getSessionId() === sessionId
  }

  // Auto-refresh session before expiration
  static startSessionRefreshTimer(expiresAt: string, refreshMinutes: number = 10): NodeJS.Timeout {
    const expirationTime = new Date(expiresAt).getTime()
    const refreshTime = expirationTime - (refreshMinutes * 60 * 1000)
    const timeUntilRefresh = refreshTime - Date.now()

    if (timeUntilRefresh <= 0) {
      // Session expires soon, refresh immediately
      this.extendSession().catch(console.error)
      return setTimeout(() => {}, 0)
    }

    return setTimeout(async () => {
      try {
        await this.extendSession()
        console.log('Session refreshed automatically')
      } catch (error) {
        console.error('Failed to refresh session:', error)
      }
    }, timeUntilRefresh)
  }

  // Clear session refresh timer
  static clearSessionRefreshTimer(timer: NodeJS.Timeout): void {
    clearTimeout(timer)
  }

  // Handle session expiration
  static handleSessionExpiration(): void {
    // Clear auth data
    apiClient.clearAuthData()
    
    // Redirect to login
    const currentPath = window.location.pathname + window.location.search
    if (currentPath !== '/login' && currentPath !== '/register') {
      sessionStorage.setItem('auth_redirect', currentPath)
      window.location.href = '/login?expired=true'
    }
  }

  // Monitor session status
  static startSessionMonitoring(checkIntervalMinutes: number = 5): NodeJS.Timeout {
    return setInterval(async () => {
      try {
        const validation = await this.validateSession()
        if (!validation.isValid) {
          this.handleSessionExpiration()
        }
      } catch (error) {
        console.error('Session validation failed:', error)
        this.handleSessionExpiration()
      }
    }, checkIntervalMinutes * 60 * 1000)
  }

  // Stop session monitoring
  static stopSessionMonitoring(timer: NodeJS.Timeout): void {
    clearInterval(timer)
  }
}
