import { apiClient } from './api.client'

export interface SsoProvider {
  id: string
  name: string
  type: 'oauth' | 'saml' | 'oidc'
  enabled: boolean
}

export interface SsoAuthResult {
  user: {
    id: string
    email: string
    firstName: string
    lastName: string
    tenantId: string
    roles: string[]
    permissions: string[]
  }
  token: string
  isNewUser: boolean
}

export class SsoService {
  // Get available SSO providers for a tenant
  static async getProviders(tenantId: string): Promise<SsoProvider[]> {
    const response = await apiClient.get(`/sso/providers/${tenantId}`)
    return response.data.providers
  }

  // Generate SSO authorization URL
  static async generateAuthUrl(
    provider: string,
    tenantId: string,
    redirectUri?: string
  ): Promise<string> {
    const currentUrl = window.location.origin
    const defaultRedirectUri = `${currentUrl}/auth/callback`
    
    const params = new URLSearchParams({
      provider,
      tenantId,
      redirectUri: redirectUri || defaultRedirectUri,
      state: this.generateState()
    })

    const response = await apiClient.get(`/sso/auth?${params.toString()}`)
    return response.data.authUrl
  }

  // Initiate SSO login
  static async initiateLogin(
    provider: string,
    tenantId: string,
    redirectUri?: string
  ): Promise<void> {
    try {
      const authUrl = await this.generateAuthUrl(provider, tenantId, redirectUri)
      
      // Store provider and tenant info for callback handling
      sessionStorage.setItem('sso_provider', provider)
      sessionStorage.setItem('sso_tenant', tenantId)
      
      // Redirect to provider
      window.location.href = authUrl
    } catch (error) {
      console.error('Failed to initiate SSO login:', error)
      throw error
    }
  }

  // Handle SSO callback
  static async handleCallback(
    code: string,
    state: string
  ): Promise<SsoAuthResult> {
    try {
      const response = await apiClient.get('/sso/callback', {
        params: { code, state }
      })
      
      // Clear stored SSO data
      sessionStorage.removeItem('sso_provider')
      sessionStorage.removeItem('sso_tenant')
      
      return response.data
    } catch (error) {
      // Clear stored SSO data on error
      sessionStorage.removeItem('sso_provider')
      sessionStorage.removeItem('sso_tenant')
      throw error
    }
  }

  // Handle SSO callback with POST (alternative method)
  static async handleCallbackPost(
    code: string,
    state: string
  ): Promise<SsoAuthResult> {
    try {
      const response = await apiClient.post('/sso/callback', {
        code,
        state
      })
      
      // Clear stored SSO data
      sessionStorage.removeItem('sso_provider')
      sessionStorage.removeItem('sso_tenant')
      
      return response.data
    } catch (error) {
      // Clear stored SSO data on error
      sessionStorage.removeItem('sso_provider')
      sessionStorage.removeItem('sso_tenant')
      throw error
    }
  }

  // Get stored SSO context
  static getSsoContext(): { provider?: string; tenantId?: string } {
    return {
      provider: sessionStorage.getItem('sso_provider') || undefined,
      tenantId: sessionStorage.getItem('sso_tenant') || undefined
    }
  }

  // Generate secure state parameter
  private static generateState(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  // Parse URL parameters
  static parseUrlParams(): { code?: string; state?: string; error?: string } {
    const urlParams = new URLSearchParams(window.location.search)
    return {
      code: urlParams.get('code') || undefined,
      state: urlParams.get('state') || undefined,
      error: urlParams.get('error') || undefined
    }
  }

  // Check if current URL is an SSO callback
  static isCallbackUrl(): boolean {
    const params = this.parseUrlParams()
    return !!(params.code && params.state) || !!params.error
  }

  // Handle SSO error from callback
  static handleSsoError(error: string, errorDescription?: string): Error {
    const errorMessages: Record<string, string> = {
      'access_denied': 'Access was denied. Please try again or contact support.',
      'invalid_request': 'Invalid request. Please try again.',
      'unauthorized_client': 'Unauthorized client. Please contact support.',
      'unsupported_response_type': 'Unsupported response type. Please contact support.',
      'invalid_scope': 'Invalid scope. Please contact support.',
      'server_error': 'Server error. Please try again later.',
      'temporarily_unavailable': 'Service temporarily unavailable. Please try again later.'
    }

    const message = errorMessages[error] || errorDescription || 'An unknown error occurred during SSO authentication.'
    return new Error(message)
  }

  // Clear SSO session data
  static clearSsoData(): void {
    sessionStorage.removeItem('sso_provider')
    sessionStorage.removeItem('sso_tenant')
  }
}
