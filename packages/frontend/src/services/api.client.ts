import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  error?: {
    code: string
    message: string
    details?: any
  }
  meta?: {
    timestamp: string
    [key: string]: any
  }
}

class ApiClient {
  private client: AxiosInstance
  private baseURL: string

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api'
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = this.getAuthToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // Add session ID if available
        const sessionId = this.getSessionId()
        if (sessionId) {
          config.headers['X-Session-ID'] = sessionId
        }

        // Add tenant ID if available
        const tenantId = this.getTenantId()
        if (tenantId) {
          config.headers['X-Tenant-ID'] = tenantId
        }

        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response
      },
      async (error) => {
        const originalRequest = error.config

        // Handle 401 errors (unauthorized)
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          // Try to refresh token
          try {
            await this.refreshToken()
            
            // Retry original request with new token
            const token = this.getAuthToken()
            if (token) {
              originalRequest.headers.Authorization = `Bearer ${token}`
            }
            
            return this.client(originalRequest)
          } catch (refreshError) {
            // Refresh failed, redirect to login
            this.handleAuthFailure()
            return Promise.reject(refreshError)
          }
        }

        // Handle other errors
        return Promise.reject(this.normalizeError(error))
      }
    )
  }

  // HTTP methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.get(url, config)
    return response.data
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post(url, data, config)
    return response.data
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put(url, data, config)
    return response.data
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.patch(url, data, config)
    return response.data
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete(url, config)
    return response.data
  }

  // Auth token management
  setAuthToken(token: string): void {
    localStorage.setItem('auth_token', token)
  }

  getAuthToken(): string | null {
    return localStorage.getItem('auth_token')
  }

  removeAuthToken(): void {
    localStorage.removeItem('auth_token')
  }

  // Session management
  setSessionId(sessionId: string): void {
    localStorage.setItem('session_id', sessionId)
  }

  getSessionId(): string | null {
    return localStorage.getItem('session_id')
  }

  removeSessionId(): void {
    localStorage.removeItem('session_id')
  }

  // Tenant management
  setTenantId(tenantId: string): void {
    localStorage.setItem('tenant_id', tenantId)
  }

  getTenantId(): string | null {
    return localStorage.getItem('tenant_id')
  }

  removeTenantId(): void {
    localStorage.removeItem('tenant_id')
  }

  // User data management
  setUserData(user: any): void {
    localStorage.setItem('user_data', JSON.stringify(user))
  }

  getUserData(): any | null {
    const userData = localStorage.getItem('user_data')
    return userData ? JSON.parse(userData) : null
  }

  removeUserData(): void {
    localStorage.removeItem('user_data')
  }

  // Clear all auth data
  clearAuthData(): void {
    this.removeAuthToken()
    this.removeSessionId()
    this.removeTenantId()
    this.removeUserData()
  }

  // Token refresh
  private async refreshToken(): Promise<void> {
    const sessionId = this.getSessionId()
    if (!sessionId) {
      throw new Error('No session ID available')
    }

    try {
      const response = await this.client.post('/auth/refresh', {}, {
        headers: {
          'X-Session-ID': sessionId
        }
      })

      if (response.data.success && response.data.data.token) {
        this.setAuthToken(response.data.data.token)
      } else {
        throw new Error('Token refresh failed')
      }
    } catch (error) {
      throw error
    }
  }

  // Handle authentication failure
  private handleAuthFailure(): void {
    this.clearAuthData()
    
    // Redirect to login page
    const currentPath = window.location.pathname
    if (currentPath !== '/login' && currentPath !== '/register') {
      window.location.href = '/login'
    }
  }

  // Normalize error responses
  private normalizeError(error: any): Error {
    if (error.response?.data?.error) {
      const apiError = error.response.data.error
      const errorMessage = apiError.message || 'An error occurred'
      const customError = new Error(errorMessage)
      ;(customError as any).code = apiError.code
      ;(customError as any).details = apiError.details
      return customError
    }

    if (error.response?.data?.message) {
      return new Error(error.response.data.message)
    }

    if (error.message) {
      return new Error(error.message)
    }

    return new Error('An unexpected error occurred')
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!(this.getAuthToken() && this.getSessionId())
  }

  // Get base URL
  getBaseURL(): string {
    return this.baseURL
  }
}

// Export singleton instance
export const apiClient = new ApiClient()
