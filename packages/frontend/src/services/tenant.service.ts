import { apiClient } from './api.client'

export interface Tenant {
  id: string
  name: string
  slug: string
  subdomain: string
  domain?: string
  description?: string
  logo?: string
  website?: string
  industry?: string
  size?: 'SMALL' | 'MEDIUM' | 'LARGE' | 'ENTERPRISE'
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'TRIAL' | 'EXPIRED' | 'DELETED'
  plan: 'STARTER' | 'PROFESSIONAL' | 'ENTERPRISE' | 'CUSTOM'
  features: Record<string, any>
  limits: Record<string, any>
  settings: Record<string, any>
  branding: Record<string, any>
  trialEndsAt?: string
  createdAt: string
  updatedAt: string
}

export interface CreateTenantData {
  name: string
  slug?: string
  subdomain: string
  domain?: string
  description?: string
  industry?: string
  size?: 'SMALL' | 'MEDIUM' | 'LARGE' | 'ENTERPRISE'
  plan?: 'STARTER' | 'PROFESSIONAL' | 'ENTERPRISE' | 'CUSTOM'
  billingEmail?: string
  settings?: Record<string, any>
  features?: Record<string, any>
  limits?: Record<string, any>
  branding?: Record<string, any>
}

export interface UpdateTenantData {
  name?: string
  description?: string
  logo?: string
  website?: string
  industry?: string
  size?: 'SMALL' | 'MEDIUM' | 'LARGE' | 'ENTERPRISE'
  plan?: 'STARTER' | 'PROFESSIONAL' | 'ENTERPRISE' | 'CUSTOM'
  billingEmail?: string
  settings?: Record<string, any>
  features?: Record<string, any>
  limits?: Record<string, any>
  branding?: Record<string, any>
}

export interface TenantInvitation {
  id: string
  email: string
  role: string
  status: 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'CANCELLED'
  expiresAt: string
  createdAt: string
}

export interface TenantApiKey {
  id: string
  name: string
  key?: string // Only returned when creating
  permissions: Record<string, any>
  lastUsedAt?: string
  expiresAt?: string
  isActive: boolean
  createdAt: string
}

export class TenantService {
  // Create a new tenant
  static async createTenant(data: CreateTenantData): Promise<Tenant> {
    const response = await apiClient.post('/tenants', data)
    return response.data
  }

  // Get tenant by ID
  static async getTenantById(id: string): Promise<Tenant> {
    const response = await apiClient.get(`/tenants/${id}`)
    return response.data
  }

  // Get tenant by slug
  static async getTenantBySlug(slug: string): Promise<Tenant> {
    const response = await apiClient.get(`/tenants/slug/${slug}`)
    return response.data
  }

  // Get tenant by subdomain
  static async getTenantBySubdomain(subdomain: string): Promise<Tenant> {
    const response = await apiClient.get(`/tenants/subdomain/${subdomain}`)
    return response.data
  }

  // Update tenant
  static async updateTenant(id: string, data: UpdateTenantData): Promise<Tenant> {
    const response = await apiClient.put(`/tenants/${id}`, data)
    return response.data
  }

  // Delete tenant
  static async deleteTenant(id: string): Promise<void> {
    await apiClient.delete(`/tenants/${id}`)
  }

  // Invite user to tenant
  static async inviteUser(tenantId: string, email: string, role: string): Promise<TenantInvitation> {
    const response = await apiClient.post(`/tenants/${tenantId}/invite`, {
      email,
      role
    })
    return response.data
  }

  // Accept tenant invitation
  static async acceptInvitation(token: string, userData: {
    firstName: string
    lastName: string
    password: string
  }): Promise<any> {
    const response = await apiClient.post('/tenants/accept-invitation', {
      token,
      ...userData
    })
    return response.data
  }

  // Generate API key
  static async generateApiKey(tenantId: string, name: string, permissions?: Record<string, any>): Promise<TenantApiKey> {
    const response = await apiClient.post(`/tenants/${tenantId}/api-keys`, {
      name,
      permissions
    })
    return response.data
  }

  // Get current tenant from URL
  static getCurrentTenantFromUrl(): { type: 'subdomain' | 'domain' | null; value: string | null } {
    const hostname = window.location.hostname
    
    // Check for subdomain
    const parts = hostname.split('.')
    if (parts.length >= 3) {
      const subdomain = parts[0]
      if (subdomain !== 'www' && subdomain !== 'api') {
        return { type: 'subdomain', value: subdomain }
      }
    }
    
    // Check for custom domain
    const mainDomains = [
      'localhost',
      '127.0.0.1',
      process.env.REACT_APP_MAIN_DOMAIN,
      process.env.REACT_APP_API_DOMAIN
    ].filter(Boolean)
    
    const isMainDomain = mainDomains.some(domain => 
      hostname === domain || hostname.endsWith(`.${domain}`)
    )
    
    if (!isMainDomain) {
      return { type: 'domain', value: hostname }
    }
    
    return { type: null, value: null }
  }

  // Resolve tenant from current URL
  static async resolveTenantFromUrl(): Promise<Tenant | null> {
    const urlInfo = this.getCurrentTenantFromUrl()
    
    if (!urlInfo.type || !urlInfo.value) {
      return null
    }
    
    try {
      switch (urlInfo.type) {
        case 'subdomain':
          return await this.getTenantBySubdomain(urlInfo.value)
        case 'domain':
          // For custom domains, we'd need a different endpoint
          // For now, return null
          return null
        default:
          return null
      }
    } catch (error) {
      console.error('Failed to resolve tenant from URL:', error)
      return null
    }
  }

  // Check if feature is enabled for tenant
  static hasFeature(tenant: Tenant, feature: string): boolean {
    return tenant.features?.[feature] === true
  }

  // Check if tenant is within limits
  static isWithinLimit(tenant: Tenant, limitType: string, currentValue: number): boolean {
    const limit = tenant.limits?.[limitType]
    
    // -1 means unlimited
    if (limit === -1) {
      return true
    }
    
    // If no limit is set, allow
    if (typeof limit !== 'number') {
      return true
    }
    
    return currentValue < limit
  }

  // Get tenant branding
  static getTenantBranding(tenant: Tenant): {
    primaryColor?: string
    secondaryColor?: string
    logo?: string
    favicon?: string
    customCss?: string
  } {
    return tenant.branding || {}
  }

  // Apply tenant branding to the page
  static applyTenantBranding(tenant: Tenant): void {
    const branding = this.getTenantBranding(tenant)
    
    // Apply primary color
    if (branding.primaryColor) {
      document.documentElement.style.setProperty('--primary-color', branding.primaryColor)
    }
    
    // Apply secondary color
    if (branding.secondaryColor) {
      document.documentElement.style.setProperty('--secondary-color', branding.secondaryColor)
    }
    
    // Apply favicon
    if (branding.favicon) {
      const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
      if (favicon) {
        favicon.href = branding.favicon
      }
    }
    
    // Apply custom CSS
    if (branding.customCss) {
      const styleElement = document.createElement('style')
      styleElement.textContent = branding.customCss
      document.head.appendChild(styleElement)
    }
    
    // Update page title if tenant name is available
    if (tenant.name) {
      document.title = `${tenant.name} - M&A Platform`
    }
  }

  // Remove tenant branding
  static removeTenantBranding(): void {
    // Reset CSS variables
    document.documentElement.style.removeProperty('--primary-color')
    document.documentElement.style.removeProperty('--secondary-color')
    
    // Reset favicon
    const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
    if (favicon) {
      favicon.href = '/favicon.ico' // Default favicon
    }
    
    // Remove custom CSS
    const customStyles = document.querySelectorAll('style[data-tenant-custom]')
    customStyles.forEach(style => style.remove())
    
    // Reset page title
    document.title = 'M&A Platform'
  }

  // Get tenant plan features
  static getPlanFeatures(plan: string): Record<string, any> {
    const planFeatures = {
      STARTER: {
        maxUsers: 5,
        maxDeals: 10,
        maxStorage: 1024 * 1024 * 1024, // 1GB
        customBranding: false,
        apiAccess: false,
        advancedReporting: false,
        sso: false
      },
      PROFESSIONAL: {
        maxUsers: 25,
        maxDeals: 100,
        maxStorage: 10 * 1024 * 1024 * 1024, // 10GB
        customBranding: true,
        apiAccess: true,
        advancedReporting: true,
        sso: false
      },
      ENTERPRISE: {
        maxUsers: -1, // Unlimited
        maxDeals: -1, // Unlimited
        maxStorage: -1, // Unlimited
        customBranding: true,
        apiAccess: true,
        advancedReporting: true,
        sso: true,
        customIntegrations: true
      },
      CUSTOM: {
        // Custom plans have their own feature set
      }
    }
    
    return planFeatures[plan as keyof typeof planFeatures] || planFeatures.STARTER
  }

  // Format storage size
  static formatStorageSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`
  }

  // Check if tenant is in trial
  static isInTrial(tenant: Tenant): boolean {
    return tenant.status === 'TRIAL'
  }

  // Get days remaining in trial
  static getTrialDaysRemaining(tenant: Tenant): number {
    if (!tenant.trialEndsAt || tenant.status !== 'TRIAL') {
      return 0
    }
    
    const trialEnd = new Date(tenant.trialEndsAt)
    const now = new Date()
    const diffTime = trialEnd.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return Math.max(0, diffDays)
  }
}
