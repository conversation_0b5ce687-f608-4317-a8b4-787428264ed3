import { describe, it, expect } from 'vitest'
import { render, screen } from './test/utils'
import App from './App'

describe('App', () => {
  it('renders without crashing', () => {
    render(<App />)
    expect(document.body).toBeInTheDocument()
  })

  it('renders the main application structure', () => {
    render(<App />)
    // The app should render without throwing errors
    expect(document.querySelector('#root')).toBeInTheDocument()
  })
})
