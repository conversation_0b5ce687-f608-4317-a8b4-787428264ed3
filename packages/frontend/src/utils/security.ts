// Content Security Policy utilities
export class CSPUtils {
  // Generate nonce for inline scripts/styles
  static generateNonce(): string {
    const array = new Uint8Array(16)
    crypto.getRandomValues(array)
    return btoa(String.fromCharCode(...array))
  }

  // Validate if a URL is safe for redirects
  static isSafeRedirectUrl(url: string, allowedDomains: string[] = []): boolean {
    try {
      const urlObj = new URL(url, window.location.origin)
      
      // Only allow same origin by default
      if (urlObj.origin === window.location.origin) {
        return true
      }
      
      // Check against allowed domains
      return allowedDomains.some(domain => 
        urlObj.hostname === domain || urlObj.hostname.endsWith(`.${domain}`)
      )
    } catch {
      return false
    }
  }

  // Sanitize URL for safe usage
  static sanitizeUrl(url: string): string {
    try {
      const urlObj = new URL(url, window.location.origin)
      
      // Remove dangerous protocols
      if (!['http:', 'https:', 'mailto:', 'tel:'].includes(urlObj.protocol)) {
        return '#'
      }
      
      return urlObj.toString()
    } catch {
      return '#'
    }
  }
}

// Input sanitization utilities
export class SanitizationUtils {
  // Escape HTML to prevent XSS
  static escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  // Remove potentially dangerous characters from user input
  static sanitizeInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim()
  }

  // Validate email format
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email) && email.length <= 254
  }

  // Validate phone number format
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
    return phoneRegex.test(phone)
  }

  // Sanitize filename for safe usage
  static sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9\-_\.]/g, '_')
      .replace(/_{2,}/g, '_')
      .substring(0, 255)
  }
}

// Local storage security utilities
export class StorageUtils {
  // Encrypt data before storing (simple XOR encryption for demo)
  private static encrypt(data: string, key: string): string {
    let result = ''
    for (let i = 0; i < data.length; i++) {
      result += String.fromCharCode(
        data.charCodeAt(i) ^ key.charCodeAt(i % key.length)
      )
    }
    return btoa(result)
  }

  // Decrypt data after retrieving
  private static decrypt(encryptedData: string, key: string): string {
    try {
      const data = atob(encryptedData)
      let result = ''
      for (let i = 0; i < data.length; i++) {
        result += String.fromCharCode(
          data.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        )
      }
      return result
    } catch {
      return ''
    }
  }

  // Securely store sensitive data
  static setSecureItem(key: string, value: any, encrypt: boolean = true): void {
    try {
      const stringValue = JSON.stringify(value)
      const finalValue = encrypt ? this.encrypt(stringValue, key) : stringValue
      localStorage.setItem(key, finalValue)
    } catch (error) {
      console.error('Failed to store secure item:', error)
    }
  }

  // Securely retrieve sensitive data
  static getSecureItem<T>(key: string, decrypt: boolean = true): T | null {
    try {
      const storedValue = localStorage.getItem(key)
      if (!storedValue) return null

      const finalValue = decrypt ? this.decrypt(storedValue, key) : storedValue
      return JSON.parse(finalValue)
    } catch (error) {
      console.error('Failed to retrieve secure item:', error)
      return null
    }
  }

  // Remove sensitive data
  static removeSecureItem(key: string): void {
    localStorage.removeItem(key)
  }

  // Clear all sensitive data
  static clearSecureStorage(): void {
    const keysToRemove = [
      'auth_token',
      'session_id',
      'user_data',
      'tenant_id'
    ]
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
    })
  }
}

// Rate limiting utilities for frontend
export class RateLimitUtils {
  private static attempts: Map<string, { count: number; resetTime: number }> = new Map()

  // Check if action is rate limited
  static isRateLimited(key: string, maxAttempts: number, windowMs: number): boolean {
    const now = Date.now()
    const attempt = this.attempts.get(key)

    if (!attempt || now > attempt.resetTime) {
      this.attempts.set(key, { count: 1, resetTime: now + windowMs })
      return false
    }

    if (attempt.count >= maxAttempts) {
      return true
    }

    attempt.count++
    return false
  }

  // Reset rate limit for a key
  static resetRateLimit(key: string): void {
    this.attempts.delete(key)
  }

  // Get remaining attempts
  static getRemainingAttempts(key: string, maxAttempts: number): number {
    const attempt = this.attempts.get(key)
    if (!attempt) return maxAttempts
    return Math.max(0, maxAttempts - attempt.count)
  }

  // Get time until reset
  static getTimeUntilReset(key: string): number {
    const attempt = this.attempts.get(key)
    if (!attempt) return 0
    return Math.max(0, attempt.resetTime - Date.now())
  }
}

// Security headers validation
export class SecurityHeaderUtils {
  // Check if page has proper security headers
  static validateSecurityHeaders(): {
    hasCSP: boolean
    hasHSTS: boolean
    hasXFrameOptions: boolean
    hasXContentTypeOptions: boolean
    warnings: string[]
  } {
    const warnings: string[] = []
    
    // Note: In a real application, these would be checked server-side
    // This is just for demonstration
    const hasCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]') !== null
    const hasHSTS = window.location.protocol === 'https:'
    const hasXFrameOptions = true // Assume present
    const hasXContentTypeOptions = true // Assume present

    if (!hasCSP) {
      warnings.push('Content Security Policy not detected')
    }
    
    if (!hasHSTS && window.location.protocol === 'http:') {
      warnings.push('HTTPS not enforced')
    }

    return {
      hasCSP,
      hasHSTS,
      hasXFrameOptions,
      hasXContentTypeOptions,
      warnings
    }
  }

  // Check if running in secure context
  static isSecureContext(): boolean {
    return window.isSecureContext
  }

  // Validate referrer policy
  static hasSecureReferrerPolicy(): boolean {
    const metaReferrer = document.querySelector('meta[name="referrer"]')
    if (!metaReferrer) return false
    
    const content = metaReferrer.getAttribute('content')
    const securePolicies = [
      'strict-origin',
      'strict-origin-when-cross-origin',
      'same-origin',
      'no-referrer'
    ]
    
    return securePolicies.includes(content || '')
  }
}

// Password security utilities
export class PasswordUtils {
  // Check password strength
  static checkPasswordStrength(password: string): {
    score: number
    feedback: string[]
    isStrong: boolean
  } {
    const feedback: string[] = []
    let score = 0

    // Length check
    if (password.length >= 8) score += 1
    else feedback.push('Use at least 8 characters')

    if (password.length >= 12) score += 1

    // Character variety checks
    if (/[a-z]/.test(password)) score += 1
    else feedback.push('Include lowercase letters')

    if (/[A-Z]/.test(password)) score += 1
    else feedback.push('Include uppercase letters')

    if (/\d/.test(password)) score += 1
    else feedback.push('Include numbers')

    if (/[^a-zA-Z\d]/.test(password)) score += 1
    else feedback.push('Include special characters')

    // Common patterns check
    if (!/(.)\1{2,}/.test(password)) score += 1
    else feedback.push('Avoid repeating characters')

    const isStrong = score >= 5 && password.length >= 8

    return { score, feedback, isStrong }
  }

  // Generate secure password
  static generateSecurePassword(length: number = 16): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
    const array = new Uint8Array(length)
    crypto.getRandomValues(array)
    
    return Array.from(array, byte => charset[byte % charset.length]).join('')
  }
}

// Device fingerprinting utilities (for security monitoring)
export class DeviceUtils {
  // Get basic device fingerprint
  static getDeviceFingerprint(): string {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx?.fillText('fingerprint', 10, 10)
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|')
    
    // Simple hash
    let hash = 0
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36)
  }

  // Detect if running in incognito/private mode
  static async isIncognitoMode(): Promise<boolean> {
    try {
      // This is a simplified check - real implementation would be more complex
      const fs = await navigator.storage?.estimate()
      return (fs?.quota || 0) < 120000000 // Less than ~120MB suggests incognito
    } catch {
      return false
    }
  }

  // Check for developer tools
  static isDevToolsOpen(): boolean {
    const threshold = 160
    return (
      window.outerHeight - window.innerHeight > threshold ||
      window.outerWidth - window.innerWidth > threshold
    )
  }
}
