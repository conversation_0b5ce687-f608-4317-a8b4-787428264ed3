import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { 
  BarChart3, 
  TrendingUp, 
  FileText, 
  Download, 
  Calendar,
  Filter,
  RefreshCw,
  PieChart,
  LineChart,
  Target,
  DollarSign,
  Users,
  Clock
} from 'lucide-react'
import { PipelineReport } from './pipeline-report'
import { PerformanceReport } from './performance-report'
import { ForecastReport } from './forecast-report'
import { ReportBuilder } from './report-builder'
import { ReportHistory } from './report-history'

interface ReportsDashboardProps {
  className?: string
}

export function ReportsDashboard({ className }: ReportsDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
    to: new Date()
  })

  const reportTypes = [
    {
      id: 'pipeline',
      name: 'Pipeline Report',
      description: 'Comprehensive view of deal pipeline and stage distribution',
      icon: <BarChart3 className="h-8 w-8 text-blue-600" />,
      color: 'bg-blue-50 border-blue-200',
      lastGenerated: '2 hours ago',
      frequency: 'Daily'
    },
    {
      id: 'performance',
      name: 'Performance Report',
      description: 'Team performance metrics and deal velocity analysis',
      icon: <TrendingUp className="h-8 w-8 text-green-600" />,
      color: 'bg-green-50 border-green-200',
      lastGenerated: '1 day ago',
      frequency: 'Weekly'
    },
    {
      id: 'forecast',
      name: 'Forecast Report',
      description: 'Revenue forecasting and pipeline predictions',
      icon: <Target className="h-8 w-8 text-purple-600" />,
      color: 'bg-purple-50 border-purple-200',
      lastGenerated: '3 days ago',
      frequency: 'Monthly'
    },
    {
      id: 'activity',
      name: 'Activity Report',
      description: 'Deal activities and engagement tracking',
      icon: <Clock className="h-8 w-8 text-orange-600" />,
      color: 'bg-orange-50 border-orange-200',
      lastGenerated: '1 hour ago',
      frequency: 'Daily'
    }
  ]

  const quickStats = [
    {
      title: 'Reports Generated',
      value: '156',
      change: '+12%',
      trend: 'up',
      icon: <FileText className="h-4 w-4" />
    },
    {
      title: 'Avg Generation Time',
      value: '2.3s',
      change: '-15%',
      trend: 'down',
      icon: <Clock className="h-4 w-4" />
    },
    {
      title: 'Data Points',
      value: '45.2K',
      change: '+8%',
      trend: 'up',
      icon: <BarChart3 className="h-4 w-4" />
    },
    {
      title: 'Export Downloads',
      value: '89',
      change: '+23%',
      trend: 'up',
      icon: <Download className="h-4 w-4" />
    }
  ]

  const handleGenerateReport = (reportType: string) => {
    console.log('Generating report:', reportType)
    // This would trigger report generation
  }

  const handleExportReport = (reportType: string, format: string) => {
    console.log('Exporting report:', reportType, 'as', format)
    // This would trigger report export
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports & Analytics</h1>
          <p className="text-muted-foreground">
            Generate insights and track performance across your deal pipeline
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Button>
            <FileText className="h-4 w-4 mr-2" />
            New Report
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {quickStats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              {stat.icon}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className={`text-xs ${
                stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stat.change} from last period
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="pipeline">Pipeline</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="forecast">Forecast</TabsTrigger>
          <TabsTrigger value="builder">Builder</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Report Types Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {reportTypes.map((report) => (
              <Card key={report.id} className={`${report.color} transition-all hover:shadow-md`}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {report.icon}
                      <div>
                        <CardTitle className="text-lg">{report.name}</CardTitle>
                        <CardDescription className="mt-1">
                          {report.description}
                        </CardDescription>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-sm text-gray-600">
                      <div>Last generated: {report.lastGenerated}</div>
                      <div>Frequency: {report.frequency}</div>
                    </div>
                    <Badge variant="outline">{report.frequency}</Badge>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleGenerateReport(report.id)}
                    >
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Generate
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveTab(report.id)}
                    >
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Recent Reports */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Reports</CardTitle>
              <CardDescription>
                Your latest generated reports and analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ReportHistory limit={5} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pipeline Report Tab */}
        <TabsContent value="pipeline" className="space-y-4">
          <PipelineReport 
            dateRange={dateRange}
            onDateRangeChange={setDateRange}
            onExport={handleExportReport}
          />
        </TabsContent>

        {/* Performance Report Tab */}
        <TabsContent value="performance" className="space-y-4">
          <PerformanceReport 
            dateRange={dateRange}
            onDateRangeChange={setDateRange}
            onExport={handleExportReport}
          />
        </TabsContent>

        {/* Forecast Report Tab */}
        <TabsContent value="forecast" className="space-y-4">
          <ForecastReport 
            dateRange={dateRange}
            onDateRangeChange={setDateRange}
            onExport={handleExportReport}
          />
        </TabsContent>

        {/* Report Builder Tab */}
        <TabsContent value="builder" className="space-y-4">
          <ReportBuilder />
        </TabsContent>
      </Tabs>

      {/* Export Options Modal would go here */}
    </div>
  )
}
