import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Download, 
  Calendar, 
  BarChart3, 
  PieChart, 
  TrendingUp,
  DollarSign,
  Target,
  Clock,
  Users,
  AlertTriangle
} from 'lucide-react'
import { DateRangePicker } from '@/components/ui/date-range-picker'
import { DealChart } from '@/components/deals/deal-chart'
import { useDealAnalytics } from '@/hooks/use-deal-analytics'
import { usePipelineMetrics } from '@/hooks/use-pipeline-metrics'

interface PipelineReportProps {
  dateRange: { from: Date; to: Date }
  onDateRangeChange: (range: { from: Date; to: Date }) => void
  onExport: (reportType: string, format: string) => void
}

export function PipelineReport({ dateRange, onDateRangeChange, onExport }: PipelineReportProps) {
  const [loading, setLoading] = useState(false)
  const [reportData, setReportData] = useState<any>(null)

  const { analytics, loading: analyticsLoading } = useDealAnalytics(dateRange)
  const { metrics, loading: metricsLoading } = usePipelineMetrics()

  useEffect(() => {
    generateReport()
  }, [dateRange])

  const generateReport = async () => {
    setLoading(true)
    try {
      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, this would call the backend API
      setReportData({
        generatedAt: new Date(),
        summary: {
          totalDeals: analytics?.totalDeals || 0,
          totalValue: analytics?.totalValue || 0,
          conversionRate: analytics?.conversionRate || 0,
          averageDaysInPipeline: analytics?.averageDaysInPipeline || 0
        },
        insights: [
          'Pipeline velocity has improved by 15% compared to last quarter',
          'Conversion rate is highest in the closing stage at 85%',
          'Average deal size has increased by 12% month-over-month',
          'Due diligence stage shows the longest average duration at 18 days'
        ],
        recommendations: [
          'Focus on accelerating deals in the due diligence stage',
          'Implement automated follow-up for pipeline deals',
          'Consider additional resources for high-value opportunities',
          'Review and optimize stage transition criteria'
        ]
      })
    } catch (error) {
      console.error('Failed to generate report:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = (format: string) => {
    onExport('pipeline', format)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  if (loading || analyticsLoading || metricsLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Pipeline Report
              </CardTitle>
              <CardDescription>
                Comprehensive analysis of your deal pipeline performance
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-4">
              <DateRangePicker
                value={dateRange}
                onChange={onDateRangeChange}
              />
              
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={() => handleExport('pdf')}>
                  <Download className="h-4 w-4 mr-2" />
                  PDF
                </Button>
                <Button variant="outline" onClick={() => handleExport('excel')}>
                  <Download className="h-4 w-4 mr-2" />
                  Excel
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Executive Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Deals</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.totalDeals || 0}</div>
            <p className="text-xs text-muted-foreground">
              Active in pipeline
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pipeline Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics?.totalValue || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total deal value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(analytics?.conversionRate || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Pipeline to close
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Cycle Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(analytics?.averageDaysInPipeline || 0)}d
            </div>
            <p className="text-xs text-muted-foreground">
              Days to close
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pipeline Funnel */}
        <Card>
          <CardHeader>
            <CardTitle>Pipeline Funnel</CardTitle>
            <CardDescription>
              Deal distribution across pipeline stages
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analytics?.dealsByStage && (
              <DealChart
                type="funnel"
                data={Object.entries(analytics.dealsByStage).map(([stage, count]) => ({
                  name: stage,
                  value: count
                }))}
                height={300}
              />
            )}
          </CardContent>
        </Card>

        {/* Value by Stage */}
        <Card>
          <CardHeader>
            <CardTitle>Value by Stage</CardTitle>
            <CardDescription>
              Deal value distribution across stages
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analytics?.valueByStage && (
              <DealChart
                type="bar"
                data={Object.entries(analytics.valueByStage).map(([stage, value]) => ({
                  name: stage,
                  value: value
                }))}
                height={300}
              />
            )}
          </CardContent>
        </Card>

        {/* Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Deal Status Distribution</CardTitle>
            <CardDescription>
              Current status of all deals
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analytics?.dealsByStatus && (
              <DealChart
                type="pie"
                data={Object.entries(analytics.dealsByStatus).map(([status, count]) => ({
                  name: status.replace('_', ' '),
                  value: count
                }))}
                height={300}
              />
            )}
          </CardContent>
        </Card>

        {/* Stage Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Stage Performance</CardTitle>
            <CardDescription>
              Average time spent in each stage
            </CardDescription>
          </CardHeader>
          <CardContent>
            {metrics && (
              <DealChart
                type="bar"
                data={metrics.map(metric => ({
                  name: metric.stageName,
                  value: metric.averageDaysInStage
                }))}
                height={300}
              />
            )}
          </CardContent>
        </Card>
      </div>

      {/* Insights and Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Key Insights */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              Key Insights
            </CardTitle>
            <CardDescription>
              Important findings from your pipeline data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {reportData?.insights?.map((insight: string, index: number) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="text-sm text-blue-800">{insight}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              Recommendations
            </CardTitle>
            <CardDescription>
              Actionable steps to improve pipeline performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {reportData?.recommendations?.map((recommendation: string, index: number) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                  <div className="w-2 h-2 bg-orange-600 rounded-full mt-2"></div>
                  <p className="text-sm text-orange-800">{recommendation}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics Table */}
      <Card>
        <CardHeader>
          <CardTitle>Stage Metrics</CardTitle>
          <CardDescription>
            Detailed performance metrics for each pipeline stage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3">Stage</th>
                  <th className="text-left p-3">Deal Count</th>
                  <th className="text-left p-3">Total Value</th>
                  <th className="text-left p-3">Avg Days</th>
                  <th className="text-left p-3">Conversion Rate</th>
                  <th className="text-left p-3">Drop-off Rate</th>
                </tr>
              </thead>
              <tbody>
                {metrics?.map((metric) => (
                  <tr key={metric.stageId} className="border-b">
                    <td className="p-3 font-medium">{metric.stageName}</td>
                    <td className="p-3">{metric.dealCount}</td>
                    <td className="p-3">{formatCurrency(metric.totalValue)}</td>
                    <td className="p-3">{Math.round(metric.averageDaysInStage)}d</td>
                    <td className="p-3">
                      <Badge variant="outline">
                        {formatPercentage(metric.conversionRate)}
                      </Badge>
                    </td>
                    <td className="p-3">
                      <Badge variant="outline">
                        {formatPercentage(metric.dropOffRate)}
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Report Footer */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Generated on {reportData?.generatedAt?.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-2">
              <span>Report Period: {dateRange.from.toLocaleDateString()} - {dateRange.to.toLocaleDateString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
