import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Download, 
  Calendar, 
  TrendingUp, 
  Users,
  Target,
  Clock,
  Award,
  Activity,
  BarChart3,
  LineChart
} from 'lucide-react'
import { DateRangePicker } from '@/components/ui/date-range-picker'
import { DealChart } from '@/components/deals/deal-chart'

interface PerformanceReportProps {
  dateRange: { from: Date; to: Date }
  onDateRangeChange: (range: { from: Date; to: Date }) => void
  onExport: (reportType: string, format: string) => void
}

export function PerformanceReport({ dateRange, onDateRangeChange, onExport }: PerformanceReportProps) {
  const [loading, setLoading] = useState(false)
  const [performanceData, setPerformanceData] = useState<any>(null)

  useEffect(() => {
    generateReport()
  }, [dateRange])

  const generateReport = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setPerformanceData({
        generatedAt: new Date(),
        teamMetrics: {
          totalTeamMembers: 8,
          activeDeals: 45,
          averageDealsPerMember: 5.6,
          totalActivities: 234
        },
        topPerformers: [
          {
            id: '1',
            name: 'Sarah Johnson',
            dealsCount: 12,
            totalValue: 2400000,
            conversionRate: 85,
            avgDaysToClose: 42
          },
          {
            id: '2',
            name: 'Michael Chen',
            dealsCount: 10,
            totalValue: 1800000,
            conversionRate: 78,
            avgDaysToClose: 38
          },
          {
            id: '3',
            name: 'Emily Rodriguez',
            dealsCount: 9,
            totalValue: 1650000,
            conversionRate: 82,
            avgDaysToClose: 45
          }
        ],
        velocityTrend: [
          { period: 'Jan', velocity: 28 },
          { period: 'Feb', velocity: 32 },
          { period: 'Mar', velocity: 35 },
          { period: 'Apr', velocity: 31 },
          { period: 'May', velocity: 38 },
          { period: 'Jun', velocity: 42 }
        ],
        conversionRates: [
          { stage: 'Pipeline', rate: 90 },
          { stage: 'Due Diligence', rate: 75 },
          { stage: 'Negotiation', rate: 68 },
          { stage: 'Closing', rate: 85 }
        ],
        activityMetrics: {
          totalCalls: 156,
          totalMeetings: 89,
          totalEmails: 234,
          totalPresentations: 23
        }
      })
    } catch (error) {
      console.error('Failed to generate performance report:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = (format: string) => {
    onExport('performance', format)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Performance Report
              </CardTitle>
              <CardDescription>
                Team performance metrics and deal velocity analysis
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-4">
              <DateRangePicker
                value={dateRange}
                onChange={onDateRangeChange}
              />
              
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={() => handleExport('pdf')}>
                  <Download className="h-4 w-4 mr-2" />
                  PDF
                </Button>
                <Button variant="outline" onClick={() => handleExport('excel')}>
                  <Download className="h-4 w-4 mr-2" />
                  Excel
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Team Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceData?.teamMetrics.totalTeamMembers}</div>
            <p className="text-xs text-muted-foreground">
              Active team members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Deals</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceData?.teamMetrics.activeDeals}</div>
            <p className="text-xs text-muted-foreground">
              Currently in pipeline
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Deals/Member</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceData?.teamMetrics.averageDealsPerMember}</div>
            <p className="text-xs text-muted-foreground">
              Deals per team member
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceData?.teamMetrics.totalActivities}</div>
            <p className="text-xs text-muted-foreground">
              This period
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Deal Velocity Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Deal Velocity Trend</CardTitle>
            <CardDescription>
              Average days to close deals over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DealChart
              type="line"
              data={performanceData?.velocityTrend || []}
              height={300}
            />
          </CardContent>
        </Card>

        {/* Conversion Rates by Stage */}
        <Card>
          <CardHeader>
            <CardTitle>Conversion Rates by Stage</CardTitle>
            <CardDescription>
              Success rates at each pipeline stage
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DealChart
              type="bar"
              data={performanceData?.conversionRates?.map((item: any) => ({
                name: item.stage,
                value: item.rate
              })) || []}
              height={300}
            />
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5 text-yellow-600" />
            Top Performers
          </CardTitle>
          <CardDescription>
            Highest performing team members this period
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {performanceData?.topPerformers?.map((performer: any, index: number) => (
              <div key={performer.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-yellow-100 text-yellow-800 rounded-full font-semibold">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-semibold">{performer.name}</div>
                    <div className="text-sm text-gray-600">
                      {performer.dealsCount} deals • {formatCurrency(performer.totalValue)}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-sm font-medium">{performer.conversionRate}%</div>
                    <div className="text-xs text-gray-500">Conversion</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-medium">{performer.avgDaysToClose}d</div>
                    <div className="text-xs text-gray-500">Avg Close</div>
                  </div>
                  <Badge variant="outline">Top Performer</Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Activity Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Activity Types */}
        <Card>
          <CardHeader>
            <CardTitle>Activity Breakdown</CardTitle>
            <CardDescription>
              Distribution of team activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Calls</span>
                <div className="flex items-center gap-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: '65%' }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium">{performanceData?.activityMetrics.totalCalls}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Meetings</span>
                <div className="flex items-center gap-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: '45%' }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium">{performanceData?.activityMetrics.totalMeetings}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Emails</span>
                <div className="flex items-center gap-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-purple-600 h-2 rounded-full" 
                      style={{ width: '85%' }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium">{performanceData?.activityMetrics.totalEmails}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Presentations</span>
                <div className="flex items-center gap-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-orange-600 h-2 rounded-full" 
                      style={{ width: '25%' }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium">{performanceData?.activityMetrics.totalPresentations}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Insights */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Insights</CardTitle>
            <CardDescription>
              Key findings and recommendations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-green-50 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-800">Strong Performance</span>
                </div>
                <p className="text-sm text-green-700">
                  Team velocity has improved by 15% compared to last quarter
                </p>
              </div>
              
              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Target className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-800">Opportunity</span>
                </div>
                <p className="text-sm text-blue-700">
                  Focus on improving conversion rates in the negotiation stage
                </p>
              </div>
              
              <div className="p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Clock className="h-4 w-4 text-orange-600" />
                  <span className="font-medium text-orange-800">Action Needed</span>
                </div>
                <p className="text-sm text-orange-700">
                  Consider additional training for underperforming team members
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Report Footer */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Generated on {performanceData?.generatedAt?.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-2">
              <span>Report Period: {dateRange.from.toLocaleDateString()} - {dateRange.to.toLocaleDateString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
