import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Download, 
  Calendar, 
  Target, 
  TrendingUp,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3
} from 'lucide-react'
import { DateRangePicker } from '@/components/ui/date-range-picker'
import { DealChart } from '@/components/deals/deal-chart'

interface ForecastReportProps {
  dateRange: { from: Date; to: Date }
  onDateRangeChange: (range: { from: Date; to: Date }) => void
  onExport: (reportType: string, format: string) => void
}

export function ForecastReport({ dateRange, onDateRangeChange, onExport }: ForecastReportProps) {
  const [loading, setLoading] = useState(false)
  const [forecastData, setForecastData] = useState<any>(null)

  useEffect(() => {
    generateReport()
  }, [dateRange])

  const generateReport = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setForecastData({
        generatedAt: new Date(),
        quarterlyForecast: [
          {
            quarter: '2024 Q1',
            bestCase: 8500000,
            commit: 6200000,
            pipeline: 4800000,
            closed: 3200000,
            confidence: 85
          },
          {
            quarter: '2024 Q2',
            bestCase: 9200000,
            commit: 7100000,
            pipeline: 5400000,
            closed: 0,
            confidence: 78
          },
          {
            quarter: '2024 Q3',
            bestCase: 10100000,
            commit: 7800000,
            pipeline: 6200000,
            closed: 0,
            confidence: 72
          },
          {
            quarter: '2024 Q4',
            bestCase: 11500000,
            commit: 8900000,
            pipeline: 7100000,
            closed: 0,
            confidence: 68
          }
        ],
        currentQuarter: {
          target: 6500000,
          committed: 4800000,
          achieved: 3200000,
          remaining: 3300000,
          daysLeft: 45,
          runRate: 71000
        },
        riskAnalysis: {
          highRiskDeals: 8,
          mediumRiskDeals: 15,
          lowRiskDeals: 22,
          totalAtRisk: 2400000
        },
        topOpportunities: [
          {
            id: '1',
            title: 'TechCorp Acquisition',
            value: 1200000,
            probability: 85,
            expectedClose: '2024-03-15',
            stage: 'Closing'
          },
          {
            id: '2',
            title: 'HealthTech Merger',
            value: 950000,
            probability: 75,
            expectedClose: '2024-03-22',
            stage: 'Negotiation'
          },
          {
            id: '3',
            title: 'FinServ Partnership',
            value: 800000,
            probability: 70,
            expectedClose: '2024-04-10',
            stage: 'Due Diligence'
          }
        ]
      })
    } catch (error) {
      console.error('Failed to generate forecast report:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = (format: string) => {
    onExport('forecast', format)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600'
    if (confidence >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 80) return <Badge className="bg-green-100 text-green-800">High</Badge>
    if (confidence >= 70) return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>
    return <Badge className="bg-red-100 text-red-800">Low</Badge>
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Forecast Report
              </CardTitle>
              <CardDescription>
                Revenue forecasting and pipeline predictions
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-4">
              <DateRangePicker
                value={dateRange}
                onChange={onDateRangeChange}
              />
              
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={() => handleExport('pdf')}>
                  <Download className="h-4 w-4 mr-2" />
                  PDF
                </Button>
                <Button variant="outline" onClick={() => handleExport('excel')}>
                  <Download className="h-4 w-4 mr-2" />
                  Excel
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Current Quarter Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quarter Target</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(forecastData?.currentQuarter.target)}
            </div>
            <p className="text-xs text-muted-foreground">
              Q1 2024 goal
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Committed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(forecastData?.currentQuarter.committed)}
            </div>
            <p className="text-xs text-muted-foreground">
              High confidence deals
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Achieved</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(forecastData?.currentQuarter.achieved)}
            </div>
            <p className="text-xs text-muted-foreground">
              Closed this quarter
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Days Left</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {forecastData?.currentQuarter.daysLeft}
            </div>
            <p className="text-xs text-muted-foreground">
              In current quarter
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quarterly Forecast Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Quarterly Forecast</CardTitle>
          <CardDescription>
            Revenue projections for the next four quarters
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DealChart
            type="bar"
            data={forecastData?.quarterlyForecast?.map((q: any) => ({
              quarter: q.quarter,
              'Best Case': q.bestCase,
              'Commit': q.commit,
              'Pipeline': q.pipeline,
              'Closed': q.closed
            })) || []}
            height={400}
          />
        </CardContent>
      </Card>

      {/* Forecast Details Table */}
      <Card>
        <CardHeader>
          <CardTitle>Forecast Breakdown</CardTitle>
          <CardDescription>
            Detailed quarterly projections with confidence levels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3">Quarter</th>
                  <th className="text-left p-3">Best Case</th>
                  <th className="text-left p-3">Commit</th>
                  <th className="text-left p-3">Pipeline</th>
                  <th className="text-left p-3">Closed</th>
                  <th className="text-left p-3">Confidence</th>
                </tr>
              </thead>
              <tbody>
                {forecastData?.quarterlyForecast?.map((quarter: any) => (
                  <tr key={quarter.quarter} className="border-b">
                    <td className="p-3 font-medium">{quarter.quarter}</td>
                    <td className="p-3">{formatCurrency(quarter.bestCase)}</td>
                    <td className="p-3 text-green-600">{formatCurrency(quarter.commit)}</td>
                    <td className="p-3 text-blue-600">{formatCurrency(quarter.pipeline)}</td>
                    <td className="p-3 text-gray-600">{formatCurrency(quarter.closed)}</td>
                    <td className="p-3">
                      <div className="flex items-center gap-2">
                        <span className={getConfidenceColor(quarter.confidence)}>
                          {quarter.confidence}%
                        </span>
                        {getConfidenceBadge(quarter.confidence)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Risk Analysis and Top Opportunities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Risk Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              Risk Analysis
            </CardTitle>
            <CardDescription>
              Deal risk distribution and potential impact
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div>
                  <div className="font-medium text-red-800">High Risk</div>
                  <div className="text-sm text-red-600">Immediate attention needed</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-red-800">{forecastData?.riskAnalysis.highRiskDeals}</div>
                  <div className="text-xs text-red-600">deals</div>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div>
                  <div className="font-medium text-yellow-800">Medium Risk</div>
                  <div className="text-sm text-yellow-600">Monitor closely</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-yellow-800">{forecastData?.riskAnalysis.mediumRiskDeals}</div>
                  <div className="text-xs text-yellow-600">deals</div>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div>
                  <div className="font-medium text-green-800">Low Risk</div>
                  <div className="text-sm text-green-600">On track</div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-green-800">{forecastData?.riskAnalysis.lowRiskDeals}</div>
                  <div className="text-xs text-green-600">deals</div>
                </div>
              </div>
              
              <div className="pt-3 border-t">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Total Value at Risk</span>
                  <span className="font-bold text-red-600">
                    {formatCurrency(forecastData?.riskAnalysis.totalAtRisk)}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Top Opportunities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Top Opportunities
            </CardTitle>
            <CardDescription>
              Highest value deals likely to close this quarter
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {forecastData?.topOpportunities?.map((opportunity: any, index: number) => (
                <div key={opportunity.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium">{opportunity.title}</div>
                      <div className="text-sm text-gray-600">
                        Expected: {new Date(opportunity.expectedClose).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-bold">{formatCurrency(opportunity.value)}</div>
                    <div className="text-sm text-gray-600">{opportunity.probability}% probability</div>
                    <Badge variant="outline" className="mt-1">
                      {opportunity.stage}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Forecast Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Forecast Insights</CardTitle>
          <CardDescription>
            Key findings and recommendations for achieving targets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-semibold text-green-800">Positive Indicators</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Q2 forecast shows 20% increase over Q1</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Pipeline value is sufficient to meet quarterly targets</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Conversion rates are improving month-over-month</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold text-orange-800">Areas of Concern</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  <span>8 high-risk deals need immediate attention</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  <span>Q4 confidence level is below target threshold</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  <span>Need to increase prospecting for future quarters</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Footer */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Generated on {forecastData?.generatedAt?.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-2">
              <span>Forecast Period: Next 4 Quarters</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
