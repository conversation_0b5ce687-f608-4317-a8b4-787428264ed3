import React, { useState } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  X,
  Plus,
  AlertCircle,
  Building,
  DollarSign,
  Calendar,
  Tag
} from 'lucide-react'
import { 
  CreateDealRequest, 
  DealType, 
  DealSource, 
  DealPriority 
} from '@/types/deal'
import { useCreateDeal } from '@/hooks/use-deals'

interface DealCreateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function DealCreateDialog({ open, onOpenChange, onSuccess }: DealCreateDialogProps) {
  const [formData, setFormData] = useState<CreateDealRequest>({
    title: '',
    description: '',
    dealType: DealType.ACQUISITION,
    dealSource: DealSource.DIRECT,
    targetCompany: '',
    targetIndustry: '',
    dealValue: undefined,
    currency: 'USD',
    priority: DealPriority.MEDIUM,
    expectedCloseDate: '',
    assignedTo: '',
    tags: [],
    confidentiality: ''
  })
  
  const [tagInput, setTagInput] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  const { createDeal, loading } = useCreateDeal()

  const handleInputChange = (field: keyof CreateDealRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }))
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Deal title is required'
    }

    if (!formData.targetCompany.trim()) {
      newErrors.targetCompany = 'Target company is required'
    }

    if (formData.dealValue && formData.dealValue <= 0) {
      newErrors.dealValue = 'Deal value must be positive'
    }

    if (formData.expectedCloseDate) {
      const closeDate = new Date(formData.expectedCloseDate)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      if (closeDate < today) {
        newErrors.expectedCloseDate = 'Expected close date cannot be in the past'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      // Clean up form data
      const submitData: CreateDealRequest = {
        ...formData,
        dealValue: formData.dealValue || undefined,
        expectedCloseDate: formData.expectedCloseDate || undefined,
        assignedTo: formData.assignedTo || undefined,
        targetIndustry: formData.targetIndustry || undefined,
        confidentiality: formData.confidentiality || undefined
      }

      await createDeal(submitData)
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        dealType: DealType.ACQUISITION,
        dealSource: DealSource.DIRECT,
        targetCompany: '',
        targetIndustry: '',
        dealValue: undefined,
        currency: 'USD',
        priority: DealPriority.MEDIUM,
        expectedCloseDate: '',
        assignedTo: '',
        tags: [],
        confidentiality: ''
      })
      setTagInput('')
      setErrors({})
      
      onSuccess?.()
    } catch (error) {
      console.error('Failed to create deal:', error)
    }
  }

  const handleCancel = () => {
    setFormData({
      title: '',
      description: '',
      dealType: DealType.ACQUISITION,
      dealSource: DealSource.DIRECT,
      targetCompany: '',
      targetIndustry: '',
      dealValue: undefined,
      currency: 'USD',
      priority: DealPriority.MEDIUM,
      expectedCloseDate: '',
      assignedTo: '',
      tags: [],
      confidentiality: ''
    })
    setTagInput('')
    setErrors({})
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Deal</DialogTitle>
          <DialogDescription>
            Add a new deal to your pipeline
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Deal Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter deal title"
                  className={errors.title ? 'border-red-500' : ''}
                />
                {errors.title && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.title}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="targetCompany">Target Company *</Label>
                <div className="relative">
                  <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="targetCompany"
                    value={formData.targetCompany}
                    onChange={(e) => handleInputChange('targetCompany', e.target.value)}
                    placeholder="Enter target company"
                    className={`pl-10 ${errors.targetCompany ? 'border-red-500' : ''}`}
                  />
                </div>
                {errors.targetCompany && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.targetCompany}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter deal description"
                rows={3}
              />
            </div>
          </div>

          {/* Deal Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Deal Details</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dealType">Deal Type</Label>
                <Select
                  value={formData.dealType}
                  onValueChange={(value) => handleInputChange('dealType', value as DealType)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(DealType).map(type => (
                      <SelectItem key={type} value={type}>
                        {type.replace('_', ' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dealSource">Deal Source</Label>
                <Select
                  value={formData.dealSource}
                  onValueChange={(value) => handleInputChange('dealSource', value as DealSource)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(DealSource).map(source => (
                      <SelectItem key={source} value={source}>
                        {source.replace('_', ' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dealValue">Deal Value</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="dealValue"
                    type="number"
                    value={formData.dealValue || ''}
                    onChange={(e) => handleInputChange('dealValue', e.target.value ? Number(e.target.value) : undefined)}
                    placeholder="0"
                    className={`pl-10 ${errors.dealValue ? 'border-red-500' : ''}`}
                  />
                </div>
                {errors.dealValue && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.dealValue}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={formData.currency}
                  onValueChange={(value) => handleInputChange('currency', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                    <SelectItem value="CAD">CAD</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => handleInputChange('priority', value as DealPriority)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(DealPriority).map(priority => (
                      <SelectItem key={priority} value={priority}>
                        {priority}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="targetIndustry">Target Industry</Label>
                <Input
                  id="targetIndustry"
                  value={formData.targetIndustry}
                  onChange={(e) => handleInputChange('targetIndustry', e.target.value)}
                  placeholder="e.g., Technology, Healthcare"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="expectedCloseDate">Expected Close Date</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="expectedCloseDate"
                    type="date"
                    value={formData.expectedCloseDate}
                    onChange={(e) => handleInputChange('expectedCloseDate', e.target.value)}
                    className={`pl-10 ${errors.expectedCloseDate ? 'border-red-500' : ''}`}
                  />
                </div>
                {errors.expectedCloseDate && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.expectedCloseDate}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Tags</h3>
            
            <div className="space-y-2">
              <Label htmlFor="tags">Add Tags</Label>
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="tags"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Enter tag and press Add"
                    className="pl-10"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        handleAddTag()
                      }
                    }}
                  />
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddTag}
                  disabled={!tagInput.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              {formData.tags && formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => handleRemoveTag(tag)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Deal'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
