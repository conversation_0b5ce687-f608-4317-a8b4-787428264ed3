import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Search, 
  SortAsc, 
  SortDesc, 
  Eye, 
  Edit, 
  Trash2,
  ChevronLeft,
  ChevronRight,
  Target,
  DollarSign,
  Calendar,
  User,
  Building,
  TrendingUp,
  AlertTriangle
} from 'lucide-react'
import { 
  Deal, 
  DealSortOptions, 
  DealStatus, 
  DealPriority, 
  RiskLevel,
  DEAL_STATUS_COLORS,
  DEAL_PRIORITY_COLORS,
  RISK_LEVEL_COLORS
} from '@/types/deal'

interface DealListProps {
  deals: Deal[]
  loading?: boolean
  error?: string | null
  total?: number
  currentPage?: number
  pageSize?: number
  hasMore?: boolean
  onPageChange?: (page: number) => void
  onSortChange?: (sort: DealSortOptions) => void
  sortOptions?: DealSortOptions
  onDealClick?: (deal: Deal) => void
  onDealEdit?: (deal: Deal) => void
  onDealDelete?: (deal: Deal) => void
}

export function DealList({
  deals,
  loading = false,
  error = null,
  total = 0,
  currentPage = 1,
  pageSize = 20,
  hasMore = false,
  onPageChange,
  onSortChange,
  sortOptions = { field: 'createdAt', direction: 'desc' },
  onDealClick,
  onDealEdit,
  onDealDelete
}: DealListProps) {
  const [searchTerm, setSearchTerm] = useState('')

  const handleSort = (field: DealSortOptions['field']) => {
    if (!onSortChange) return
    
    const direction = sortOptions.field === field && sortOptions.direction === 'asc' ? 'desc' : 'asc'
    onSortChange({ field, direction })
  }

  const getSortIcon = (field: DealSortOptions['field']) => {
    if (sortOptions.field !== field) return null
    return sortOptions.direction === 'asc' ? 
      <SortAsc className="h-4 w-4" /> : 
      <SortDesc className="h-4 w-4" />
  }

  const getStatusBadge = (status: DealStatus) => {
    return (
      <Badge 
        variant="outline" 
        style={{ 
          borderColor: DEAL_STATUS_COLORS[status],
          color: DEAL_STATUS_COLORS[status]
        }}
      >
        {status.replace('_', ' ')}
      </Badge>
    )
  }

  const getPriorityBadge = (priority: DealPriority) => {
    return (
      <Badge 
        variant="outline"
        style={{ 
          borderColor: DEAL_PRIORITY_COLORS[priority],
          color: DEAL_PRIORITY_COLORS[priority]
        }}
      >
        {priority}
      </Badge>
    )
  }

  const getRiskBadge = (riskLevel: RiskLevel) => {
    return (
      <Badge 
        variant="outline"
        style={{ 
          borderColor: RISK_LEVEL_COLORS[riskLevel],
          color: RISK_LEVEL_COLORS[riskLevel]
        }}
      >
        {riskLevel}
      </Badge>
    )
  }

  const formatCurrency = (amount?: number, currency = 'USD') => {
    if (!amount) return '-'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString()
  }

  const getHealthScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500'
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const filteredDeals = deals.filter(deal =>
    searchTerm === '' ||
    deal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    deal.targetCompany.toLowerCase().includes(searchTerm.toLowerCase()) ||
    deal.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
            <p>Failed to load deals: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search and Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search deals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="text-sm text-gray-600">
              {total} deal{total !== 1 ? 's' : ''} total
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Deals Table */}
      <Card>
        <CardHeader>
          <CardTitle>Deals</CardTitle>
          <CardDescription>
            Manage your deal pipeline
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {filteredDeals.length === 0 ? (
            <div className="text-center py-8">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Deals Found</h3>
              <p className="text-gray-600">
                {searchTerm ? 'No deals match your search criteria.' : 'No deals in your pipeline yet.'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('title')}
                        className="font-semibold"
                      >
                        Deal
                        {getSortIcon('title')}
                      </Button>
                    </th>
                    <th className="text-left p-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('dealValue')}
                        className="font-semibold"
                      >
                        Value
                        {getSortIcon('dealValue')}
                      </Button>
                    </th>
                    <th className="text-left p-3">Status</th>
                    <th className="text-left p-3">Stage</th>
                    <th className="text-left p-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('priority')}
                        className="font-semibold"
                      >
                        Priority
                        {getSortIcon('priority')}
                      </Button>
                    </th>
                    <th className="text-left p-3">Risk</th>
                    <th className="text-left p-3">Health</th>
                    <th className="text-left p-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('expectedCloseDate')}
                        className="font-semibold"
                      >
                        Expected Close
                        {getSortIcon('expectedCloseDate')}
                      </Button>
                    </th>
                    <th className="text-left p-3">Assigned To</th>
                    <th className="text-right p-3">Actions</th>
                  </tr>
                </thead>
                
                <tbody>
                  {filteredDeals.map((deal) => (
                    <tr 
                      key={deal.id} 
                      className="border-b hover:bg-gray-50 cursor-pointer"
                      onClick={() => onDealClick?.(deal)}
                    >
                      <td className="p-3">
                        <div>
                          <div className="font-medium">{deal.title}</div>
                          <div className="text-sm text-gray-500 flex items-center gap-1">
                            <Building className="h-3 w-3" />
                            {deal.targetCompany}
                          </div>
                        </div>
                      </td>
                      
                      <td className="p-3">
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4 text-gray-400" />
                          <span className="font-medium">
                            {formatCurrency(deal.dealValue, deal.currency)}
                          </span>
                        </div>
                      </td>
                      
                      <td className="p-3">
                        {getStatusBadge(deal.status)}
                      </td>
                      
                      <td className="p-3">
                        <Badge variant="secondary">{deal.stage}</Badge>
                      </td>
                      
                      <td className="p-3">
                        {getPriorityBadge(deal.priority)}
                      </td>
                      
                      <td className="p-3">
                        {getRiskBadge(deal.riskLevel)}
                      </td>
                      
                      <td className="p-3">
                        <div className="flex items-center gap-1">
                          <TrendingUp className={`h-4 w-4 ${getHealthScoreColor(deal.healthScore)}`} />
                          <span className={getHealthScoreColor(deal.healthScore)}>
                            {deal.healthScore || '-'}
                          </span>
                        </div>
                      </td>
                      
                      <td className="p-3">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            {formatDate(deal.expectedCloseDate)}
                          </span>
                        </div>
                      </td>
                      
                      <td className="p-3">
                        {deal.assignee ? (
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">
                              {deal.assignee.firstName} {deal.assignee.lastName}
                            </span>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">Unassigned</span>
                        )}
                      </td>
                      
                      <td className="p-3">
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              onDealClick?.(deal)
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              onDealEdit?.(deal)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              onDealDelete?.(deal)
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {total > pageSize && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, total)} of {total} deals
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange?.(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, Math.ceil(total / pageSize)) }, (_, i) => {
                    const page = i + 1
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => onPageChange?.(page)}
                      >
                        {page}
                      </Button>
                    )
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange?.(currentPage + 1)}
                  disabled={!hasMore}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
