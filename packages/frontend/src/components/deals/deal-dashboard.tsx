import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { DealFilters, DealSortOptions } from '@/types/deal'
import {
    AlertTriangle,
    BarChart3,
    Clock,
    DollarSign,
    Filter,
    Plus,
    RefreshCw,
    Target,
    TrendingDown,
    TrendingUp,
    Users
} from 'lucide-react'
import { useState } from 'react'
// import { DealList } from './deal-list'
// import { DealFilters as DealFiltersComponent } from './deal-filters'
// import { DealCreateDialog } from './deal-create-dialog'
// import { DealAnalyticsCards } from './deal-analytics-cards'
// import { DealChart } from './deal-chart'
import { useDealAnalytics } from '@/hooks/use-deal-analytics'
import { useDeals } from '@/hooks/use-deals'

interface DealDashboardProps {
  className?: string
}

export function DealDashboard({ className }: DealDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<DealFilters>({})
  const [sortOptions, setSortOptions] = useState<DealSortOptions>({
    field: 'createdAt',
    direction: 'desc'
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)

  const {
    deals,
    loading: dealsLoading,
    error: dealsError,
    total,
    hasMore,
    refetch: refetchDeals
  } = useDeals({
    filters,
    sort: sortOptions,
    page: currentPage,
    limit: pageSize
  })

  const {
    analytics,
    loading: analyticsLoading,
    error: analyticsError,
    refetch: refetchAnalytics
  } = useDealAnalytics()

  const handleRefresh = async () => {
    await Promise.all([refetchDeals(), refetchAnalytics()])
  }

  const handleFiltersChange = (newFilters: DealFilters) => {
    setFilters(newFilters)
    setCurrentPage(1) // Reset to first page when filters change
  }

  const handleSortChange = (newSort: DealSortOptions) => {
    setSortOptions(newSort)
    setCurrentPage(1) // Reset to first page when sort changes
  }

  const handleDealCreated = () => {
    setShowCreateDialog(false)
    handleRefresh()
  }

  const getHealthScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500'
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const formatCurrency = (amount?: number, currency = 'USD') => {
    if (!amount) return '-'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value?: number) => {
    if (value === undefined || value === null) return '-'
    return `${value.toFixed(1)}%`
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Deal Pipeline</h1>
          <p className="text-muted-foreground">
            Manage your M&A deals and track pipeline performance
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Deal
          </Button>
        </div>
      </div>

      {/* Analytics Cards */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Deals</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.totalDeals}</div>
              <p className="text-xs text-muted-foreground">
                Active deals in pipeline
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pipeline Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(analytics.totalValue)}
              </div>
              <p className="text-xs text-muted-foreground">
                Total deal value
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Deal Size</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(analytics.averageDealSize)}
              </div>
              <p className="text-xs text-muted-foreground">
                Average deal value
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
              {analytics.conversionRate >= 70 ? (
                <TrendingUp className="h-4 w-4 text-green-600" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-600" />
              )}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatPercentage(analytics.conversionRate)}
              </div>
              <p className="text-xs text-muted-foreground">
                Pipeline to close rate
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Additional Metrics */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Days in Pipeline</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(analytics.averageDaysInPipeline)}
              </div>
              <p className="text-xs text-muted-foreground">
                Days from start to close
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Deals at Risk</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {analytics.dealsAtRisk}
              </div>
              <p className="text-xs text-muted-foreground">
                High risk deals
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Health Score</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getHealthScoreColor(analytics.averageHealthScore)}`}>
                {Math.round(analytics.averageHealthScore)}
              </div>
              <p className="text-xs text-muted-foreground">
                Average pipeline health
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
            <CardDescription>
              Filter deals by various criteria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DealFiltersComponent
              filters={filters}
              onFiltersChange={handleFiltersChange}
            />
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="deals">Deals</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Deals */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Deals</CardTitle>
                <CardDescription>
                  Latest deals in your pipeline
                </CardDescription>
              </CardHeader>
              <CardContent>
                {dealsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                  </div>
                ) : deals && deals.length > 0 ? (
                  <div className="space-y-3">
                    {deals.slice(0, 5).map((deal) => (
                      <div key={deal.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{deal.title}</div>
                          <div className="text-sm text-gray-500">{deal.targetCompany}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{deal.stage}</Badge>
                          <div className="text-sm font-medium">
                            {formatCurrency(deal.dealValue, deal.currency)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Deals Yet</h3>
                    <p className="text-gray-600 mb-4">
                      Create your first deal to get started
                    </p>
                    <Button onClick={() => setShowCreateDialog(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Deal
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Pipeline Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Pipeline by Stage</CardTitle>
                <CardDescription>
                  Deal distribution across pipeline stages
                </CardDescription>
              </CardHeader>
              <CardContent>
                {analytics ? (
                  <DealChart
                    type="bar"
                    data={Object.entries(analytics.dealsByStage).map(([stage, count]) => ({
                      name: stage,
                      value: count
                    }))}
                    height={300}
                  />
                ) : (
                  <div className="flex items-center justify-center h-[300px]">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="deals" className="space-y-4">
          <DealList
            deals={deals || []}
            loading={dealsLoading}
            error={dealsError}
            total={total}
            currentPage={currentPage}
            pageSize={pageSize}
            hasMore={hasMore}
            onPageChange={setCurrentPage}
            onSortChange={handleSortChange}
            sortOptions={sortOptions}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <DealAnalyticsCards analytics={analytics} loading={analyticsLoading} />
        </TabsContent>
      </Tabs>

      {/* Create Deal Dialog */}
      <DealCreateDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={handleDealCreated}
      />
    </div>
  )
}
