import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { useSession } from '@/hooks/use-session'
import { SessionService } from '@/services/session.service'
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Clock, 
  MapPin, 
  Shield, 
  Trash2, 
  RefreshCw,
  AlertTriangle,
  Loader2
} from 'lucide-react'

interface SessionManagerProps {
  onClose?: () => void
}

export function SessionManager({ onClose }: SessionManagerProps) {
  const {
    currentSession,
    allSessions,
    loading,
    error,
    isExpiringSoon,
    timeUntilExpiration,
    loadAllSessions,
    extendSession,
    destroySession,
    destroyAllOtherSessions,
    clearError
  } = useSession()

  const [actionLoading, setActionLoading] = useState<string | null>(null)

  const handleExtendSession = async () => {
    setActionLoading('extend')
    try {
      await extendSession()
    } catch (error) {
      // Error handled by hook
    } finally {
      setActionLoading(null)
    }
  }

  const handleDestroySession = async (sessionId: string) => {
    setActionLoading(`destroy-${sessionId}`)
    try {
      await destroySession(sessionId)
    } catch (error) {
      // Error handled by hook
    } finally {
      setActionLoading(null)
    }
  }

  const handleDestroyAllOther = async () => {
    setActionLoading('destroy-all')
    try {
      await destroyAllOtherSessions()
    } catch (error) {
      // Error handled by hook
    } finally {
      setActionLoading(null)
    }
  }

  const getDeviceIcon = (deviceInfo?: { device?: string }) => {
    switch (deviceInfo?.device) {
      case 'Mobile':
        return <Smartphone className="h-4 w-4" />
      case 'Tablet':
        return <Tablet className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  const formatLocation = (location?: { city?: string; region?: string; country?: string }) => {
    if (!location) return 'Unknown location'
    
    const parts = [location.city, location.region, location.country].filter(Boolean)
    return parts.length > 0 ? parts.join(', ') : 'Unknown location'
  }

  return (
    <div className="space-y-6">
      {/* Current Session Warning */}
      {isExpiringSoon && timeUntilExpiration && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>
              Your session expires in {timeUntilExpiration.hours > 0 && `${timeUntilExpiration.hours}h `}
              {timeUntilExpiration.minutes}m
            </span>
            <Button
              size="sm"
              variant="outline"
              onClick={handleExtendSession}
              disabled={actionLoading === 'extend'}
            >
              {actionLoading === 'extend' && (
                <Loader2 className="mr-2 h-3 w-3 animate-spin" />
              )}
              Extend
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button size="sm" variant="outline" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Session Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Active Sessions</span>
              </CardTitle>
              <CardDescription>
                Manage your active sessions across all devices
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={loadAllSessions}
                disabled={loading}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </Button>
              {allSessions.length > 1 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDestroyAllOther}
                  disabled={actionLoading === 'destroy-all'}
                >
                  {actionLoading === 'destroy-all' && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Sign Out Others
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {loading && allSessions.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="space-y-3">
              {allSessions.map((session) => (
                <div
                  key={session.id}
                  className={`p-4 border rounded-lg ${
                    session.isCurrent ? 'border-primary bg-primary/5' : 'border-border'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className="mt-1">
                        {getDeviceIcon(session.deviceInfo)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className="font-medium">
                            {session.deviceInfo?.browser || 'Unknown Browser'} on{' '}
                            {session.deviceInfo?.os || 'Unknown OS'}
                          </p>
                          {session.isCurrent && (
                            <Badge variant="default" className="text-xs">
                              Current
                            </Badge>
                          )}
                        </div>
                        
                        <div className="mt-1 space-y-1 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span>{formatLocation(session.location)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>
                              Active for {SessionService.formatSessionDuration(
                                session.createdAt,
                                session.lastAccessedAt
                              )}
                            </span>
                          </div>
                          {session.ipAddress && (
                            <div className="text-xs">
                              IP: {SessionService.formatIpAddress(session.ipAddress)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {session.isCurrent ? (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleExtendSession}
                          disabled={actionLoading === 'extend'}
                        >
                          {actionLoading === 'extend' && (
                            <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                          )}
                          Extend
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDestroySession(session.id)}
                          disabled={actionLoading === `destroy-${session.id}`}
                        >
                          {actionLoading === `destroy-${session.id}` ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            <Trash2 className="h-3 w-3" />
                          )}
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Session expiration info */}
                  {session.isCurrent && timeUntilExpiration && (
                    <div className="mt-3 pt-3 border-t">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">
                          Session expires in:
                        </span>
                        <span className={timeUntilExpiration.totalMinutes < 30 ? 'text-red-600' : 'text-muted-foreground'}>
                          {timeUntilExpiration.hours > 0 && `${timeUntilExpiration.hours}h `}
                          {timeUntilExpiration.minutes}m
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {allSessions.length === 0 && !loading && (
            <div className="text-center py-8 text-muted-foreground">
              No active sessions found
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Security Tips</CardTitle>
        </CardHeader>
        <CardContent className="text-sm text-muted-foreground space-y-2">
          <p>• Always sign out from shared or public devices</p>
          <p>• Regularly review your active sessions</p>
          <p>• Sign out other sessions if you notice suspicious activity</p>
          <p>• Your session will automatically extend when you're active</p>
        </CardContent>
      </Card>

      {/* Close button */}
      {onClose && (
        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      )}
    </div>
  )
}
