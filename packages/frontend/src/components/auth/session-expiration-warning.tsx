import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useSession } from '@/hooks/use-session'
import { Clock, AlertTriangle, Loader2, X } from 'lucide-react'

interface SessionExpirationWarningProps {
  warningMinutes?: number
  autoShow?: boolean
  onExtend?: () => void
  onDismiss?: () => void
}

export function SessionExpirationWarning({
  warningMinutes = 5,
  autoShow = true,
  onExtend,
  onDismiss
}: SessionExpirationWarningProps) {
  const {
    currentSession,
    isExpiringSoon,
    timeUntilExpiration,
    extendSession,
    loading
  } = useSession({ warningMinutes })

  const [isVisible, setIsVisible] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)
  const [extending, setExtending] = useState(false)

  // Show warning when session is expiring soon
  useEffect(() => {
    if (autoShow && isExpiringSoon && !isDismissed && currentSession) {
      setIsVisible(true)
    } else {
      setIsVisible(false)
    }
  }, [autoShow, isExpiringSoon, isDismissed, currentSession])

  const handleExtend = async () => {
    setExtending(true)
    try {
      await extendSession()
      setIsVisible(false)
      setIsDismissed(false) // Reset dismissal after successful extension
      onExtend?.()
    } catch (error) {
      console.error('Failed to extend session:', error)
    } finally {
      setExtending(false)
    }
  }

  const handleDismiss = () => {
    setIsVisible(false)
    setIsDismissed(true)
    onDismiss?.()
  }

  if (!isVisible || !timeUntilExpiration || timeUntilExpiration.isExpired) {
    return null
  }

  const isUrgent = timeUntilExpiration.totalMinutes <= 2

  return (
    <div className="fixed top-4 right-4 z-50 w-96">
      <Alert variant={isUrgent ? 'destructive' : 'default'} className="shadow-lg">
        <AlertTriangle className="h-4 w-4" />
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <AlertDescription>
              <div className="font-medium mb-1">
                Session Expiring Soon
              </div>
              <div className="text-sm">
                Your session will expire in{' '}
                <span className="font-medium">
                  {timeUntilExpiration.hours > 0 && `${timeUntilExpiration.hours}h `}
                  {timeUntilExpiration.minutes}m
                </span>
              </div>
            </AlertDescription>
            <div className="flex space-x-2 mt-3">
              <Button
                size="sm"
                onClick={handleExtend}
                disabled={extending || loading}
                className="h-8"
              >
                {extending && (
                  <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                )}
                Extend Session
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleDismiss}
                className="h-8"
              >
                Dismiss
              </Button>
            </div>
          </div>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleDismiss}
            className="h-6 w-6 p-0 ml-2"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </Alert>
    </div>
  )
}

// Countdown component for more detailed display
interface SessionCountdownProps {
  expiresAt: string
  onExpired?: () => void
  className?: string
}

export function SessionCountdown({ expiresAt, onExpired, className }: SessionCountdownProps) {
  const [timeLeft, setTimeLeft] = useState<{
    hours: number
    minutes: number
    seconds: number
    isExpired: boolean
  }>({ hours: 0, minutes: 0, seconds: 0, isExpired: false })

  useEffect(() => {
    const updateCountdown = () => {
      const expirationTime = new Date(expiresAt).getTime()
      const currentTime = Date.now()
      const timeDiff = expirationTime - currentTime

      if (timeDiff <= 0) {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0, isExpired: true })
        onExpired?.()
        return
      }

      const hours = Math.floor(timeDiff / (1000 * 60 * 60))
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000)

      setTimeLeft({ hours, minutes, seconds, isExpired: false })
    }

    // Update immediately
    updateCountdown()

    // Update every second
    const interval = setInterval(updateCountdown, 1000)

    return () => clearInterval(interval)
  }, [expiresAt, onExpired])

  if (timeLeft.isExpired) {
    return (
      <span className={`text-red-600 font-medium ${className}`}>
        Session Expired
      </span>
    )
  }

  const isUrgent = timeLeft.hours === 0 && timeLeft.minutes < 5

  return (
    <span className={`${isUrgent ? 'text-red-600' : 'text-muted-foreground'} font-mono ${className}`}>
      {timeLeft.hours > 0 && `${timeLeft.hours.toString().padStart(2, '0')}:`}
      {timeLeft.minutes.toString().padStart(2, '0')}:
      {timeLeft.seconds.toString().padStart(2, '0')}
    </span>
  )
}

// Session status indicator
interface SessionStatusProps {
  className?: string
}

export function SessionStatus({ className }: SessionStatusProps) {
  const { currentSession, isExpiringSoon, timeUntilExpiration } = useSession()

  if (!currentSession || !timeUntilExpiration) {
    return null
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Clock className="h-4 w-4 text-muted-foreground" />
      <SessionCountdown expiresAt={currentSession.expiresAt} />
      {isExpiringSoon && (
        <AlertTriangle className="h-4 w-4 text-orange-500" />
      )}
    </div>
  )
}
