import { Alert, AlertDescription } from '@/components/ui/alert'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { zodResolver } from '@hookform/resolvers/zod'
import { Eye, EyeOff, Loader2 } from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional()
})

type LoginFormData = z.infer<typeof loginSchema>

interface LoginFormProps {
  onSubmit: (data: LoginFormData) => Promise<void>
  onForgotPassword: () => void
  onSignUp: () => void
  onSsoLogin?: (providerId: string) => Promise<void>
  tenantId?: string
  isLoading?: boolean
  error?: string
  showSso?: boolean
}

export function LoginForm({
  onSubmit,
  onForgotPassword,
  onSignUp,
  onSsoLogin,
  tenantId,
  isLoading = false,
  error,
  showSso = true
}: LoginFormProps) {
  const [showPassword, setShowPassword] = useState(false)

  // SSO integration
  const { providers, initiateLogin, isLoading: isSsoLoading } = useSso({
    tenantId,
    autoLoad: showSso && !!tenantId
  })

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema)
  })

  const handleFormSubmit = async (data: LoginFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      // Error handling is done by parent component
    }
  }

  const handleSsoLogin = async (providerId: string) => {
    if (!tenantId) {
      console.error('Tenant ID is required for SSO login')
      return
    }

    try {
      if (onSsoLogin) {
        await onSsoLogin(providerId)
      } else {
        await initiateLogin(providerId, tenantId)
      }
    } catch (error) {
      console.error('SSO login failed:', error)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl text-center">Sign in</CardTitle>
        <CardDescription className="text-center">
          Enter your email and password to access your account
        </CardDescription>
      </CardHeader>
      
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              {...register('email')}
              disabled={isLoading || isSubmitting}
            />
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                {...register('password')}
                disabled={isLoading || isSubmitting}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading || isSubmitting}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.password && (
              <p className="text-sm text-destructive">{errors.password.message}</p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <input
              id="rememberMe"
              type="checkbox"
              {...register('rememberMe')}
              disabled={isLoading || isSubmitting}
              className="h-4 w-4 rounded border-gray-300"
            />
            <Label htmlFor="rememberMe" className="text-sm font-normal">
              Remember me
            </Label>
          </div>
        </CardContent>

        <CardFooter className="flex flex-col space-y-4">
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading || isSubmitting}
          >
            {(isLoading || isSubmitting) && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Sign in
          </Button>

          {/* SSO Buttons */}
          {showSso && providers.length > 0 && (
            <SsoButtons
              providers={providers}
              onSignIn={handleSsoLogin}
              isLoading={isLoading}
              disabled={isSubmitting}
              loadingProvider={providers.find(p => isSsoLoading(p.id))?.id}
            />
          )}

          <div className="flex flex-col space-y-2 text-center text-sm">
            <Button
              type="button"
              variant="link"
              onClick={onForgotPassword}
              disabled={isLoading || isSubmitting}
              className="p-0 h-auto"
            >
              Forgot your password?
            </Button>

            <div className="text-muted-foreground">
              Don't have an account?{' '}
              <Button
                type="button"
                variant="link"
                onClick={onSignUp}
                disabled={isLoading || isSubmitting}
                className="p-0 h-auto"
              >
                Sign up
              </Button>
            </div>
          </div>
        </CardFooter>
      </form>
    </Card>
  )
}
