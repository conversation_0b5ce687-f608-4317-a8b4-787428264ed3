import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Loader2, Shield, Smartphone, Key } from 'lucide-react'

const mfaSchema = z.object({
  code: z.string().min(1, 'Code is required')
})

type MfaFormData = z.infer<typeof mfaSchema>

interface MfaVerificationProps {
  onVerify: (code: string) => Promise<void>
  onBack: () => void
  isLoading?: boolean
  error?: string
  email?: string
}

export function MfaVerification({ 
  onVerify, 
  onBack, 
  isLoading = false, 
  error,
  email 
}: MfaVerificationProps) {
  const [codeType, setCodeType] = useState<'totp' | 'backup'>('totp')

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset
  } = useForm<MfaFormData>({
    resolver: zodResolver(mfaSchema)
  })

  const handleVerify = async (data: MfaFormData) => {
    try {
      await onVerify(data.code)
    } catch (error) {
      // Error handling is done by parent component
    }
  }

  const handleTabChange = (value: string) => {
    setCodeType(value as 'totp' | 'backup')
    reset() // Clear form when switching tabs
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <div className="flex items-center space-x-2">
          <Shield className="h-5 w-5 text-primary" />
          <CardTitle className="text-xl">Two-Factor Authentication</CardTitle>
        </div>
        <CardDescription>
          {email && `Signing in as ${email}`}
        </CardDescription>
      </CardHeader>
      
      <form onSubmit={handleSubmit(handleVerify)}>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Tabs value={codeType} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="totp" className="flex items-center space-x-2">
                <Smartphone className="h-4 w-4" />
                <span>Authenticator</span>
              </TabsTrigger>
              <TabsTrigger value="backup" className="flex items-center space-x-2">
                <Key className="h-4 w-4" />
                <span>Backup Code</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="totp" className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Enter the 6-digit code from your authenticator app
              </div>
              <div className="space-y-2">
                <Label htmlFor="totp-code">Authenticator Code</Label>
                <Input
                  id="totp-code"
                  placeholder="000000"
                  maxLength={6}
                  {...register('code')}
                  disabled={isLoading || isSubmitting}
                  className="text-center text-lg tracking-widest"
                  autoComplete="one-time-code"
                />
                {errors.code && (
                  <p className="text-sm text-destructive">{errors.code.message}</p>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="backup" className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Enter one of your backup codes. Each code can only be used once.
              </div>
              <div className="space-y-2">
                <Label htmlFor="backup-code">Backup Code</Label>
                <Input
                  id="backup-code"
                  placeholder="Enter backup code"
                  maxLength={8}
                  {...register('code')}
                  disabled={isLoading || isSubmitting}
                  className="text-center text-lg tracking-widest font-mono"
                  autoComplete="one-time-code"
                />
                {errors.code && (
                  <p className="text-sm text-destructive">{errors.code.message}</p>
                )}
              </div>
              <Alert>
                <Key className="h-4 w-4" />
                <AlertDescription>
                  Backup codes are 8-character alphanumeric codes that you saved when setting up MFA.
                </AlertDescription>
              </Alert>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex space-x-2">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onBack} 
            className="flex-1"
            disabled={isLoading || isSubmitting}
          >
            Back
          </Button>
          <Button 
            type="submit" 
            className="flex-1"
            disabled={isLoading || isSubmitting}
          >
            {(isLoading || isSubmitting) && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Verify
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
