import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Loader2, Copy, Check, Download, Shield } from 'lucide-react'

const verificationSchema = z.object({
  code: z.string()
    .length(6, 'Verification code must be 6 digits')
    .regex(/^\d{6}$/, 'Verification code must contain only digits')
})

type VerificationFormData = z.infer<typeof verificationSchema>

interface MfaSetupData {
  qrCodeUrl: string
  manualEntryKey: string
  backupCodes: string[]
}

interface MfaSetupProps {
  setupData: MfaSetupData
  onVerify: (code: string) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
  error?: string
}

export function MfaSetup({ 
  setupData, 
  onVerify, 
  onCancel, 
  isLoading = false, 
  error 
}: MfaSetupProps) {
  const [step, setStep] = useState<'setup' | 'verify' | 'backup'>('setup')
  const [copiedKey, setCopiedKey] = useState(false)
  const [copiedCodes, setCopiedCodes] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<VerificationFormData>({
    resolver: zodResolver(verificationSchema)
  })

  const handleVerify = async (data: VerificationFormData) => {
    try {
      await onVerify(data.code)
      setStep('backup')
    } catch (error) {
      // Error handling is done by parent component
    }
  }

  const copyToClipboard = async (text: string, type: 'key' | 'codes') => {
    try {
      await navigator.clipboard.writeText(text)
      if (type === 'key') {
        setCopiedKey(true)
        setTimeout(() => setCopiedKey(false), 2000)
      } else {
        setCopiedCodes(true)
        setTimeout(() => setCopiedCodes(false), 2000)
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const downloadBackupCodes = () => {
    const content = `M&A Platform - Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\n\nBackup Codes:\n${setupData.backupCodes.join('\n')}\n\nImportant:\n- Keep these codes safe and secure\n- Each code can only be used once\n- Use these codes if you lose access to your authenticator app\n- Generate new codes if you suspect these have been compromised`
    
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'ma-platform-backup-codes.txt'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (step === 'setup') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="space-y-1">
          <div className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-primary" />
            <CardTitle className="text-xl">Set up Two-Factor Authentication</CardTitle>
          </div>
          <CardDescription>
            Add an extra layer of security to your account
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="qr" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="qr">QR Code</TabsTrigger>
              <TabsTrigger value="manual">Manual Entry</TabsTrigger>
            </TabsList>
            
            <TabsContent value="qr" className="space-y-4">
              <div className="text-sm text-muted-foreground">
                1. Install an authenticator app (Google Authenticator, Authy, etc.)
              </div>
              <div className="text-sm text-muted-foreground">
                2. Scan this QR code with your authenticator app:
              </div>
              <div className="flex justify-center p-4 bg-white rounded-lg border">
                <img 
                  src={setupData.qrCodeUrl} 
                  alt="QR Code for MFA setup"
                  className="w-48 h-48"
                />
              </div>
            </TabsContent>
            
            <TabsContent value="manual" className="space-y-4">
              <div className="text-sm text-muted-foreground">
                1. Install an authenticator app (Google Authenticator, Authy, etc.)
              </div>
              <div className="text-sm text-muted-foreground">
                2. Add a new account manually using this key:
              </div>
              <div className="space-y-2">
                <Label>Secret Key</Label>
                <div className="flex space-x-2">
                  <Input
                    value={setupData.manualEntryKey}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(setupData.manualEntryKey, 'key')}
                  >
                    {copiedKey ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex space-x-2">
          <Button variant="outline" onClick={onCancel} className="flex-1">
            Cancel
          </Button>
          <Button 
            onClick={() => setStep('verify')} 
            className="flex-1"
            disabled={isLoading}
          >
            Continue
          </Button>
        </CardFooter>
      </Card>
    )
  }

  if (step === 'verify') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="space-y-1">
          <CardTitle className="text-xl">Verify Setup</CardTitle>
          <CardDescription>
            Enter the 6-digit code from your authenticator app
          </CardDescription>
        </CardHeader>
        
        <form onSubmit={handleSubmit(handleVerify)}>
          <CardContent className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="code">Verification Code</Label>
              <Input
                id="code"
                placeholder="000000"
                maxLength={6}
                {...register('code')}
                disabled={isLoading || isSubmitting}
                className="text-center text-lg tracking-widest"
              />
              {errors.code && (
                <p className="text-sm text-destructive">{errors.code.message}</p>
              )}
            </div>
          </CardContent>

          <CardFooter className="flex space-x-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setStep('setup')} 
              className="flex-1"
              disabled={isLoading || isSubmitting}
            >
              Back
            </Button>
            <Button 
              type="submit" 
              className="flex-1"
              disabled={isLoading || isSubmitting}
            >
              {(isLoading || isSubmitting) && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Verify
            </Button>
          </CardFooter>
        </form>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-xl">Save Backup Codes</CardTitle>
        <CardDescription>
          Store these codes safely. You can use them to access your account if you lose your authenticator device.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Each backup code can only be used once. Generate new codes if you suspect these have been compromised.
          </AlertDescription>
        </Alert>

        <div className="space-y-2">
          <Label>Backup Codes</Label>
          <div className="grid grid-cols-2 gap-2 p-3 bg-muted rounded-md font-mono text-sm">
            {setupData.backupCodes.map((code, index) => (
              <div key={index} className="text-center">
                {code}
              </div>
            ))}
          </div>
        </div>

        <div className="flex space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => copyToClipboard(setupData.backupCodes.join('\n'), 'codes')}
            className="flex-1"
          >
            {copiedCodes ? (
              <Check className="mr-2 h-4 w-4" />
            ) : (
              <Copy className="mr-2 h-4 w-4" />
            )}
            Copy Codes
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={downloadBackupCodes}
            className="flex-1"
          >
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
        </div>
      </CardContent>

      <CardFooter>
        <Button onClick={onCancel} className="w-full">
          Complete Setup
        </Button>
      </CardFooter>
    </Card>
  )
}
