import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { MfaSetup } from './mfa-setup'
import { MfaVerification } from './mfa-verification'
import { useMfa } from '@/hooks/use-mfa'
import { Shield, ShieldCheck, ShieldX, Key, Download, RefreshCw, Loader2 } from 'lucide-react'

interface MfaSettingsProps {
  onClose?: () => void
}

export function MfaSettings({ onClose }: MfaSettingsProps) {
  const {
    status,
    loading,
    error,
    setupData,
    setupLoading,
    setupError,
    startSetup,
    verifySetup,
    disableMfa,
    generateBackupCodes,
    clearErrors,
    clearSetup
  } = useMfa()

  const [showSetup, setShowSetup] = useState(false)
  const [showDisable, setShowDisable] = useState(false)
  const [showBackupCodes, setShowBackupCodes] = useState(false)
  const [newBackupCodes, setNewBackupCodes] = useState<string[]>([])

  const handleStartSetup = async () => {
    clearErrors()
    await startSetup()
    setShowSetup(true)
  }

  const handleVerifySetup = async (code: string) => {
    const verified = await verifySetup(code)
    if (verified) {
      setShowSetup(false)
      clearSetup()
    }
  }

  const handleCancelSetup = () => {
    setShowSetup(false)
    clearSetup()
    clearErrors()
  }

  const handleDisableMfa = async (code: string) => {
    try {
      await disableMfa(code)
      setShowDisable(false)
    } catch (error) {
      // Error is handled by the hook
    }
  }

  const handleCancelDisable = () => {
    setShowDisable(false)
    clearErrors()
  }

  const handleGenerateBackupCodes = async (code: string) => {
    try {
      const codes = await generateBackupCodes(code)
      setNewBackupCodes(codes)
      setShowBackupCodes(false)
    } catch (error) {
      // Error is handled by the hook
    }
  }

  const handleCancelBackupCodes = () => {
    setShowBackupCodes(false)
    clearErrors()
  }

  if (loading && !status) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  // Show MFA setup flow
  if (showSetup && setupData) {
    return (
      <MfaSetup
        setupData={setupData}
        onVerify={handleVerifySetup}
        onCancel={handleCancelSetup}
        isLoading={setupLoading}
        error={setupError}
      />
    )
  }

  // Show MFA disable verification
  if (showDisable) {
    return (
      <MfaVerification
        onVerify={handleDisableMfa}
        onBack={handleCancelDisable}
        isLoading={loading}
        error={error}
      />
    )
  }

  // Show backup codes generation verification
  if (showBackupCodes) {
    return (
      <MfaVerification
        onVerify={handleGenerateBackupCodes}
        onBack={handleCancelBackupCodes}
        isLoading={loading}
        error={error}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* MFA Status Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {status?.enabled ? (
                <ShieldCheck className="h-6 w-6 text-green-500" />
              ) : (
                <ShieldX className="h-6 w-6 text-gray-400" />
              )}
              <div>
                <CardTitle>Two-Factor Authentication</CardTitle>
                <CardDescription>
                  Add an extra layer of security to your account
                </CardDescription>
              </div>
            </div>
            <Badge variant={status?.enabled ? 'default' : 'secondary'}>
              {status?.enabled ? 'Enabled' : 'Disabled'}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {status?.enabled ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="font-medium">MFA is active</p>
                    <p className="text-sm text-muted-foreground">
                      Your account is protected with two-factor authentication
                    </p>
                    {status.setupDate && (
                      <p className="text-xs text-muted-foreground">
                        Enabled on {new Date(status.setupDate).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setShowDisable(true)}
                  disabled={loading}
                >
                  Disable
                </Button>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Key className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="font-medium">Backup codes</p>
                    <p className="text-sm text-muted-foreground">
                      {status.backupCodesRemaining} backup codes remaining
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setShowBackupCodes(true)}
                  disabled={loading}
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Generate New
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Secure your account</h3>
              <p className="text-muted-foreground mb-6">
                Two-factor authentication adds an extra layer of security to your account.
                Even if someone knows your password, they won't be able to access your account.
              </p>
              <Button
                onClick={handleStartSetup}
                disabled={setupLoading}
                className="w-full sm:w-auto"
              >
                {setupLoading && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Enable Two-Factor Authentication
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* New Backup Codes Display */}
      {newBackupCodes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Key className="h-5 w-5" />
              <span>New Backup Codes</span>
            </CardTitle>
            <CardDescription>
              Save these codes in a safe place. Each code can only be used once.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-2 p-4 bg-muted rounded-lg font-mono text-sm">
              {newBackupCodes.map((code, index) => (
                <div key={index} className="text-center">
                  {code}
                </div>
              ))}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => navigator.clipboard.writeText(newBackupCodes.join('\n'))}
                className="flex-1"
              >
                Copy Codes
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  const content = `M&A Platform - Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\n\nBackup Codes:\n${newBackupCodes.join('\n')}\n\nImportant:\n- Keep these codes safe and secure\n- Each code can only be used once\n- Use these codes if you lose access to your authenticator app\n- Generate new codes if you suspect these have been compromised`
                  
                  const blob = new Blob([content], { type: 'text/plain' })
                  const url = URL.createObjectURL(blob)
                  const a = document.createElement('a')
                  a.href = url
                  a.download = 'ma-platform-backup-codes.txt'
                  document.body.appendChild(a)
                  a.click()
                  document.body.removeChild(a)
                  URL.revokeObjectURL(url)
                }}
                className="flex-1"
              >
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            </div>
            <Button
              variant="ghost"
              onClick={() => setNewBackupCodes([])}
              className="w-full"
            >
              Done
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Close button */}
      {onClose && (
        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      )}
    </div>
  )
}
