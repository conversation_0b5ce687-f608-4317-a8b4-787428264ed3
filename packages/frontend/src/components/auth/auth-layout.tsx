import { ReactNode } from 'react'

interface AuthLayoutProps {
  children: ReactNode
  title?: string
  subtitle?: string
  showLogo?: boolean
}

export function AuthLayout({ 
  children, 
  title = "M&A Enterprise Platform",
  subtitle = "Streamline your mergers and acquisitions",
  showLogo = true 
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen flex">
      {/* Left side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-purple-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          {showLogo && (
            <div className="mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-xl">M&A</span>
                </div>
                <span className="text-2xl font-bold">{title}</span>
              </div>
            </div>
          )}
          
          <div className="max-w-md">
            <h1 className="text-4xl font-bold mb-4">
              {title}
            </h1>
            <p className="text-xl text-blue-100 mb-8">
              {subtitle}
            </p>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-white rounded-full" />
                <span className="text-blue-100">Secure document management</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-white rounded-full" />
                <span className="text-blue-100">Real-time collaboration</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-white rounded-full" />
                <span className="text-blue-100">Advanced analytics</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-white rounded-full" />
                <span className="text-blue-100">Enterprise-grade security</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32" />
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/10 rounded-full translate-y-24 -translate-x-24" />
      </div>

      {/* Right side - Auth form */}
      <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md mx-auto">
          {/* Mobile logo */}
          <div className="lg:hidden mb-8 text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">M&A</span>
              </div>
              <span className="text-xl font-bold text-gray-900">{title}</span>
            </div>
            <p className="text-gray-600">{subtitle}</p>
          </div>

          {children}
        </div>
        
        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <div className="flex justify-center space-x-6">
            <a href="/privacy" className="hover:text-gray-700">
              Privacy Policy
            </a>
            <a href="/terms" className="hover:text-gray-700">
              Terms of Service
            </a>
            <a href="/support" className="hover:text-gray-700">
              Support
            </a>
          </div>
          <div className="mt-2">
            © 2024 M&A Enterprise Platform. All rights reserved.
          </div>
        </div>
      </div>
    </div>
  )
}
