import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { useSecurity } from '@/contexts/security.context'
import { 
  Shield, 
  ShieldCheck, 
  ShieldX, 
  AlertTriangle, 
  Eye, 
  EyeOff,
  Lock,
  Unlock,
  Monitor,
  Fingerprint,
  RefreshCw
} from 'lucide-react'

interface SecurityDashboardProps {
  onClose?: () => void
}

export function SecurityDashboard({ onClose }: SecurityDashboardProps) {
  const {
    isSecureContext,
    hasSecurityHeaders,
    deviceFingerprint,
    isIncognitoMode,
    isDevToolsOpen,
    warnings,
    checkSecurityStatus
  } = useSecurity()

  const [showFingerprint, setShowFingerprint] = useState(false)

  const getSecurityScore = (): { score: number; level: string; color: string } => {
    let score = 0
    
    if (isSecureContext) score += 25
    if (hasSecurityHeaders) score += 25
    if (!isDevToolsOpen) score += 25
    if (!isIncognitoMode) score += 25
    
    let level = 'Poor'
    let color = 'text-red-600'
    
    if (score >= 75) {
      level = 'Excellent'
      color = 'text-green-600'
    } else if (score >= 50) {
      level = 'Good'
      color = 'text-yellow-600'
    } else if (score >= 25) {
      level = 'Fair'
      color = 'text-orange-600'
    }
    
    return { score, level, color }
  }

  const securityScore = getSecurityScore()

  return (
    <div className="space-y-6">
      {/* Security Score Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="h-6 w-6 text-primary" />
              <div>
                <CardTitle>Security Status</CardTitle>
                <CardDescription>
                  Overall security assessment of your session
                </CardDescription>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={checkSecurityStatus}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Security Score</span>
            <div className="flex items-center space-x-2">
              <span className={`text-2xl font-bold ${securityScore.color}`}>
                {securityScore.score}%
              </span>
              <Badge variant={securityScore.score >= 75 ? 'default' : 'secondary'}>
                {securityScore.level}
              </Badge>
            </div>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                securityScore.score >= 75 ? 'bg-green-500' :
                securityScore.score >= 50 ? 'bg-yellow-500' :
                securityScore.score >= 25 ? 'bg-orange-500' : 'bg-red-500'
              }`}
              style={{ width: `${securityScore.score}%` }}
            />
          </div>

          {warnings.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="font-medium">Security Issues Detected:</p>
                  <ul className="list-disc list-inside text-sm">
                    {warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Security Checks */}
      <Card>
        <CardHeader>
          <CardTitle>Security Checks</CardTitle>
          <CardDescription>
            Detailed security status of your current session
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="grid gap-4">
            {/* Secure Context */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                {isSecureContext ? (
                  <Lock className="h-5 w-5 text-green-500" />
                ) : (
                  <Unlock className="h-5 w-5 text-red-500" />
                )}
                <div>
                  <p className="font-medium">Secure Context</p>
                  <p className="text-sm text-muted-foreground">
                    {isSecureContext ? 'HTTPS connection established' : 'Insecure HTTP connection'}
                  </p>
                </div>
              </div>
              <Badge variant={isSecureContext ? 'default' : 'destructive'}>
                {isSecureContext ? 'Secure' : 'Insecure'}
              </Badge>
            </div>

            {/* Security Headers */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                {hasSecurityHeaders ? (
                  <ShieldCheck className="h-5 w-5 text-green-500" />
                ) : (
                  <ShieldX className="h-5 w-5 text-red-500" />
                )}
                <div>
                  <p className="font-medium">Security Headers</p>
                  <p className="text-sm text-muted-foreground">
                    {hasSecurityHeaders ? 'All security headers present' : 'Missing security headers'}
                  </p>
                </div>
              </div>
              <Badge variant={hasSecurityHeaders ? 'default' : 'destructive'}>
                {hasSecurityHeaders ? 'Protected' : 'Vulnerable'}
              </Badge>
            </div>

            {/* Developer Tools */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                {isDevToolsOpen ? (
                  <Eye className="h-5 w-5 text-orange-500" />
                ) : (
                  <EyeOff className="h-5 w-5 text-green-500" />
                )}
                <div>
                  <p className="font-medium">Developer Tools</p>
                  <p className="text-sm text-muted-foreground">
                    {isDevToolsOpen ? 'Developer tools are open' : 'Developer tools are closed'}
                  </p>
                </div>
              </div>
              <Badge variant={isDevToolsOpen ? 'secondary' : 'default'}>
                {isDevToolsOpen ? 'Open' : 'Closed'}
              </Badge>
            </div>

            {/* Incognito Mode */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Monitor className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="font-medium">Browsing Mode</p>
                  <p className="text-sm text-muted-foreground">
                    {isIncognitoMode ? 'Private/Incognito mode detected' : 'Normal browsing mode'}
                  </p>
                </div>
              </div>
              <Badge variant="outline">
                {isIncognitoMode ? 'Private' : 'Normal'}
              </Badge>
            </div>

            {/* Device Fingerprint */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Fingerprint className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="font-medium">Device Fingerprint</p>
                  <p className="text-sm text-muted-foreground font-mono">
                    {showFingerprint ? deviceFingerprint : '••••••••'}
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFingerprint(!showFingerprint)}
              >
                {showFingerprint ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Security Recommendations</CardTitle>
          <CardDescription>
            Steps to improve your security posture
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-3">
          {!isSecureContext && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Use HTTPS:</strong> Always access the application through a secure HTTPS connection to protect your data in transit.
              </AlertDescription>
            </Alert>
          )}

          {isDevToolsOpen && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Close Developer Tools:</strong> Having developer tools open can expose sensitive information and reduce security.
              </AlertDescription>
            </Alert>
          )}

          {warnings.length === 0 && securityScore.score === 100 && (
            <Alert>
              <ShieldCheck className="h-4 w-4" />
              <AlertDescription>
                <strong>Excellent Security:</strong> Your session meets all security requirements. Keep up the good practices!
              </AlertDescription>
            </Alert>
          )}

          <div className="text-sm text-muted-foreground space-y-2">
            <p><strong>General Security Tips:</strong></p>
            <ul className="list-disc list-inside space-y-1">
              <li>Always log out when using shared computers</li>
              <li>Keep your browser updated to the latest version</li>
              <li>Use strong, unique passwords for your accounts</li>
              <li>Enable two-factor authentication when available</li>
              <li>Be cautious of phishing attempts and suspicious links</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Close button */}
      {onClose && (
        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      )}
    </div>
  )
}
