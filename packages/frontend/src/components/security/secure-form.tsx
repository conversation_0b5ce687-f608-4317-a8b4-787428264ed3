import React, { useState, useEffect, FormEvent, ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useSecurity } from '@/contexts/security.context'
import { SanitizationUtils } from '@/utils/security'
import { <PERSON><PERSON><PERSON>riangle, Shield } from 'lucide-react'

interface SecureFormProps {
  children: ReactNode
  onSubmit: (data: FormData, sanitizedData: Record<string, any>) => Promise<void> | void
  rateLimitKey?: string
  maxAttempts?: number
  windowMs?: number
  enableCSRFProtection?: boolean
  enableSanitization?: boolean
  className?: string
}

export function SecureForm({
  children,
  onSubmit,
  rateLimitKey = 'form_submit',
  maxAttempts = 5,
  windowMs = 60000, // 1 minute
  enableCSRFProtection = true,
  enableSanitization = true,
  className = ''
}: SecureFormProps) {
  const { isRateLimited, getRemainingAttempts, reportSecurityEvent } = useSecurity()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [csrfToken, setCsrfToken] = useState<string>('')

  // Generate CSRF token
  useEffect(() => {
    if (enableCSRFProtection) {
      const token = generateCSRFToken()
      setCsrfToken(token)
    }
  }, [enableCSRFProtection])

  const generateCSRFToken = (): string => {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  const sanitizeFormData = (formData: FormData): Record<string, any> => {
    const sanitized: Record<string, any> = {}
    
    for (const [key, value] of formData.entries()) {
      if (typeof value === 'string') {
        sanitized[key] = enableSanitization ? SanitizationUtils.sanitizeInput(value) : value
      } else {
        sanitized[key] = value
      }
    }
    
    return sanitized
  }

  const validateFormData = (sanitizedData: Record<string, any>): string[] => {
    const errors: string[] = []
    
    // Check for common injection patterns
    for (const [key, value] of Object.entries(sanitizedData)) {
      if (typeof value === 'string') {
        // Check for script tags
        if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(value)) {
          errors.push(`Invalid content detected in ${key}`)
        }
        
        // Check for SQL injection patterns
        if (/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)/gi.test(value)) {
          errors.push(`Suspicious content detected in ${key}`)
        }
        
        // Check for excessive length
        if (value.length > 10000) {
          errors.push(`Content too long in ${key}`)
        }
      }
    }
    
    return errors
  }

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setError(null)

    // Check rate limiting
    if (isRateLimited(rateLimitKey, maxAttempts, windowMs)) {
      const remaining = getRemainingAttempts(rateLimitKey, maxAttempts)
      setError(`Too many attempts. ${remaining} attempts remaining.`)
      
      reportSecurityEvent({
        type: 'rate_limit_exceeded',
        details: { 
          form: rateLimitKey,
          maxAttempts,
          windowMs
        }
      })
      
      return
    }

    setIsSubmitting(true)

    try {
      const formData = new FormData(event.currentTarget)
      
      // Add CSRF token if enabled
      if (enableCSRFProtection && csrfToken) {
        formData.append('_csrf', csrfToken)
      }

      // Sanitize form data
      const sanitizedData = sanitizeFormData(formData)

      // Validate form data
      const validationErrors = validateFormData(sanitizedData)
      if (validationErrors.length > 0) {
        setError(validationErrors.join(', '))
        
        reportSecurityEvent({
          type: 'suspicious_activity',
          details: { 
            event: 'form_validation_failed',
            errors: validationErrors,
            form: rateLimitKey
          }
        })
        
        return
      }

      // Submit form
      await onSubmit(formData, sanitizedData)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred'
      setError(errorMessage)
      
      reportSecurityEvent({
        type: 'security_warning',
        details: { 
          event: 'form_submit_error',
          error: errorMessage,
          form: rateLimitKey
        }
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const remainingAttempts = getRemainingAttempts(rateLimitKey, maxAttempts)
  const showRateLimitWarning = remainingAttempts <= 2 && remainingAttempts > 0

  return (
    <form onSubmit={handleSubmit} className={className}>
      {/* CSRF Token */}
      {enableCSRFProtection && csrfToken && (
        <input type="hidden" name="_csrf" value={csrfToken} />
      )}

      {/* Security Warnings */}
      {showRateLimitWarning && (
        <Alert className="mb-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Warning: {remainingAttempts} attempts remaining before rate limit.
          </AlertDescription>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Form Content */}
      {children}

      {/* Security Indicator */}
      <div className="flex items-center justify-between mt-4 text-xs text-muted-foreground">
        <div className="flex items-center space-x-1">
          <Shield className="h-3 w-3" />
          <span>Secure form</span>
        </div>
        {enableCSRFProtection && (
          <span>CSRF protected</span>
        )}
      </div>
    </form>
  )
}

// Secure input component with built-in validation
interface SecureInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  sanitize?: boolean
  validateEmail?: boolean
  validatePhone?: boolean
}

export function SecureInput({
  label,
  error,
  sanitize = true,
  validateEmail = false,
  validatePhone = false,
  onChange,
  ...props
}: SecureInputProps) {
  const [inputError, setInputError] = useState<string>('')

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    let value = event.target.value
    setInputError('')

    // Sanitize input
    if (sanitize) {
      value = SanitizationUtils.sanitizeInput(value)
      event.target.value = value
    }

    // Validate email
    if (validateEmail && value && !SanitizationUtils.isValidEmail(value)) {
      setInputError('Invalid email format')
    }

    // Validate phone
    if (validatePhone && value && !SanitizationUtils.isValidPhone(value)) {
      setInputError('Invalid phone format')
    }

    onChange?.(event)
  }

  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
        </label>
      )}
      <input
        {...props}
        onChange={handleChange}
        className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${
          (error || inputError) ? 'border-red-500' : ''
        }`}
      />
      {(error || inputError) && (
        <p className="text-sm text-red-600">{error || inputError}</p>
      )}
    </div>
  )
}

// Secure textarea component
interface SecureTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  sanitize?: boolean
  maxLength?: number
}

export function SecureTextarea({
  label,
  error,
  sanitize = true,
  maxLength = 1000,
  onChange,
  ...props
}: SecureTextareaProps) {
  const [inputError, setInputError] = useState<string>('')
  const [charCount, setCharCount] = useState(0)

  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    let value = event.target.value
    setInputError('')

    // Check length
    if (value.length > maxLength) {
      value = value.substring(0, maxLength)
      event.target.value = value
      setInputError(`Maximum ${maxLength} characters allowed`)
    }

    setCharCount(value.length)

    // Sanitize input
    if (sanitize) {
      value = SanitizationUtils.sanitizeInput(value)
      event.target.value = value
    }

    onChange?.(event)
  }

  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
        </label>
      )}
      <textarea
        {...props}
        onChange={handleChange}
        className={`flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${
          (error || inputError) ? 'border-red-500' : ''
        }`}
      />
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>{error || inputError}</span>
        <span>{charCount}/{maxLength}</span>
      </div>
    </div>
  )
}
