import React, { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Bar<PERSON>hart3, 
  TrendingUp,
  TrendingDown,
  Clock,
  Users,
  AlertTriangle,
  CheckCircle,
  Target,
  Calendar,
  Activity,
  Zap,
  Award,
  RefreshCw,
  Download,
  Filter,
  Eye,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react'

interface DDSummary {
  totalItems: number
  completedItems: number
  inProgressItems: number
  pendingItems: number
  blockedItems: number
  overallProgress: number
  estimatedCompletion: string
  actualHours: number
  estimatedHours: number
  efficiency: number
  qualityScore: number
}

interface DDCategoryBreakdown {
  categoryId: string
  categoryName: string
  categoryType: string
  totalItems: number
  completedItems: number
  progress: number
  estimatedHours: number
  actualHours: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

interface DDTeamPerformance {
  userId: string
  userName: string
  assignedItems: number
  completedItems: number
  overdueItems: number
  efficiency: number
  qualityScore: number
  contributionPercentage: number
}

interface DDBottleneck {
  itemId: string
  itemTitle: string
  categoryName: string
  assignedTo: string
  daysStuck: number
  impact: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  suggestedAction: string
}

interface DDProgressDashboardProps {
  checklistId: string
  dealId: string
  onRefresh?: () => void
}

export function DDProgressDashboard({
  checklistId,
  dealId,
  onRefresh
}: DDProgressDashboardProps) {
  const [summary, setSummary] = useState<DDSummary | null>(null)
  const [categoryBreakdown, setCategoryBreakdown] = useState<DDCategoryBreakdown[]>([])
  const [teamPerformance, setTeamPerformance] = useState<DDTeamPerformance[]>([])
  const [bottlenecks, setBottlenecks] = useState<DDBottleneck[]>([])
  const [loading, setLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<string>('')

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockSummary: DDSummary = {
      totalItems: 156,
      completedItems: 89,
      inProgressItems: 23,
      pendingItems: 38,
      blockedItems: 6,
      overallProgress: 57,
      estimatedCompletion: '2024-03-15T17:00:00Z',
      actualHours: 342,
      estimatedHours: 480,
      efficiency: 1.4,
      qualityScore: 4.3
    }

    const mockCategories: DDCategoryBreakdown[] = [
      {
        categoryId: 'cat-1',
        categoryName: 'Financial Analysis',
        categoryType: 'FINANCIAL',
        totalItems: 45,
        completedItems: 32,
        progress: 71,
        estimatedHours: 120,
        actualHours: 95,
        riskLevel: 'LOW'
      },
      {
        categoryId: 'cat-2',
        categoryName: 'Legal Review',
        categoryType: 'LEGAL',
        totalItems: 38,
        completedItems: 18,
        progress: 47,
        estimatedHours: 150,
        actualHours: 89,
        riskLevel: 'HIGH'
      },
      {
        categoryId: 'cat-3',
        categoryName: 'Technical Assessment',
        categoryType: 'TECHNICAL',
        totalItems: 42,
        completedItems: 25,
        progress: 60,
        estimatedHours: 140,
        actualHours: 112,
        riskLevel: 'MEDIUM'
      },
      {
        categoryId: 'cat-4',
        categoryName: 'Commercial Analysis',
        categoryType: 'COMMERCIAL',
        totalItems: 31,
        completedItems: 14,
        progress: 45,
        estimatedHours: 70,
        actualHours: 46,
        riskLevel: 'CRITICAL'
      }
    ]

    const mockTeam: DDTeamPerformance[] = [
      {
        userId: 'user-1',
        userName: 'John Doe',
        assignedItems: 28,
        completedItems: 22,
        overdueItems: 1,
        efficiency: 1.2,
        qualityScore: 4.5,
        contributionPercentage: 79
      },
      {
        userId: 'user-2',
        userName: 'Jane Smith',
        assignedItems: 35,
        completedItems: 24,
        overdueItems: 3,
        efficiency: 0.9,
        qualityScore: 4.1,
        contributionPercentage: 69
      },
      {
        userId: 'user-3',
        userName: 'Mike Johnson',
        assignedItems: 22,
        completedItems: 19,
        overdueItems: 0,
        efficiency: 1.5,
        qualityScore: 4.7,
        contributionPercentage: 86
      }
    ]

    const mockBottlenecks: DDBottleneck[] = [
      {
        itemId: 'item-1',
        itemTitle: 'Customer Contract Analysis',
        categoryName: 'Legal Review',
        assignedTo: 'Jane Smith',
        daysStuck: 8,
        impact: 'CRITICAL',
        suggestedAction: 'Escalate to senior legal counsel'
      },
      {
        itemId: 'item-2',
        itemTitle: 'Revenue Recognition Review',
        categoryName: 'Financial Analysis',
        assignedTo: 'John Doe',
        daysStuck: 5,
        impact: 'HIGH',
        suggestedAction: 'Request additional accounting expertise'
      }
    ]

    setSummary(mockSummary)
    setCategoryBreakdown(mockCategories)
    setTeamPerformance(mockTeam)
    setBottlenecks(mockBottlenecks)
    setLastUpdated(new Date().toLocaleString())
    setLoading(false)
  }, [checklistId, dealId])

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'LOW': return 'text-green-600 bg-green-50 border-green-200'
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'HIGH': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'CRITICAL': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'LOW': return 'text-green-600'
      case 'MEDIUM': return 'text-yellow-600'
      case 'HIGH': return 'text-orange-600'
      case 'CRITICAL': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getTrendIcon = (current: number, target: number) => {
    if (current > target * 1.1) return <ArrowUp className="h-4 w-4 text-green-600" />
    if (current < target * 0.9) return <ArrowDown className="h-4 w-4 text-red-600" />
    return <Minus className="h-4 w-4 text-gray-600" />
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const handleRefresh = () => {
    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      setLastUpdated(new Date().toLocaleString())
      setLoading(false)
      onRefresh?.()
    }, 1000)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!summary) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Data Available</h3>
          <p className="text-gray-600">Unable to load progress data</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Progress Dashboard
              </CardTitle>
              <CardDescription>
                Real-time insights into due diligence progress and team performance
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                Last updated: {lastUpdated}
              </span>
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Overall Progress</p>
                <p className="text-3xl font-bold">{summary.overallProgress}%</p>
                <p className="text-sm text-gray-500">
                  {summary.completedItems} of {summary.totalItems} items
                </p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Target className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <Progress value={summary.overallProgress} className="mt-3" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Estimated Completion</p>
                <p className="text-lg font-bold">{formatDate(summary.estimatedCompletion)}</p>
                <p className="text-sm text-gray-500">
                  {Math.ceil((new Date(summary.estimatedCompletion).getTime() - Date.now()) / (1000 * 60 * 60 * 24))} days remaining
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Efficiency</p>
                <p className="text-3xl font-bold">{summary.efficiency}x</p>
                <p className="text-sm text-gray-500">
                  {summary.actualHours}h / {summary.estimatedHours}h
                </p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Zap className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              {getTrendIcon(summary.efficiency, 1.0)}
              <span className="text-sm text-gray-500 ml-1">vs target</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Quality Score</p>
                <p className="text-3xl font-bold">{summary.qualityScore}</p>
                <p className="text-sm text-gray-500">out of 5.0</p>
              </div>
              <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Award className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              {getTrendIcon(summary.qualityScore, 4.0)}
              <span className="text-sm text-gray-500 ml-1">vs target</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Status Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{summary.completedItems}</div>
              <div className="text-sm text-gray-600">Completed</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className="bg-green-600 h-2 rounded-full" 
                  style={{ width: `${(summary.completedItems / summary.totalItems) * 100}%` }}
                ></div>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{summary.inProgressItems}</div>
              <div className="text-sm text-gray-600">In Progress</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${(summary.inProgressItems / summary.totalItems) * 100}%` }}
                ></div>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">{summary.pendingItems}</div>
              <div className="text-sm text-gray-600">Pending</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className="bg-gray-600 h-2 rounded-full" 
                  style={{ width: `${(summary.pendingItems / summary.totalItems) * 100}%` }}
                ></div>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{summary.blockedItems}</div>
              <div className="text-sm text-gray-600">Blocked</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className="bg-red-600 h-2 rounded-full" 
                  style={{ width: `${(summary.blockedItems / summary.totalItems) * 100}%` }}
                ></div>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-800">{summary.totalItems}</div>
              <div className="text-sm text-gray-600">Total Items</div>
              <div className="w-full bg-gray-800 rounded-full h-2 mt-2"></div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Category Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Category Progress</CardTitle>
            <CardDescription>Progress by due diligence category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {categoryBreakdown.map((category) => (
                <div key={category.categoryId} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{category.categoryName}</span>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getRiskColor(category.riskLevel)}`}
                      >
                        {category.riskLevel}
                      </Badge>
                    </div>
                    <span className="text-sm text-gray-600">
                      {category.completedItems}/{category.totalItems}
                    </span>
                  </div>
                  <Progress value={category.progress} />
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{category.progress}% complete</span>
                    <span>{category.actualHours}h / {category.estimatedHours}h</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Team Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Team Performance</CardTitle>
            <CardDescription>Individual contributor metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {teamPerformance.map((member) => (
                <div key={member.userId} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <Users className="h-4 w-4" />
                      </div>
                      <span className="font-medium">{member.userName}</span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {member.contributionPercentage}% completion
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div>
                      <div className="text-gray-600">Assigned</div>
                      <div className="font-medium">{member.assignedItems}</div>
                    </div>
                    <div>
                      <div className="text-gray-600">Completed</div>
                      <div className="font-medium text-green-600">{member.completedItems}</div>
                    </div>
                    <div>
                      <div className="text-gray-600">Overdue</div>
                      <div className={`font-medium ${member.overdueItems > 0 ? 'text-red-600' : 'text-gray-600'}`}>
                        {member.overdueItems}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                    <span>Efficiency: {member.efficiency}x</span>
                    <span>Quality: {member.qualityScore}/5</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bottlenecks */}
      {bottlenecks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              Bottlenecks & Issues
            </CardTitle>
            <CardDescription>Items requiring immediate attention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {bottlenecks.map((bottleneck) => (
                <div key={bottleneck.itemId} className="p-4 border border-orange-200 bg-orange-50 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{bottleneck.itemTitle}</span>
                        <Badge variant="outline" className={getImpactColor(bottleneck.impact)}>
                          {bottleneck.impact}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 mb-2">
                        {bottleneck.categoryName} • Assigned to {bottleneck.assignedTo}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium">Stuck for {bottleneck.daysStuck} days</span>
                        <span className="text-gray-600"> • {bottleneck.suggestedAction}</span>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
