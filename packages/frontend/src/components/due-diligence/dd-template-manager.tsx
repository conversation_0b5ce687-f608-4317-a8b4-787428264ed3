import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Search, 
  Filter,
  Copy,
  Edit,
  Trash2,
  Eye,
  Download,
  Upload,
  Star,
  Clock,
  Users,
  CheckSquare,
  MoreHorizontal,
  Grid,
  List,
  SortAsc,
  SortDesc
} from 'lucide-react'
import { 
  DDTemplate, 
  DDIndustryType,
  DD_CATEGORY_ICONS,
  DDCategoryType
} from '@/types/due-diligence'

interface DDTemplateManagerProps {
  onCreateTemplate?: () => void
  onEditTemplate?: (template: DDTemplate) => void
  onCloneTemplate?: (template: DDTemplate) => void
  onDeleteTemplate?: (template: DDTemplate) => void
  onPreviewTemplate?: (template: DDTemplate) => void
  onUseTemplate?: (template: DDTemplate) => void
}

export function DDTemplateManager({
  onCreateTemplate,
  onEditTemplate,
  onCloneTemplate,
  onDeleteTemplate,
  onPreviewTemplate,
  onUseTemplate
}: DDTemplateManagerProps) {
  const [templates, setTemplates] = useState<DDTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedIndustry, setSelectedIndustry] = useState<DDIndustryType | 'all'>('all')
  const [showPublicOnly, setShowPublicOnly] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState<'name' | 'created' | 'usage' | 'updated'>('usage')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockTemplates: DDTemplate[] = [
      {
        id: '1',
        name: 'Technology M&A Standard',
        description: 'Comprehensive due diligence template for technology acquisitions',
        version: '2.1.0',
        industryType: DDIndustryType.TECHNOLOGY,
        isPublic: true,
        isDefault: true,
        createdBy: 'system',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-02-01T14:30:00Z',
        tenantId: 'public',
        categories: [],
        totalItems: 156,
        estimatedHours: 240,
        usageCount: 89,
        lastUsedAt: '2024-02-15T09:15:00Z',
        allowCustomization: true,
        requiresApproval: false,
        tags: ['technology', 'standard', 'comprehensive']
      },
      {
        id: '2',
        name: 'Healthcare Due Diligence',
        description: 'Specialized template for healthcare and pharmaceutical deals',
        version: '1.5.0',
        industryType: DDIndustryType.HEALTHCARE,
        isPublic: true,
        isDefault: true,
        createdBy: 'system',
        createdAt: '2024-01-10T08:00:00Z',
        updatedAt: '2024-01-25T16:45:00Z',
        tenantId: 'public',
        categories: [],
        totalItems: 203,
        estimatedHours: 320,
        usageCount: 45,
        lastUsedAt: '2024-02-10T11:30:00Z',
        allowCustomization: true,
        requiresApproval: true,
        tags: ['healthcare', 'pharmaceutical', 'regulatory']
      },
      {
        id: '3',
        name: 'Financial Services Quick Check',
        description: 'Streamlined template for financial services acquisitions',
        version: '1.0.0',
        industryType: DDIndustryType.FINANCIAL_SERVICES,
        isPublic: false,
        isDefault: false,
        createdBy: 'user-123',
        createdAt: '2024-02-01T12:00:00Z',
        updatedAt: '2024-02-05T10:20:00Z',
        tenantId: 'tenant-456',
        categories: [],
        totalItems: 98,
        estimatedHours: 160,
        usageCount: 12,
        lastUsedAt: '2024-02-12T15:45:00Z',
        allowCustomization: true,
        requiresApproval: false,
        tags: ['financial', 'quick', 'streamlined']
      }
    ]

    setTemplates(mockTemplates)
    setLoading(false)
  }, [])

  // Filter and sort templates
  const filteredTemplates = templates
    .filter(template => {
      const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesIndustry = selectedIndustry === 'all' || template.industryType === selectedIndustry
      const matchesPublic = !showPublicOnly || template.isPublic
      
      return matchesSearch && matchesIndustry && matchesPublic
    })
    .sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'created':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          break
        case 'usage':
          comparison = a.usageCount - b.usageCount
          break
        case 'updated':
          comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
          break
      }
      
      return sortDirection === 'asc' ? comparison : -comparison
    })

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortDirection('desc')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getCategoryPreview = (template: DDTemplate) => {
    // Mock category types for preview
    const mockCategories = [
      DDCategoryType.FINANCIAL,
      DDCategoryType.LEGAL,
      DDCategoryType.COMMERCIAL,
      DDCategoryType.TECHNICAL
    ]
    
    return mockCategories.slice(0, 3).map(type => DD_CATEGORY_ICONS[type]).join(' ')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <CheckSquare className="h-5 w-5" />
                Due Diligence Templates
              </CardTitle>
              <CardDescription>
                Manage and customize due diligence checklist templates
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => {}}>
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button onClick={onCreateTemplate}>
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Filters and Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={selectedIndustry}
                onChange={(e) => setSelectedIndustry(e.target.value as DDIndustryType | 'all')}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Industries</option>
                {Object.values(DDIndustryType).map(industry => (
                  <option key={industry} value={industry}>
                    {industry.replace('_', ' ')}
                  </option>
                ))}
              </select>
              
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={showPublicOnly}
                  onChange={(e) => setShowPublicOnly(e.target.checked)}
                />
                <span className="text-sm">Public only</span>
              </label>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 border rounded-md">
                <Button
                  variant={sortBy === 'usage' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleSort('usage')}
                  className="rounded-r-none"
                >
                  Popular
                  {sortBy === 'usage' && (
                    sortDirection === 'desc' ? <SortDesc className="h-3 w-3 ml-1" /> : <SortAsc className="h-3 w-3 ml-1" />
                  )}
                </Button>
                <Button
                  variant={sortBy === 'updated' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleSort('updated')}
                  className="rounded-none"
                >
                  Recent
                  {sortBy === 'updated' && (
                    sortDirection === 'desc' ? <SortDesc className="h-3 w-3 ml-1" /> : <SortAsc className="h-3 w-3 ml-1" />
                  )}
                </Button>
                <Button
                  variant={sortBy === 'name' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleSort('name')}
                  className="rounded-l-none"
                >
                  Name
                  {sortBy === 'name' && (
                    sortDirection === 'desc' ? <SortDesc className="h-3 w-3 ml-1" /> : <SortAsc className="h-3 w-3 ml-1" />
                  )}
                </Button>
              </div>
              
              <div className="flex items-center border rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => (
            <Card key={template.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      {template.isDefault && (
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      )}
                    </div>
                    <CardDescription className="line-clamp-2">
                      {template.description}
                    </CardDescription>
                  </div>
                  
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline">
                    {template.industryType.replace('_', ' ')}
                  </Badge>
                  {template.isPublic && (
                    <Badge variant="secondary">Public</Badge>
                  )}
                  <Badge variant="outline">v{template.version}</Badge>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <CheckSquare className="h-3 w-3" />
                      {template.totalItems} items
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {template.estimatedHours}h
                    </span>
                    <span className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {template.usageCount} uses
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    <div className="flex items-center gap-1 mb-1">
                      <span>Categories:</span>
                      <span>{getCategoryPreview(template)}</span>
                    </div>
                    <div>Updated: {formatDate(template.updatedAt)}</div>
                  </div>
                  
                  <div className="flex items-center gap-2 pt-2">
                    <Button 
                      size="sm" 
                      onClick={() => onUseTemplate?.(template)}
                      className="flex-1"
                    >
                      Use Template
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onPreviewTemplate?.(template)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onCloneTemplate?.(template)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b bg-gray-50">
                  <tr>
                    <th className="text-left p-4 font-medium">Template</th>
                    <th className="text-left p-4 font-medium">Industry</th>
                    <th className="text-left p-4 font-medium">Items</th>
                    <th className="text-left p-4 font-medium">Est. Hours</th>
                    <th className="text-left p-4 font-medium">Usage</th>
                    <th className="text-left p-4 font-medium">Updated</th>
                    <th className="text-right p-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTemplates.map((template) => (
                    <tr key={template.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center gap-3">
                          <div>
                            <div className="flex items-center gap-2">
                              <div className="font-medium">{template.name}</div>
                              {template.isDefault && (
                                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                              )}
                            </div>
                            <div className="text-sm text-gray-500 line-clamp-1">
                              {template.description}
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                v{template.version}
                              </Badge>
                              {template.isPublic && (
                                <Badge variant="secondary" className="text-xs">Public</Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge variant="outline">
                          {template.industryType.replace('_', ' ')}
                        </Badge>
                      </td>
                      <td className="p-4">{template.totalItems}</td>
                      <td className="p-4">{template.estimatedHours}h</td>
                      <td className="p-4">{template.usageCount}</td>
                      <td className="p-4">{formatDate(template.updatedAt)}</td>
                      <td className="p-4">
                        <div className="flex items-center justify-end gap-1">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => onUseTemplate?.(template)}
                          >
                            Use
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => onPreviewTemplate?.(template)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => onCloneTemplate?.(template)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => onEditTemplate?.(template)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {filteredTemplates.length === 0 && (
                <div className="text-center py-12">
                  <CheckSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No templates found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm ? 'No templates match your search criteria.' : 'No templates available.'}
                  </p>
                  <Button onClick={onCreateTemplate}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Template
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
