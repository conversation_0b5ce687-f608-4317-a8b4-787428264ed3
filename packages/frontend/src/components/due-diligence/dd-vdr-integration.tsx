import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Database, 
  Folder,
  FileText,
  Upload,
  Download,
  Link,
  Sync,
  CheckCircle,
  AlertCircle,
  Clock,
  Settings,
  Eye,
  RefreshCw,
  ArrowRight,
  ArrowLeftRight,
  FolderOpen,
  FileCheck,
  FileX,
  ExternalLink
} from 'lucide-react'

interface DDVDRIntegration {
  id: string
  checklistId: string
  vdrId: string
  vdrName: string
  dealId: string
  autoSync: boolean
  syncDirection: 'DD_TO_VDR' | 'VDR_TO_DD' | 'BIDIRECTIONAL'
  syncStatus: 'ACTIVE' | 'PAUSED' | 'ERROR' | 'DISABLED'
  lastSyncAt?: string
  documentsLinked: number
  documentsSynced: number
  lastSyncDuration: number
}

interface DDDocumentRequirement {
  itemId: string
  itemTitle: string
  categoryId: string
  categoryName: string
  isRequired: boolean
  hasDocument: boolean
  uploadStatus: 'PENDING' | 'UPLOADED' | 'FAILED' | 'NOT_REQUIRED'
  vdrDocumentId?: string
  dueDate?: string
  assignedTo?: string
}

interface DDCategoryMapping {
  categoryId: string
  categoryName: string
  vdrFolderId: string
  vdrFolderPath: string
  documentCount: number
  syncStatus: 'SYNCED' | 'PENDING' | 'ERROR'
}

interface DDVDRIntegrationProps {
  checklistId: string
  dealId: string
  onCreateIntegration?: () => void
  onConfigureIntegration?: (integration: DDVDRIntegration) => void
  onUploadDocument?: (itemId: string) => void
  onViewDocument?: (vdrDocumentId: string) => void
}

export function DDVDRIntegration({
  checklistId,
  dealId,
  onCreateIntegration,
  onConfigureIntegration,
  onUploadDocument,
  onViewDocument
}: DDVDRIntegrationProps) {
  const [integration, setIntegration] = useState<DDVDRIntegration | null>(null)
  const [requirements, setRequirements] = useState<DDDocumentRequirement[]>([])
  const [categoryMappings, setCategoryMappings] = useState<DDCategoryMapping[]>([])
  const [loading, setLoading] = useState(true)
  const [syncing, setSyncing] = useState(false)

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockIntegration: DDVDRIntegration = {
      id: 'integration-1',
      checklistId,
      vdrId: 'vdr-1',
      vdrName: 'Technology M&A Data Room',
      dealId,
      autoSync: true,
      syncDirection: 'BIDIRECTIONAL',
      syncStatus: 'ACTIVE',
      lastSyncAt: '2024-02-15T10:30:00Z',
      documentsLinked: 23,
      documentsSynced: 18,
      lastSyncDuration: 45000
    }

    const mockRequirements: DDDocumentRequirement[] = [
      {
        itemId: 'item-1',
        itemTitle: 'Audited Financial Statements (3 years)',
        categoryId: 'cat-1',
        categoryName: 'Financial Documents',
        isRequired: true,
        hasDocument: true,
        uploadStatus: 'UPLOADED',
        vdrDocumentId: 'doc-1',
        dueDate: '2024-02-20T17:00:00Z',
        assignedTo: 'John Doe'
      },
      {
        itemId: 'item-2',
        itemTitle: 'Customer Contracts',
        categoryId: 'cat-2',
        categoryName: 'Legal Documents',
        isRequired: true,
        hasDocument: false,
        uploadStatus: 'PENDING',
        dueDate: '2024-02-18T17:00:00Z',
        assignedTo: 'Jane Smith'
      },
      {
        itemId: 'item-3',
        itemTitle: 'Technical Architecture Documentation',
        categoryId: 'cat-3',
        categoryName: 'Technical Documents',
        isRequired: false,
        hasDocument: true,
        uploadStatus: 'UPLOADED',
        vdrDocumentId: 'doc-3',
        assignedTo: 'Mike Johnson'
      },
      {
        itemId: 'item-4',
        itemTitle: 'Employee Handbook',
        categoryId: 'cat-4',
        categoryName: 'HR Documents',
        isRequired: true,
        hasDocument: false,
        uploadStatus: 'FAILED',
        dueDate: '2024-02-22T17:00:00Z',
        assignedTo: 'Sarah Wilson'
      }
    ]

    const mockMappings: DDCategoryMapping[] = [
      {
        categoryId: 'cat-1',
        categoryName: 'Financial Documents',
        vdrFolderId: 'folder-1',
        vdrFolderPath: '/Financial',
        documentCount: 8,
        syncStatus: 'SYNCED'
      },
      {
        categoryId: 'cat-2',
        categoryName: 'Legal Documents',
        vdrFolderId: 'folder-2',
        vdrFolderPath: '/Legal',
        documentCount: 5,
        syncStatus: 'PENDING'
      },
      {
        categoryId: 'cat-3',
        categoryName: 'Technical Documents',
        vdrFolderId: 'folder-3',
        vdrFolderPath: '/Technical',
        documentCount: 12,
        syncStatus: 'SYNCED'
      },
      {
        categoryId: 'cat-4',
        categoryName: 'HR Documents',
        vdrFolderId: 'folder-4',
        vdrFolderPath: '/HR',
        documentCount: 3,
        syncStatus: 'ERROR'
      }
    ]

    setIntegration(mockIntegration)
    setRequirements(mockRequirements)
    setCategoryMappings(mockMappings)
    setLoading(false)
  }, [checklistId, dealId])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
      case 'SYNCED':
      case 'UPLOADED':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'ERROR':
      case 'FAILED':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'PAUSED':
      case 'DISABLED':
        return <Clock className="h-4 w-4 text-gray-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
      case 'SYNCED':
      case 'UPLOADED':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'PENDING':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'ERROR':
      case 'FAILED':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getSyncDirectionIcon = (direction: string) => {
    switch (direction) {
      case 'DD_TO_VDR':
        return <ArrowRight className="h-4 w-4" />
      case 'VDR_TO_DD':
        return <ArrowRight className="h-4 w-4 rotate-180" />
      case 'BIDIRECTIONAL':
        return <ArrowLeftRight className="h-4 w-4" />
      default:
        return <ArrowLeftRight className="h-4 w-4" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatDuration = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000)
    return `${seconds}s`
  }

  const isOverdue = (dueDate?: string) => {
    if (!dueDate) return false
    return new Date(dueDate) < new Date()
  }

  const handleSync = async () => {
    setSyncing(true)
    // Simulate sync process
    setTimeout(() => {
      setSyncing(false)
      if (integration) {
        setIntegration({
          ...integration,
          lastSyncAt: new Date().toISOString(),
          lastSyncDuration: Math.random() * 60000 + 10000
        })
      }
    }, 3000)
  }

  const handleUploadDocument = (itemId: string) => {
    onUploadDocument?.(itemId)
  }

  const handleViewDocument = (vdrDocumentId: string) => {
    onViewDocument?.(vdrDocumentId)
  }

  const completedRequirements = requirements.filter(r => r.hasDocument).length
  const totalRequirements = requirements.length
  const completionPercentage = totalRequirements > 0 ? Math.round((completedRequirements / totalRequirements) * 100) : 0

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!integration) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No VDR Integration</h3>
          <p className="text-gray-600 mb-4">
            Connect this checklist to a Virtual Data Room to manage document requirements
          </p>
          <Button onClick={onCreateIntegration}>
            <Link className="h-4 w-4 mr-2" />
            Connect to VDR
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Integration Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                VDR Integration
              </CardTitle>
              <CardDescription>
                Connected to {integration.vdrName}
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(integration.syncStatus)}>
                {getStatusIcon(integration.syncStatus)}
                <span className="ml-1">{integration.syncStatus}</span>
              </Badge>
              
              <Button variant="outline" size="sm" onClick={() => onConfigureIntegration?.(integration)}>
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{integration.documentsLinked}</div>
              <div className="text-sm text-gray-600">Documents Linked</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold">{integration.documentsSynced}</div>
              <div className="text-sm text-gray-600">Documents Synced</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold">{completionPercentage}%</div>
              <div className="text-sm text-gray-600">Requirements Met</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-sm text-gray-600 mb-1">
                {getSyncDirectionIcon(integration.syncDirection)}
                <span>{integration.syncDirection.replace('_', ' ')}</span>
              </div>
              <div className="text-xs text-gray-500">
                Last sync: {integration.lastSyncAt ? formatDate(integration.lastSyncAt) : 'Never'}
              </div>
            </div>
          </div>
          
          <div className="mt-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleSync}
                disabled={syncing}
              >
                {syncing ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Sync className="h-4 w-4 mr-2" />
                )}
                {syncing ? 'Syncing...' : 'Sync Now'}
              </Button>
              
              {integration.lastSyncDuration > 0 && (
                <span className="text-sm text-gray-500">
                  Last sync took {formatDuration(integration.lastSyncDuration)}
                </span>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Progress:</span>
              <Progress value={completionPercentage} className="w-32" />
              <span className="text-sm font-medium">{completionPercentage}%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Document Requirements */}
        <Card>
          <CardHeader>
            <CardTitle>Document Requirements</CardTitle>
            <CardDescription>
              Track document upload status for checklist items
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {requirements.map((requirement) => (
                <div key={requirement.itemId} className="p-3 border rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h5 className="font-medium text-sm">{requirement.itemTitle}</h5>
                        {requirement.isRequired && (
                          <Badge variant="outline" className="text-xs">Required</Badge>
                        )}
                        {getStatusIcon(requirement.uploadStatus)}
                      </div>
                      
                      <div className="text-xs text-gray-600 mb-2">
                        {requirement.categoryName}
                        {requirement.assignedTo && ` • Assigned to ${requirement.assignedTo}`}
                      </div>
                      
                      {requirement.dueDate && (
                        <div className={`text-xs ${isOverdue(requirement.dueDate) ? 'text-red-600' : 'text-gray-500'}`}>
                          Due: {formatDate(requirement.dueDate)}
                          {isOverdue(requirement.dueDate) && ' (Overdue)'}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-1 ml-2">
                      {requirement.hasDocument && requirement.vdrDocumentId ? (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDocument(requirement.vdrDocumentId!)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      ) : (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleUploadDocument(requirement.itemId)}
                        >
                          <Upload className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Category Mappings */}
        <Card>
          <CardHeader>
            <CardTitle>Category Mappings</CardTitle>
            <CardDescription>
              VDR folder structure and sync status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {categoryMappings.map((mapping) => (
                <div key={mapping.categoryId} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <FolderOpen className="h-5 w-5 text-blue-600" />
                      <div>
                        <div className="font-medium text-sm">{mapping.categoryName}</div>
                        <div className="text-xs text-gray-600">{mapping.vdrFolderPath}</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">
                        {mapping.documentCount} docs
                      </span>
                      {getStatusIcon(mapping.syncStatus)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 pt-4 border-t">
              <Button variant="outline" size="sm" className="w-full">
                <ExternalLink className="h-4 w-4 mr-2" />
                Open VDR
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <Upload className="h-6 w-6 mb-2" />
              <span>Upload Documents</span>
            </Button>
            
            <Button variant="outline" className="h-20 flex-col">
              <Download className="h-6 w-6 mb-2" />
              <span>Download All</span>
            </Button>
            
            <Button variant="outline" className="h-20 flex-col">
              <FileCheck className="h-6 w-6 mb-2" />
              <span>Review Status</span>
            </Button>
            
            <Button variant="outline" className="h-20 flex-col">
              <Settings className="h-6 w-6 mb-2" />
              <span>Configure Sync</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
