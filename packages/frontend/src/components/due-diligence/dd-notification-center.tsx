import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Bell, 
  BellRing,
  Check,
  CheckCheck,
  Clock,
  AlertTriangle,
  Info,
  User,
  Calendar,
  FileText,
  MessageSquare,
  Settings,
  Filter,
  MoreHorizontal,
  X,
  Eye,
  EyeOff,
  Trash2,
  Archive
} from 'lucide-react'

interface DDNotification {
  id: string
  type: string
  title: string
  message: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  status: 'PENDING' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED'
  createdAt: string
  readAt?: string
  
  // Context
  checklistId?: string
  dealId?: string
  itemId?: string
  assignmentId?: string
  
  // Metadata
  data?: any
}

interface DDNotificationCenterProps {
  userId: string
  onNotificationClick?: (notification: DDNotification) => void
  onMarkAsRead?: (notificationId: string) => void
  onMarkAllAsRead?: () => void
  onDeleteNotification?: (notificationId: string) => void
}

export function DDNotificationCenter({
  userId,
  onNotificationClick,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification
}: DDNotificationCenterProps) {
  const [notifications, setNotifications] = useState<DDNotification[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [selectedNotifications, setSelectedNotifications] = useState<Set<string>>(new Set())

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockNotifications: DDNotification[] = [
      {
        id: 'notif-1',
        type: 'TASK_ASSIGNED',
        title: 'New Task Assigned',
        message: 'You have been assigned "Financial Statement Review" in Technology M&A Due Diligence',
        priority: 'HIGH',
        status: 'DELIVERED',
        createdAt: '2024-02-15T10:30:00Z',
        checklistId: 'checklist-1',
        dealId: 'deal-1',
        itemId: 'item-1',
        assignmentId: 'assign-1'
      },
      {
        id: 'notif-2',
        type: 'TASK_DUE_SOON',
        title: 'Task Due Soon',
        message: 'Legal Contract Analysis is due in 2 hours',
        priority: 'URGENT',
        status: 'DELIVERED',
        createdAt: '2024-02-15T08:00:00Z',
        checklistId: 'checklist-1',
        itemId: 'item-2'
      },
      {
        id: 'notif-3',
        type: 'COMMENT_ADDED',
        title: 'New Comment',
        message: 'John Doe added a comment to "Technical Infrastructure Audit"',
        priority: 'MEDIUM',
        status: 'READ',
        createdAt: '2024-02-14T16:45:00Z',
        readAt: '2024-02-14T17:00:00Z',
        itemId: 'item-3'
      },
      {
        id: 'notif-4',
        type: 'MILESTONE_REACHED',
        title: 'Milestone Reached',
        message: 'Financial Analysis category is 75% complete',
        priority: 'MEDIUM',
        status: 'DELIVERED',
        createdAt: '2024-02-14T14:20:00Z',
        checklistId: 'checklist-1'
      },
      {
        id: 'notif-5',
        type: 'APPROVAL_REQUIRED',
        title: 'Approval Required',
        message: 'Technical Assessment requires your approval before proceeding',
        priority: 'HIGH',
        status: 'DELIVERED',
        createdAt: '2024-02-14T11:15:00Z',
        itemId: 'item-4'
      }
    ]

    setNotifications(mockNotifications)
    setLoading(false)
  }, [userId])

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    const statusMatch = filter === 'all' || 
                       (filter === 'unread' && notification.status !== 'READ') ||
                       (filter === 'read' && notification.status === 'read')
    
    const typeMatch = typeFilter === 'all' || notification.type === typeFilter
    
    return statusMatch && typeMatch
  })

  const unreadCount = notifications.filter(n => n.status !== 'read').length

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'TASK_ASSIGNED':
      case 'TASK_REASSIGNED':
        return <User className="h-4 w-4" />
      case 'TASK_DUE_SOON':
      case 'TASK_OVERDUE':
        return <Clock className="h-4 w-4" />
      case 'TASK_COMPLETED':
        return <CheckCheck className="h-4 w-4" />
      case 'COMMENT_ADDED':
      case 'MENTION_RECEIVED':
        return <MessageSquare className="h-4 w-4" />
      case 'MILESTONE_REACHED':
        return <Calendar className="h-4 w-4" />
      case 'APPROVAL_REQUIRED':
        return <AlertTriangle className="h-4 w-4" />
      case 'SYSTEM_ALERT':
        return <Info className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW': return 'text-green-600 bg-green-50 border-green-200'
      case 'MEDIUM': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'HIGH': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'URGENT': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    
    return date.toLocaleDateString()
  }

  const handleNotificationClick = (notification: DDNotification) => {
    if (notification.status !== 'read') {
      onMarkAsRead?.(notification.id)
      setNotifications(prev => 
        prev.map(n => 
          n.id === notification.id 
            ? { ...n, status: 'read', readAt: new Date().toISOString() }
            : n
        )
      )
    }
    
    onNotificationClick?.(notification)
  }

  const handleMarkAsRead = (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    onMarkAsRead?.(notificationId)
    
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId 
          ? { ...n, status: 'read', readAt: new Date().toISOString() }
          : n
      )
    )
  }

  const handleMarkAllAsRead = () => {
    onMarkAllAsRead?.()
    
    setNotifications(prev => 
      prev.map(n => 
        n.status !== 'read' 
          ? { ...n, status: 'read', readAt: new Date().toISOString() }
          : n
      )
    )
  }

  const handleDeleteNotification = (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    onDeleteNotification?.(notificationId)
    
    setNotifications(prev => prev.filter(n => n.id !== notificationId))
  }

  const handleSelectNotification = (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    
    setSelectedNotifications(prev => {
      const newSet = new Set(prev)
      if (newSet.has(notificationId)) {
        newSet.delete(notificationId)
      } else {
        newSet.add(notificationId)
      }
      return newSet
    })
  }

  const handleBulkAction = (action: 'read' | 'delete') => {
    const selectedIds = Array.from(selectedNotifications)
    
    if (action === 'read') {
      selectedIds.forEach(id => onMarkAsRead?.(id))
      setNotifications(prev => 
        prev.map(n => 
          selectedIds.includes(n.id) 
            ? { ...n, status: 'read', readAt: new Date().toISOString() }
            : n
        )
      )
    } else if (action === 'delete') {
      selectedIds.forEach(id => onDeleteNotification?.(id))
      setNotifications(prev => prev.filter(n => !selectedIds.includes(n.id)))
    }
    
    setSelectedNotifications(new Set())
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
              </CardTitle>
              {unreadCount > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {unreadCount}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <Button variant="outline" size="sm" onClick={handleMarkAllAsRead}>
                  <CheckCheck className="h-4 w-4 mr-2" />
                  Mark All Read
                </Button>
              )}
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1 border rounded-md">
                <Button
                  variant={filter === 'all' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setFilter('all')}
                  className="rounded-r-none"
                >
                  All ({notifications.length})
                </Button>
                <Button
                  variant={filter === 'unread' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setFilter('unread')}
                  className="rounded-none"
                >
                  Unread ({unreadCount})
                </Button>
                <Button
                  variant={filter === 'read' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setFilter('read')}
                  className="rounded-l-none"
                >
                  Read ({notifications.length - unreadCount})
                </Button>
              </div>
              
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="all">All Types</option>
                <option value="TASK_ASSIGNED">Task Assigned</option>
                <option value="TASK_DUE_SOON">Due Soon</option>
                <option value="COMMENT_ADDED">Comments</option>
                <option value="MILESTONE_REACHED">Milestones</option>
                <option value="APPROVAL_REQUIRED">Approvals</option>
              </select>
            </div>
            
            {selectedNotifications.size > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {selectedNotifications.size} selected
                </span>
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('read')}>
                  <Eye className="h-4 w-4 mr-2" />
                  Mark Read
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('delete')}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Notifications List */}
      <Card>
        <CardContent className="p-0">
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-12">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No notifications</h3>
              <p className="text-gray-600">
                {filter === 'unread' 
                  ? "You're all caught up! No unread notifications."
                  : "No notifications to display."
                }
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                    notification.status !== 'read' ? 'bg-blue-50/30' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start gap-3">
                    <input
                      type="checkbox"
                      checked={selectedNotifications.has(notification.id)}
                      onChange={(e) => handleSelectNotification(notification.id, e)}
                      className="mt-1"
                      onClick={(e) => e.stopPropagation()}
                    />
                    
                    <div className={`p-2 rounded-lg ${getPriorityColor(notification.priority)}`}>
                      {getNotificationIcon(notification.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{notification.title}</h4>
                            {notification.status !== 'read' && (
                              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                            )}
                            <Badge variant="outline" className="text-xs">
                              {notification.priority}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                            <span>{formatTimeAgo(notification.createdAt)}</span>
                            {notification.readAt && (
                              <span>Read {formatTimeAgo(notification.readAt)}</span>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-1 ml-4">
                          {notification.status !== 'read' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => handleMarkAsRead(notification.id, e)}
                              title="Mark as read"
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                          )}
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => handleDeleteNotification(notification.id, e)}
                            title="Delete notification"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
