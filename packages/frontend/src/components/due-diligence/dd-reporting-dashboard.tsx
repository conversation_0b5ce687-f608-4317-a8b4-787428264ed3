import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Download,
  Calendar,
  Clock,
  Users,
  <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>dingUp,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  Share,
  Settings,
  FileSpreadsheet,
  FileImage,
  Globe,
  Lock,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface DDReport {
  id: string
  name: string
  description?: string
  type: string
  format: 'PDF' | 'EXCEL' | 'CSV' | 'JSON' | 'HTML'
  status: 'PENDING' | 'GENERATING' | 'COMPLETED' | 'FAILED'
  generatedAt?: string
  fileUrl?: string
  fileSize?: number
  visibility: 'PRIVATE' | 'TEAM' | 'PUBLIC'
  isScheduled: boolean
  schedule?: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
    time: string
    recipients: string[]
    isActive: boolean
  }
  createdBy: string
  createdAt: string
}

interface DDReportTemplate {
  id: string
  name: string
  description?: string
  type: string
  format: string
  isPublic: boolean
  usageCount: number
}

interface DDReportingDashboardProps {
  checklistId?: string
  dealId?: string
  onCreateReport?: () => void
  onEditReport?: (report: DDReport) => void
  onDeleteReport?: (reportId: string) => void
}

export function DDReportingDashboard({
  checklistId,
  dealId,
  onCreateReport,
  onEditReport,
  onDeleteReport
}: DDReportingDashboardProps) {
  const [reports, setReports] = useState<DDReport[]>([])
  const [templates, setTemplates] = useState<DDReportTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'scheduled' | 'completed' | 'failed'>('all')
  const [selectedReports, setSelectedReports] = useState<Set<string>>(new Set())

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockReports: DDReport[] = [
      {
        id: 'report-1',
        name: 'Weekly Progress Summary',
        description: 'Comprehensive progress report for Technology M&A Due Diligence',
        type: 'PROGRESS_SUMMARY',
        format: 'PDF',
        status: 'COMPLETED',
        generatedAt: '2024-02-15T10:30:00Z',
        fileUrl: '/reports/weekly-progress-summary.pdf',
        fileSize: 2048576,
        visibility: 'TEAM',
        isScheduled: true,
        schedule: {
          frequency: 'WEEKLY',
          time: '09:00',
          recipients: ['<EMAIL>', '<EMAIL>'],
          isActive: true
        },
        createdBy: 'user-1',
        createdAt: '2024-02-01T09:00:00Z'
      },
      {
        id: 'report-2',
        name: 'Team Performance Analysis',
        description: 'Detailed analysis of team performance and efficiency metrics',
        type: 'TEAM_PERFORMANCE',
        format: 'EXCEL',
        status: 'COMPLETED',
        generatedAt: '2024-02-14T16:45:00Z',
        fileUrl: '/reports/team-performance-analysis.xlsx',
        fileSize: 1536000,
        visibility: 'PRIVATE',
        isScheduled: false,
        createdBy: 'user-1',
        createdAt: '2024-02-14T16:00:00Z'
      },
      {
        id: 'report-3',
        name: 'Category Breakdown Report',
        description: 'Progress breakdown by due diligence categories',
        type: 'CATEGORY_BREAKDOWN',
        format: 'PDF',
        status: 'GENERATING',
        visibility: 'TEAM',
        isScheduled: false,
        createdBy: 'user-2',
        createdAt: '2024-02-15T11:00:00Z'
      },
      {
        id: 'report-4',
        name: 'Executive Summary',
        description: 'High-level executive summary for stakeholders',
        type: 'EXECUTIVE_SUMMARY',
        format: 'PDF',
        status: 'FAILED',
        visibility: 'PUBLIC',
        isScheduled: false,
        createdBy: 'user-1',
        createdAt: '2024-02-15T08:30:00Z'
      }
    ]

    const mockTemplates: DDReportTemplate[] = [
      {
        id: 'template-1',
        name: 'Standard Progress Report',
        description: 'Standard template for progress reporting',
        type: 'PROGRESS_SUMMARY',
        format: 'PDF',
        isPublic: true,
        usageCount: 45
      },
      {
        id: 'template-2',
        name: 'Executive Dashboard',
        description: 'Executive-level summary with key metrics',
        type: 'EXECUTIVE_SUMMARY',
        format: 'PDF',
        isPublic: true,
        usageCount: 32
      },
      {
        id: 'template-3',
        name: 'Team Performance Tracker',
        description: 'Detailed team performance analysis',
        type: 'TEAM_PERFORMANCE',
        format: 'EXCEL',
        isPublic: false,
        usageCount: 18
      }
    ]

    setReports(mockReports)
    setTemplates(mockTemplates)
    setLoading(false)
  }, [checklistId, dealId])

  // Filter reports
  const filteredReports = reports.filter(report => {
    switch (filter) {
      case 'scheduled':
        return report.isScheduled
      case 'completed':
        return report.status === 'COMPLETED'
      case 'failed':
        return report.status === 'FAILED'
      default:
        return true
    }
  })

  const getStatusIcon = (status: DDReport['status']) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'GENERATING':
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
      case 'FAILED':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'PDF':
        return <FileText className="h-4 w-4 text-red-600" />
      case 'EXCEL':
        return <FileSpreadsheet className="h-4 w-4 text-green-600" />
      case 'CSV':
        return <FileSpreadsheet className="h-4 w-4 text-blue-600" />
      case 'HTML':
        return <Globe className="h-4 w-4 text-orange-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'PUBLIC':
        return <Globe className="h-3 w-3" />
      case 'TEAM':
        return <Users className="h-3 w-3" />
      default:
        return <Lock className="h-3 w-3" />
    }
  }

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '-'
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(1)} MB`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const handleDownload = (report: DDReport) => {
    if (report.fileUrl) {
      window.open(report.fileUrl, '_blank')
    }
  }

  const handleGenerateFromTemplate = (template: DDReportTemplate) => {
    // Implementation would open dialog to configure report generation
    console.log('Generate from template:', template.id)
  }

  const handleScheduleReport = (report: DDReport) => {
    // Implementation would open scheduling dialog
    console.log('Schedule report:', report.id)
  }

  const handleBulkAction = (action: 'download' | 'delete' | 'share') => {
    const selectedIds = Array.from(selectedReports)
    console.log('Bulk action:', action, selectedIds)
    
    if (action === 'delete') {
      selectedIds.forEach(id => onDeleteReport?.(id))
      setReports(prev => prev.filter(r => !selectedIds.includes(r.id)))
      setSelectedReports(new Set())
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Reports & Analytics
              </CardTitle>
              <CardDescription>
                Generate, schedule, and manage due diligence reports
              </CardDescription>
            </div>
            
            <Button onClick={onCreateReport}>
              <Plus className="h-4 w-4 mr-2" />
              Create Report
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-3xl font-bold">{reports.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Scheduled</p>
                <p className="text-3xl font-bold">{reports.filter(r => r.isScheduled).length}</p>
              </div>
              <Calendar className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-3xl font-bold">{reports.filter(r => r.status === 'COMPLETED').length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Templates</p>
                <p className="text-3xl font-bold">{templates.length}</p>
              </div>
              <Settings className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Reports List */}
        <div className="lg:col-span-2 space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1 border rounded-md">
                    <Button
                      variant={filter === 'all' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setFilter('all')}
                      className="rounded-r-none"
                    >
                      All ({reports.length})
                    </Button>
                    <Button
                      variant={filter === 'scheduled' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setFilter('scheduled')}
                      className="rounded-none"
                    >
                      Scheduled ({reports.filter(r => r.isScheduled).length})
                    </Button>
                    <Button
                      variant={filter === 'completed' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setFilter('completed')}
                      className="rounded-none"
                    >
                      Completed ({reports.filter(r => r.status === 'COMPLETED').length})
                    </Button>
                    <Button
                      variant={filter === 'failed' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setFilter('failed')}
                      className="rounded-l-none"
                    >
                      Failed ({reports.filter(r => r.status === 'FAILED').length})
                    </Button>
                  </div>
                </div>
                
                {selectedReports.size > 0 && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                      {selectedReports.size} selected
                    </span>
                    <Button variant="outline" size="sm" onClick={() => handleBulkAction('download')}>
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleBulkAction('delete')}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Reports */}
          <Card>
            <CardContent className="p-0">
              {filteredReports.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No reports found</h3>
                  <p className="text-gray-600 mb-4">
                    Create your first report to get started
                  </p>
                  <Button onClick={onCreateReport}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Report
                  </Button>
                </div>
              ) : (
                <div className="divide-y">
                  {filteredReports.map((report) => (
                    <div key={report.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-start gap-3">
                        <input
                          type="checkbox"
                          checked={selectedReports.has(report.id)}
                          onChange={(e) => {
                            const newSet = new Set(selectedReports)
                            if (e.target.checked) {
                              newSet.add(report.id)
                            } else {
                              newSet.delete(report.id)
                            }
                            setSelectedReports(newSet)
                          }}
                          className="mt-1"
                        />
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium">{report.name}</h4>
                                {getStatusIcon(report.status)}
                                {report.isScheduled && (
                                  <Badge variant="outline" className="text-xs">
                                    <Calendar className="h-3 w-3 mr-1" />
                                    Scheduled
                                  </Badge>
                                )}
                              </div>
                              
                              {report.description && (
                                <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                                  {report.description}
                                </p>
                              )}
                              
                              <div className="flex items-center gap-4 text-xs text-gray-500">
                                <div className="flex items-center gap-1">
                                  {getFormatIcon(report.format)}
                                  <span>{report.format}</span>
                                </div>
                                
                                <div className="flex items-center gap-1">
                                  {getVisibilityIcon(report.visibility)}
                                  <span>{report.visibility}</span>
                                </div>
                                
                                {report.fileSize && (
                                  <span>{formatFileSize(report.fileSize)}</span>
                                )}
                                
                                <span>Created {formatDate(report.createdAt)}</span>
                                
                                {report.generatedAt && (
                                  <span>Generated {formatDate(report.generatedAt)}</span>
                                )}
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-1 ml-4">
                              {report.status === 'COMPLETED' && report.fileUrl && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDownload(report)}
                                >
                                  <Download className="h-4 w-4" />
                                </Button>
                              )}
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onEditReport?.(report)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleScheduleReport(report)}
                              >
                                <Calendar className="h-4 w-4" />
                              </Button>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onDeleteReport?.(report.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Templates Sidebar */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Report Templates</CardTitle>
              <CardDescription>
                Quick start with pre-built templates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {templates.map((template) => (
                <div key={template.id} className="p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h5 className="font-medium text-sm">{template.name}</h5>
                        {template.isPublic && (
                          <Badge variant="outline" className="text-xs">
                            Public
                          </Badge>
                        )}
                      </div>
                      
                      {template.description && (
                        <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                          {template.description}
                        </p>
                      )}
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{template.format}</span>
                        <span>{template.usageCount} uses</span>
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full mt-2"
                    onClick={() => handleGenerateFromTemplate(template)}
                  >
                    Use Template
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
