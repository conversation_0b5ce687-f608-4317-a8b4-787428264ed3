import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  UserPlus, 
  Users, 
  Clock,
  AlertCircle,
  CheckCircle,
  User,
  Calendar,
  BarChart3,
  ArrowRight,
  RefreshCw,
  MessageSquare,
  Eye,
  Edit,
  Trash2,
  Filter,
  Search,
  TrendingUp,
  TrendingDown
} from 'lucide-react'
import { 
  DDItemPriority,
  DD_PRIORITY_COLORS,
  DDItemStatus,
  DD_STATUS_COLORS
} from '@/types/due-diligence'

interface TaskAssignment {
  id: string
  itemId: string
  itemTitle: string
  checklistId: string
  dealId: string
  
  // Assignment details
  assignedTo: string
  assignedToName: string
  assignedBy: string
  assignedByName: string
  assignedAt: string
  dueDate?: string
  priority: DDItemPriority
  
  // Status
  status: 'ASSIGNED' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'REJECTED' | 'REASSIGNED'
  acceptedAt?: string
  startedAt?: string
  completedAt?: string
  
  // Progress
  progressPercentage: number
  estimatedHours: number
  actualHours?: number
  
  // Context
  instructions?: string
  collaborators: string[]
  watchers: string[]
  
  // Metadata
  lastUpdateAt: string
  lastUpdateBy: string
}

interface UserWorkload {
  userId: string
  userName: string
  email: string
  activeAssignments: number
  totalEstimatedHours: number
  overdueAssignments: number
  maxConcurrentTasks: number
  availableHours: number
  completionRate: number
  isAvailable: boolean
}

interface DDTaskAssignmentProps {
  checklistId: string
  dealId: string
  onAssignmentChange?: (assignment: TaskAssignment) => void
}

export function DDTaskAssignment({
  checklistId,
  dealId,
  onAssignmentChange
}: DDTaskAssignmentProps) {
  const [assignments, setAssignments] = useState<TaskAssignment[]>([])
  const [teamWorkload, setTeamWorkload] = useState<UserWorkload[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedAssignment, setSelectedAssignment] = useState<string | null>(null)
  const [showAssignDialog, setShowAssignDialog] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [assigneeFilter, setAssigneeFilter] = useState<string>('all')

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockAssignments: TaskAssignment[] = [
      {
        id: 'assign-1',
        itemId: 'item-1',
        itemTitle: 'Financial Statement Review',
        checklistId,
        dealId,
        assignedTo: 'user-1',
        assignedToName: 'John Doe',
        assignedBy: 'user-admin',
        assignedByName: 'Admin User',
        assignedAt: '2024-02-01T09:00:00Z',
        dueDate: '2024-02-05T17:00:00Z',
        priority: DDItemPriority.HIGH,
        status: 'IN_PROGRESS',
        acceptedAt: '2024-02-01T10:30:00Z',
        startedAt: '2024-02-01T11:00:00Z',
        progressPercentage: 65,
        estimatedHours: 8,
        actualHours: 5.5,
        instructions: 'Review the last 3 years of financial statements and identify any red flags',
        collaborators: ['user-2'],
        watchers: ['user-admin'],
        lastUpdateAt: '2024-02-03T14:30:00Z',
        lastUpdateBy: 'user-1'
      },
      {
        id: 'assign-2',
        itemId: 'item-2',
        itemTitle: 'Legal Contract Analysis',
        checklistId,
        dealId,
        assignedTo: 'user-2',
        assignedToName: 'Jane Smith',
        assignedBy: 'user-admin',
        assignedByName: 'Admin User',
        assignedAt: '2024-02-02T14:00:00Z',
        dueDate: '2024-02-07T17:00:00Z',
        priority: DDItemPriority.CRITICAL,
        status: 'ASSIGNED',
        progressPercentage: 0,
        estimatedHours: 12,
        instructions: 'Analyze all customer contracts for potential liabilities',
        collaborators: [],
        watchers: ['user-admin', 'user-1'],
        lastUpdateAt: '2024-02-02T14:00:00Z',
        lastUpdateBy: 'user-admin'
      },
      {
        id: 'assign-3',
        itemId: 'item-3',
        itemTitle: 'Technical Infrastructure Audit',
        checklistId,
        dealId,
        assignedTo: 'user-3',
        assignedToName: 'Mike Johnson',
        assignedBy: 'user-1',
        assignedByName: 'John Doe',
        assignedAt: '2024-02-01T16:00:00Z',
        dueDate: '2024-02-06T17:00:00Z',
        priority: DDItemPriority.MEDIUM,
        status: 'COMPLETED',
        acceptedAt: '2024-02-01T17:00:00Z',
        startedAt: '2024-02-02T09:00:00Z',
        completedAt: '2024-02-04T15:30:00Z',
        progressPercentage: 100,
        estimatedHours: 16,
        actualHours: 14,
        instructions: 'Evaluate the technical infrastructure and identify scalability issues',
        collaborators: ['user-4'],
        watchers: ['user-admin'],
        lastUpdateAt: '2024-02-04T15:30:00Z',
        lastUpdateBy: 'user-3'
      }
    ]

    const mockWorkload: UserWorkload[] = [
      {
        userId: 'user-1',
        userName: 'John Doe',
        email: '<EMAIL>',
        activeAssignments: 3,
        totalEstimatedHours: 24,
        overdueAssignments: 0,
        maxConcurrentTasks: 5,
        availableHours: 16,
        completionRate: 0.92,
        isAvailable: true
      },
      {
        userId: 'user-2',
        userName: 'Jane Smith',
        email: '<EMAIL>',
        activeAssignments: 4,
        totalEstimatedHours: 32,
        overdueAssignments: 1,
        maxConcurrentTasks: 5,
        availableHours: 8,
        completionRate: 0.88,
        isAvailable: true
      },
      {
        userId: 'user-3',
        userName: 'Mike Johnson',
        email: '<EMAIL>',
        activeAssignments: 2,
        totalEstimatedHours: 16,
        overdueAssignments: 0,
        maxConcurrentTasks: 4,
        availableHours: 24,
        completionRate: 0.95,
        isAvailable: true
      }
    ]

    setAssignments(mockAssignments)
    setTeamWorkload(mockWorkload)
    setLoading(false)
  }, [checklistId, dealId])

  // Filter assignments
  const filteredAssignments = assignments.filter(assignment => {
    const matchesSearch = assignment.itemTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.assignedToName.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || assignment.status === statusFilter
    const matchesAssignee = assigneeFilter === 'all' || assignment.assignedTo === assigneeFilter
    
    return matchesSearch && matchesStatus && matchesAssignee
  })

  const getStatusBadge = (status: TaskAssignment['status']) => {
    const variants = {
      ASSIGNED: 'secondary',
      ACCEPTED: 'default',
      IN_PROGRESS: 'default',
      COMPLETED: 'default',
      REJECTED: 'destructive',
      REASSIGNED: 'secondary'
    } as const

    const colors = {
      ASSIGNED: 'text-yellow-600',
      ACCEPTED: 'text-blue-600',
      IN_PROGRESS: 'text-blue-600',
      COMPLETED: 'text-green-600',
      REJECTED: 'text-red-600',
      REASSIGNED: 'text-orange-600'
    }

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status.replace('_', ' ')}
      </Badge>
    )
  }

  const getPriorityBadge = (priority: DDItemPriority) => {
    return (
      <Badge 
        variant="outline" 
        style={{ 
          borderColor: DD_PRIORITY_COLORS[priority],
          color: DD_PRIORITY_COLORS[priority]
        }}
      >
        {priority}
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const isOverdue = (dueDate?: string) => {
    if (!dueDate) return false
    return new Date(dueDate) < new Date()
  }

  const getWorkloadColor = (workload: UserWorkload) => {
    const utilizationRate = workload.activeAssignments / workload.maxConcurrentTasks
    if (utilizationRate >= 1) return 'text-red-600'
    if (utilizationRate >= 0.8) return 'text-yellow-600'
    return 'text-green-600'
  }

  const handleAssignmentAction = (assignmentId: string, action: string) => {
    // Implementation would handle assignment actions
    console.log('Assignment action:', { assignmentId, action })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Task Assignments
              </CardTitle>
              <CardDescription>
                Manage task assignments and track team workload
              </CardDescription>
            </div>
            
            <Button onClick={() => setShowAssignDialog(true)}>
              <UserPlus className="h-4 w-4 mr-2" />
              Assign Task
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Team Workload Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Team Workload</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {teamWorkload.map((member) => (
              <Card key={member.userId} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <User className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium">{member.userName}</div>
                      <div className="text-xs text-gray-500">{member.email}</div>
                    </div>
                  </div>
                  
                  <div className={`text-sm font-medium ${getWorkloadColor(member)}`}>
                    {member.activeAssignments}/{member.maxConcurrentTasks}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Capacity</span>
                    <span>{Math.round((member.activeAssignments / member.maxConcurrentTasks) * 100)}%</span>
                  </div>
                  <Progress value={(member.activeAssignments / member.maxConcurrentTasks) * 100} />
                  
                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div>Hours: {member.totalEstimatedHours}h</div>
                    <div>Available: {member.availableHours}h</div>
                    <div className="flex items-center gap-1">
                      <span>Rate:</span>
                      <span className="flex items-center gap-1">
                        {member.completionRate >= 0.9 ? (
                          <TrendingUp className="h-3 w-3 text-green-600" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-600" />
                        )}
                        {Math.round(member.completionRate * 100)}%
                      </span>
                    </div>
                    <div>Overdue: {member.overdueAssignments}</div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search assignments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Status</option>
              <option value="ASSIGNED">Assigned</option>
              <option value="ACCEPTED">Accepted</option>
              <option value="IN_PROGRESS">In Progress</option>
              <option value="COMPLETED">Completed</option>
              <option value="REJECTED">Rejected</option>
            </select>
            
            <select
              value={assigneeFilter}
              onChange={(e) => setAssigneeFilter(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Assignees</option>
              {teamWorkload.map(member => (
                <option key={member.userId} value={member.userId}>
                  {member.userName}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Assignments List */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b bg-gray-50">
                <tr>
                  <th className="text-left p-4 font-medium">Task</th>
                  <th className="text-left p-4 font-medium">Assignee</th>
                  <th className="text-left p-4 font-medium">Status</th>
                  <th className="text-left p-4 font-medium">Priority</th>
                  <th className="text-left p-4 font-medium">Progress</th>
                  <th className="text-left p-4 font-medium">Due Date</th>
                  <th className="text-right p-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredAssignments.map((assignment) => (
                  <tr key={assignment.id} className="border-b hover:bg-gray-50">
                    <td className="p-4">
                      <div>
                        <div className="font-medium">{assignment.itemTitle}</div>
                        <div className="text-sm text-gray-500">
                          {assignment.estimatedHours}h estimated
                          {assignment.actualHours && ` • ${assignment.actualHours}h actual`}
                        </div>
                      </div>
                    </td>
                    
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="h-3 w-3" />
                        </div>
                        <div>
                          <div className="font-medium">{assignment.assignedToName}</div>
                          <div className="text-xs text-gray-500">
                            Assigned by {assignment.assignedByName}
                          </div>
                        </div>
                      </div>
                    </td>
                    
                    <td className="p-4">
                      {getStatusBadge(assignment.status)}
                    </td>
                    
                    <td className="p-4">
                      {getPriorityBadge(assignment.priority)}
                    </td>
                    
                    <td className="p-4">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>{assignment.progressPercentage}%</span>
                        </div>
                        <Progress value={assignment.progressPercentage} className="w-20" />
                      </div>
                    </td>
                    
                    <td className="p-4">
                      {assignment.dueDate ? (
                        <div className={`text-sm ${isOverdue(assignment.dueDate) ? 'text-red-600' : ''}`}>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(assignment.dueDate)}
                          </div>
                          {isOverdue(assignment.dueDate) && (
                            <div className="flex items-center gap-1 text-red-600">
                              <AlertCircle className="h-3 w-3" />
                              Overdue
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400">No due date</span>
                      )}
                    </td>
                    
                    <td className="p-4">
                      <div className="flex items-center justify-end gap-1">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleAssignmentAction(assignment.id, 'view')}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleAssignmentAction(assignment.id, 'reassign')}
                        >
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                        
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleAssignmentAction(assignment.id, 'comment')}
                        >
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {filteredAssignments.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No assignments found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm ? 'No assignments match your search criteria.' : 'No tasks have been assigned yet.'}
                </p>
                <Button onClick={() => setShowAssignDialog(true)}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Assign First Task
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
