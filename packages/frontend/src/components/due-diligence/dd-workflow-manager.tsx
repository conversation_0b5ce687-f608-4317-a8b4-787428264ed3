import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Play, 
  Pause, 
  Square,
  SkipForward,
  RotateCcw,
  Clock,
  User,
  CheckCircle,
  AlertCircle,
  XCircle,
  ArrowRight,
  Settings,
  Eye,
  Edit,
  GitBranch,
  Workflow,
  Timer,
  Users
} from 'lucide-react'

interface WorkflowStage {
  id: string
  name: string
  type: 'start' | 'task' | 'approval' | 'parallel' | 'end'
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  assignedTo?: string
  estimatedDuration: number
  actualDuration?: number
  startedAt?: string
  completedAt?: string
  order: number
}

interface WorkflowInstance {
  id: string
  name: string
  status: 'running' | 'paused' | 'completed' | 'failed' | 'cancelled'
  currentStageId: string
  startedAt: string
  completedAt?: string
  stages: WorkflowStage[]
  progress: number
  checklistId: string
  dealId: string
}

interface DDWorkflowManagerProps {
  checklistId: string
  dealId: string
  onStageAction?: (stageId: string, action: string) => void
  onWorkflowAction?: (action: string) => void
}

export function DDWorkflowManager({
  checklistId,
  dealId,
  onStageAction,
  onWorkflowAction
}: DDWorkflowManagerProps) {
  const [workflow, setWorkflow] = useState<WorkflowInstance | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedStage, setSelectedStage] = useState<string | null>(null)

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockWorkflow: WorkflowInstance = {
      id: 'workflow-1',
      name: 'Technology M&A Due Diligence',
      status: 'running',
      currentStageId: 'stage-3',
      startedAt: '2024-02-01T09:00:00Z',
      checklistId,
      dealId,
      progress: 45,
      stages: [
        {
          id: 'stage-1',
          name: 'Initial Review',
          type: 'start',
          status: 'completed',
          assignedTo: 'John Doe',
          estimatedDuration: 120,
          actualDuration: 95,
          startedAt: '2024-02-01T09:00:00Z',
          completedAt: '2024-02-01T10:35:00Z',
          order: 1
        },
        {
          id: 'stage-2',
          name: 'Financial Analysis',
          type: 'task',
          status: 'completed',
          assignedTo: 'Jane Smith',
          estimatedDuration: 240,
          actualDuration: 220,
          startedAt: '2024-02-01T10:35:00Z',
          completedAt: '2024-02-02T14:15:00Z',
          order: 2
        },
        {
          id: 'stage-3',
          name: 'Legal Review',
          type: 'task',
          status: 'running',
          assignedTo: 'Mike Johnson',
          estimatedDuration: 180,
          startedAt: '2024-02-02T14:15:00Z',
          order: 3
        },
        {
          id: 'stage-4',
          name: 'Technical Assessment',
          type: 'parallel',
          status: 'pending',
          assignedTo: 'Sarah Wilson',
          estimatedDuration: 300,
          order: 4
        },
        {
          id: 'stage-5',
          name: 'Management Approval',
          type: 'approval',
          status: 'pending',
          assignedTo: 'David Brown',
          estimatedDuration: 60,
          order: 5
        },
        {
          id: 'stage-6',
          name: 'Final Report',
          type: 'end',
          status: 'pending',
          assignedTo: 'John Doe',
          estimatedDuration: 120,
          order: 6
        }
      ]
    }

    setWorkflow(mockWorkflow)
    setLoading(false)
  }, [checklistId, dealId])

  const getStageIcon = (stage: WorkflowStage) => {
    switch (stage.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'running':
        return <Clock className="h-5 w-5 text-blue-600 animate-pulse" />
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-600" />
      case 'skipped':
        return <SkipForward className="h-5 w-5 text-gray-400" />
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />
    }
  }

  const getStageTypeIcon = (type: WorkflowStage['type']) => {
    switch (type) {
      case 'start':
        return <Play className="h-4 w-4" />
      case 'task':
        return <CheckCircle className="h-4 w-4" />
      case 'approval':
        return <User className="h-4 w-4" />
      case 'parallel':
        return <GitBranch className="h-4 w-4" />
      case 'end':
        return <Square className="h-4 w-4" />
    }
  }

  const getStatusBadge = (status: WorkflowInstance['status']) => {
    const variants = {
      running: 'default',
      paused: 'secondary',
      completed: 'default',
      failed: 'destructive',
      cancelled: 'secondary'
    } as const

    const colors = {
      running: 'text-blue-600',
      paused: 'text-yellow-600',
      completed: 'text-green-600',
      failed: 'text-red-600',
      cancelled: 'text-gray-600'
    }

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const calculateProgress = (stages: WorkflowStage[]) => {
    const completedStages = stages.filter(s => s.status === 'completed').length
    return Math.round((completedStages / stages.length) * 100)
  }

  const handleStageClick = (stageId: string) => {
    setSelectedStage(selectedStage === stageId ? null : stageId)
  }

  const handleStageAction = (stageId: string, action: string) => {
    onStageAction?.(stageId, action)
  }

  const handleWorkflowAction = (action: string) => {
    onWorkflowAction?.(action)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!workflow) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Workflow className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Workflow Active</h3>
          <p className="text-gray-600 mb-4">
            Start a workflow to manage the due diligence process
          </p>
          <Button onClick={() => handleWorkflowAction('start')}>
            <Play className="h-4 w-4 mr-2" />
            Start Workflow
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Workflow Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Workflow className="h-5 w-5" />
                {workflow.name}
              </CardTitle>
              <CardDescription>
                Started {formatDate(workflow.startedAt)}
                {workflow.completedAt && ` • Completed ${formatDate(workflow.completedAt)}`}
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              {getStatusBadge(workflow.status)}
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Progress</span>
              <span>{workflow.progress}%</span>
            </div>
            <Progress value={workflow.progress} />
          </div>
        </CardHeader>
      </Card>

      {/* Workflow Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {workflow.status === 'running' ? (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleWorkflowAction('pause')}
                >
                  <Pause className="h-4 w-4 mr-2" />
                  Pause
                </Button>
              ) : workflow.status === 'paused' ? (
                <Button 
                  size="sm"
                  onClick={() => handleWorkflowAction('resume')}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Resume
                </Button>
              ) : null}
              
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleWorkflowAction('restart')}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Restart
              </Button>
              
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleWorkflowAction('cancel')}
              >
                <Square className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
            
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span className="flex items-center gap-1">
                <Timer className="h-4 w-4" />
                {workflow.stages.reduce((total, stage) => total + (stage.actualDuration || stage.estimatedDuration), 0)} min total
              </span>
              <span className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {new Set(workflow.stages.map(s => s.assignedTo).filter(Boolean)).size} assignees
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Workflow Stages */}
      <Card>
        <CardHeader>
          <CardTitle>Workflow Stages</CardTitle>
          <CardDescription>
            Track progress through each stage of the due diligence process
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="space-y-0">
            {workflow.stages.map((stage, index) => (
              <div key={stage.id}>
                <div 
                  className={`p-4 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                    selectedStage === stage.id ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                  onClick={() => handleStageClick(stage.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        {getStageIcon(stage)}
                        <span className="text-sm font-medium text-gray-500">
                          {stage.order}
                        </span>
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{stage.name}</h4>
                          {getStageTypeIcon(stage.type)}
                          <Badge variant="outline" className="text-xs">
                            {stage.type}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                          {stage.assignedTo && (
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {stage.assignedTo}
                            </span>
                          )}
                          
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {stage.actualDuration 
                              ? `${formatDuration(stage.actualDuration)} (est. ${formatDuration(stage.estimatedDuration)})`
                              : `Est. ${formatDuration(stage.estimatedDuration)}`
                            }
                          </span>
                          
                          {stage.startedAt && (
                            <span>Started: {formatDate(stage.startedAt)}</span>
                          )}
                          
                          {stage.completedAt && (
                            <span>Completed: {formatDate(stage.completedAt)}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {stage.status === 'running' && (
                        <div className="flex gap-1">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleStageAction(stage.id, 'complete')
                            }}
                          >
                            Complete
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleStageAction(stage.id, 'skip')
                            }}
                          >
                            Skip
                          </Button>
                        </div>
                      )}
                      
                      {stage.status === 'pending' && stage.id === workflow.currentStageId && (
                        <Button 
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleStageAction(stage.id, 'start')
                          }}
                        >
                          <Play className="h-4 w-4 mr-2" />
                          Start
                        </Button>
                      )}
                      
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleStageAction(stage.id, 'view')
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Stage Details (Expanded) */}
                  {selectedStage === stage.id && (
                    <div className="mt-4 pt-4 border-t bg-white rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <h5 className="font-medium mb-2">Stage Information</h5>
                          <div className="space-y-1 text-sm text-gray-600">
                            <div>Type: {stage.type}</div>
                            <div>Status: {stage.status}</div>
                            <div>Order: {stage.order}</div>
                          </div>
                        </div>
                        
                        <div>
                          <h5 className="font-medium mb-2">Timing</h5>
                          <div className="space-y-1 text-sm text-gray-600">
                            <div>Estimated: {formatDuration(stage.estimatedDuration)}</div>
                            {stage.actualDuration && (
                              <div>Actual: {formatDuration(stage.actualDuration)}</div>
                            )}
                            {stage.startedAt && (
                              <div>Started: {formatDate(stage.startedAt)}</div>
                            )}
                            {stage.completedAt && (
                              <div>Completed: {formatDate(stage.completedAt)}</div>
                            )}
                          </div>
                        </div>
                        
                        <div>
                          <h5 className="font-medium mb-2">Assignment</h5>
                          <div className="space-y-1 text-sm text-gray-600">
                            {stage.assignedTo ? (
                              <div>Assigned to: {stage.assignedTo}</div>
                            ) : (
                              <div>Not assigned</div>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 mt-4">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Stage
                        </Button>
                        <Button variant="outline" size="sm">
                          <User className="h-4 w-4 mr-2" />
                          Reassign
                        </Button>
                        <Button variant="outline" size="sm">
                          <AlertCircle className="h-4 w-4 mr-2" />
                          Add Note
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Stage Connector */}
                {index < workflow.stages.length - 1 && (
                  <div className="flex justify-center py-2">
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
