import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Shield, 
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  BarChart3,
  Settings,
  RefreshCw,
  Download,
  Bell,
  Activity,
  Target,
  Users,
  Database,
  TrendingUp,
  TrendingDown,
  Eye,
  Calendar,
  Zap
} from 'lucide-react'

// Import sub-components
import { ComplianceMonitoringDashboard } from './compliance-monitoring-dashboard'
import { ComplianceDocumentManager } from './compliance-document-manager'
import { ComplianceReportingDashboard } from './compliance-reporting-dashboard'

interface ComplianceSystemStatus {
  dealId: string
  isActive: boolean
  lastHealthCheck: string
  services: {
    compliance: { status: 'HEALTHY' | 'DEGRADED' | 'DOWN'; lastCheck: string }
    monitoring: { status: 'HEALTHY' | 'DEGRADED' | 'DOWN'; lastCheck: string }
    reporting: { status: 'HEALTHY' | 'DEGRADED' | 'DOWN'; lastCheck: string }
    documents: { status: 'HEALTHY' | 'DEGRADED' | 'DOWN'; lastCheck: string }
    integration: { status: 'HEALTHY' | 'DEGRADED' | 'DOWN'; lastCheck: string }
  }
  metrics: {
    totalRequirements: number
    activeAlerts: number
    documentsProcessed: number
    reportsGenerated: number
    systemUptime: number
  }
}

interface ComplianceOverview {
  dealId: string
  dealName: string
  summary: {
    totalRequirements: number
    completedRequirements: number
    overdueRequirements: number
    criticalAlerts: number
    riskScore: number
    completionPercentage: number
  }
  recentActivity: Array<{
    id: string
    type: 'REQUIREMENT_COMPLETED' | 'DOCUMENT_UPLOADED' | 'ALERT_CREATED' | 'REPORT_GENERATED'
    title: string
    description: string
    timestamp: string
    user: string
  }>
  upcomingDeadlines: Array<{
    id: string
    name: string
    dueDate: string
    daysRemaining: number
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  }>
}

interface ComplianceDashboardProps {
  dealId: string
  onInitializeCompliance?: () => void
  onConfigureSystem?: () => void
}

export function ComplianceDashboard({
  dealId,
  onInitializeCompliance,
  onConfigureSystem
}: ComplianceDashboardProps) {
  const [overview, setOverview] = useState<ComplianceOverview | null>(null)
  const [systemStatus, setSystemStatus] = useState<ComplianceSystemStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockOverview: ComplianceOverview = {
      dealId,
      dealName: 'Technology M&A Transaction',
      summary: {
        totalRequirements: 24,
        completedRequirements: 16,
        overdueRequirements: 3,
        criticalAlerts: 2,
        riskScore: 45,
        completionPercentage: 67
      },
      recentActivity: [
        {
          id: 'activity-1',
          type: 'REQUIREMENT_COMPLETED',
          title: 'HSR Filing Completed',
          description: 'Hart-Scott-Rodino notification filing has been completed and submitted',
          timestamp: '2024-02-15T10:30:00Z',
          user: 'John Doe'
        },
        {
          id: 'activity-2',
          type: 'DOCUMENT_UPLOADED',
          title: 'Financial Statements Uploaded',
          description: 'Audited financial statements for 2023 have been uploaded and are under review',
          timestamp: '2024-02-15T09:15:00Z',
          user: 'Jane Smith'
        },
        {
          id: 'activity-3',
          type: 'ALERT_CREATED',
          title: 'Critical Deadline Alert',
          description: 'SEC 8-K filing deadline is approaching in 2 days',
          timestamp: '2024-02-15T08:45:00Z',
          user: 'System'
        },
        {
          id: 'activity-4',
          type: 'REPORT_GENERATED',
          title: 'Weekly Compliance Report',
          description: 'Automated weekly compliance status report has been generated',
          timestamp: '2024-02-14T17:00:00Z',
          user: 'System'
        }
      ],
      upcomingDeadlines: [
        {
          id: 'deadline-1',
          name: 'SEC 8-K Filing',
          dueDate: '2024-02-17T17:00:00Z',
          daysRemaining: 2,
          priority: 'CRITICAL'
        },
        {
          id: 'deadline-2',
          name: 'Proxy Statement Filing',
          dueDate: '2024-02-20T17:00:00Z',
          daysRemaining: 5,
          priority: 'HIGH'
        },
        {
          id: 'deadline-3',
          name: 'CFIUS Declaration',
          dueDate: '2024-02-25T17:00:00Z',
          daysRemaining: 10,
          priority: 'MEDIUM'
        }
      ]
    }

    const mockSystemStatus: ComplianceSystemStatus = {
      dealId,
      isActive: true,
      lastHealthCheck: '2024-02-15T11:00:00Z',
      services: {
        compliance: { status: 'HEALTHY', lastCheck: '2024-02-15T11:00:00Z' },
        monitoring: { status: 'HEALTHY', lastCheck: '2024-02-15T11:00:00Z' },
        reporting: { status: 'HEALTHY', lastCheck: '2024-02-15T11:00:00Z' },
        documents: { status: 'HEALTHY', lastCheck: '2024-02-15T11:00:00Z' },
        integration: { status: 'HEALTHY', lastCheck: '2024-02-15T11:00:00Z' }
      },
      metrics: {
        totalRequirements: 24,
        activeAlerts: 5,
        documentsProcessed: 48,
        reportsGenerated: 12,
        systemUptime: 99.9
      }
    }

    setOverview(mockOverview)
    setSystemStatus(mockSystemStatus)
    setLoading(false)
  }, [dealId])

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'REQUIREMENT_COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'DOCUMENT_UPLOADED':
        return <FileText className="h-4 w-4 text-blue-600" />
      case 'ALERT_CREATED':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'REPORT_GENERATED':
        return <BarChart3 className="h-4 w-4 text-purple-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'HIGH':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'LOW':
        return 'text-green-600 bg-green-50 border-green-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getServiceStatusIcon = (status: string) => {
    switch (status) {
      case 'HEALTHY':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'DEGRADED':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'DOWN':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString()
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false)
    }, 1000)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!overview || !systemStatus) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Compliance System Not Initialized</h3>
          <p className="text-gray-600 mb-4">
            Initialize the compliance management system to start tracking regulatory requirements
          </p>
          <Button onClick={onInitializeCompliance}>
            <Zap className="h-4 w-4 mr-2" />
            Initialize Compliance System
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Compliance Management
              </CardTitle>
              <CardDescription>
                {overview.dealName} - Comprehensive compliance tracking and management
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={onConfigureSystem}>
                <Settings className="h-4 w-4 mr-2" />
                Configure
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Status
            <Badge className={systemStatus.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
              {systemStatus.isActive ? 'Active' : 'Inactive'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {Object.entries(systemStatus.services).map(([service, status]) => (
              <div key={service} className="flex items-center gap-2 p-3 border rounded-lg">
                {getServiceStatusIcon(status.status)}
                <div>
                  <div className="font-medium text-sm capitalize">{service}</div>
                  <div className="text-xs text-gray-500">{status.status}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Completion</p>
                    <p className="text-3xl font-bold">{overview.summary.completionPercentage}%</p>
                    <p className="text-sm text-gray-500">
                      {overview.summary.completedRequirements} of {overview.summary.totalRequirements}
                    </p>
                  </div>
                  <Target className="h-8 w-8 text-blue-600" />
                </div>
                <Progress value={overview.summary.completionPercentage} className="mt-3" />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Risk Score</p>
                    <p className="text-3xl font-bold">{overview.summary.riskScore}</p>
                    <p className="text-sm text-gray-500">out of 100</p>
                  </div>
                  <Activity className="h-8 w-8 text-orange-600" />
                </div>
                <div className="flex items-center mt-2">
                  {overview.summary.riskScore < 50 ? (
                    <TrendingDown className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingUp className="h-4 w-4 text-red-600" />
                  )}
                  <span className="text-sm text-gray-500 ml-1">vs last week</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Critical Alerts</p>
                    <p className="text-3xl font-bold text-red-600">{overview.summary.criticalAlerts}</p>
                    <p className="text-sm text-gray-500">require attention</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Overdue</p>
                    <p className="text-3xl font-bold text-orange-600">{overview.summary.overdueRequirements}</p>
                    <p className="text-sm text-gray-500">requirements</p>
                  </div>
                  <Clock className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest compliance activities and updates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {overview.recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3 p-3 border rounded-lg">
                      {getActivityIcon(activity.type)}
                      <div className="flex-1">
                        <h5 className="font-medium text-sm">{activity.title}</h5>
                        <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                        <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                          <span>{formatDate(activity.timestamp)} at {formatTime(activity.timestamp)}</span>
                          <span>•</span>
                          <span>{activity.user}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Deadlines */}
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Deadlines</CardTitle>
                <CardDescription>Critical deadlines requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {overview.upcomingDeadlines.map((deadline) => (
                    <div key={deadline.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h5 className="font-medium text-sm">{deadline.name}</h5>
                          <Badge className={`text-xs ${getPriorityColor(deadline.priority)}`}>
                            {deadline.priority}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Calendar className="h-3 w-3" />
                          <span>Due: {formatDate(deadline.dueDate)}</span>
                          <span className={deadline.daysRemaining < 0 ? 'text-red-600' : ''}>
                            ({deadline.daysRemaining < 0 ? 'Overdue' : `${deadline.daysRemaining} days`})
                          </span>
                        </div>
                      </div>
                      
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="monitoring">
          <ComplianceMonitoringDashboard
            dealId={dealId}
            onAlertAction={(alertId, action) => console.log('Alert action:', alertId, action)}
            onConfigureMonitoring={() => console.log('Configure monitoring')}
          />
        </TabsContent>

        <TabsContent value="documents">
          <ComplianceDocumentManager
            dealId={dealId}
            onDocumentUploaded={(doc) => console.log('Document uploaded:', doc)}
            onDocumentReviewed={(docId, approved) => console.log('Document reviewed:', docId, approved)}
          />
        </TabsContent>

        <TabsContent value="reports">
          <ComplianceReportingDashboard
            dealId={dealId}
            onCreateReport={() => console.log('Create report')}
            onEditReport={(report) => console.log('Edit report:', report)}
            onDeleteReport={(reportId) => console.log('Delete report:', reportId)}
            onScheduleReport={(report) => console.log('Schedule report:', report)}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
