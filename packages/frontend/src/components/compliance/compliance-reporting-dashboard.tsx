import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Download,
  Calendar,
  Clock,
  BarChart3,
  TrendingUp,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  Share,
  Settings,
  FileSpreadsheet,
  Globe,
  Lock,
  CheckCircle,
  AlertCircle,
  Loader2,
  Users,
  Target,
  Activity
} from 'lucide-react'

interface ComplianceReport {
  id: string
  name: string
  type: 'COMPLIANCE_STATUS' | 'DEADLINE_TRACKER' | 'APPROVAL_STATUS' | 'DOCUMENT_STATUS' | 'RISK_ASSESSMENT' | 'AUDIT_TRAIL' | 'EXECUTIVE_SUMMARY'
  format: 'PDF' | 'EXCEL' | 'CSV' | 'HTML' | 'JSON'
  dealId?: string
  generatedDate?: string
  reportUrl?: string
  visibility: 'PRIVATE' | 'TEAM' | 'ORGANIZATION' | 'PUBLIC'
  isScheduled: boolean
  schedule?: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
    time: string
    recipients: string[]
    isActive: boolean
  }
  createdBy: string
  createdByName: string
  createdAt: string
}

interface ReportTemplate {
  id: string
  name: string
  description: string
  type: string
  format: string
  isPublic: boolean
  usageCount: number
}

interface ComplianceReportingDashboardProps {
  dealId?: string
  onCreateReport?: () => void
  onEditReport?: (report: ComplianceReport) => void
  onDeleteReport?: (reportId: string) => void
  onScheduleReport?: (report: ComplianceReport) => void
}

export function ComplianceReportingDashboard({
  dealId,
  onCreateReport,
  onEditReport,
  onDeleteReport,
  onScheduleReport
}: ComplianceReportingDashboardProps) {
  const [reports, setReports] = useState<ComplianceReport[]>([])
  const [templates, setTemplates] = useState<ReportTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<'all' | 'scheduled' | 'generated'>('all')
  const [selectedReports, setSelectedReports] = useState<Set<string>>(new Set())

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockReports: ComplianceReport[] = [
      {
        id: 'report-1',
        name: 'Weekly Compliance Status',
        type: 'COMPLIANCE_STATUS',
        format: 'PDF',
        dealId: dealId || 'deal-1',
        generatedDate: '2024-02-15T10:30:00Z',
        reportUrl: '/reports/weekly-compliance-status.pdf',
        visibility: 'TEAM',
        isScheduled: true,
        schedule: {
          frequency: 'WEEKLY',
          time: '09:00',
          recipients: ['<EMAIL>', '<EMAIL>'],
          isActive: true
        },
        createdBy: 'user-1',
        createdByName: 'John Doe',
        createdAt: '2024-02-01T09:00:00Z'
      },
      {
        id: 'report-2',
        name: 'Executive Summary - Q1 2024',
        type: 'EXECUTIVE_SUMMARY',
        format: 'PDF',
        dealId: dealId || 'deal-1',
        generatedDate: '2024-02-14T16:45:00Z',
        reportUrl: '/reports/executive-summary-q1.pdf',
        visibility: 'ORGANIZATION',
        isScheduled: false,
        createdBy: 'user-2',
        createdByName: 'Jane Smith',
        createdAt: '2024-02-14T16:00:00Z'
      },
      {
        id: 'report-3',
        name: 'Audit Trail Report',
        type: 'AUDIT_TRAIL',
        format: 'EXCEL',
        dealId: dealId || 'deal-1',
        generatedDate: '2024-02-13T14:20:00Z',
        reportUrl: '/reports/audit-trail.xlsx',
        visibility: 'PRIVATE',
        isScheduled: false,
        createdBy: 'user-1',
        createdByName: 'John Doe',
        createdAt: '2024-02-13T14:00:00Z'
      },
      {
        id: 'report-4',
        name: 'Risk Assessment Dashboard',
        type: 'RISK_ASSESSMENT',
        format: 'HTML',
        dealId: dealId || 'deal-1',
        visibility: 'TEAM',
        isScheduled: true,
        schedule: {
          frequency: 'MONTHLY',
          time: '08:00',
          recipients: ['<EMAIL>'],
          isActive: true
        },
        createdBy: 'user-3',
        createdByName: 'Mike Johnson',
        createdAt: '2024-02-12T11:30:00Z'
      }
    ]

    const mockTemplates: ReportTemplate[] = [
      {
        id: 'template-1',
        name: 'Standard Compliance Report',
        description: 'Comprehensive compliance status report',
        type: 'COMPLIANCE_STATUS',
        format: 'PDF',
        isPublic: true,
        usageCount: 45
      },
      {
        id: 'template-2',
        name: 'Executive Dashboard',
        description: 'High-level executive summary',
        type: 'EXECUTIVE_SUMMARY',
        format: 'PDF',
        isPublic: true,
        usageCount: 32
      },
      {
        id: 'template-3',
        name: 'Audit Trail Export',
        description: 'Detailed audit trail for compliance review',
        type: 'AUDIT_TRAIL',
        format: 'EXCEL',
        isPublic: false,
        usageCount: 18
      }
    ]

    setReports(mockReports)
    setTemplates(mockTemplates)
    setLoading(false)
  }, [dealId])

  // Filter reports
  const filteredReports = reports.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === 'all' || report.type === typeFilter
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'scheduled' && report.isScheduled) ||
                         (statusFilter === 'generated' && report.generatedDate)
    
    return matchesSearch && matchesType && matchesStatus
  })

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'PDF':
        return <FileText className="h-4 w-4 text-red-600" />
      case 'EXCEL':
        return <FileSpreadsheet className="h-4 w-4 text-green-600" />
      case 'CSV':
        return <FileSpreadsheet className="h-4 w-4 text-blue-600" />
      case 'HTML':
        return <Globe className="h-4 w-4 text-orange-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'PUBLIC':
      case 'ORGANIZATION':
        return <Globe className="h-3 w-3" />
      case 'TEAM':
        return <Users className="h-3 w-3" />
      default:
        return <Lock className="h-3 w-3" />
    }
  }

  const getTypeDisplayName = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const handleDownload = (report: ComplianceReport) => {
    if (report.reportUrl) {
      window.open(report.reportUrl, '_blank')
    }
  }

  const handleGenerateFromTemplate = (template: ReportTemplate) => {
    console.log('Generate from template:', template.id)
    // Implementation would open dialog to configure report generation
  }

  const handleBulkAction = (action: 'download' | 'delete' | 'share') => {
    const selectedIds = Array.from(selectedReports)
    console.log('Bulk action:', action, selectedIds)
    
    if (action === 'delete') {
      selectedIds.forEach(id => onDeleteReport?.(id))
      setReports(prev => prev.filter(r => !selectedIds.includes(r.id)))
      setSelectedReports(new Set())
    }
  }

  const scheduledReports = reports.filter(r => r.isScheduled).length
  const generatedReports = reports.filter(r => r.generatedDate).length

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Compliance Reports
              </CardTitle>
              <CardDescription>
                Generate, schedule, and manage compliance reports
              </CardDescription>
            </div>
            
            <Button onClick={onCreateReport}>
              <Plus className="h-4 w-4 mr-2" />
              Create Report
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-3xl font-bold">{reports.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Scheduled</p>
                <p className="text-3xl font-bold">{scheduledReports}</p>
              </div>
              <Calendar className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Generated</p>
                <p className="text-3xl font-bold">{generatedReports}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Templates</p>
                <p className="text-3xl font-bold">{templates.length}</p>
              </div>
              <Settings className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Reports List */}
        <div className="lg:col-span-2 space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <Input
                      placeholder="Search reports..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-64"
                    />
                  </div>
                  
                  <select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    className="px-3 py-2 border rounded-md"
                  >
                    <option value="all">All Types</option>
                    <option value="COMPLIANCE_STATUS">Compliance Status</option>
                    <option value="EXECUTIVE_SUMMARY">Executive Summary</option>
                    <option value="AUDIT_TRAIL">Audit Trail</option>
                    <option value="RISK_ASSESSMENT">Risk Assessment</option>
                  </select>
                  
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value as any)}
                    className="px-3 py-2 border rounded-md"
                  >
                    <option value="all">All Reports</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="generated">Generated</option>
                  </select>
                </div>
                
                {selectedReports.size > 0 && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                      {selectedReports.size} selected
                    </span>
                    <Button variant="outline" size="sm" onClick={() => handleBulkAction('download')}>
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleBulkAction('delete')}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Reports */}
          <Card>
            <CardContent className="p-0">
              {filteredReports.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No reports found</h3>
                  <p className="text-gray-600 mb-4">
                    Create your first compliance report to get started
                  </p>
                  <Button onClick={onCreateReport}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Report
                  </Button>
                </div>
              ) : (
                <div className="divide-y">
                  {filteredReports.map((report) => (
                    <div key={report.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-start gap-3">
                        <input
                          type="checkbox"
                          checked={selectedReports.has(report.id)}
                          onChange={(e) => {
                            const newSet = new Set(selectedReports)
                            if (e.target.checked) {
                              newSet.add(report.id)
                            } else {
                              newSet.delete(report.id)
                            }
                            setSelectedReports(newSet)
                          }}
                          className="mt-1"
                        />
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium">{report.name}</h4>
                                {report.isScheduled && (
                                  <Badge variant="outline" className="text-xs">
                                    <Calendar className="h-3 w-3 mr-1" />
                                    Scheduled
                                  </Badge>
                                )}
                                {report.generatedDate && (
                                  <Badge variant="outline" className="text-xs text-green-600">
                                    <CheckCircle className="h-3 w-3 mr-1" />
                                    Generated
                                  </Badge>
                                )}
                              </div>
                              
                              <div className="flex items-center gap-4 text-xs text-gray-500 mb-2">
                                <div className="flex items-center gap-1">
                                  {getFormatIcon(report.format)}
                                  <span>{report.format}</span>
                                </div>
                                
                                <div className="flex items-center gap-1">
                                  {getVisibilityIcon(report.visibility)}
                                  <span>{report.visibility}</span>
                                </div>
                                
                                <span>{getTypeDisplayName(report.type)}</span>
                                
                                <span>Created {formatDate(report.createdAt)}</span>
                                
                                {report.generatedDate && (
                                  <span>Generated {formatDate(report.generatedDate)}</span>
                                )}
                              </div>
                              
                              <div className="text-xs text-gray-500">
                                Created by {report.createdByName}
                                {report.schedule && (
                                  <span> • {report.schedule.frequency} at {report.schedule.time}</span>
                                )}
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-1 ml-4">
                              {report.generatedDate && report.reportUrl && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDownload(report)}
                                >
                                  <Download className="h-4 w-4" />
                                </Button>
                              )}
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onEditReport?.(report)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onScheduleReport?.(report)}
                              >
                                <Calendar className="h-4 w-4" />
                              </Button>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onDeleteReport?.(report.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Templates Sidebar */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Report Templates</CardTitle>
              <CardDescription>
                Quick start with pre-built templates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {templates.map((template) => (
                <div key={template.id} className="p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h5 className="font-medium text-sm">{template.name}</h5>
                        {template.isPublic && (
                          <Badge variant="outline" className="text-xs">
                            Public
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                        {template.description}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{template.format}</span>
                        <span>{template.usageCount} uses</span>
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full mt-2"
                    onClick={() => handleGenerateFromTemplate(template)}
                  >
                    Use Template
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Target className="h-4 w-4 mr-2" />
                Executive Summary
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Activity className="h-4 w-4 mr-2" />
                Risk Assessment
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Clock className="h-4 w-4 mr-2" />
                Deadline Tracker
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <FileText className="h-4 w-4 mr-2" />
                Audit Trail
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
