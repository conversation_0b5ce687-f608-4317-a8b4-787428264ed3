import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Shield, 
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Bell,
  Eye,
  Calendar,
  BarChart3,
  Activity,
  Target,
  Zap,
  RefreshCw,
  Settings,
  Filter,
  Download,
  Users,
  FileText,
  AlertCircle,
  XCircle
} from 'lucide-react'

interface MonitoringDashboard {
  dealId: string
  summary: {
    totalRequirements: number
    completedRequirements: number
    overdueRequirements: number
    criticalAlerts: number
    riskScore: number
  }
  alerts: ComplianceAlert[]
  deadlines: Array<{
    requirementId: string
    requirementName: string
    dueDate: string
    daysRemaining: number
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    status: string
  }>
  riskFactors: Array<{
    factor: string
    impact: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    description: string
    recommendation: string
  }>
  trends: {
    completionRate: Array<{ date: string; rate: number }>
    riskScore: Array<{ date: string; score: number }>
    alertVolume: Array<{ date: string; count: number }>
  }
}

interface ComplianceAlert {
  id: string
  type: string
  severity: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL'
  title: string
  message: string
  triggerDate: string
  status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED' | 'DISMISSED'
  complianceStatusId?: string
  acknowledgedBy?: string
  acknowledgedDate?: string
}

interface ComplianceMonitoringDashboardProps {
  dealId: string
  onAlertAction?: (alertId: string, action: 'acknowledge' | 'resolve' | 'dismiss') => void
  onConfigureMonitoring?: () => void
}

export function ComplianceMonitoringDashboard({
  dealId,
  onAlertAction,
  onConfigureMonitoring
}: ComplianceMonitoringDashboardProps) {
  const [dashboard, setDashboard] = useState<MonitoringDashboard | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [selectedTimeframe, setSelectedTimeframe] = useState<'7d' | '30d' | '90d'>('30d')

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockDashboard: MonitoringDashboard = {
      dealId,
      summary: {
        totalRequirements: 24,
        completedRequirements: 16,
        overdueRequirements: 3,
        criticalAlerts: 2,
        riskScore: 45
      },
      alerts: [
        {
          id: 'alert-1',
          type: 'DEADLINE_APPROACHING',
          severity: 'CRITICAL',
          title: 'Critical Deadline Approaching',
          message: 'HSR filing deadline is in 2 days',
          triggerDate: '2024-02-15T10:30:00Z',
          status: 'ACTIVE',
          complianceStatusId: 'status-1'
        },
        {
          id: 'alert-2',
          type: 'DOCUMENT_MISSING',
          severity: 'WARNING',
          title: 'Missing Required Document',
          message: 'Financial statements not yet submitted',
          triggerDate: '2024-02-14T16:45:00Z',
          status: 'ACKNOWLEDGED',
          complianceStatusId: 'status-2',
          acknowledgedBy: 'Jane Smith',
          acknowledgedDate: '2024-02-15T09:00:00Z'
        },
        {
          id: 'alert-3',
          type: 'APPROVAL_DELAY',
          severity: 'ERROR',
          title: 'Approval Delay',
          message: 'Document review taking longer than expected',
          triggerDate: '2024-02-13T14:20:00Z',
          status: 'ACTIVE',
          complianceStatusId: 'status-3'
        }
      ],
      deadlines: [
        {
          requirementId: 'req-1',
          requirementName: 'HSR Notification Filing',
          dueDate: '2024-02-17T17:00:00Z',
          daysRemaining: 2,
          riskLevel: 'CRITICAL',
          status: 'IN_PROGRESS'
        },
        {
          requirementId: 'req-2',
          requirementName: 'SEC 8-K Filing',
          dueDate: '2024-02-20T17:00:00Z',
          daysRemaining: 5,
          riskLevel: 'HIGH',
          status: 'NOT_STARTED'
        },
        {
          requirementId: 'req-3',
          requirementName: 'Proxy Statement',
          dueDate: '2024-02-25T17:00:00Z',
          daysRemaining: 10,
          riskLevel: 'MEDIUM',
          status: 'IN_PROGRESS'
        }
      ],
      riskFactors: [
        {
          factor: 'Overdue Requirements',
          impact: 'HIGH',
          description: '3 requirements are overdue',
          recommendation: 'Prioritize completion of overdue requirements'
        },
        {
          factor: 'Critical Alerts',
          impact: 'CRITICAL',
          description: '2 critical alerts require immediate attention',
          recommendation: 'Address critical alerts immediately'
        },
        {
          factor: 'Resource Constraints',
          impact: 'MEDIUM',
          description: 'Limited team capacity for document review',
          recommendation: 'Consider additional resources or prioritization'
        }
      ],
      trends: {
        completionRate: [
          { date: '2024-02-01', rate: 20 },
          { date: '2024-02-08', rate: 35 },
          { date: '2024-02-15', rate: 67 }
        ],
        riskScore: [
          { date: '2024-02-01', score: 75 },
          { date: '2024-02-08', score: 60 },
          { date: '2024-02-15', score: 45 }
        ],
        alertVolume: [
          { date: '2024-02-01', count: 8 },
          { date: '2024-02-08', count: 5 },
          { date: '2024-02-15', count: 3 }
        ]
      }
    }

    setDashboard(mockDashboard)
    setLoading(false)
  }, [dealId])

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'ERROR':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'WARNING':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case 'INFO':
        return <Bell className="h-4 w-4 text-blue-600" />
      default:
        return <Bell className="h-4 w-4 text-gray-600" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'ERROR':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'WARNING':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'INFO':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'CRITICAL':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'HIGH':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'LOW':
        return 'text-green-600 bg-green-50 border-green-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'CRITICAL':
        return 'text-red-600'
      case 'HIGH':
        return 'text-orange-600'
      case 'MEDIUM':
        return 'text-yellow-600'
      case 'LOW':
        return 'text-green-600'
      default:
        return 'text-gray-600'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false)
    }, 1000)
  }

  const handleAlertAction = (alertId: string, action: 'acknowledge' | 'resolve' | 'dismiss') => {
    onAlertAction?.(alertId, action)
    
    // Update local state
    setDashboard(prev => {
      if (!prev) return prev
      
      return {
        ...prev,
        alerts: prev.alerts.map(alert => 
          alert.id === alertId 
            ? { 
                ...alert, 
                status: action === 'acknowledge' ? 'ACKNOWLEDGED' : 
                        action === 'resolve' ? 'RESOLVED' : 'DISMISSED',
                acknowledgedDate: action === 'acknowledge' ? new Date().toISOString() : alert.acknowledgedDate
              }
            : alert
        )
      }
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!dashboard) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Monitoring Data</h3>
          <p className="text-gray-600">Unable to load compliance monitoring data</p>
        </CardContent>
      </Card>
    )
  }

  const completionPercentage = Math.round((dashboard.summary.completedRequirements / dashboard.summary.totalRequirements) * 100)

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Compliance Monitoring
              </CardTitle>
              <CardDescription>
                Real-time compliance status and risk monitoring
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={onConfigureMonitoring}>
                <Settings className="h-4 w-4 mr-2" />
                Configure
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completion</p>
                <p className="text-3xl font-bold">{completionPercentage}%</p>
                <p className="text-sm text-gray-500">
                  {dashboard.summary.completedRequirements} of {dashboard.summary.totalRequirements}
                </p>
              </div>
              <Target className="h-8 w-8 text-blue-600" />
            </div>
            <Progress value={completionPercentage} className="mt-3" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Risk Score</p>
                <p className="text-3xl font-bold">{dashboard.summary.riskScore}</p>
                <p className="text-sm text-gray-500">out of 100</p>
              </div>
              <Activity className="h-8 w-8 text-orange-600" />
            </div>
            <div className="flex items-center mt-2">
              {dashboard.summary.riskScore < 50 ? (
                <TrendingDown className="h-4 w-4 text-green-600" />
              ) : (
                <TrendingUp className="h-4 w-4 text-red-600" />
              )}
              <span className="text-sm text-gray-500 ml-1">vs last week</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical Alerts</p>
                <p className="text-3xl font-bold text-red-600">{dashboard.summary.criticalAlerts}</p>
                <p className="text-sm text-gray-500">require attention</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Overdue</p>
                <p className="text-3xl font-bold text-orange-600">{dashboard.summary.overdueRequirements}</p>
                <p className="text-sm text-gray-500">requirements</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Alerts</p>
                <p className="text-3xl font-bold">{dashboard.alerts.filter(a => a.status === 'ACTIVE').length}</p>
                <p className="text-sm text-gray-500">total alerts</p>
              </div>
              <Bell className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Alerts */}
        <Card>
          <CardHeader>
            <CardTitle>Active Alerts</CardTitle>
            <CardDescription>Alerts requiring immediate attention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dashboard.alerts.filter(alert => alert.status === 'ACTIVE').map((alert) => (
                <div key={alert.id} className="p-3 border rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      {getSeverityIcon(alert.severity)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h5 className="font-medium text-sm">{alert.title}</h5>
                          <Badge className={`text-xs ${getSeverityColor(alert.severity)}`}>
                            {alert.severity}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{alert.message}</p>
                        <p className="text-xs text-gray-500">
                          Triggered: {formatDate(alert.triggerDate)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1 ml-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleAlertAction(alert.id, 'acknowledge')}
                      >
                        <CheckCircle className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleAlertAction(alert.id, 'resolve')}
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
              
              {dashboard.alerts.filter(alert => alert.status === 'ACTIVE').length === 0 && (
                <div className="text-center py-8">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">No active alerts</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Deadlines */}
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Deadlines</CardTitle>
            <CardDescription>Critical deadlines to monitor</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dashboard.deadlines.slice(0, 5).map((deadline) => (
                <div key={deadline.requirementId} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h5 className="font-medium text-sm">{deadline.requirementName}</h5>
                        <Badge className={`text-xs ${getRiskLevelColor(deadline.riskLevel)}`}>
                          {deadline.riskLevel}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>Due: {formatDate(deadline.dueDate)}</span>
                        <span className={deadline.daysRemaining < 0 ? 'text-red-600' : ''}>
                          {deadline.daysRemaining < 0 ? 'Overdue' : `${deadline.daysRemaining} days`}
                        </span>
                      </div>
                    </div>
                    
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Risk Factors */}
      <Card>
        <CardHeader>
          <CardTitle>Risk Factors</CardTitle>
          <CardDescription>Identified compliance risks and recommendations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {dashboard.riskFactors.map((factor, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className={`h-4 w-4 ${getImpactColor(factor.impact)}`} />
                  <h5 className="font-medium">{factor.factor}</h5>
                  <Badge variant="outline" className={`text-xs ${getImpactColor(factor.impact)}`}>
                    {factor.impact}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-2">{factor.description}</p>
                <p className="text-xs text-blue-600">{factor.recommendation}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
