import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  FileText, 
  Upload,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Search,
  Filter,
  MoreHorizontal,
  Trash2,
  Edit,
  Share,
  Archive,
  Shield,
  Calendar,
  User,
  FileCheck,
  FileX
} from 'lucide-react'

interface ComplianceDocument {
  id: string
  name: string
  type: string
  description?: string
  fileUrl: string
  fileSize: number
  mimeType: string
  version: number
  status: 'DRAFT' | 'SUBMITTED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'REQUIRES_REVISION'
  complianceStatusId: string
  submittedBy: string
  submittedByName: string
  submittedDate: string
  reviewedBy?: string
  reviewedByName?: string
  reviewedDate?: string
  approvedBy?: string
  approvedByName?: string
  approvedDate?: string
  rejectionReason?: string
  retentionDate?: string
}

interface DocumentRequirement {
  id: string
  name: string
  type: string
  description: string
  isRequired: boolean
  acceptedFormats: string[]
  maxFileSize: number
  hasDocument: boolean
  documents: ComplianceDocument[]
  dueDate?: string
  status: 'PENDING' | 'SUBMITTED' | 'APPROVED' | 'REJECTED'
}

interface ComplianceDocumentManagerProps {
  dealId: string
  complianceStatusId?: string
  onDocumentUploaded?: (document: ComplianceDocument) => void
  onDocumentReviewed?: (documentId: string, approved: boolean) => void
}

export function ComplianceDocumentManager({
  dealId,
  complianceStatusId,
  onDocumentUploaded,
  onDocumentReviewed
}: ComplianceDocumentManagerProps) {
  const [requirements, setRequirements] = useState<DocumentRequirement[]>([])
  const [documents, setDocuments] = useState<ComplianceDocument[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set())
  const [uploadingFiles, setUploadingFiles] = useState<Set<string>>(new Set())

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    const mockRequirements: DocumentRequirement[] = [
      {
        id: 'req-1',
        name: 'Audited Financial Statements',
        type: 'FINANCIAL_STATEMENT',
        description: 'Last 3 years of audited financial statements',
        isRequired: true,
        acceptedFormats: ['PDF'],
        maxFileSize: 50 * 1024 * 1024,
        hasDocument: true,
        documents: [],
        dueDate: '2024-02-20T17:00:00Z',
        status: 'APPROVED'
      },
      {
        id: 'req-2',
        name: 'Tax Returns',
        type: 'TAX_RETURN',
        description: 'Corporate tax returns for last 3 years',
        isRequired: true,
        acceptedFormats: ['PDF'],
        maxFileSize: 50 * 1024 * 1024,
        hasDocument: false,
        documents: [],
        dueDate: '2024-02-18T17:00:00Z',
        status: 'PENDING'
      },
      {
        id: 'req-3',
        name: 'Material Contracts',
        type: 'CONTRACT',
        description: 'All material customer and supplier contracts',
        isRequired: true,
        acceptedFormats: ['PDF', 'DOC', 'DOCX'],
        maxFileSize: 50 * 1024 * 1024,
        hasDocument: true,
        documents: [],
        dueDate: '2024-02-22T17:00:00Z',
        status: 'SUBMITTED'
      }
    ]

    const mockDocuments: ComplianceDocument[] = [
      {
        id: 'doc-1',
        name: 'Financial_Statements_2023.pdf',
        type: 'FINANCIAL_STATEMENT',
        description: 'Audited financial statements for 2023',
        fileUrl: '/documents/financial-statements-2023.pdf',
        fileSize: 2048576,
        mimeType: 'application/pdf',
        version: 1,
        status: 'APPROVED',
        complianceStatusId: 'status-1',
        submittedBy: 'user-1',
        submittedByName: 'John Doe',
        submittedDate: '2024-02-10T14:30:00Z',
        reviewedBy: 'user-2',
        reviewedByName: 'Jane Smith',
        reviewedDate: '2024-02-12T09:15:00Z',
        approvedBy: 'user-2',
        approvedByName: 'Jane Smith',
        approvedDate: '2024-02-12T09:15:00Z'
      },
      {
        id: 'doc-2',
        name: 'Customer_Contracts_Summary.pdf',
        type: 'CONTRACT',
        description: 'Summary of all customer contracts',
        fileUrl: '/documents/customer-contracts.pdf',
        fileSize: 1536000,
        mimeType: 'application/pdf',
        version: 2,
        status: 'UNDER_REVIEW',
        complianceStatusId: 'status-2',
        submittedBy: 'user-3',
        submittedByName: 'Mike Johnson',
        submittedDate: '2024-02-14T16:45:00Z',
        reviewedBy: 'user-2',
        reviewedByName: 'Jane Smith',
        reviewedDate: '2024-02-15T10:30:00Z'
      },
      {
        id: 'doc-3',
        name: 'Insurance_Policies.pdf',
        type: 'INSURANCE',
        description: 'Current insurance policies',
        fileUrl: '/documents/insurance-policies.pdf',
        fileSize: 1024000,
        mimeType: 'application/pdf',
        version: 1,
        status: 'REJECTED',
        complianceStatusId: 'status-3',
        submittedBy: 'user-1',
        submittedByName: 'John Doe',
        submittedDate: '2024-02-13T11:20:00Z',
        reviewedBy: 'user-2',
        reviewedByName: 'Jane Smith',
        reviewedDate: '2024-02-14T14:00:00Z',
        rejectionReason: 'Document is incomplete - missing liability coverage details'
      }
    ]

    setRequirements(mockRequirements)
    setDocuments(mockDocuments)
    setLoading(false)
  }, [dealId, complianceStatusId])

  // Filter documents
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || doc.status === statusFilter
    const matchesType = typeFilter === 'all' || doc.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'UNDER_REVIEW':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'SUBMITTED':
        return <FileCheck className="h-4 w-4 text-blue-600" />
      case 'REQUIRES_REVISION':
        return <FileX className="h-4 w-4 text-orange-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'REJECTED':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'UNDER_REVIEW':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'SUBMITTED':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'REQUIRES_REVISION':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(1)} MB`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const isOverdue = (dueDate?: string) => {
    if (!dueDate) return false
    return new Date(dueDate) < new Date()
  }

  const handleFileUpload = async (requirementId: string, files: FileList) => {
    const file = files[0]
    if (!file) return

    const uploadId = `${requirementId}-${Date.now()}`
    setUploadingFiles(prev => new Set([...prev, uploadId]))

    try {
      // Simulate file upload
      await new Promise(resolve => setTimeout(resolve, 2000))

      const newDocument: ComplianceDocument = {
        id: `doc-${Date.now()}`,
        name: file.name,
        type: requirements.find(r => r.id === requirementId)?.type || 'UNKNOWN',
        description: `Uploaded document: ${file.name}`,
        fileUrl: URL.createObjectURL(file),
        fileSize: file.size,
        mimeType: file.type,
        version: 1,
        status: 'SUBMITTED',
        complianceStatusId: complianceStatusId || 'default',
        submittedBy: 'current-user',
        submittedByName: 'Current User',
        submittedDate: new Date().toISOString()
      }

      setDocuments(prev => [...prev, newDocument])
      onDocumentUploaded?.(newDocument)
    } catch (error) {
      console.error('Upload failed:', error)
    } finally {
      setUploadingFiles(prev => {
        const newSet = new Set(prev)
        newSet.delete(uploadId)
        return newSet
      })
    }
  }

  const handleDocumentAction = (documentId: string, action: string) => {
    const document = documents.find(d => d.id === documentId)
    if (!document) return

    switch (action) {
      case 'download':
        window.open(document.fileUrl, '_blank')
        break
      case 'approve':
        onDocumentReviewed?.(documentId, true)
        setDocuments(prev => prev.map(d => 
          d.id === documentId 
            ? { ...d, status: 'APPROVED', approvedDate: new Date().toISOString() }
            : d
        ))
        break
      case 'reject':
        onDocumentReviewed?.(documentId, false)
        setDocuments(prev => prev.map(d => 
          d.id === documentId 
            ? { ...d, status: 'REJECTED' }
            : d
        ))
        break
      case 'delete':
        setDocuments(prev => prev.filter(d => d.id !== documentId))
        break
    }
  }

  const completedRequirements = requirements.filter(r => r.status === 'APPROVED').length
  const totalRequirements = requirements.length
  const completionPercentage = totalRequirements > 0 ? Math.round((completedRequirements / totalRequirements) * 100) : 0

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Compliance Documents
              </CardTitle>
              <CardDescription>
                Manage and track compliance document submissions
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className="text-sm text-gray-600">Completion</div>
                <div className="flex items-center gap-2">
                  <Progress value={completionPercentage} className="w-20" />
                  <span className="text-sm font-medium">{completionPercentage}%</span>
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Document Requirements */}
      <Card>
        <CardHeader>
          <CardTitle>Document Requirements</CardTitle>
          <CardDescription>
            Upload required documents for compliance verification
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {requirements.map((requirement) => (
              <div key={requirement.id} className="p-4 border rounded-lg">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-medium">{requirement.name}</h4>
                      {requirement.isRequired && (
                        <Badge variant="outline" className="text-xs">Required</Badge>
                      )}
                      {getStatusIcon(requirement.status)}
                      {requirement.dueDate && isOverdue(requirement.dueDate) && (
                        <Badge variant="destructive" className="text-xs">Overdue</Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{requirement.description}</p>
                    
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>Formats: {requirement.acceptedFormats.join(', ')}</span>
                      <span>Max size: {formatFileSize(requirement.maxFileSize)}</span>
                      {requirement.dueDate && (
                        <span>Due: {formatDate(requirement.dueDate)}</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    {requirement.hasDocument ? (
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                    ) : (
                      <div>
                        <input
                          type="file"
                          id={`upload-${requirement.id}`}
                          className="hidden"
                          accept={requirement.acceptedFormats.map(f => `.${f.toLowerCase()}`).join(',')}
                          onChange={(e) => e.target.files && handleFileUpload(requirement.id, e.target.files)}
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById(`upload-${requirement.id}`)?.click()}
                          disabled={uploadingFiles.has(requirement.id)}
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          {uploadingFiles.has(requirement.id) ? 'Uploading...' : 'Upload'}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Document List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Submitted Documents</CardTitle>
            
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Status</option>
                <option value="SUBMITTED">Submitted</option>
                <option value="UNDER_REVIEW">Under Review</option>
                <option value="APPROVED">Approved</option>
                <option value="REJECTED">Rejected</option>
              </select>
              
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Types</option>
                <option value="FINANCIAL_STATEMENT">Financial</option>
                <option value="CONTRACT">Contracts</option>
                <option value="TAX_RETURN">Tax Returns</option>
                <option value="INSURANCE">Insurance</option>
              </select>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-0">
          {filteredDocuments.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No documents found</h3>
              <p className="text-gray-600">
                {searchTerm ? 'No documents match your search criteria.' : 'No documents have been uploaded yet.'}
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {filteredDocuments.map((document) => (
                <div key={document.id} className="p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <FileText className="h-5 w-5 text-blue-600 mt-1" />
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">{document.name}</h4>
                          <Badge className={`text-xs ${getStatusColor(document.status)}`}>
                            {document.status.replace('_', ' ')}
                          </Badge>
                          <span className="text-xs text-gray-500">v{document.version}</span>
                        </div>
                        
                        {document.description && (
                          <p className="text-sm text-gray-600 mb-2">{document.description}</p>
                        )}
                        
                        <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
                          <div>
                            <span className="font-medium">Submitted:</span> {formatDate(document.submittedDate)} by {document.submittedByName}
                          </div>
                          <div>
                            <span className="font-medium">Size:</span> {formatFileSize(document.fileSize)}
                          </div>
                          {document.reviewedDate && (
                            <div>
                              <span className="font-medium">Reviewed:</span> {formatDate(document.reviewedDate)} by {document.reviewedByName}
                            </div>
                          )}
                          {document.rejectionReason && (
                            <div className="col-span-2 text-red-600">
                              <span className="font-medium">Rejection reason:</span> {document.rejectionReason}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDocumentAction(document.id, 'download')}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      
                      {document.status === 'UNDER_REVIEW' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDocumentAction(document.id, 'approve')}
                          >
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDocumentAction(document.id, 'reject')}
                          >
                            <XCircle className="h-4 w-4 text-red-600" />
                          </Button>
                        </>
                      )}
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDocumentAction(document.id, 'delete')}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
