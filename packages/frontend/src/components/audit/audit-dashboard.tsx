import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { 
  Shield, 
  Activity, 
  AlertTriangle, 
  Users, 
  Eye,
  Download,
  Filter,
  Calendar,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { AuditOverview } from './audit-overview'
import { AuditEventsList } from './audit-events-list'
import { AuditStatistics } from './audit-statistics'
import { SecurityEvents } from './security-events'
import { UserActivity } from './user-activity'
import { useAuditData } from '@/hooks/use-audit-data'
import { DateRangePicker } from '@/components/ui/date-range-picker'

interface AuditDashboardProps {
  className?: string
}

export function AuditDashboard({ className }: AuditDashboardProps) {
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
    to: new Date()
  })
  const [activeTab, setActiveTab] = useState('overview')
  const [refreshInterval, setRefreshInterval] = useState<number | null>(30000) // 30 seconds

  const { 
    statistics, 
    recentEvents, 
    securityEvents,
    loading, 
    error, 
    refetch 
  } = useAuditData(dateRange)

  // Auto-refresh functionality
  useEffect(() => {
    if (!refreshInterval) return

    const interval = setInterval(() => {
      refetch()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [refreshInterval, refetch])

  const handleDateRangeChange = (range: { from: Date; to: Date }) => {
    setDateRange(range)
  }

  const handleExportData = () => {
    // This would trigger an export of audit data
    console.log('Exporting audit data for range:', dateRange)
  }

  const getSecurityAlertLevel = () => {
    if (!securityEvents) return 'low'
    
    const criticalCount = securityEvents.filter(e => e.severity === 'critical').length
    const highCount = securityEvents.filter(e => e.severity === 'high').length
    
    if (criticalCount > 0) return 'critical'
    if (highCount > 5) return 'high'
    if (highCount > 0) return 'medium'
    return 'low'
  }

  const getAlertColor = (level: string) => {
    switch (level) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-green-100 text-green-800 border-green-200'
    }
  }

  if (loading && !statistics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Failed to Load Audit Data</h3>
            <p className="mb-4">There was an error loading the audit dashboard.</p>
            <Button variant="outline" onClick={refetch}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const securityLevel = getSecurityAlertLevel()

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Audit Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor system activity, security events, and compliance
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <DateRangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
          />
          
          <Button variant="outline" onClick={handleExportData}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          
          <Button variant="outline" onClick={refetch}>
            <Activity className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Security Alert Banner */}
      {securityLevel !== 'low' && (
        <Card className={`border-2 ${getAlertColor(securityLevel)}`}>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5" />
              <div className="flex-1">
                <h3 className="font-semibold">Security Alert - {securityLevel.toUpperCase()}</h3>
                <p className="text-sm">
                  {securityLevel === 'critical' 
                    ? 'Critical security events detected. Immediate attention required.'
                    : securityLevel === 'high'
                    ? 'Multiple high-severity security events detected.'
                    : 'Security events require review.'
                  }
                </p>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setActiveTab('security')}
              >
                View Details
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Events</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.totalEvents || 0}</div>
            <p className="text-xs text-muted-foreground">
              Last {Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))} days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statistics?.successRate ? `${statistics.successRate.toFixed(1)}%` : '0%'}
            </div>
            <p className="text-xs text-muted-foreground">
              Successful operations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Security Events</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{securityEvents?.length || 0}</div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.topUsers?.length || 0}</div>
            <p className="text-xs text-muted-foreground">
              Users with activity
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Auto-refresh Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">Auto-refresh:</span>
              </div>
              
              <div className="flex items-center gap-2">
                {[
                  { label: 'Off', value: null },
                  { label: '30s', value: 30000 },
                  { label: '1m', value: 60000 },
                  { label: '5m', value: 300000 }
                ].map(option => (
                  <Button
                    key={option.label}
                    variant={refreshInterval === option.value ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setRefreshInterval(option.value)}
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>
            
            <div className="text-sm text-gray-500">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <AuditOverview 
            statistics={statistics}
            recentEvents={recentEvents}
            dateRange={dateRange}
          />
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <AuditEventsList 
            dateRange={dateRange}
            onEventSelect={(event) => console.log('Selected event:', event)}
          />
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <SecurityEvents 
            events={securityEvents}
            dateRange={dateRange}
            onEventSelect={(event) => console.log('Selected security event:', event)}
          />
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <UserActivity 
            dateRange={dateRange}
            topUsers={statistics?.topUsers}
          />
        </TabsContent>

        <TabsContent value="statistics" className="space-y-4">
          <AuditStatistics 
            statistics={statistics}
            dateRange={dateRange}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
