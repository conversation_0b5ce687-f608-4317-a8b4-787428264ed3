import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Activity, 
  Shield, 
  Users, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Eye
} from 'lucide-react'
import { AuditStatistics, AuditEvent } from '@/types/audit'

interface AuditOverviewProps {
  statistics?: AuditStatistics
  recentEvents?: AuditEvent[]
  dateRange: { from: Date; to: Date }
}

export function AuditOverview({ statistics, recentEvents, dateRange }: AuditOverviewProps) {
  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'authentication':
        return <Users className="h-4 w-4" />
      case 'authorization':
        return <Shield className="h-4 w-4" />
      case 'security_event':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  const getEventColor = (success: boolean, severity?: string) => {
    if (!success) return 'text-red-600'
    if (severity === 'high' || severity === 'critical') return 'text-orange-600'
    return 'text-green-600'
  }

  const getSeverityBadge = (severity: string) => {
    const colors = {
      critical: 'bg-red-100 text-red-800',
      high: 'bg-orange-100 text-orange-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    }
    
    return (
      <Badge className={colors[severity as keyof typeof colors] || colors.low}>
        {severity}
      </Badge>
    )
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return `${diffDays}d ago`
  }

  return (
    <div className="space-y-6">
      {/* Activity Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Event Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {statistics?.eventsByType && Object.entries(statistics.eventsByType).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getEventIcon(type)}
                    <span className="text-sm capitalize">{type.replace('_', ' ')}</span>
                  </div>
                  <Badge variant="outline">{count}</Badge>
                </div>
              ))}
              
              {(!statistics?.eventsByType || Object.keys(statistics.eventsByType).length === 0) && (
                <p className="text-sm text-gray-500">No events in selected period</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Security Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {statistics?.eventsBySeverity && Object.entries(statistics.eventsBySeverity).map(([severity, count]) => (
                <div key={severity} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{severity}</span>
                  <div className="flex items-center gap-2">
                    {getSeverityBadge(severity)}
                    <span className="text-sm">{count}</span>
                  </div>
                </div>
              ))}
              
              {(!statistics?.eventsBySeverity || Object.keys(statistics.eventsBySeverity).length === 0) && (
                <p className="text-sm text-gray-500">No security events</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Top Active Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {statistics?.topUsers?.slice(0, 5).map((user, index) => (
                <div key={user.userId} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs">
                      {index + 1}
                    </div>
                    <span className="text-sm">{user.userId}</span>
                  </div>
                  <Badge variant="outline">{user.eventCount}</Badge>
                </div>
              ))}
              
              {(!statistics?.topUsers || statistics.topUsers.length === 0) && (
                <p className="text-sm text-gray-500">No user activity</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Events */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Events</CardTitle>
              <CardDescription>
                Latest audit events from the past {Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))} days
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentEvents?.slice(0, 10).map((event) => (
              <div key={event.id} className="flex items-center gap-4 p-3 border rounded-lg hover:bg-gray-50">
                <div className={`${getEventColor(event.success, event.severity)}`}>
                  {getEventIcon(event.eventType)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">{event.action}</span>
                    <Badge variant="outline" className="text-xs">
                      {event.resourceType}
                    </Badge>
                    {event.severity && getSeverityBadge(event.severity)}
                  </div>
                  
                  <div className="text-xs text-gray-500 mt-1">
                    User: {event.userId} • {event.ipAddress}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {event.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  
                  <div className="text-xs text-gray-500">
                    {formatTimeAgo(new Date(event.timestamp))}
                  </div>
                </div>
              </div>
            ))}
            
            {(!recentEvents || recentEvents.length === 0) && (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Recent Events</h3>
                <p className="text-gray-600">
                  No audit events found for the selected time period.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* System Health Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">System Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Success Rate</span>
                <div className="flex items-center gap-2">
                  {(statistics?.successRate || 0) >= 95 ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className="font-medium">
                    {statistics?.successRate ? `${statistics.successRate.toFixed(1)}%` : '0%'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Failed Operations</span>
                <span className="font-medium text-red-600">
                  {statistics?.totalEvents ? 
                    Math.round(statistics.totalEvents * (1 - (statistics.successRate || 0) / 100)) : 0
                  }
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Security Incidents</span>
                <span className="font-medium text-orange-600">
                  {statistics?.eventsBySeverity?.high || 0}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Compliance Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Audit Coverage</span>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="font-medium">100%</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Data Retention</span>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="font-medium">Compliant</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Access Monitoring</span>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="font-medium">Active</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
