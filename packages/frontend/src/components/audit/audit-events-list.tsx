import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Filter, 
  Download, 
  Eye,
  ChevronLeft,
  ChevronRight,
  Calendar,
  User,
  Shield,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'
import { AuditEvent } from '@/types/audit'
import { useAuditEvents } from '@/hooks/use-audit-events'

interface AuditEventsListProps {
  dateRange: { from: Date; to: Date }
  onEventSelect?: (event: AuditEvent) => void
}

export function AuditEventsList({ dateRange, onEventSelect }: AuditEventsListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedEventType, setSelectedEventType] = useState<string>('all')
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all')
  const [selectedSuccess, setSelectedSuccess] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(50)

  const { 
    events, 
    loading, 
    error, 
    totalCount,
    refetch 
  } = useAuditEvents({
    dateRange,
    searchTerm,
    eventType: selectedEventType === 'all' ? undefined : selectedEventType,
    severity: selectedSeverity === 'all' ? undefined : selectedSeverity,
    success: selectedSuccess === 'all' ? undefined : selectedSuccess === 'true',
    page: currentPage,
    pageSize
  })

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'authentication':
        return <User className="h-4 w-4" />
      case 'authorization':
        return <Shield className="h-4 w-4" />
      case 'security_event':
        return <AlertTriangle className="h-4 w-4" />
      case 'data_access':
      case 'data_modification':
        return <Activity className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  const getEventColor = (success: boolean, severity?: string) => {
    if (!success) return 'text-red-600'
    if (severity === 'high' || severity === 'critical') return 'text-orange-600'
    return 'text-green-600'
  }

  const getSeverityBadge = (severity: string) => {
    const colors = {
      critical: 'bg-red-100 text-red-800',
      high: 'bg-orange-100 text-orange-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    }
    
    return (
      <Badge className={colors[severity as keyof typeof colors] || colors.low}>
        {severity}
      </Badge>
    )
  }

  const formatDateTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(new Date(date))
  }

  const handleExport = () => {
    // This would trigger an export of the filtered events
    console.log('Exporting filtered events')
  }

  const totalPages = Math.ceil((totalCount || 0) / pageSize)

  return (
    <div className="space-y-4">
      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search events by action, user, or resource..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Button variant="outline" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
            
            <div className="flex items-center gap-4">
              <select
                value={selectedEventType}
                onChange={(e) => setSelectedEventType(e.target.value)}
                className="border rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Event Types</option>
                <option value="authentication">Authentication</option>
                <option value="authorization">Authorization</option>
                <option value="data_access">Data Access</option>
                <option value="data_modification">Data Modification</option>
                <option value="security_event">Security Event</option>
                <option value="system_configuration">System Configuration</option>
              </select>
              
              <select
                value={selectedSeverity}
                onChange={(e) => setSelectedSeverity(e.target.value)}
                className="border rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Severities</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
              
              <select
                value={selectedSuccess}
                onChange={(e) => setSelectedSuccess(e.target.value)}
                className="border rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Results</option>
                <option value="true">Success Only</option>
                <option value="false">Failures Only</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Events List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Audit Events</CardTitle>
              <CardDescription>
                {totalCount ? `${totalCount} events found` : 'No events found'}
              </CardDescription>
            </div>
            
            <Button variant="outline" onClick={refetch}>
              <Activity className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-600">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
              <p>Failed to load audit events</p>
              <Button variant="outline" onClick={refetch} className="mt-2">
                Try Again
              </Button>
            </div>
          ) : events && events.length > 0 ? (
            <div className="space-y-2">
              {events.map((event) => (
                <div
                  key={event.id}
                  className="flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => onEventSelect?.(event)}
                >
                  <div className={`${getEventColor(event.success, event.severity)}`}>
                    {getEventIcon(event.eventType)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">{event.action}</span>
                      <Badge variant="outline" className="text-xs">
                        {event.resourceType}
                      </Badge>
                      {event.severity && getSeverityBadge(event.severity)}
                    </div>
                    
                    <div className="text-sm text-gray-600">
                      <span>User: {event.userId}</span>
                      {event.resourceId && (
                        <span> • Resource: {event.resourceId}</span>
                      )}
                      {event.ipAddress && (
                        <span> • IP: {event.ipAddress}</span>
                      )}
                    </div>
                    
                    {event.details && (
                      <div className="text-xs text-gray-500 mt-1 truncate">
                        {typeof event.details === 'string' 
                          ? event.details 
                          : JSON.stringify(event.details)
                        }
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="text-right">
                      <div className="flex items-center gap-1 mb-1">
                        {event.success ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-600" />
                        )}
                        <span className="text-xs">
                          {event.success ? 'Success' : 'Failed'}
                        </span>
                      </div>
                      
                      <div className="text-xs text-gray-500 flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDateTime(event.timestamp)}
                      </div>
                    </div>
                    
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Events Found</h3>
              <p className="text-gray-600">
                No audit events match your current filter criteria.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount || 0)} of {totalCount} events
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    )
                  })}
                  
                  {totalPages > 5 && (
                    <>
                      <span className="text-gray-400">...</span>
                      <Button
                        variant={currentPage === totalPages ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(totalPages)}
                      >
                        {totalPages}
                      </Button>
                    </>
                  )}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
