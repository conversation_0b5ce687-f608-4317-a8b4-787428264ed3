import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Edit, 
  Trash2, 
  Users, 
  Shield, 
  Settings,
  Lock,
  Calendar,
  Tag,
  Key,
  UserPlus,
  UserMinus,
  History
} from 'lucide-react'
import { Role, User } from '@/types/rbac'
import { RoleEditDialog } from './role-edit-dialog'
import { RoleDeleteDialog } from './role-delete-dialog'
import { UserAssignmentDialog } from './user-assignment-dialog'

interface RoleDetailsProps {
  role: Role
  onUpdate: (roleData: any) => Promise<void>
  onDelete: () => Promise<void>
  onUserAssign: (userId: string) => Promise<void>
  onUserRevoke: (userId: string) => Promise<void>
}

export function RoleDetails({ 
  role, 
  onUpdate, 
  onDelete, 
  onUserAssign, 
  onUserRevoke 
}: RoleDetailsProps) {
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showUserAssignDialog, setShowUserAssignDialog] = useState(false)

  const getRoleIcon = () => {
    if (role.isSystem) return <Shield className="h-5 w-5 text-blue-600" />
    return <Users className="h-5 w-5 text-gray-600" />
  }

  const getPermissionsByCategory = () => {
    const categories: Record<string, string[]> = {}
    
    role.permissions?.forEach(permission => {
      const [resource] = permission.split(':')
      if (!categories[resource]) {
        categories[resource] = []
      }
      categories[resource].push(permission)
    })
    
    return categories
  }

  const formatPermission = (permission: string) => {
    const [resource, action, scope] = permission.split(':')
    return {
      resource: resource.charAt(0).toUpperCase() + resource.slice(1),
      action: action.charAt(0).toUpperCase() + action.slice(1),
      scope: scope ? scope.charAt(0).toUpperCase() + scope.slice(1) : 'All'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getRoleIcon()}
              <div>
                <CardTitle className="flex items-center gap-2">
                  {role.name}
                  {role.isSystem && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Lock className="h-3 w-3" />
                      System
                    </Badge>
                  )}
                  {role.isDefault && (
                    <Badge variant="outline">Default</Badge>
                  )}
                </CardTitle>
                <CardDescription>{role.description}</CardDescription>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowEditDialog(true)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              
              {!role.isSystem && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Role Details Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Role Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Priority:</span>
                  <Badge variant="outline">{role.priority}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Category:</span>
                  <span className="text-sm font-medium">{role.category || 'Custom'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Created:</span>
                  <span className="text-sm">
                    {role.createdAt ? new Date(role.createdAt).toLocaleDateString() : 'Unknown'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Updated:</span>
                  <span className="text-sm">
                    {role.updatedAt ? new Date(role.updatedAt).toLocaleDateString() : 'Unknown'}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Usage Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Active Users:</span>
                  <span className="text-sm font-medium">{role.userCount || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Permissions:</span>
                  <span className="text-sm font-medium">{role.permissions?.length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Type:</span>
                  <span className="text-sm font-medium">
                    {role.isSystem ? 'System Role' : 'Custom Role'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tags */}
          {role.tags && role.tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  Tags
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {role.tags.map((tag) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Role Permissions</h3>
            <Badge variant="outline">
              {role.permissions?.length || 0} permissions
            </Badge>
          </div>

          <div className="space-y-4">
            {Object.entries(getPermissionsByCategory()).map(([category, permissions]) => (
              <Card key={category}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base capitalize">{category}</CardTitle>
                  <CardDescription>
                    {permissions.length} permission{permissions.length !== 1 ? 's' : ''}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {permissions.map((permission) => {
                      const formatted = formatPermission(permission)
                      return (
                        <div
                          key={permission}
                          className="p-3 border rounded-lg bg-gray-50"
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="font-medium text-sm">
                                {formatted.action} {formatted.resource}
                              </div>
                              <div className="text-xs text-gray-500">
                                Scope: {formatted.scope}
                              </div>
                            </div>
                            <Key className="h-4 w-4 text-gray-400" />
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            ))}

            {(!role.permissions || role.permissions.length === 0) && (
              <Card>
                <CardContent className="p-8 text-center">
                  <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Permissions</h3>
                  <p className="text-gray-600">
                    This role doesn't have any permissions assigned.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Users with this Role</h3>
            <Button
              size="sm"
              onClick={() => setShowUserAssignDialog(true)}
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Assign User
            </Button>
          </div>

          <Card>
            <CardContent className="p-6">
              {role.users && role.users.length > 0 ? (
                <div className="space-y-3">
                  {role.users.map((user: User) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <Users className="h-4 w-4 text-gray-600" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {user.firstName} {user.lastName}
                          </div>
                          <div className="text-sm text-gray-600">{user.email}</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{user.status}</Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onUserRevoke(user.id)}
                        >
                          <UserMinus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Users Assigned</h3>
                  <p className="text-gray-600 mb-4">
                    No users currently have this role assigned.
                  </p>
                  <Button onClick={() => setShowUserAssignDialog(true)}>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Assign First User
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Role Activity</h3>
            <Badge variant="outline">Recent changes</Badge>
          </div>

          <Card>
            <CardContent className="p-6">
              <div className="text-center py-8">
                <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Activity Log</h3>
                <p className="text-gray-600">
                  Activity logging will be available in a future update.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <RoleEditDialog
        role={role}
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSubmit={onUpdate}
      />

      <RoleDeleteDialog
        role={role}
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={onDelete}
      />

      <UserAssignmentDialog
        role={role}
        open={showUserAssignDialog}
        onOpenChange={setShowUserAssignDialog}
        onAssign={onUserAssign}
      />
    </div>
  )
}
