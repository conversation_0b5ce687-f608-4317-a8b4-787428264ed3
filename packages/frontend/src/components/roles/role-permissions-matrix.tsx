import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Filter, 
  Download, 
  Eye,
  EyeOff,
  Check,
  X,
  Shield,
  Users,
  RotateCcw
} from 'lucide-react'
import { Role } from '@/types/rbac'
import { useRoles } from '@/hooks/use-roles'
import { usePermissions } from '@/hooks/use-permissions'

interface RolePermissionsMatrixProps {
  onRoleSelect?: (role: Role) => void
  selectedRoleId?: string
}

export function RolePermissionsMatrix({ onRoleSelect, selectedRoleId }: RolePermissionsMatrixProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [showSystemRoles, setShowSystemRoles] = useState(true)
  const [showInheritedPermissions, setShowInheritedPermissions] = useState(false)
  const [compactView, setCompactView] = useState(false)

  const { roles, loading: rolesLoading } = useRoles()
  const { availablePermissions, permissionCategories } = usePermissions()

  // Filter and organize data
  const filteredRoles = useMemo(() => {
    if (!roles) return []
    
    return roles.filter(role => {
      const matchesSearch = role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           role.description?.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesSystemFilter = showSystemRoles || !role.isSystem
      
      return matchesSearch && matchesSystemFilter
    })
  }, [roles, searchTerm, showSystemRoles])

  const filteredPermissions = useMemo(() => {
    if (!availablePermissions) return []
    
    return availablePermissions.filter(permission => {
      const matchesCategory = selectedCategory === 'all' || permission.category === selectedCategory
      const matchesSearch = permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           permission.description?.toLowerCase().includes(searchTerm.toLowerCase())
      
      return matchesCategory && matchesSearch
    })
  }, [availablePermissions, selectedCategory, searchTerm])

  const hasPermission = (role: Role, permissionId: string): 'direct' | 'inherited' | 'none' => {
    // Check direct permissions
    if (role.permissions?.includes(permissionId)) {
      return 'direct'
    }
    
    // Check inherited permissions (if enabled)
    if (showInheritedPermissions && role.inheritedPermissions?.includes(permissionId)) {
      return 'inherited'
    }
    
    return 'none'
  }

  const exportMatrix = () => {
    // Create CSV data
    const headers = ['Role', 'Type', 'Priority', ...filteredPermissions.map(p => p.name)]
    const rows = filteredRoles.map(role => [
      role.name,
      role.isSystem ? 'System' : 'Custom',
      role.priority?.toString() || '0',
      ...filteredPermissions.map(permission => {
        const status = hasPermission(role, permission.id)
        return status === 'direct' ? 'Yes' : status === 'inherited' ? 'Inherited' : 'No'
      })
    ])

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n')

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `role-permissions-matrix-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  const resetFilters = () => {
    setSearchTerm('')
    setSelectedCategory('all')
    setShowSystemRoles(true)
    setShowInheritedPermissions(false)
  }

  if (rolesLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Role Permissions Matrix</CardTitle>
            <CardDescription>
              Comprehensive view of all roles and their permissions
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCompactView(!compactView)}
            >
              {compactView ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
              {compactView ? 'Detailed' : 'Compact'}
            </Button>
            
            <Button variant="ghost" size="sm" onClick={exportMatrix}>
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Filters */}
        <div className="space-y-4 mb-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search roles or permissions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border rounded-md px-3 py-2 text-sm"
            >
              <option value="all">All Categories</option>
              {permissionCategories?.map(category => (
                <option key={category.name} value={category.name}>
                  {category.displayName}
                </option>
              ))}
            </select>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={resetFilters}
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              Reset
            </Button>
          </div>
          
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={showSystemRoles}
                onChange={(e) => setShowSystemRoles(e.target.checked)}
                className="rounded"
              />
              Show System Roles
            </label>
            
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={showInheritedPermissions}
                onChange={(e) => setShowInheritedPermissions(e.target.checked)}
                className="rounded"
              />
              Show Inherited Permissions
            </label>
          </div>
        </div>

        {/* Matrix */}
        <div className="border rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="sticky left-0 bg-gray-50 px-4 py-3 text-left text-sm font-medium text-gray-900 border-r">
                    Role
                  </th>
                  {!compactView && (
                    <>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                        Type
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                        Priority
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                        Users
                      </th>
                    </>
                  )}
                  {filteredPermissions.map(permission => (
                    <th
                      key={permission.id}
                      className="px-2 py-3 text-center text-xs font-medium text-gray-900 min-w-[80px] border-l"
                      title={permission.description}
                    >
                      <div className="transform -rotate-45 origin-center whitespace-nowrap">
                        {permission.name}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRoles.map((role, roleIndex) => (
                  <tr
                    key={role.id}
                    className={`hover:bg-gray-50 cursor-pointer ${
                      selectedRoleId === role.id ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => onRoleSelect?.(role)}
                  >
                    <td className="sticky left-0 bg-white px-4 py-3 border-r">
                      <div className="flex items-center gap-2">
                        {role.isSystem ? (
                          <Shield className="h-4 w-4 text-blue-600" />
                        ) : (
                          <Users className="h-4 w-4 text-gray-600" />
                        )}
                        <div>
                          <div className="font-medium text-sm">{role.name}</div>
                          {!compactView && role.description && (
                            <div className="text-xs text-gray-500 truncate max-w-[200px]">
                              {role.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    
                    {!compactView && (
                      <>
                        <td className="px-4 py-3">
                          <Badge variant={role.isSystem ? "secondary" : "outline"} className="text-xs">
                            {role.isSystem ? 'System' : 'Custom'}
                          </Badge>
                        </td>
                        <td className="px-4 py-3 text-sm">
                          {role.priority || 0}
                        </td>
                        <td className="px-4 py-3 text-sm">
                          {role.userCount || 0}
                        </td>
                      </>
                    )}
                    
                    {filteredPermissions.map(permission => {
                      const status = hasPermission(role, permission.id)
                      return (
                        <td key={permission.id} className="px-2 py-3 text-center border-l">
                          {status === 'direct' && (
                            <Check className="h-4 w-4 text-green-600 mx-auto" title="Direct permission" />
                          )}
                          {status === 'inherited' && (
                            <Check className="h-4 w-4 text-blue-600 mx-auto" title="Inherited permission" />
                          )}
                          {status === 'none' && (
                            <X className="h-4 w-4 text-gray-300 mx-auto" title="No permission" />
                          )}
                        </td>
                      )
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Legend */}
        <div className="mt-4 flex items-center gap-6 text-sm">
          <div className="flex items-center gap-2">
            <Check className="h-4 w-4 text-green-600" />
            <span>Direct Permission</span>
          </div>
          {showInheritedPermissions && (
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-blue-600" />
              <span>Inherited Permission</span>
            </div>
          )}
          <div className="flex items-center gap-2">
            <X className="h-4 w-4 text-gray-300" />
            <span>No Permission</span>
          </div>
        </div>

        {/* Summary */}
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium">Total Roles</div>
              <div className="text-gray-600">{filteredRoles.length}</div>
            </div>
            <div>
              <div className="font-medium">System Roles</div>
              <div className="text-gray-600">
                {filteredRoles.filter(r => r.isSystem).length}
              </div>
            </div>
            <div>
              <div className="font-medium">Custom Roles</div>
              <div className="text-gray-600">
                {filteredRoles.filter(r => !r.isSystem).length}
              </div>
            </div>
            <div>
              <div className="font-medium">Permissions</div>
              <div className="text-gray-600">{filteredPermissions.length}</div>
            </div>
          </div>
        </div>

        {filteredRoles.length === 0 && (
          <div className="text-center py-8">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Roles Found</h3>
            <p className="text-gray-600">
              No roles match your current filter criteria.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
