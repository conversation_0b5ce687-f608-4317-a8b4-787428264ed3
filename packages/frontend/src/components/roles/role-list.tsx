import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Users, 
  Shield, 
  Settings,
  ChevronRight,
  Filter
} from 'lucide-react'
import { useRoles } from '@/hooks/use-roles'
import { Role } from '@/types/rbac'
import { RoleCreateDialog } from './role-create-dialog'
import { RoleEditDialog } from './role-edit-dialog'
import { RoleDeleteDialog } from './role-delete-dialog'

interface RoleListProps {
  onRoleSelect?: (role: Role) => void
  selectedRoleId?: string
}

export function RoleList({ onRoleSelect, selectedRoleId }: RoleListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [deletingRole, setDeletingRole] = useState<Role | null>(null)
  const [filterType, setFilterType] = useState<'all' | 'system' | 'custom'>('all')

  const { 
    roles, 
    loading, 
    error, 
    refetch,
    createRole,
    updateRole,
    deleteRole 
  } = useRoles()

  // Filter roles based on search term and type
  const filteredRoles = roles?.filter(role => {
    const matchesSearch = role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         role.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = filterType === 'all' || 
                       (filterType === 'system' && role.isSystem) ||
                       (filterType === 'custom' && !role.isSystem)
    
    return matchesSearch && matchesType
  }) || []

  const handleCreateRole = async (roleData: any) => {
    try {
      await createRole(roleData)
      setShowCreateDialog(false)
      refetch()
    } catch (error) {
      console.error('Failed to create role:', error)
    }
  }

  const handleUpdateRole = async (roleData: any) => {
    if (!editingRole) return
    
    try {
      await updateRole(editingRole.id, roleData)
      setEditingRole(null)
      refetch()
    } catch (error) {
      console.error('Failed to update role:', error)
    }
  }

  const handleDeleteRole = async () => {
    if (!deletingRole) return
    
    try {
      await deleteRole(deletingRole.id)
      setDeletingRole(null)
      refetch()
    } catch (error) {
      console.error('Failed to delete role:', error)
    }
  }

  const getRolePriorityColor = (priority: number) => {
    if (priority >= 80) return 'bg-red-100 text-red-800'
    if (priority >= 60) return 'bg-orange-100 text-orange-800'
    if (priority >= 40) return 'bg-yellow-100 text-yellow-800'
    return 'bg-green-100 text-green-800'
  }

  const getRoleIcon = (role: Role) => {
    if (role.isSystem) return <Shield className="h-4 w-4" />
    return <Users className="h-4 w-4" />
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Failed to load roles</p>
            <Button variant="outline" onClick={refetch} className="mt-2">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Roles</h2>
          <p className="text-muted-foreground">
            Manage user roles and permissions
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Role
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search roles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="border rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Roles</option>
                <option value="system">System Roles</option>
                <option value="custom">Custom Roles</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role List */}
      <div className="grid gap-4">
        {filteredRoles.map((role) => (
          <Card 
            key={role.id}
            className={`cursor-pointer transition-colors hover:bg-gray-50 ${
              selectedRoleId === role.id ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => onRoleSelect?.(role)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getRoleIcon(role)}
                    <div>
                      <h3 className="font-semibold">{role.name}</h3>
                      {role.description && (
                        <p className="text-sm text-gray-600">{role.description}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {role.isSystem && (
                      <Badge variant="secondary">System</Badge>
                    )}
                    {role.isDefault && (
                      <Badge variant="outline">Default</Badge>
                    )}
                    <Badge 
                      className={getRolePriorityColor(role.priority)}
                      variant="secondary"
                    >
                      Priority: {role.priority}
                    </Badge>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <div className="text-sm text-gray-500">
                    {role.userCount || 0} users
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        setEditingRole(role)
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    
                    {!role.isSystem && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          setDeletingRole(role)
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                    
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              {/* Role Permissions Preview */}
              <div className="mt-3 pt-3 border-t">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    {role.permissions?.length || 0} permissions
                  </div>
                  <div className="flex items-center space-x-1">
                    {role.permissions?.slice(0, 3).map((permission, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {permission.split(':')[0]}
                      </Badge>
                    ))}
                    {(role.permissions?.length || 0) > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{(role.permissions?.length || 0) - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredRoles.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No roles found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm ? 'No roles match your search criteria.' : 'Get started by creating your first role.'}
              </p>
              {!searchTerm && (
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Role
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Dialogs */}
      <RoleCreateDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSubmit={handleCreateRole}
      />

      {editingRole && (
        <RoleEditDialog
          role={editingRole}
          open={!!editingRole}
          onOpenChange={(open) => !open && setEditingRole(null)}
          onSubmit={handleUpdateRole}
        />
      )}

      {deletingRole && (
        <RoleDeleteDialog
          role={deletingRole}
          open={!!deletingRole}
          onOpenChange={(open) => !open && setDeletingRole(null)}
          onConfirm={handleDeleteRole}
        />
      )}
    </div>
  )
}
