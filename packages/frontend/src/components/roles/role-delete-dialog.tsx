import React, { useState } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  AlertTriangle, 
  Users, 
  Shield, 
  Lock,
  AlertCircle
} from 'lucide-react'
import { Role } from '@/types/rbac'

interface RoleDeleteDialogProps {
  role: Role
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => Promise<void>
}

export function RoleDeleteDialog({ role, open, onOpenChange, onConfirm }: RoleDeleteDialogProps) {
  const [loading, setLoading] = useState(false)
  const [confirmText, setConfirmText] = useState('')
  const [error, setError] = useState('')

  const handleConfirm = async () => {
    if (confirmText !== role.name) {
      setError('Role name does not match')
      return
    }

    setLoading(true)
    setError('')
    
    try {
      await onConfirm()
      setConfirmText('')
    } catch (error) {
      setError('Failed to delete role. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setConfirmText('')
    setError('')
    onOpenChange(false)
  }

  const canDelete = !role.isSystem && (role.userCount || 0) === 0
  const hasUsers = (role.userCount || 0) > 0

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete Role
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. Please review the details below.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Role Information */}
          <div className="p-4 border rounded-lg bg-gray-50">
            <div className="flex items-center gap-3 mb-2">
              {role.isSystem ? (
                <Shield className="h-5 w-5 text-blue-600" />
              ) : (
                <Users className="h-5 w-5 text-gray-600" />
              )}
              <div>
                <h3 className="font-semibold">{role.name}</h3>
                {role.description && (
                  <p className="text-sm text-gray-600">{role.description}</p>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {role.isSystem && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Lock className="h-3 w-3" />
                  System Role
                </Badge>
              )}
              {role.isDefault && (
                <Badge variant="outline">Default</Badge>
              )}
              <Badge variant="outline">
                {role.userCount || 0} users
              </Badge>
            </div>
          </div>

          {/* Warnings and Restrictions */}
          {role.isSystem && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start gap-2">
                <Lock className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-800">Cannot Delete System Role</h4>
                  <p className="text-sm text-red-700 mt-1">
                    System roles are protected and cannot be deleted to maintain platform security and functionality.
                  </p>
                </div>
              </div>
            </div>
          )}

          {hasUsers && !role.isSystem && (
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-start gap-2">
                <Users className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-amber-800">Role Has Active Users</h4>
                  <p className="text-sm text-amber-700 mt-1">
                    This role is currently assigned to {role.userCount} user{role.userCount !== 1 ? 's' : ''}. 
                    You must remove all users from this role before it can be deleted.
                  </p>
                </div>
              </div>
            </div>
          )}

          {canDelete && (
            <>
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-red-800">Permanent Deletion</h4>
                    <p className="text-sm text-red-700 mt-1">
                      This will permanently delete the role and all its associated permissions. 
                      This action cannot be undone.
                    </p>
                  </div>
                </div>
              </div>

              {/* Confirmation Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Type <code className="bg-gray-100 px-1 rounded">{role.name}</code> to confirm deletion:
                </label>
                <input
                  type="text"
                  value={confirmText}
                  onChange={(e) => {
                    setConfirmText(e.target.value)
                    if (error) setError('')
                  }}
                  placeholder="Enter role name"
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  disabled={loading}
                />
                {error && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {error}
                  </p>
                )}
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          
          {canDelete ? (
            <Button
              variant="destructive"
              onClick={handleConfirm}
              disabled={loading || confirmText !== role.name}
            >
              {loading ? 'Deleting...' : 'Delete Role'}
            </Button>
          ) : (
            <Button variant="destructive" disabled>
              Cannot Delete
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
