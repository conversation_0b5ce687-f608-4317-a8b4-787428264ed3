import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Plus, 
  X, 
  Shield, 
  Users, 
  Settings,
  Check,
  AlertCircle,
  Lock
} from 'lucide-react'
import { usePermissions } from '@/hooks/use-permissions'
import { Role, UpdateRoleData } from '@/types/rbac'

interface RoleEditDialogProps {
  role: Role
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: UpdateRoleData) => Promise<void>
}

export function RoleEditDialog({ role, open, onOpenChange, onSubmit }: RoleEditDialogProps) {
  const [formData, setFormData] = useState<UpdateRoleData>({
    name: '',
    description: '',
    permissions: [],
    priority: 50,
    color: '#3B82F6',
    icon: 'users',
    category: 'custom',
    tags: []
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [selectedPermissions, setSelectedPermissions] = useState<Set<string>>(new Set())
  const [newTag, setNewTag] = useState('')

  const { availablePermissions, permissionCategories } = usePermissions()

  // Initialize form data when role changes
  useEffect(() => {
    if (role) {
      setFormData({
        name: role.name,
        description: role.description || '',
        permissions: role.permissions || [],
        priority: role.priority || 50,
        color: role.color || '#3B82F6',
        icon: role.icon || 'users',
        category: role.category || 'custom',
        tags: role.tags || []
      })
      setSelectedPermissions(new Set(role.permissions || []))
    }
  }, [role])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    const newErrors: Record<string, string> = {}
    
    if (!formData.name?.trim()) {
      newErrors.name = 'Role name is required'
    }
    
    if (selectedPermissions.size === 0) {
      newErrors.permissions = 'At least one permission is required'
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    setLoading(true)
    try {
      await onSubmit({
        ...formData,
        permissions: Array.from(selectedPermissions)
      })
      setErrors({})
    } catch (error) {
      setErrors({ submit: 'Failed to update role. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handlePermissionToggle = (permission: string) => {
    const newSelected = new Set(selectedPermissions)
    if (newSelected.has(permission)) {
      newSelected.delete(permission)
    } else {
      newSelected.add(permission)
    }
    setSelectedPermissions(newSelected)
    
    // Clear permission error if permissions are selected
    if (newSelected.size > 0 && errors.permissions) {
      setErrors({ ...errors, permissions: '' })
    }
  }

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData({
        ...formData,
        tags: [...(formData.tags || []), newTag.trim()]
      })
      setNewTag('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData({
      ...formData,
      tags: formData.tags?.filter(tag => tag !== tagToRemove) || []
    })
  }

  const getPermissionsByCategory = (category: string) => {
    return availablePermissions?.filter(p => p.category === category) || []
  }

  const isSystemRole = role?.isSystem
  const canEditBasicInfo = !isSystemRole
  const canEditPermissions = !isSystemRole

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Edit Role: {role?.name}
            {isSystemRole && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Lock className="h-3 w-3" />
                System Role
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            {isSystemRole 
              ? 'System roles have limited editing capabilities to maintain security.'
              : 'Update role information, permissions, and settings.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-4">
              {isSystemRole && (
                <div className="p-3 bg-amber-50 border border-amber-200 rounded-md">
                  <p className="text-sm text-amber-700 flex items-center">
                    <Lock className="h-4 w-4 mr-1" />
                    System roles have restricted editing to maintain security and stability.
                  </p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Role Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => {
                      setFormData({ ...formData, name: e.target.value })
                      if (errors.name) setErrors({ ...errors, name: '' })
                    }}
                    placeholder="Enter role name"
                    className={errors.name ? 'border-red-500' : ''}
                    disabled={!canEditBasicInfo}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {errors.name}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Input
                    id="priority"
                    type="number"
                    min="0"
                    max="100"
                    value={formData.priority}
                    onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 0 })}
                    placeholder="50"
                    disabled={!canEditBasicInfo}
                  />
                  <p className="text-xs text-gray-500">Higher numbers = higher priority</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Describe what this role is for..."
                  rows={3}
                  disabled={!canEditBasicInfo}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <select
                    id="category"
                    value={formData.category}
                    onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                    className="w-full border rounded-md px-3 py-2"
                    disabled={!canEditBasicInfo}
                  >
                    <option value="custom">Custom</option>
                    <option value="management">Management</option>
                    <option value="operations">Operations</option>
                    <option value="support">Support</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color">Color</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="color"
                      type="color"
                      value={formData.color}
                      onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                      className="w-16 h-10"
                      disabled={!canEditBasicInfo}
                    />
                    <Input
                      value={formData.color}
                      onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                      placeholder="#3B82F6"
                      className="flex-1"
                      disabled={!canEditBasicInfo}
                    />
                  </div>
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add a tag..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                    disabled={!canEditBasicInfo}
                  />
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={handleAddTag}
                    disabled={!canEditBasicInfo}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                {(formData.tags?.length || 0) > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {formData.tags?.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        {canEditBasicInfo && (
                          <X 
                            className="h-3 w-3 cursor-pointer" 
                            onClick={() => handleRemoveTag(tag)}
                          />
                        )}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            {/* Permissions Tab */}
            <TabsContent value="permissions" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Role Permissions</h3>
                  <p className="text-sm text-gray-600">
                    {canEditPermissions 
                      ? 'Modify the permissions for this role'
                      : 'View the permissions for this system role'
                    }
                  </p>
                </div>
                <Badge variant="outline">
                  {selectedPermissions.size} selected
                </Badge>
              </div>

              {!canEditPermissions && (
                <div className="p-3 bg-amber-50 border border-amber-200 rounded-md">
                  <p className="text-sm text-amber-700 flex items-center">
                    <Lock className="h-4 w-4 mr-1" />
                    System role permissions cannot be modified.
                  </p>
                </div>
              )}

              {errors.permissions && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errors.permissions}
                  </p>
                </div>
              )}

              <div className="space-y-4">
                {permissionCategories?.map((category) => {
                  const categoryPermissions = getPermissionsByCategory(category.name)
                  if (categoryPermissions.length === 0) return null

                  return (
                    <Card key={category.name}>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">{category.displayName}</CardTitle>
                        <CardDescription>{category.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-2">
                          {categoryPermissions.map((permission) => (
                            <div
                              key={permission.id}
                              className={`p-3 border rounded-lg transition-colors ${
                                selectedPermissions.has(permission.id)
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200'
                              } ${
                                canEditPermissions 
                                  ? 'cursor-pointer hover:border-gray-300' 
                                  : 'opacity-75'
                              }`}
                              onClick={() => canEditPermissions && handlePermissionToggle(permission.id)}
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <div className="font-medium text-sm">{permission.name}</div>
                                  <div className="text-xs text-gray-500">{permission.description}</div>
                                </div>
                                {selectedPermissions.has(permission.id) && (
                                  <Check className="h-4 w-4 text-blue-600" />
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </TabsContent>

            {/* Advanced Tab */}
            <TabsContent value="advanced" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Role Information</CardTitle>
                  <CardDescription>
                    Additional details about this role
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Created</Label>
                      <p className="text-sm text-gray-600">
                        {role?.createdAt ? new Date(role.createdAt).toLocaleDateString() : 'Unknown'}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label>Last Updated</Label>
                      <p className="text-sm text-gray-600">
                        {role?.updatedAt ? new Date(role.updatedAt).toLocaleDateString() : 'Unknown'}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Users with this role</Label>
                    <p className="text-sm text-gray-600">
                      {role?.userCount || 0} users currently have this role
                    </p>
                  </div>

                  {canEditBasicInfo && (
                    <div className="space-y-2">
                      <Label htmlFor="icon">Icon</Label>
                      <select
                        id="icon"
                        value={formData.icon}
                        onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                        className="w-full border rounded-md px-3 py-2"
                      >
                        <option value="users">Users</option>
                        <option value="shield">Shield</option>
                        <option value="settings">Settings</option>
                        <option value="star">Star</option>
                        <option value="crown">Crown</option>
                      </select>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {errors.submit && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.submit}
              </p>
            </div>
          )}

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={loading || (!canEditBasicInfo && !canEditPermissions)}
            >
              {loading ? 'Updating...' : 'Update Role'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
