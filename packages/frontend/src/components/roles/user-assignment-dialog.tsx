import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Users, 
  Check,
  AlertCircle,
  Calendar,
  Clock
} from 'lucide-react'
import { Role, User } from '@/types/rbac'
import { useUsers } from '@/hooks/use-users'

interface UserAssignmentDialogProps {
  role: Role
  open: boolean
  onOpenChange: (open: boolean) => void
  onAssign: (userId: string, options?: AssignmentOptions) => Promise<void>
}

interface AssignmentOptions {
  expiresAt?: Date
  conditions?: any
}

export function UserAssignmentDialog({ role, open, onOpenChange, onAssign }: UserAssignmentDialogProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [expirationDate, setExpirationDate] = useState('')
  const [expirationTime, setExpirationTime] = useState('')

  const { users, loading: usersLoading, error: usersError } = useUsers()

  // Filter users that don't already have this role
  const availableUsers = users?.filter(user => {
    const hasRole = user.roles?.some(userRole => userRole.id === role.id)
    const matchesSearch = user.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email?.toLowerCase().includes(searchTerm.toLowerCase())
    
    return !hasRole && matchesSearch
  }) || []

  const handleUserToggle = (userId: string) => {
    const newSelected = new Set(selectedUsers)
    if (newSelected.has(userId)) {
      newSelected.delete(userId)
    } else {
      newSelected.add(userId)
    }
    setSelectedUsers(newSelected)
  }

  const handleAssign = async () => {
    if (selectedUsers.size === 0) {
      setError('Please select at least one user')
      return
    }

    setLoading(true)
    setError('')

    try {
      const options: AssignmentOptions = {}

      // Handle expiration date/time
      if (expirationDate) {
        const expiresAt = new Date(expirationDate)
        if (expirationTime) {
          const [hours, minutes] = expirationTime.split(':')
          expiresAt.setHours(parseInt(hours), parseInt(minutes))
        }
        options.expiresAt = expiresAt
      }

      // Assign role to all selected users
      for (const userId of selectedUsers) {
        await onAssign(userId, options)
      }

      // Reset form
      setSelectedUsers(new Set())
      setSearchTerm('')
      setExpirationDate('')
      setExpirationTime('')
      setShowAdvanced(false)
      onOpenChange(false)
    } catch (error) {
      setError('Failed to assign role. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setSelectedUsers(new Set())
    setSearchTerm('')
    setError('')
    setExpirationDate('')
    setExpirationTime('')
    setShowAdvanced(false)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Assign Users to Role</DialogTitle>
          <DialogDescription>
            Select users to assign the "{role.name}" role to.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search users by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Selected Users Summary */}
          {selectedUsers.size > 0 && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-700">
                {selectedUsers.size} user{selectedUsers.size !== 1 ? 's' : ''} selected for role assignment
              </p>
            </div>
          )}

          {/* Advanced Options Toggle */}
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Available Users</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              Advanced Options
            </Button>
          </div>

          {/* Advanced Options */}
          {showAdvanced && (
            <div className="p-4 border rounded-lg bg-gray-50 space-y-4">
              <h4 className="font-medium text-sm">Assignment Options</h4>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expirationDate" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Expiration Date
                  </Label>
                  <Input
                    id="expirationDate"
                    type="date"
                    value={expirationDate}
                    onChange={(e) => setExpirationDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="expirationTime" className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Expiration Time
                  </Label>
                  <Input
                    id="expirationTime"
                    type="time"
                    value={expirationTime}
                    onChange={(e) => setExpirationTime(e.target.value)}
                    disabled={!expirationDate}
                  />
                </div>
              </div>
              
              <p className="text-xs text-gray-600">
                Leave expiration empty for permanent assignment
              </p>
            </div>
          )}

          {/* User List */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {usersLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : usersError ? (
              <div className="text-center py-8 text-red-600">
                <p>Failed to load users</p>
              </div>
            ) : availableUsers.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Available Users</h3>
                <p className="text-gray-600">
                  {searchTerm 
                    ? 'No users match your search criteria.'
                    : 'All users already have this role assigned.'
                  }
                </p>
              </div>
            ) : (
              availableUsers.map((user) => (
                <div
                  key={user.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedUsers.has(user.id)
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleUserToggle(user.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <Users className="h-4 w-4 text-gray-600" />
                      </div>
                      <div>
                        <div className="font-medium">
                          {user.firstName} {user.lastName}
                        </div>
                        <div className="text-sm text-gray-600">{user.email}</div>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {user.status}
                          </Badge>
                          {user.roles && user.roles.length > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {user.roles.length} role{user.roles.length !== 1 ? 's' : ''}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {selectedUsers.has(user.id) && (
                      <Check className="h-5 w-5 text-blue-600" />
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {error}
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleAssign}
            disabled={loading || selectedUsers.size === 0}
          >
            {loading 
              ? 'Assigning...' 
              : `Assign Role${selectedUsers.size > 1 ? ` (${selectedUsers.size})` : ''}`
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
