import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronDown, 
  ChevronRight, 
  Users, 
  Shield, 
  Crown,
  Star,
  Settings,
  Plus,
  Minus,
  Eye,
  EyeOff
} from 'lucide-react'
import { Role, RoleHierarchy } from '@/types/rbac'
import { useRoleHierarchy } from '@/hooks/use-role-hierarchy'

interface RoleHierarchyProps {
  onRoleSelect?: (role: Role) => void
  selectedRoleId?: string
  showPermissionCount?: boolean
  showUserCount?: boolean
  allowCollapse?: boolean
}

interface HierarchyNodeProps {
  node: RoleHierarchy
  level: number
  onRoleSelect?: (role: Role) => void
  selectedRoleId?: string
  showPermissionCount?: boolean
  showUserCount?: boolean
  allowCollapse?: boolean
  expandedNodes: Set<string>
  onToggleExpand: (nodeId: string) => void
}

export function RoleHierarchy({ 
  onRoleSelect, 
  selectedRoleId,
  showPermissionCount = true,
  showUserCount = true,
  allowCollapse = true
}: RoleHierarchyProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())
  const [showInheritedPermissions, setShowInheritedPermissions] = useState(false)
  
  const { hierarchy, loading, error, refetch } = useRoleHierarchy()

  // Expand all nodes by default
  useEffect(() => {
    if (hierarchy && expandedNodes.size === 0) {
      const allNodeIds = new Set<string>()
      const collectNodeIds = (nodes: RoleHierarchy[]) => {
        nodes.forEach(node => {
          allNodeIds.add(node.roleId)
          if (node.children) {
            collectNodeIds(node.children)
          }
        })
      }
      collectNodeIds(hierarchy)
      setExpandedNodes(allNodeIds)
    }
  }, [hierarchy])

  const handleToggleExpand = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpandedNodes(newExpanded)
  }

  const expandAll = () => {
    if (!hierarchy) return
    const allNodeIds = new Set<string>()
    const collectNodeIds = (nodes: RoleHierarchy[]) => {
      nodes.forEach(node => {
        allNodeIds.add(node.roleId)
        if (node.children) {
          collectNodeIds(node.children)
        }
      })
    }
    collectNodeIds(hierarchy)
    setExpandedNodes(allNodeIds)
  }

  const collapseAll = () => {
    setExpandedNodes(new Set())
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Failed to load role hierarchy</p>
            <Button variant="outline" onClick={refetch} className="mt-2">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Role Hierarchy</CardTitle>
            <CardDescription>
              Visual representation of role relationships and inheritance
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowInheritedPermissions(!showInheritedPermissions)}
            >
              {showInheritedPermissions ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showInheritedPermissions ? 'Hide' : 'Show'} Inherited
            </Button>
            
            {allowCollapse && (
              <>
                <Button variant="ghost" size="sm" onClick={expandAll}>
                  <Plus className="h-4 w-4 mr-1" />
                  Expand All
                </Button>
                <Button variant="ghost" size="sm" onClick={collapseAll}>
                  <Minus className="h-4 w-4 mr-1" />
                  Collapse All
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {hierarchy && hierarchy.length > 0 ? (
          <div className="space-y-2">
            {hierarchy.map((node) => (
              <HierarchyNode
                key={node.roleId}
                node={node}
                level={0}
                onRoleSelect={onRoleSelect}
                selectedRoleId={selectedRoleId}
                showPermissionCount={showPermissionCount}
                showUserCount={showUserCount}
                allowCollapse={allowCollapse}
                expandedNodes={expandedNodes}
                onToggleExpand={handleToggleExpand}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Role Hierarchy</h3>
            <p className="text-gray-600">
              No role hierarchy found. Roles may not have parent-child relationships configured.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

function HierarchyNode({ 
  node, 
  level, 
  onRoleSelect, 
  selectedRoleId,
  showPermissionCount,
  showUserCount,
  allowCollapse,
  expandedNodes,
  onToggleExpand
}: HierarchyNodeProps) {
  const hasChildren = node.children && node.children.length > 0
  const isExpanded = expandedNodes.has(node.roleId)
  const isSelected = selectedRoleId === node.roleId

  const getRoleIcon = (role: any) => {
    if (role.isSystem) {
      switch (role.name.toLowerCase()) {
        case 'super_admin':
        case 'admin':
          return <Crown className="h-4 w-4 text-yellow-600" />
        case 'manager':
          return <Star className="h-4 w-4 text-blue-600" />
        default:
          return <Shield className="h-4 w-4 text-blue-600" />
      }
    }
    return <Users className="h-4 w-4 text-gray-600" />
  }

  const getPriorityColor = (priority: number) => {
    if (priority >= 80) return 'bg-red-100 text-red-800 border-red-200'
    if (priority >= 60) return 'bg-orange-100 text-orange-800 border-orange-200'
    if (priority >= 40) return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    return 'bg-green-100 text-green-800 border-green-200'
  }

  const getIndentation = (level: number) => {
    return level * 24 // 24px per level
  }

  return (
    <div>
      {/* Role Node */}
      <div
        className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
          isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'border-gray-200'
        }`}
        style={{ marginLeft: `${getIndentation(level)}px` }}
        onClick={() => onRoleSelect?.(node.role)}
      >
        {/* Expand/Collapse Button */}
        <div className="flex items-center mr-3">
          {hasChildren && allowCollapse ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation()
                onToggleExpand(node.roleId)
              }}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          ) : (
            <div className="w-6" />
          )}
        </div>

        {/* Role Icon */}
        <div className="mr-3">
          {getRoleIcon(node.role)}
        </div>

        {/* Role Information */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h4 className="font-medium truncate">{node.role.name}</h4>
            
            {node.role.isSystem && (
              <Badge variant="secondary" className="text-xs">
                System
              </Badge>
            )}
            
            {node.role.isDefault && (
              <Badge variant="outline" className="text-xs">
                Default
              </Badge>
            )}
            
            <Badge 
              className={`text-xs ${getPriorityColor(node.role.priority || 0)}`}
              variant="outline"
            >
              P{node.role.priority || 0}
            </Badge>
          </div>
          
          {node.role.description && (
            <p className="text-sm text-gray-600 truncate mt-1">
              {node.role.description}
            </p>
          )}
        </div>

        {/* Statistics */}
        <div className="flex items-center gap-3 ml-4">
          {showUserCount && (
            <div className="text-sm text-gray-500">
              <Users className="h-4 w-4 inline mr-1" />
              {node.role.userCount || 0}
            </div>
          )}
          
          {showPermissionCount && (
            <div className="text-sm text-gray-500">
              <Settings className="h-4 w-4 inline mr-1" />
              {node.inheritedPermissions?.length || 0}
            </div>
          )}
        </div>
      </div>

      {/* Child Nodes */}
      {hasChildren && isExpanded && (
        <div className="mt-2 space-y-2">
          {node.children!.map((childNode) => (
            <HierarchyNode
              key={childNode.roleId}
              node={childNode}
              level={level + 1}
              onRoleSelect={onRoleSelect}
              selectedRoleId={selectedRoleId}
              showPermissionCount={showPermissionCount}
              showUserCount={showUserCount}
              allowCollapse={allowCollapse}
              expandedNodes={expandedNodes}
              onToggleExpand={onToggleExpand}
            />
          ))}
        </div>
      )}

      {/* Connection Lines for Visual Hierarchy */}
      {level > 0 && (
        <div
          className="absolute border-l-2 border-gray-200"
          style={{
            left: `${getIndentation(level - 1) + 12}px`,
            top: '0',
            height: '100%',
            width: '1px'
          }}
        />
      )}
    </div>
  )
}
