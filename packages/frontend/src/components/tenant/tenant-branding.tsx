import { useEffect } from 'react'
import { useTenant } from '@/contexts/tenant.context'
import { useTenantBranding } from '@/hooks/use-tenant-features'

interface TenantBrandingProviderProps {
  children: React.ReactNode
}

// Component that applies tenant branding globally
export function TenantBrandingProvider({ children }: TenantBrandingProviderProps) {
  const { tenant } = useTenant()
  const { branding, getBrandingStyles } = useTenantBranding()

  useEffect(() => {
    if (tenant && branding) {
      // Apply CSS custom properties for tenant branding
      const root = document.documentElement
      const styles = getBrandingStyles()
      
      Object.entries(styles).forEach(([property, value]) => {
        root.style.setProperty(property, value)
      })

      // Apply custom CSS if provided
      if (branding.customCss) {
        const styleId = 'tenant-custom-styles'
        let styleElement = document.getElementById(styleId) as HTMLStyleElement
        
        if (!styleElement) {
          styleElement = document.createElement('style')
          styleElement.id = styleId
          document.head.appendChild(styleElement)
        }
        
        styleElement.textContent = branding.customCss
      }

      // Apply favicon if provided
      if (branding.favicon) {
        const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
        if (favicon) {
          favicon.href = branding.favicon
        } else {
          const newFavicon = document.createElement('link')
          newFavicon.rel = 'icon'
          newFavicon.href = branding.favicon
          document.head.appendChild(newFavicon)
        }
      }

      // Update page title with tenant name
      if (tenant.name) {
        const currentTitle = document.title
        if (!currentTitle.includes(tenant.name)) {
          document.title = `${tenant.name} - M&A Platform`
        }
      }
    }

    // Cleanup function to remove tenant-specific styles
    return () => {
      if (!tenant) {
        const root = document.documentElement
        root.style.removeProperty('--primary-color')
        root.style.removeProperty('--secondary-color')
        
        const customStyles = document.getElementById('tenant-custom-styles')
        if (customStyles) {
          customStyles.remove()
        }
        
        document.title = 'M&A Platform'
      }
    }
  }, [tenant, branding, getBrandingStyles])

  return <>{children}</>
}

// Component for tenant logo display
interface TenantLogoProps {
  className?: string
  fallbackText?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

export function TenantLogo({ 
  className = '', 
  fallbackText,
  size = 'md' 
}: TenantLogoProps) {
  const { tenant } = useTenant()
  const { getLogoUrl } = useTenantBranding()

  if (!tenant) {
    return null
  }

  const sizeClasses = {
    sm: 'h-6 w-auto',
    md: 'h-8 w-auto',
    lg: 'h-12 w-auto',
    xl: 'h-16 w-auto'
  }

  const logoUrl = getLogoUrl('/default-logo.png')
  const displayText = fallbackText || tenant.name

  return (
    <div className={`flex items-center ${className}`}>
      {logoUrl ? (
        <img 
          src={logoUrl} 
          alt={tenant.name}
          className={`${sizeClasses[size]} object-contain`}
          onError={(e) => {
            // Fallback to text if image fails to load
            const target = e.target as HTMLImageElement
            target.style.display = 'none'
            const textElement = target.nextElementSibling as HTMLElement
            if (textElement) {
              textElement.style.display = 'block'
            }
          }}
        />
      ) : null}
      <span 
        className={`font-bold text-primary ${logoUrl ? 'hidden' : 'block'}`}
        style={{ display: logoUrl ? 'none' : 'block' }}
      >
        {displayText}
      </span>
    </div>
  )
}

// Component for tenant-branded header
interface TenantHeaderProps {
  children?: React.ReactNode
  className?: string
  showLogo?: boolean
  showName?: boolean
  showPlan?: boolean
}

export function TenantHeader({ 
  children, 
  className = '',
  showLogo = true,
  showName = true,
  showPlan = false
}: TenantHeaderProps) {
  const { tenant } = useTenant()
  const { primaryColor } = useTenantBranding()

  if (!tenant) {
    return null
  }

  return (
    <header 
      className={`border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 ${className}`}
      style={{ borderBottomColor: primaryColor + '20' }}
    >
      <div className="container flex h-14 items-center">
        <div className="flex items-center space-x-4">
          {showLogo && <TenantLogo size="md" />}
          {showName && !showLogo && (
            <h1 className="text-lg font-semibold">{tenant.name}</h1>
          )}
          {showPlan && (
            <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
              {tenant.plan}
            </span>
          )}
        </div>
        
        <div className="flex flex-1 items-center justify-end space-x-4">
          {children}
        </div>
      </div>
    </header>
  )
}

// Component for tenant-branded footer
interface TenantFooterProps {
  className?: string
  showCopyright?: boolean
  showLinks?: boolean
}

export function TenantFooter({ 
  className = '',
  showCopyright = true,
  showLinks = true
}: TenantFooterProps) {
  const { tenant } = useTenant()

  if (!tenant) {
    return null
  }

  const currentYear = new Date().getFullYear()

  return (
    <footer className={`border-t bg-background ${className}`}>
      <div className="container py-6">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <TenantLogo size="sm" />
            {showCopyright && (
              <p className="text-sm text-muted-foreground">
                © {currentYear} {tenant.name}. All rights reserved.
              </p>
            )}
          </div>
          
          {showLinks && (
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              {tenant.website && (
                <a 
                  href={tenant.website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="hover:text-foreground transition-colors"
                >
                  Website
                </a>
              )}
              <a href="/privacy" className="hover:text-foreground transition-colors">
                Privacy
              </a>
              <a href="/terms" className="hover:text-foreground transition-colors">
                Terms
              </a>
              <a href="/support" className="hover:text-foreground transition-colors">
                Support
              </a>
            </div>
          )}
        </div>
      </div>
    </footer>
  )
}

// Component for tenant-branded loading screen
interface TenantLoadingScreenProps {
  message?: string
  className?: string
}

export function TenantLoadingScreen({ 
  message = 'Loading...',
  className = ''
}: TenantLoadingScreenProps) {
  const { tenant } = useTenant()
  const { primaryColor } = useTenantBranding()

  return (
    <div className={`flex items-center justify-center min-h-screen ${className}`}>
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          {tenant ? (
            <TenantLogo size="xl" />
          ) : (
            <div className="h-16 w-16 bg-primary/10 rounded-lg flex items-center justify-center">
              <div className="h-8 w-8 bg-primary/20 rounded animate-pulse" />
            </div>
          )}
        </div>
        
        <div className="space-y-2">
          <div 
            className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto"
            style={{ borderBottomColor: primaryColor }}
          />
          <p className="text-muted-foreground">{message}</p>
          {tenant && (
            <p className="text-xs text-muted-foreground">
              {tenant.name}
            </p>
          )}
        </div>
      </div>
    </div>
  )
}

// Component for tenant-branded error screen
interface TenantErrorScreenProps {
  title?: string
  message?: string
  onRetry?: () => void
  className?: string
}

export function TenantErrorScreen({ 
  title = 'Something went wrong',
  message = 'We encountered an error while loading your data.',
  onRetry,
  className = ''
}: TenantErrorScreenProps) {
  const { tenant } = useTenant()

  return (
    <div className={`flex items-center justify-center min-h-screen ${className}`}>
      <div className="text-center space-y-6 max-w-md">
        <div className="flex justify-center">
          {tenant ? (
            <TenantLogo size="lg" />
          ) : (
            <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
              <div className="h-6 w-6 bg-red-500 rounded" />
            </div>
          )}
        </div>
        
        <div className="space-y-2">
          <h1 className="text-2xl font-bold text-foreground">{title}</h1>
          <p className="text-muted-foreground">{message}</p>
          {tenant && (
            <p className="text-xs text-muted-foreground">
              Organization: {tenant.name}
            </p>
          )}
        </div>
        
        {onRetry && (
          <button
            onClick={onRetry}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Try Again
          </button>
        )}
      </div>
    </div>
  )
}

// Hook for applying tenant theme
export function useTenantTheme() {
  const { tenant } = useTenant()
  const { branding, primaryColor, secondaryColor } = useTenantBranding()

  const applyTheme = () => {
    if (tenant && branding) {
      const root = document.documentElement
      root.style.setProperty('--primary', primaryColor)
      root.style.setProperty('--secondary', secondaryColor)
      
      // Apply additional theme variables if needed
      if (branding.customCss) {
        const styleId = 'tenant-theme'
        let styleElement = document.getElementById(styleId) as HTMLStyleElement
        
        if (!styleElement) {
          styleElement = document.createElement('style')
          styleElement.id = styleId
          document.head.appendChild(styleElement)
        }
        
        styleElement.textContent = branding.customCss
      }
    }
  }

  const removeTheme = () => {
    const root = document.documentElement
    root.style.removeProperty('--primary')
    root.style.removeProperty('--secondary')
    
    const themeElement = document.getElementById('tenant-theme')
    if (themeElement) {
      themeElement.remove()
    }
  }

  return {
    applyTheme,
    removeTheme,
    primaryColor,
    secondaryColor,
    branding
  }
}
