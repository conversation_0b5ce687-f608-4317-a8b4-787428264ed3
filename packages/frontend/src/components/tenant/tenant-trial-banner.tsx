import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { useTenantTrial } from '@/hooks/use-tenant-features'
import { Clock, Crown, X, CreditCard } from 'lucide-react'

interface TenantTrialBannerProps {
  onUpgrade?: () => void
  onDismiss?: () => void
  dismissible?: boolean
  className?: string
}

export function TenantTrialBanner({ 
  onUpgrade, 
  onDismiss,
  dismissible = true,
  className = '' 
}: TenantTrialBannerProps) {
  const {
    isInTrial,
    isTrialExpired,
    isTrialExpiringSoon,
    trialDaysRemaining,
    getTrialStatus,
    getTrialMessage
  } = useTenantTrial()

  const [isDismissed, setIsDismissed] = useState(false)

  if (!isInTrial || isDismissed) {
    return null
  }

  const status = getTrialStatus()
  const message = getTrialMessage()

  const handleDismiss = () => {
    setIsDismissed(true)
    onDismiss?.()
  }

  const handleUpgrade = () => {
    onUpgrade?.()
  }

  const getVariant = () => {
    switch (status) {
      case 'expired':
        return 'destructive'
      case 'expiring_soon':
        return 'default'
      default:
        return 'default'
    }
  }

  const getIcon = () => {
    switch (status) {
      case 'expired':
        return <Clock className="h-4 w-4" />
      case 'expiring_soon':
        return <Clock className="h-4 w-4" />
      default:
        return <Crown className="h-4 w-4" />
    }
  }

  return (
    <Alert variant={getVariant()} className={`${className}`}>
      <div className="flex items-center space-x-2">
        {getIcon()}
        <div className="flex-1">
          <AlertDescription className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span>{message}</span>
              <Badge variant="outline" className="text-xs">
                Trial
              </Badge>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                onClick={handleUpgrade}
                className="h-8"
              >
                <CreditCard className="mr-2 h-3 w-3" />
                Upgrade Now
              </Button>
              
              {dismissible && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleDismiss}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </AlertDescription>
        </div>
      </div>
    </Alert>
  )
}

// Compact version for smaller spaces
export function TenantTrialBadge({ 
  onUpgrade,
  className = '' 
}: {
  onUpgrade?: () => void
  className?: string
}) {
  const {
    isInTrial,
    isTrialExpired,
    isTrialExpiringSoon,
    trialDaysRemaining
  } = useTenantTrial()

  if (!isInTrial) {
    return null
  }

  const getVariant = () => {
    if (isTrialExpired) return 'destructive'
    if (isTrialExpiringSoon) return 'secondary'
    return 'outline'
  }

  const getText = () => {
    if (isTrialExpired) return 'Trial Expired'
    return `${trialDaysRemaining} day${trialDaysRemaining === 1 ? '' : 's'} left`
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Badge variant={getVariant()} className="cursor-pointer" onClick={onUpgrade}>
        <Crown className="mr-1 h-3 w-3" />
        {getText()}
      </Badge>
    </div>
  )
}

// Trial countdown component
export function TenantTrialCountdown({ 
  onUpgrade,
  className = '' 
}: {
  onUpgrade?: () => void
  className?: string
}) {
  const {
    isInTrial,
    isTrialExpired,
    trialDaysRemaining,
    trialEndDate
  } = useTenantTrial()

  if (!isInTrial) {
    return null
  }

  const formatTimeRemaining = () => {
    if (isTrialExpired) {
      return 'Trial has expired'
    }

    if (trialDaysRemaining === 0) {
      return 'Trial expires today'
    }

    if (trialDaysRemaining === 1) {
      return 'Trial expires tomorrow'
    }

    return `Trial expires in ${trialDaysRemaining} days`
  }

  const getProgressPercentage = () => {
    if (!trialEndDate) return 0
    
    const trialStart = new Date(trialEndDate.getTime() - (30 * 24 * 60 * 60 * 1000)) // Assume 30-day trial
    const now = new Date()
    const totalDuration = trialEndDate.getTime() - trialStart.getTime()
    const elapsed = now.getTime() - trialStart.getTime()
    
    return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100))
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Crown className="h-4 w-4 text-yellow-500" />
          <span className="text-sm font-medium">{formatTimeRemaining()}</span>
        </div>
        <Button size="sm" onClick={onUpgrade}>
          Upgrade
        </Button>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            isTrialExpired ? 'bg-red-500' :
            trialDaysRemaining <= 7 ? 'bg-yellow-500' : 'bg-green-500'
          }`}
          style={{ width: `${getProgressPercentage()}%` }}
        />
      </div>
      
      {trialEndDate && (
        <p className="text-xs text-muted-foreground">
          Trial ends on {trialEndDate.toLocaleDateString()}
        </p>
      )}
    </div>
  )
}
