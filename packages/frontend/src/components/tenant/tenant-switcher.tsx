import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import { useTenant } from '@/contexts/tenant.context'
import { useTenantNavigation } from '@/components/routing'
import { useAuth } from '@/contexts/auth.context'
import { TenantService, Tenant } from '@/services/tenant.service'
import { ChevronDown, Building, Plus, Search, Check } from 'lucide-react'

interface TenantSwitcherProps {
  className?: string
  showCreateOption?: boolean
}

export function TenantSwitcher({ className = '', showCreateOption = false }: TenantSwitcherProps) {
  const { tenant } = useTenant()
  const { user } = useAuth()
  const { navigateToTenant } = useTenantNavigation()
  const [userTenants, setUserTenants] = useState<Tenant[]>([])
  const [loading, setLoading] = useState(false)
  const [searchOpen, setSearchOpen] = useState(false)

  // Load user's accessible tenants
  useEffect(() => {
    if (user) {
      loadUserTenants()
    }
  }, [user])

  const loadUserTenants = async () => {
    try {
      setLoading(true)
      // In a real implementation, this would fetch tenants the user has access to
      // For now, we'll just show the current tenant
      if (tenant) {
        setUserTenants([tenant])
      }
    } catch (error) {
      console.error('Failed to load user tenants:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTenantSwitch = (targetTenant: Tenant) => {
    if (targetTenant.id === tenant?.id) return
    
    // Navigate to the target tenant
    navigateToTenant('/', targetTenant.subdomain)
  }

  const getTenantInitials = (tenantName: string): string => {
    return tenantName
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (!tenant) {
    return null
  }

  return (
    <div className={className}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-full justify-between">
            <div className="flex items-center space-x-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={tenant.logo} alt={tenant.name} />
                <AvatarFallback className="text-xs">
                  {getTenantInitials(tenant.name)}
                </AvatarFallback>
              </Avatar>
              <span className="truncate">{tenant.name}</span>
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="w-80" align="start">
          <DropdownMenuLabel>Organizations</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {/* Current tenant */}
          <DropdownMenuItem className="flex items-center justify-between p-3">
            <div className="flex items-center space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={tenant.logo} alt={tenant.name} />
                <AvatarFallback className="text-xs">
                  {getTenantInitials(tenant.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="font-medium truncate">{tenant.name}</div>
                <div className="text-xs text-muted-foreground truncate">
                  {tenant.subdomain}.platform.com
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                {tenant.plan}
              </Badge>
              <Check className="h-4 w-4 text-primary" />
            </div>
          </DropdownMenuItem>

          {/* Other accessible tenants */}
          {userTenants
            .filter(t => t.id !== tenant.id)
            .map(userTenant => (
              <DropdownMenuItem
                key={userTenant.id}
                className="flex items-center justify-between p-3 cursor-pointer"
                onClick={() => handleTenantSwitch(userTenant)}
              >
                <div className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={userTenant.logo} alt={userTenant.name} />
                    <AvatarFallback className="text-xs">
                      {getTenantInitials(userTenant.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{userTenant.name}</div>
                    <div className="text-xs text-muted-foreground truncate">
                      {userTenant.subdomain}.platform.com
                    </div>
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {userTenant.plan}
                </Badge>
              </DropdownMenuItem>
            ))}

          <DropdownMenuSeparator />

          {/* Search for organizations */}
          <Dialog open={searchOpen} onOpenChange={setSearchOpen}>
            <DialogTrigger asChild>
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <Search className="mr-2 h-4 w-4" />
                Find organization...
              </DropdownMenuItem>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Find Organization</DialogTitle>
                <DialogDescription>
                  Search for an organization by name, subdomain, or domain
                </DialogDescription>
              </DialogHeader>
              <TenantSearchDialog onTenantSelect={handleTenantSwitch} />
            </DialogContent>
          </Dialog>

          {/* Create new organization */}
          {showCreateOption && (
            <DropdownMenuItem>
              <Plus className="mr-2 h-4 w-4" />
              Create organization...
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

// Tenant search dialog component
interface TenantSearchDialogProps {
  onTenantSelect: (tenant: Tenant) => void
}

function TenantSearchDialog({ onTenantSelect }: TenantSearchDialogProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchResults, setSearchResults] = useState<Tenant[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setSearchResults([])
      return
    }

    setLoading(true)
    setError(null)

    try {
      let tenant: Tenant | null = null

      // Try different search methods
      if (searchTerm.includes('.')) {
        // Looks like a domain or subdomain
        const subdomain = searchTerm.split('.')[0]
        try {
          tenant = await TenantService.getTenantBySubdomain(subdomain)
        } catch {
          // If subdomain fails, try as full domain
          try {
            tenant = await TenantService.getTenantBySubdomain(searchTerm)
          } catch {
            // Could implement domain search here
          }
        }
      } else {
        // Try as slug
        try {
          tenant = await TenantService.getTenantBySlug(searchTerm)
        } catch {
          // If slug fails, try as subdomain
          try {
            tenant = await TenantService.getTenantBySubdomain(searchTerm)
          } catch {
            // No matches found
          }
        }
      }

      if (tenant) {
        setSearchResults([tenant])
      } else {
        setSearchResults([])
        setError('No organization found with that identifier')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed'
      setError(errorMessage)
      setSearchResults([])
    } finally {
      setLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const getTenantInitials = (tenantName: string): string => {
    return tenantName
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="tenant-search">Organization Identifier</Label>
        <div className="flex space-x-2">
          <Input
            id="tenant-search"
            placeholder="e.g., acme, acme.platform.com"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={loading}
          />
          <Button onClick={handleSearch} disabled={loading || !searchTerm.trim()}>
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {error && (
        <div className="text-sm text-red-600">{error}</div>
      )}

      {searchResults.length > 0 && (
        <div className="space-y-2">
          <Label>Search Results</Label>
          {searchResults.map(result => (
            <Card 
              key={result.id} 
              className="cursor-pointer hover:bg-muted/50 transition-colors"
              onClick={() => onTenantSelect(result)}
            >
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={result.logo} alt={result.name} />
                    <AvatarFallback>
                      {getTenantInitials(result.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">{result.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {result.subdomain}.platform.com
                    </div>
                    {result.description && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {result.description}
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    <Badge variant="outline">{result.plan}</Badge>
                    <Badge variant={result.status === 'ACTIVE' ? 'default' : 'secondary'}>
                      {result.status}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

// Compact tenant switcher for smaller spaces
export function CompactTenantSwitcher({ className = '' }: { className?: string }) {
  const { tenant } = useTenant()

  if (!tenant) {
    return null
  }

  const getTenantInitials = (tenantName: string): string => {
    return tenantName
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <div className={className}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Avatar className="h-6 w-6">
              <AvatarImage src={tenant.logo} alt={tenant.name} />
              <AvatarFallback className="text-xs">
                {getTenantInitials(tenant.name)}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="start">
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium">{tenant.name}</p>
              <p className="text-xs text-muted-foreground">
                {tenant.subdomain}.platform.com
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <Building className="mr-2 h-4 w-4" />
            Switch organization...
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
