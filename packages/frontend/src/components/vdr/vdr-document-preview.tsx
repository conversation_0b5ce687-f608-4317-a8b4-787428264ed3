import React, { useState } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Download, 
  Share, 
  Eye, 
  Clock,
  User,
  FileText,
  Shield,
  Tag,
  Calendar,
  HardDrive,
  Hash,
  Activity,
  ExternalLink,
  Copy,
  Print,
  X
} from 'lucide-react'
import { 
  VDRDocument, 
  VDRPermissions, 
  FILE_TYPE_ICONS,
  ACCESS_LEVEL_COLORS,
  VDRAccessLevel
} from '@/types/vdr'

interface VDRDocumentPreviewProps {
  document: VDRDocument
  open: boolean
  onOpenChange: (open: boolean) => void
  permissions: VDRPermissions
  onDownload?: (document: VDRDocument) => void
  onShare?: (document: VDRDocument) => void
  onPrint?: (document: VDRDocument) => void
  onCopy?: (document: VDRDocument) => void
}

export function VDRDocumentPreview({
  document,
  open,
  onOpenChange,
  permissions,
  onDownload,
  onShare,
  onPrint,
  onCopy
}: VDRDocumentPreviewProps) {
  const [isLoading, setIsLoading] = useState(false)

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getFileIcon = (mimeType: string) => {
    return FILE_TYPE_ICONS[mimeType as keyof typeof FILE_TYPE_ICONS] || FILE_TYPE_ICONS.default
  }

  const getAccessLevelBadge = (level: VDRAccessLevel) => {
    return (
      <Badge 
        variant="outline" 
        style={{ 
          borderColor: ACCESS_LEVEL_COLORS[level],
          color: ACCESS_LEVEL_COLORS[level]
        }}
        className="text-xs"
      >
        <Shield className="h-3 w-3 mr-1" />
        {level.replace('_', ' ')}
      </Badge>
    )
  }

  const handleAction = async (action: () => void) => {
    setIsLoading(true)
    try {
      await action()
    } catch (error) {
      console.error('Action failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const canPreviewFile = (mimeType: string) => {
    return mimeType.startsWith('image/') || 
           mimeType === 'application/pdf' ||
           mimeType.startsWith('text/')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <span className="text-3xl">{getFileIcon(document.mimeType)}</span>
              <div>
                <DialogTitle className="text-xl">{document.name}</DialogTitle>
                <DialogDescription className="mt-1">
                  {document.originalName} • Version {document.version}
                </DialogDescription>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {getAccessLevelBadge(document.accessLevel)}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Action Buttons */}
          <div className="flex items-center gap-2 flex-wrap">
            {permissions.canView && canPreviewFile(document.mimeType) && (
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
            )}
            
            {permissions.canDownload && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleAction(() => onDownload?.(document))}
                disabled={isLoading}
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            )}
            
            {permissions.canShare && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleAction(() => onShare?.(document))}
                disabled={isLoading}
              >
                <Share className="h-4 w-4 mr-2" />
                Share
              </Button>
            )}
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleAction(() => onPrint?.(document))}
              disabled={isLoading}
            >
              <Print className="h-4 w-4 mr-2" />
              Print
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleAction(() => onCopy?.(document))}
              disabled={isLoading}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy Link
            </Button>
          </div>

          {/* Document Preview */}
          {permissions.canView && canPreviewFile(document.mimeType) && (
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Document Preview</h3>
                <p className="text-gray-600 mb-4">
                  Preview functionality would be implemented here
                </p>
                <Button variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open in New Tab
                </Button>
              </div>
            </div>
          )}

          {/* Document Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Document Information</h3>
              
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <FileText className="h-4 w-4 text-gray-500" />
                  <div>
                    <div className="text-sm font-medium">File Type</div>
                    <div className="text-sm text-gray-600">{document.mimeType}</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <HardDrive className="h-4 w-4 text-gray-500" />
                  <div>
                    <div className="text-sm font-medium">File Size</div>
                    <div className="text-sm text-gray-600">{formatFileSize(document.size)}</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Hash className="h-4 w-4 text-gray-500" />
                  <div>
                    <div className="text-sm font-medium">Version</div>
                    <div className="text-sm text-gray-600">v{document.version}</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Shield className="h-4 w-4 text-gray-500" />
                  <div>
                    <div className="text-sm font-medium">Security</div>
                    <div className="text-sm text-gray-600">
                      {document.isEncrypted ? 'Encrypted' : 'Not encrypted'}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Metadata */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Metadata</h3>
              
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <div className="text-sm font-medium">Uploaded By</div>
                    <div className="text-sm text-gray-600">
                      {document.uploader ? 
                        `${document.uploader.firstName} ${document.uploader.lastName}` : 
                        'Unknown'
                      }
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <div className="text-sm font-medium">Created</div>
                    <div className="text-sm text-gray-600">{formatDate(document.createdAt)}</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <div>
                    <div className="text-sm font-medium">Last Modified</div>
                    <div className="text-sm text-gray-600">{formatDate(document.updatedAt)}</div>
                  </div>
                </div>
                
                {document.lastViewedAt && (
                  <div className="flex items-center gap-3">
                    <Eye className="h-4 w-4 text-gray-500" />
                    <div>
                      <div className="text-sm font-medium">Last Viewed</div>
                      <div className="text-sm text-gray-600">{formatDate(document.lastViewedAt)}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Description */}
          {document.description && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Description</h3>
              <p className="text-gray-600">{document.description}</p>
            </div>
          )}

          {/* Tags */}
          {document.tags.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Tag className="h-4 w-4" />
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {document.tags.map((tag) => (
                  <Badge key={tag} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Activity Stats */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Activity
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{document.viewCount}</div>
                <div className="text-sm text-gray-600">Views</div>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{document.downloadCount}</div>
                <div className="text-sm text-gray-600">Downloads</div>
              </div>
            </div>
          </div>

          {/* Version History */}
          {document.versions && document.versions.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Version History</h3>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {document.versions.map((version) => (
                  <div key={version.id} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <div className="font-medium">Version {version.version}</div>
                      <div className="text-sm text-gray-600">{formatDate(version.createdAt)}</div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
