import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Download, 
  Share, 
  Print,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Maximize,
  Minimize,
  Eye,
  EyeOff,
  Shield,
  Clock,
  User,
  FileText,
  AlertTriangle,
  Loader2,
  X,
  ChevronLeft,
  ChevronRight,
  Search,
  Copy
} from 'lucide-react'
import { 
  VDRDocument, 
  VDRPermissions,
  FILE_TYPE_ICONS,
  ACCESS_LEVEL_COLORS
} from '@/types/vdr'

interface VDRSecureDocumentViewerProps {
  document: VDRDocument
  permissions: VDRPermissions
  sessionToken: string
  onDownload?: () => void
  onShare?: () => void
  onPrint?: () => void
  onClose?: () => void
  watermarkText?: string
  preventScreenshot?: boolean
  sessionTimeout?: number
}

interface ViewerState {
  isLoading: boolean
  error: string | null
  zoom: number
  rotation: number
  currentPage: number
  totalPages: number
  isFullscreen: boolean
  searchTerm: string
  searchResults: number
  showWatermark: boolean
}

export function VDRSecureDocumentViewer({
  document,
  permissions,
  sessionToken,
  onDownload,
  onShare,
  onPrint,
  onClose,
  watermarkText = 'CONFIDENTIAL',
  preventScreenshot = true,
  sessionTimeout = 30
}: VDRSecureDocumentViewerProps) {
  const viewerRef = useRef<HTMLDivElement>(null)
  const [viewerState, setViewerState] = useState<ViewerState>({
    isLoading: true,
    error: null,
    zoom: 100,
    rotation: 0,
    currentPage: 1,
    totalPages: 1,
    isFullscreen: false,
    searchTerm: '',
    searchResults: 0,
    showWatermark: true
  })
  
  const [sessionTimeLeft, setSessionTimeLeft] = useState(sessionTimeout * 60) // Convert to seconds
  const [documentUrl, setDocumentUrl] = useState<string | null>(null)
  const [accessAttempts, setAccessAttempts] = useState(0)

  // Session timer
  useEffect(() => {
    const timer = setInterval(() => {
      setSessionTimeLeft(prev => {
        if (prev <= 1) {
          handleSessionExpired()
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Security measures
  useEffect(() => {
    if (preventScreenshot) {
      // Disable right-click context menu
      const handleContextMenu = (e: MouseEvent) => {
        e.preventDefault()
        return false
      }

      // Disable certain keyboard shortcuts
      const handleKeyDown = (e: KeyboardEvent) => {
        // Disable F12, Ctrl+Shift+I, Ctrl+U, etc.
        if (
          e.key === 'F12' ||
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.key === 'u') ||
          (e.ctrlKey && e.shiftKey && e.key === 'C') ||
          (e.metaKey && e.altKey && e.key === 'I')
        ) {
          e.preventDefault()
          return false
        }
      }

      // Disable text selection
      const handleSelectStart = (e: Event) => {
        e.preventDefault()
        return false
      }

      document.addEventListener('contextmenu', handleContextMenu)
      document.addEventListener('keydown', handleKeyDown)
      document.addEventListener('selectstart', handleSelectStart)

      return () => {
        document.removeEventListener('contextmenu', handleContextMenu)
        document.removeEventListener('keydown', handleKeyDown)
        document.removeEventListener('selectstart', handleSelectStart)
      }
    }
  }, [preventScreenshot])

  // Load document
  useEffect(() => {
    loadDocument()
  }, [document.id, sessionToken])

  const loadDocument = async () => {
    setViewerState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      // Simulate API call to get secure document URL
      const response = await fetch(`/api/vdr/documents/${document.id}/view`, {
        headers: {
          'Authorization': `Bearer ${sessionToken}`,
          'X-VDR-Session': sessionToken
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to load document: ${response.statusText}`)
      }

      const data = await response.json()
      setDocumentUrl(data.secureUrl)
      
      // Simulate getting document metadata
      setViewerState(prev => ({
        ...prev,
        isLoading: false,
        totalPages: data.totalPages || 1
      }))

      // Log document view
      await logDocumentAccess('VIEW')

    } catch (error) {
      console.error('Failed to load document:', error)
      setViewerState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load document'
      }))
      
      setAccessAttempts(prev => prev + 1)
      if (accessAttempts >= 3) {
        await logDocumentAccess('UNAUTHORIZED_ACCESS_ATTEMPT')
      }
    }
  }

  const logDocumentAccess = async (action: string) => {
    try {
      await fetch('/api/vdr/audit/log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionToken}`
        },
        body: JSON.stringify({
          documentId: document.id,
          action,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        })
      })
    } catch (error) {
      console.error('Failed to log document access:', error)
    }
  }

  const handleSessionExpired = () => {
    setViewerState(prev => ({
      ...prev,
      error: 'Your session has expired. Please refresh to continue.'
    }))
    setDocumentUrl(null)
  }

  const handleZoom = (direction: 'in' | 'out' | 'reset') => {
    setViewerState(prev => {
      let newZoom = prev.zoom
      
      switch (direction) {
        case 'in':
          newZoom = Math.min(prev.zoom + 25, 300)
          break
        case 'out':
          newZoom = Math.max(prev.zoom - 25, 25)
          break
        case 'reset':
          newZoom = 100
          break
      }
      
      return { ...prev, zoom: newZoom }
    })
  }

  const handleRotate = () => {
    setViewerState(prev => ({
      ...prev,
      rotation: (prev.rotation + 90) % 360
    }))
  }

  const handlePageChange = (direction: 'prev' | 'next') => {
    setViewerState(prev => {
      const newPage = direction === 'next' 
        ? Math.min(prev.currentPage + 1, prev.totalPages)
        : Math.max(prev.currentPage - 1, 1)
      
      return { ...prev, currentPage: newPage }
    })
  }

  const handleFullscreen = () => {
    if (!viewerState.isFullscreen) {
      viewerRef.current?.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
    
    setViewerState(prev => ({
      ...prev,
      isFullscreen: !prev.isFullscreen
    }))
  }

  const handleSearch = (term: string) => {
    setViewerState(prev => ({
      ...prev,
      searchTerm: term,
      searchResults: term ? Math.floor(Math.random() * 10) + 1 : 0 // Simulate search results
    }))
  }

  const handleDownload = async () => {
    if (!permissions.canDownload) return
    
    try {
      await logDocumentAccess('DOWNLOAD')
      onDownload?.()
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  const handlePrint = async () => {
    if (!permissions.canView) return
    
    try {
      await logDocumentAccess('PRINT')
      onPrint?.()
    } catch (error) {
      console.error('Print failed:', error)
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getFileIcon = (mimeType: string) => {
    return FILE_TYPE_ICONS[mimeType as keyof typeof FILE_TYPE_ICONS] || FILE_TYPE_ICONS.default
  }

  const canPreviewFile = (mimeType: string) => {
    return mimeType.startsWith('image/') || 
           mimeType === 'application/pdf' ||
           mimeType.startsWith('text/')
  }

  if (viewerState.error) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Unable to Load Document</h3>
          <p className="text-gray-600 mb-4">{viewerState.error}</p>
          <div className="flex gap-2 justify-center">
            <Button onClick={loadDocument} variant="outline">
              Try Again
            </Button>
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div 
      ref={viewerRef}
      className={`bg-white ${viewerState.isFullscreen ? 'fixed inset-0 z-50' : 'relative'}`}
    >
      {/* Header */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-2xl">{getFileIcon(document.mimeType)}</span>
              <div>
                <CardTitle className="text-lg">{document.name}</CardTitle>
                <CardDescription className="flex items-center gap-2">
                  <Badge 
                    variant="outline" 
                    style={{ 
                      borderColor: ACCESS_LEVEL_COLORS[document.accessLevel],
                      color: ACCESS_LEVEL_COLORS[document.accessLevel]
                    }}
                  >
                    <Shield className="h-3 w-3 mr-1" />
                    {document.accessLevel}
                  </Badge>
                  <span>•</span>
                  <span>Version {document.version}</span>
                  {document.isEncrypted && (
                    <>
                      <span>•</span>
                      <span className="flex items-center gap-1">
                        <Shield className="h-3 w-3" />
                        Encrypted
                      </span>
                    </>
                  )}
                </CardDescription>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Session Timer */}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                <span>Session: {formatTime(sessionTimeLeft)}</span>
              </div>

              {/* Action Buttons */}
              {permissions.canView && (
                <Button variant="outline" size="sm" onClick={() => handleSearch('')}>
                  <Search className="h-4 w-4" />
                </Button>
              )}

              {permissions.canDownload && (
                <Button variant="outline" size="sm" onClick={handleDownload}>
                  <Download className="h-4 w-4" />
                </Button>
              )}

              {permissions.canShare && (
                <Button variant="outline" size="sm" onClick={onShare}>
                  <Share className="h-4 w-4" />
                </Button>
              )}

              <Button variant="outline" size="sm" onClick={handlePrint}>
                <Print className="h-4 w-4" />
              </Button>

              <Button variant="outline" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Viewer Controls */}
      {canPreviewFile(document.mimeType) && (
        <Card className="rounded-none border-x-0 border-t-0">
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* Page Navigation */}
                {viewerState.totalPages > 1 && (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange('prev')}
                      disabled={viewerState.currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <span className="text-sm">
                      {viewerState.currentPage} / {viewerState.totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange('next')}
                      disabled={viewerState.currentPage === viewerState.totalPages}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                )}

                {/* Zoom Controls */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleZoom('out')}
                    disabled={viewerState.zoom <= 25}
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <span className="text-sm min-w-[60px] text-center">
                    {viewerState.zoom}%
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleZoom('in')}
                    disabled={viewerState.zoom >= 300}
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleZoom('reset')}
                  >
                    Reset
                  </Button>
                </div>

                {/* Rotate */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRotate}
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center gap-2">
                {/* Watermark Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setViewerState(prev => ({ ...prev, showWatermark: !prev.showWatermark }))}
                >
                  {viewerState.showWatermark ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>

                {/* Fullscreen */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleFullscreen}
                >
                  {viewerState.isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Document Viewer */}
      <div className="relative bg-gray-100 min-h-[600px] flex items-center justify-center">
        {viewerState.isLoading ? (
          <div className="text-center">
            <Loader2 className="h-12 w-12 animate-spin text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Loading document...</p>
          </div>
        ) : canPreviewFile(document.mimeType) && documentUrl ? (
          <div 
            className="relative bg-white shadow-lg"
            style={{
              transform: `scale(${viewerState.zoom / 100}) rotate(${viewerState.rotation}deg)`,
              transformOrigin: 'center'
            }}
          >
            {/* Document Content */}
            {document.mimeType === 'application/pdf' ? (
              <iframe
                src={documentUrl}
                className="w-full h-[800px] border-0"
                title={document.name}
              />
            ) : document.mimeType.startsWith('image/') ? (
              <img
                src={documentUrl}
                alt={document.name}
                className="max-w-full max-h-[800px] object-contain"
                onLoad={() => setViewerState(prev => ({ ...prev, isLoading: false }))}
              />
            ) : (
              <div className="w-full h-[800px] p-8 overflow-auto">
                <pre className="whitespace-pre-wrap font-mono text-sm">
                  {/* Text content would be loaded here */}
                  Document content preview...
                </pre>
              </div>
            )}

            {/* Watermark Overlay */}
            {viewerState.showWatermark && (
              <div 
                className="absolute inset-0 pointer-events-none flex items-center justify-center"
                style={{
                  background: `repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 100px,
                    rgba(0,0,0,0.05) 100px,
                    rgba(0,0,0,0.05) 200px
                  )`
                }}
              >
                <div 
                  className="text-6xl font-bold text-gray-400 opacity-20 transform -rotate-45 select-none"
                  style={{ userSelect: 'none' }}
                >
                  {watermarkText}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Preview Not Available</h3>
            <p className="text-gray-600 mb-4">
              This file type cannot be previewed in the browser.
            </p>
            {permissions.canDownload && (
              <Button onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download to View
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      <Card className="rounded-none border-x-0 border-b-0">
        <CardContent className="p-3">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <span>Viewed by: {document.viewCount} users</span>
              <span>Downloaded: {document.downloadCount} times</span>
              {document.lastViewedAt && (
                <span>Last viewed: {new Date(document.lastViewedAt).toLocaleDateString()}</span>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span>Secure viewing session</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
