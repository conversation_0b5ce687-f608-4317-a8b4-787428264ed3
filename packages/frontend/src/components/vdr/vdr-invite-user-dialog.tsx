import React, { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  UserPlus, 
  AlertCircle,
  Mail,
  User,
  Building,
  Shield,
  Calendar,
  MessageSquare
} from 'lucide-react'
import { 
  VirtualDataRoom, 
  VDRRole, 
  VDRAccessLevel,
  InviteUserRequest,
  VDR_ROLE_COLORS,
  ACCESS_LEVEL_COLORS
} from '@/types/vdr'

interface VDRInviteUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onInvite: (invitation: InviteUserRequest) => void
  vdr: VirtualDataRoom
}

export function VDRInviteUserDialog({
  open,
  onOpenChange,
  onInvite,
  vdr
}: VDRInviteUserDialogProps) {
  const [formData, setFormData] = useState<InviteUserRequest>({
    email: '',
    firstName: '',
    lastName: '',
    company: '',
    title: '',
    role: VDRRole.VIEWER,
    accessLevel: VDRAccessLevel.RESTRICTED,
    canDownload: false,
    canPrint: false,
    canComment: false,
    message: ''
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isInviting, setIsInviting] = useState(false)

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Name validation (optional but if provided, should be valid)
    if (formData.firstName && formData.firstName.length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters'
    }
    
    if (formData.lastName && formData.lastName.length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters'
    }

    // Message length validation
    if (formData.message && formData.message.length > 500) {
      newErrors.message = 'Message must be less than 500 characters'
    }

    // Expiration date validation
    if (formData.expiresAt) {
      const expirationDate = new Date(formData.expiresAt)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      if (expirationDate <= today) {
        newErrors.expiresAt = 'Expiration date must be in the future'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof InviteUserRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsInviting(true)

    try {
      // Clean up form data
      const invitationData: InviteUserRequest = {
        ...formData,
        email: formData.email.trim().toLowerCase(),
        firstName: formData.firstName?.trim() || undefined,
        lastName: formData.lastName?.trim() || undefined,
        company: formData.company?.trim() || undefined,
        title: formData.title?.trim() || undefined,
        message: formData.message?.trim() || undefined
      }

      await onInvite(invitationData)
      
      // Reset form
      setFormData({
        email: '',
        firstName: '',
        lastName: '',
        company: '',
        title: '',
        role: VDRRole.VIEWER,
        accessLevel: VDRAccessLevel.RESTRICTED,
        canDownload: false,
        canPrint: false,
        canComment: false,
        message: ''
      })
      setErrors({})
      
    } catch (error) {
      console.error('Failed to invite user:', error)
      setErrors({ submit: 'Failed to send invitation. Please try again.' })
    } finally {
      setIsInviting(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      email: '',
      firstName: '',
      lastName: '',
      company: '',
      title: '',
      role: VDRRole.VIEWER,
      accessLevel: VDRAccessLevel.RESTRICTED,
      canDownload: false,
      canPrint: false,
      canComment: false,
      message: ''
    })
    setErrors({})
    setIsInviting(false)
    onOpenChange(false)
  }

  const getRoleDescription = (role: VDRRole) => {
    const descriptions = {
      [VDRRole.ADMIN]: 'Full access to all features and user management',
      [VDRRole.MANAGER]: 'Can manage content and invite users',
      [VDRRole.CONTRIBUTOR]: 'Can upload and manage their own content',
      [VDRRole.VIEWER]: 'Can only view content (default)'
    }
    return descriptions[role]
  }

  const getAccessLevelDescription = (level: VDRAccessLevel) => {
    const descriptions = {
      [VDRAccessLevel.PUBLIC]: 'No restrictions',
      [VDRAccessLevel.INTERNAL]: 'Team members only',
      [VDRAccessLevel.RESTRICTED]: 'Limited access (default)',
      [VDRAccessLevel.CONFIDENTIAL]: 'Highly restricted',
      [VDRAccessLevel.ADMIN_ONLY]: 'Administrators only'
    }
    return descriptions[level]
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Invite User to {vdr.name}
          </DialogTitle>
          <DialogDescription>
            Send an invitation to access this Virtual Data Room
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">User Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="email">Email Address *</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                    disabled={isInviting}
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.email}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    placeholder="John"
                    className={`pl-10 ${errors.firstName ? 'border-red-500' : ''}`}
                    disabled={isInviting}
                  />
                </div>
                {errors.firstName && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.firstName}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  placeholder="Doe"
                  className={errors.lastName ? 'border-red-500' : ''}
                  disabled={isInviting}
                />
                {errors.lastName && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.lastName}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="company">Company</Label>
                <div className="relative">
                  <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="company"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    placeholder="Acme Corp"
                    className="pl-10"
                    disabled={isInviting}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="title">Job Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Senior Analyst"
                  disabled={isInviting}
                />
              </div>
            </div>
          </div>

          {/* Access Control */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Access Control</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select
                  value={formData.role}
                  onValueChange={(value) => handleInputChange('role', value as VDRRole)}
                  disabled={isInviting}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(VDRRole).map(role => (
                      <SelectItem key={role} value={role}>
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-2 h-2 rounded-full" 
                            style={{ backgroundColor: VDR_ROLE_COLORS[role] }}
                          ></div>
                          <div>
                            <div className="font-medium">{role}</div>
                            <div className="text-xs text-gray-500">{getRoleDescription(role)}</div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="accessLevel">Access Level</Label>
                <Select
                  value={formData.accessLevel}
                  onValueChange={(value) => handleInputChange('accessLevel', value as VDRAccessLevel)}
                  disabled={isInviting}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(VDRAccessLevel).map(level => (
                      <SelectItem key={level} value={level}>
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-2 h-2 rounded-full" 
                            style={{ backgroundColor: ACCESS_LEVEL_COLORS[level] }}
                          ></div>
                          <div>
                            <div className="font-medium">{level.replace('_', ' ')}</div>
                            <div className="text-xs text-gray-500">{getAccessLevelDescription(level)}</div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Permissions */}
            <div className="space-y-3">
              <Label>Permissions</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="canDownload"
                    checked={formData.canDownload}
                    onCheckedChange={(checked) => handleInputChange('canDownload', checked)}
                    disabled={isInviting}
                  />
                  <Label htmlFor="canDownload" className="text-sm">
                    Can download documents
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="canPrint"
                    checked={formData.canPrint}
                    onCheckedChange={(checked) => handleInputChange('canPrint', checked)}
                    disabled={isInviting}
                  />
                  <Label htmlFor="canPrint" className="text-sm">
                    Can print documents
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="canComment"
                    checked={formData.canComment}
                    onCheckedChange={(checked) => handleInputChange('canComment', checked)}
                    disabled={isInviting}
                  />
                  <Label htmlFor="canComment" className="text-sm">
                    Can add comments
                  </Label>
                </div>
              </div>
            </div>

            {/* Expiration Date */}
            <div className="space-y-2">
              <Label htmlFor="expiresAt">Access Expires (Optional)</Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="expiresAt"
                  type="date"
                  value={formData.expiresAt}
                  onChange={(e) => handleInputChange('expiresAt', e.target.value)}
                  className={`pl-10 ${errors.expiresAt ? 'border-red-500' : ''}`}
                  disabled={isInviting}
                />
              </div>
              {errors.expiresAt && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.expiresAt}
                </p>
              )}
            </div>
          </div>

          {/* Personal Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Personal Message (Optional)</Label>
            <div className="relative">
              <MessageSquare className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <Textarea
                id="message"
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                placeholder="Add a personal message to the invitation..."
                rows={3}
                className={`pl-10 ${errors.message ? 'border-red-500' : ''}`}
                disabled={isInviting}
              />
            </div>
            {errors.message && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.message}
              </p>
            )}
            <p className="text-xs text-gray-500">
              {formData.message?.length || 0}/500 characters
            </p>
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.submit}
              </p>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isInviting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isInviting || !formData.email.trim()}
            >
              {isInviting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sending Invitation...
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Send Invitation
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
