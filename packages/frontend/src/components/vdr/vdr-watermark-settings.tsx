import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { 
  Droplets, 
  Eye, 
  Settings,
  Palette,
  RotateCw,
  Type,
  Shield,
  Save,
  RotateCcw,
  AlertCircle
} from 'lucide-react'

interface WatermarkSettings {
  enabled: boolean
  text: string
  opacity: number
  fontSize: number
  color: string
  position: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'diagonal'
  rotation: number
  pattern: 'single' | 'repeated' | 'background'
  includeUserEmail: boolean
  includeTimestamp: boolean
  includeDocumentId: boolean
  customText: string
}

interface VDRWatermarkSettingsProps {
  vdrId: string
  currentSettings?: WatermarkSettings
  onSave: (settings: WatermarkSettings) => void
  onPreview: (settings: WatermarkSettings) => void
}

export function VDRWatermarkSettings({
  vdrId,
  currentSettings,
  onSave,
  onPreview
}: VDRWatermarkSettingsProps) {
  const [settings, setSettings] = useState<WatermarkSettings>(
    currentSettings || {
      enabled: true,
      text: 'CONFIDENTIAL',
      opacity: 30,
      fontSize: 24,
      color: '#666666',
      position: 'diagonal',
      rotation: -45,
      pattern: 'repeated',
      includeUserEmail: true,
      includeTimestamp: true,
      includeDocumentId: false,
      customText: ''
    }
  )

  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const handleSettingChange = <K extends keyof WatermarkSettings>(
    key: K,
    value: WatermarkSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      await onSave(settings)
    } catch (error) {
      console.error('Failed to save watermark settings:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handlePreview = () => {
    setIsPreviewMode(true)
    onPreview(settings)
    setTimeout(() => setIsPreviewMode(false), 3000)
  }

  const resetToDefaults = () => {
    setSettings({
      enabled: true,
      text: 'CONFIDENTIAL',
      opacity: 30,
      fontSize: 24,
      color: '#666666',
      position: 'diagonal',
      rotation: -45,
      pattern: 'repeated',
      includeUserEmail: true,
      includeTimestamp: true,
      includeDocumentId: false,
      customText: ''
    })
  }

  const getWatermarkText = () => {
    let text = settings.customText || settings.text
    
    if (settings.includeUserEmail) {
      text += ' • <EMAIL>'
    }
    
    if (settings.includeTimestamp) {
      text += ' • ' + new Date().toLocaleDateString()
    }
    
    if (settings.includeDocumentId) {
      text += ' • DOC-123'
    }
    
    return text
  }

  const previewStyle = {
    color: settings.color,
    opacity: settings.opacity / 100,
    fontSize: `${settings.fontSize}px`,
    transform: `rotate(${settings.rotation}deg)`,
    fontWeight: 'bold',
    fontFamily: 'Arial, sans-serif',
    userSelect: 'none' as const,
    pointerEvents: 'none' as const
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Droplets className="h-5 w-5" />
            Watermark Settings
          </CardTitle>
          <CardDescription>
            Configure document watermarks for enhanced security and tracking
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Settings Panel */}
        <div className="space-y-6">
          {/* Basic Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable Watermarks</Label>
                  <p className="text-sm text-gray-500">
                    Apply watermarks to all documents
                  </p>
                </div>
                <Switch
                  checked={settings.enabled}
                  onCheckedChange={(checked) => handleSettingChange('enabled', checked)}
                />
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="watermarkText">Watermark Text</Label>
                <Input
                  id="watermarkText"
                  value={settings.text}
                  onChange={(e) => handleSettingChange('text', e.target.value)}
                  placeholder="Enter watermark text"
                  disabled={!settings.enabled}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="customText">Custom Text (Optional)</Label>
                <Input
                  id="customText"
                  value={settings.customText}
                  onChange={(e) => handleSettingChange('customText', e.target.value)}
                  placeholder="Additional custom text"
                  disabled={!settings.enabled}
                />
              </div>
            </CardContent>
          </Card>

          {/* Appearance Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Appearance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Opacity: {settings.opacity}%</Label>
                <Slider
                  value={[settings.opacity]}
                  onValueChange={([value]) => handleSettingChange('opacity', value)}
                  max={100}
                  min={5}
                  step={5}
                  disabled={!settings.enabled}
                />
              </div>

              <div className="space-y-2">
                <Label>Font Size: {settings.fontSize}px</Label>
                <Slider
                  value={[settings.fontSize]}
                  onValueChange={([value]) => handleSettingChange('fontSize', value)}
                  max={72}
                  min={8}
                  step={2}
                  disabled={!settings.enabled}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="color">Color</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="color"
                    type="color"
                    value={settings.color}
                    onChange={(e) => handleSettingChange('color', e.target.value)}
                    className="w-16 h-10 p-1"
                    disabled={!settings.enabled}
                  />
                  <Input
                    value={settings.color}
                    onChange={(e) => handleSettingChange('color', e.target.value)}
                    placeholder="#666666"
                    className="flex-1"
                    disabled={!settings.enabled}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Rotation: {settings.rotation}°</Label>
                <Slider
                  value={[settings.rotation]}
                  onValueChange={([value]) => handleSettingChange('rotation', value)}
                  max={180}
                  min={-180}
                  step={15}
                  disabled={!settings.enabled}
                />
              </div>
            </CardContent>
          </Card>

          {/* Position & Pattern */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Position & Pattern</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="position">Position</Label>
                <Select
                  value={settings.position}
                  onValueChange={(value) => handleSettingChange('position', value as any)}
                  disabled={!settings.enabled}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="center">Center</SelectItem>
                    <SelectItem value="top-left">Top Left</SelectItem>
                    <SelectItem value="top-right">Top Right</SelectItem>
                    <SelectItem value="bottom-left">Bottom Left</SelectItem>
                    <SelectItem value="bottom-right">Bottom Right</SelectItem>
                    <SelectItem value="diagonal">Diagonal</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="pattern">Pattern</Label>
                <Select
                  value={settings.pattern}
                  onValueChange={(value) => handleSettingChange('pattern', value as any)}
                  disabled={!settings.enabled}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="single">Single</SelectItem>
                    <SelectItem value="repeated">Repeated</SelectItem>
                    <SelectItem value="background">Background</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Metadata Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Security Metadata
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Include User Email</Label>
                  <p className="text-sm text-gray-500">
                    Add user email to watermark
                  </p>
                </div>
                <Switch
                  checked={settings.includeUserEmail}
                  onCheckedChange={(checked) => handleSettingChange('includeUserEmail', checked)}
                  disabled={!settings.enabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Include Timestamp</Label>
                  <p className="text-sm text-gray-500">
                    Add access timestamp
                  </p>
                </div>
                <Switch
                  checked={settings.includeTimestamp}
                  onCheckedChange={(checked) => handleSettingChange('includeTimestamp', checked)}
                  disabled={!settings.enabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Include Document ID</Label>
                  <p className="text-sm text-gray-500">
                    Add document identifier
                  </p>
                </div>
                <Switch
                  checked={settings.includeDocumentId}
                  onCheckedChange={(checked) => handleSettingChange('includeDocumentId', checked)}
                  disabled={!settings.enabled}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Preview Panel */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Preview
              </CardTitle>
              <CardDescription>
                See how the watermark will appear on documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative bg-white border-2 border-dashed border-gray-300 rounded-lg h-96 overflow-hidden">
                {/* Simulated document background */}
                <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 opacity-50"></div>
                
                {/* Document content simulation */}
                <div className="absolute inset-4 space-y-2">
                  <div className="h-3 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-300 rounded w-full"></div>
                  <div className="h-3 bg-gray-300 rounded w-5/6"></div>
                  <div className="h-3 bg-gray-300 rounded w-2/3"></div>
                  <div className="h-3 bg-gray-300 rounded w-4/5"></div>
                </div>

                {/* Watermark overlay */}
                {settings.enabled && (
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                    {settings.pattern === 'repeated' ? (
                      <div className="grid grid-cols-2 gap-8 w-full h-full items-center justify-items-center">
                        {Array.from({ length: 4 }).map((_, i) => (
                          <div key={i} style={previewStyle}>
                            {getWatermarkText()}
                          </div>
                        ))}
                      </div>
                    ) : settings.pattern === 'background' ? (
                      <div 
                        className="absolute inset-0"
                        style={{
                          backgroundImage: `url("data:image/svg+xml,${encodeURIComponent(`
                            <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                              <text x="100" y="100" text-anchor="middle" 
                                    fill="${settings.color}" 
                                    opacity="${settings.opacity / 100}"
                                    font-size="${settings.fontSize}"
                                    font-weight="bold"
                                    transform="rotate(${settings.rotation} 100 100)">
                                ${getWatermarkText()}
                              </text>
                            </svg>
                          `)}")`,
                          backgroundRepeat: 'repeat',
                          backgroundSize: '200px 200px'
                        }}
                      />
                    ) : (
                      <div 
                        style={{
                          ...previewStyle,
                          position: 'absolute',
                          ...(settings.position === 'top-left' && { top: '20px', left: '20px' }),
                          ...(settings.position === 'top-right' && { top: '20px', right: '20px' }),
                          ...(settings.position === 'bottom-left' && { bottom: '20px', left: '20px' }),
                          ...(settings.position === 'bottom-right' && { bottom: '20px', right: '20px' }),
                          ...(settings.position === 'center' && { top: '50%', left: '50%', transform: `translate(-50%, -50%) rotate(${settings.rotation}deg)` }),
                          ...(settings.position === 'diagonal' && { top: '50%', left: '50%', transform: `translate(-50%, -50%) rotate(${settings.rotation}deg)` })
                        }}
                      >
                        {getWatermarkText()}
                      </div>
                    )}
                  </div>
                )}

                {/* Preview mode indicator */}
                {isPreviewMode && (
                  <div className="absolute top-2 right-2 bg-blue-500 text-white px-2 py-1 rounded text-xs">
                    Preview Mode
                  </div>
                )}
              </div>

              <div className="mt-4 space-y-2">
                <Button 
                  onClick={handlePreview} 
                  variant="outline" 
                  className="w-full"
                  disabled={!settings.enabled}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview on Sample Document
                </Button>
                
                <div className="text-xs text-gray-500 space-y-1">
                  <p><strong>Preview Text:</strong> {getWatermarkText()}</p>
                  <p><strong>Position:</strong> {settings.position}</p>
                  <p><strong>Pattern:</strong> {settings.pattern}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security Notice */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
                <div className="space-y-2">
                  <h4 className="font-medium">Security Notice</h4>
                  <p className="text-sm text-gray-600">
                    Watermarks help track document access and deter unauthorized sharing. 
                    They are applied dynamically when documents are viewed or downloaded.
                  </p>
                  <p className="text-sm text-gray-600">
                    Each watermark contains unique metadata that can be used to trace 
                    document access back to specific users and sessions.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={resetToDefaults}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset to Defaults
            </Button>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handlePreview}
                disabled={!settings.enabled}
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              
              <Button
                onClick={handleSave}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Settings
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
