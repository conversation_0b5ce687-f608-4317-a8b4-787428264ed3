import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  X, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  File,
  Folder,
  Pause,
  Play,
  RotateCcw
} from 'lucide-react'
import { FileUploadProgress, VDRFolder, ALLOWED_FILE_TYPES, MAX_FILE_SIZE } from '@/types/vdr'

interface VDRBulkUploadProps {
  vdrId: string
  currentFolder?: VDRFolder | null
  onUploadComplete?: (results: UploadResult[]) => void
  onUploadProgress?: (progress: FileUploadProgress[]) => void
  maxConcurrentUploads?: number
  className?: string
}

interface UploadResult {
  file: File
  success: boolean
  documentId?: string
  error?: string
}

interface UploadQueue {
  file: File
  status: 'pending' | 'uploading' | 'completed' | 'error' | 'paused'
  progress: number
  error?: string
  documentId?: string
  retryCount: number
}

export function VDRBulkUpload({
  vdrId,
  currentFolder,
  onUploadComplete,
  onUploadProgress,
  maxConcurrentUploads = 3,
  className
}: VDRBulkUploadProps) {
  const [uploadQueue, setUploadQueue] = useState<UploadQueue[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [activeUploads, setActiveUploads] = useState(0)

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFiles = Array.from(e.dataTransfer.files)
      addFilesToQueue(droppedFiles)
    }
  }, [])

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files)
      addFilesToQueue(selectedFiles)
    }
  }

  const addFilesToQueue = (files: File[]) => {
    const validFiles: UploadQueue[] = []

    files.forEach(file => {
      // Validate file
      if (file.size > MAX_FILE_SIZE) {
        console.warn(`File ${file.name} exceeds size limit`)
        return
      }

      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        console.warn(`File type ${file.type} not allowed for ${file.name}`)
        return
      }

      // Check for duplicates
      if (uploadQueue.some(item => item.file.name === file.name && item.file.size === file.size)) {
        console.warn(`File ${file.name} already in queue`)
        return
      }

      validFiles.push({
        file,
        status: 'pending',
        progress: 0,
        retryCount: 0
      })
    })

    setUploadQueue(prev => [...prev, ...validFiles])
  }

  const removeFromQueue = (index: number) => {
    setUploadQueue(prev => prev.filter((_, i) => i !== index))
  }

  const retryUpload = (index: number) => {
    setUploadQueue(prev => 
      prev.map((item, i) => 
        i === index 
          ? { ...item, status: 'pending', progress: 0, error: undefined }
          : item
      )
    )
  }

  const clearCompleted = () => {
    setUploadQueue(prev => prev.filter(item => 
      item.status !== 'completed' && item.status !== 'error'
    ))
  }

  const clearAll = () => {
    if (!isUploading) {
      setUploadQueue([])
    }
  }

  const startUploads = async () => {
    if (uploadQueue.length === 0) return

    setIsUploading(true)
    setIsPaused(false)

    const pendingUploads = uploadQueue.filter(item => item.status === 'pending')
    
    // Process uploads with concurrency limit
    const uploadPromises: Promise<void>[] = []
    
    for (let i = 0; i < Math.min(maxConcurrentUploads, pendingUploads.length); i++) {
      uploadPromises.push(processUploadQueue())
    }

    await Promise.all(uploadPromises)
    
    setIsUploading(false)
    setActiveUploads(0)

    // Notify completion
    const results: UploadResult[] = uploadQueue.map(item => ({
      file: item.file,
      success: item.status === 'completed',
      documentId: item.documentId,
      error: item.error
    }))

    onUploadComplete?.(results)
  }

  const processUploadQueue = async (): Promise<void> => {
    while (true) {
      if (isPaused) {
        await new Promise(resolve => setTimeout(resolve, 1000))
        continue
      }

      const nextUpload = uploadQueue.find(item => item.status === 'pending')
      if (!nextUpload) break

      setActiveUploads(prev => prev + 1)
      await uploadFile(nextUpload)
      setActiveUploads(prev => prev - 1)
    }
  }

  const uploadFile = async (uploadItem: UploadQueue): Promise<void> => {
    const index = uploadQueue.findIndex(item => item === uploadItem)
    
    try {
      // Update status to uploading
      setUploadQueue(prev => 
        prev.map((item, i) => 
          i === index ? { ...item, status: 'uploading' } : item
        )
      )

      // Simulate upload progress
      for (let progress = 0; progress <= 100; progress += 10) {
        if (isPaused) {
          setUploadQueue(prev => 
            prev.map((item, i) => 
              i === index ? { ...item, status: 'paused' } : item
            )
          )
          return
        }

        await new Promise(resolve => setTimeout(resolve, 200))
        
        setUploadQueue(prev => 
          prev.map((item, i) => 
            i === index ? { ...item, progress } : item
          )
        )
      }

      // Simulate API call
      const formData = new FormData()
      formData.append('file', uploadItem.file)
      formData.append('vdrId', vdrId)
      if (currentFolder) {
        formData.append('folderId', currentFolder.id)
      }

      // In a real implementation, this would be an actual API call
      const response = await fetch('/api/vdr/upload', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`)
      }

      const result = await response.json()

      // Mark as completed
      setUploadQueue(prev => 
        prev.map((item, i) => 
          i === index 
            ? { 
                ...item, 
                status: 'completed', 
                progress: 100,
                documentId: result.documentId 
              }
            : item
        )
      )

    } catch (error) {
      console.error('Upload failed:', error)
      
      // Mark as error
      setUploadQueue(prev => 
        prev.map((item, i) => 
          i === index 
            ? { 
                ...item, 
                status: 'error', 
                error: error instanceof Error ? error.message : 'Upload failed',
                retryCount: item.retryCount + 1
              }
            : item
        )
      )
    }
  }

  const pauseUploads = () => {
    setIsPaused(true)
  }

  const resumeUploads = () => {
    setIsPaused(false)
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (status: UploadQueue['status']) => {
    switch (status) {
      case 'pending':
        return <File className="h-4 w-4 text-gray-500" />
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />
      default:
        return <File className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: UploadQueue['status']) => {
    const variants = {
      pending: 'secondary',
      uploading: 'default',
      completed: 'default',
      error: 'destructive',
      paused: 'secondary'
    } as const

    const colors = {
      pending: 'text-gray-600',
      uploading: 'text-blue-600',
      completed: 'text-green-600',
      error: 'text-red-600',
      paused: 'text-yellow-600'
    }

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const totalFiles = uploadQueue.length
  const completedFiles = uploadQueue.filter(item => item.status === 'completed').length
  const errorFiles = uploadQueue.filter(item => item.status === 'error').length
  const overallProgress = totalFiles > 0 ? (completedFiles / totalFiles) * 100 : 0

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Bulk Upload
        </CardTitle>
        <CardDescription>
          Upload multiple files to {currentFolder ? currentFolder.name : 'the root folder'}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Drop Zone */}
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragActive 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600 mb-2">
            Drop files here or click to browse
          </p>
          <input
            type="file"
            multiple
            onChange={handleFileInputChange}
            className="hidden"
            id="bulk-file-upload"
            accept={ALLOWED_FILE_TYPES.join(',')}
          />
          <Button asChild variant="outline" size="sm">
            <label htmlFor="bulk-file-upload" className="cursor-pointer">
              Choose Files
            </label>
          </Button>
        </div>

        {/* Upload Controls */}
        {uploadQueue.length > 0 && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                onClick={startUploads}
                disabled={isUploading || uploadQueue.every(item => 
                  item.status === 'completed' || item.status === 'error'
                )}
                size="sm"
              >
                <Upload className="h-4 w-4 mr-2" />
                Start Upload
              </Button>

              {isUploading && (
                <Button
                  onClick={isPaused ? resumeUploads : pauseUploads}
                  variant="outline"
                  size="sm"
                >
                  {isPaused ? (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Resume
                    </>
                  ) : (
                    <>
                      <Pause className="h-4 w-4 mr-2" />
                      Pause
                    </>
                  )}
                </Button>
              )}

              <Button
                onClick={clearCompleted}
                variant="outline"
                size="sm"
                disabled={completedFiles === 0 && errorFiles === 0}
              >
                Clear Completed
              </Button>

              <Button
                onClick={clearAll}
                variant="outline"
                size="sm"
                disabled={isUploading}
              >
                Clear All
              </Button>
            </div>

            <div className="text-sm text-gray-600">
              {completedFiles}/{totalFiles} completed
              {errorFiles > 0 && ` • ${errorFiles} failed`}
            </div>
          </div>
        )}

        {/* Overall Progress */}
        {totalFiles > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Overall Progress</span>
              <span>{Math.round(overallProgress)}%</span>
            </div>
            <Progress value={overallProgress} />
          </div>
        )}

        {/* Upload Queue */}
        {uploadQueue.length > 0 && (
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {uploadQueue.map((item, index) => (
              <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                {getStatusIcon(item.status)}
                
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">{item.file.name}</div>
                  <div className="text-sm text-gray-500">
                    {formatFileSize(item.file.size)}
                  </div>
                  {item.status === 'uploading' && (
                    <Progress value={item.progress} className="mt-1" />
                  )}
                  {item.error && (
                    <div className="text-xs text-red-600 mt-1">{item.error}</div>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  {getStatusBadge(item.status)}
                  
                  {item.status === 'error' && item.retryCount < 3 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => retryUpload(index)}
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {(item.status === 'pending' || item.status === 'error') && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFromQueue(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
