import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
    ACCESS_LEVEL_COLORS,
    FILE_TYPE_ICONS,
    VDRAccessLevel,
    VDRBreadcrumb,
    VDRDocument,
    VDRFolder,
    VDRPermissions,
    VirtualDataRoom
} from '@/types/vdr'
import {
    Download,
    Eye,
    File,
    Folder,
    FolderPlus,
    Grid,
    List,
    MoreHorizontal,
    Search,
    Share,
    Shield,
    Sort,
    Upload
} from 'lucide-react'
import React, { useState } from 'react'
// import { VDRFolderTree } from './vdr-folder-tree'
// import { VDRDocumentList } from './vdr-document-list'
// import { VDRUploadDialog } from './vdr-upload-dialog'
// import { VDRCreateFolderDialog } from './vdr-create-folder-dialog'
// import { VDRDocumentPreview } from './vdr-document-preview'

interface VDRFileBrowserProps {
  vdr: VirtualDataRoom
  permissions: VDRPermissions
  onDocumentView?: (document: VDRDocument) => void
  onDocumentDownload?: (document: VDRDocument) => void
  onDocumentShare?: (document: VDRDocument) => void
  onDocumentDelete?: (document: VDRDocument) => void
  onFolderCreate?: (parentId?: string, name: string) => void
  onFolderDelete?: (folder: VDRFolder) => void
  onUpload?: (files: File[], folderId?: string) => void
}

export function VDRFileBrowser({
  vdr,
  permissions,
  onDocumentView,
  onDocumentDownload,
  onDocumentShare,
  onDocumentDelete,
  onFolderCreate,
  onFolderDelete,
  onUpload
}: VDRFileBrowserProps) {
  const [currentFolder, setCurrentFolder] = useState<VDRFolder | null>(null)
  const [breadcrumbs, setBreadcrumbs] = useState<VDRBreadcrumb[]>([
    { id: 'root', name: vdr.name, path: '/' }
  ])
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size' | 'type'>('name')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [showCreateFolderDialog, setShowCreateFolderDialog] = useState(false)
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [previewDocument, setPreviewDocument] = useState<VDRDocument | null>(null)

  // Get current folder contents
  const currentFolders = vdr.folders?.filter(folder => 
    folder.parentId === currentFolder?.id
  ) || []

  const currentDocuments = vdr.documents?.filter(document => 
    document.folderId === currentFolder?.id
  ) || []

  // Filter and sort items
  const filteredFolders = currentFolders.filter(folder =>
    folder.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredDocuments = currentDocuments.filter(document =>
    document.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    document.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const sortedFolders = [...filteredFolders].sort((a, b) => {
    const direction = sortDirection === 'asc' ? 1 : -1
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name) * direction
      case 'date':
        return (new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()) * direction
      default:
        return 0
    }
  })

  const sortedDocuments = [...filteredDocuments].sort((a, b) => {
    const direction = sortDirection === 'asc' ? 1 : -1
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name) * direction
      case 'date':
        return (new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()) * direction
      case 'size':
        return (a.size - b.size) * direction
      case 'type':
        return a.mimeType.localeCompare(b.mimeType) * direction
      default:
        return 0
    }
  })

  const handleFolderClick = (folder: VDRFolder) => {
    setCurrentFolder(folder)
    setBreadcrumbs(prev => [
      ...prev,
      { id: folder.id, name: folder.name, path: folder.path }
    ])
  }

  const handleBreadcrumbClick = (index: number) => {
    const newBreadcrumbs = breadcrumbs.slice(0, index + 1)
    setBreadcrumbs(newBreadcrumbs)
    
    if (index === 0) {
      setCurrentFolder(null)
    } else {
      const targetFolder = vdr.folders?.find(f => f.id === newBreadcrumbs[index].id)
      setCurrentFolder(targetFolder || null)
    }
  }

  const handleDocumentClick = (document: VDRDocument) => {
    if (permissions.canView) {
      setPreviewDocument(document)
      onDocumentView?.(document)
    }
  }

  const handleUpload = (files: File[]) => {
    onUpload?.(files, currentFolder?.id)
    setShowUploadDialog(false)
  }

  const handleCreateFolder = (name: string) => {
    onFolderCreate?.(currentFolder?.id, name)
    setShowCreateFolderDialog(false)
  }

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortDirection('asc')
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (mimeType: string) => {
    return FILE_TYPE_ICONS[mimeType as keyof typeof FILE_TYPE_ICONS] || FILE_TYPE_ICONS.default
  }

  const getAccessLevelBadge = (level: VDRAccessLevel) => {
    return (
      <Badge 
        variant="outline" 
        style={{ 
          borderColor: ACCESS_LEVEL_COLORS[level],
          color: ACCESS_LEVEL_COLORS[level]
        }}
        className="text-xs"
      >
        <Shield className="h-3 w-3 mr-1" />
        {level}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Folder className="h-5 w-5" />
                {vdr.name}
              </CardTitle>
              <CardDescription>
                Virtual Data Room • {vdr.totalViews} views • {vdr.totalDownloads} downloads
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              {permissions.canUpload && (
                <Button onClick={() => setShowUploadDialog(true)}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload
                </Button>
              )}
              
              {permissions.canUpload && (
                <Button variant="outline" onClick={() => setShowCreateFolderDialog(true)}>
                  <FolderPlus className="h-4 w-4 mr-2" />
                  New Folder
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Breadcrumbs */}
      <div className="flex items-center gap-2 text-sm text-gray-600">
        {breadcrumbs.map((crumb, index) => (
          <React.Fragment key={crumb.id}>
            <button
              onClick={() => handleBreadcrumbClick(index)}
              className="hover:text-gray-900 hover:underline"
            >
              {crumb.name}
            </button>
            {index < breadcrumbs.length - 1 && <span>/</span>}
          </React.Fragment>
        ))}
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search files and folders..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSort('name')}
                  className={sortBy === 'name' ? 'bg-gray-100' : ''}
                >
                  Name
                  {sortBy === 'name' && (
                    <Sort className={`h-3 w-3 ml-1 ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                  )}
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSort('date')}
                  className={sortBy === 'date' ? 'bg-gray-100' : ''}
                >
                  Date
                  {sortBy === 'date' && (
                    <Sort className={`h-3 w-3 ml-1 ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                  )}
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSort('size')}
                  className={sortBy === 'size' ? 'bg-gray-100' : ''}
                >
                  Size
                  {sortBy === 'size' && (
                    <Sort className={`h-3 w-3 ml-1 ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                  )}
                </Button>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode('list')}
                className={viewMode === 'list' ? 'bg-gray-100' : ''}
              >
                <List className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode('grid')}
                className={viewMode === 'grid' ? 'bg-gray-100' : ''}
              >
                <Grid className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File Browser */}
      <Card>
        <CardContent className="p-0">
          {viewMode === 'list' ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b bg-gray-50">
                  <tr>
                    <th className="text-left p-4 font-medium">Name</th>
                    <th className="text-left p-4 font-medium">Size</th>
                    <th className="text-left p-4 font-medium">Modified</th>
                    <th className="text-left p-4 font-medium">Access</th>
                    <th className="text-right p-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {/* Folders */}
                  {sortedFolders.map((folder) => (
                    <tr 
                      key={folder.id} 
                      className="border-b hover:bg-gray-50 cursor-pointer"
                      onClick={() => handleFolderClick(folder)}
                    >
                      <td className="p-4">
                        <div className="flex items-center gap-3">
                          <Folder className="h-5 w-5 text-blue-600" />
                          <div>
                            <div className="font-medium">{folder.name}</div>
                            {folder.description && (
                              <div className="text-sm text-gray-500">{folder.description}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="p-4 text-gray-500">
                        {folder._count?.documents || 0} items
                      </td>
                      <td className="p-4 text-gray-500">
                        {new Date(folder.updatedAt).toLocaleDateString()}
                      </td>
                      <td className="p-4">
                        {getAccessLevelBadge(folder.accessLevel)}
                      </td>
                      <td className="p-4">
                        <div className="flex items-center justify-end gap-1">
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                  
                  {/* Documents */}
                  {sortedDocuments.map((document) => (
                    <tr 
                      key={document.id} 
                      className="border-b hover:bg-gray-50 cursor-pointer"
                      onClick={() => handleDocumentClick(document)}
                    >
                      <td className="p-4">
                        <div className="flex items-center gap-3">
                          <span className="text-2xl">{getFileIcon(document.mimeType)}</span>
                          <div>
                            <div className="font-medium">{document.name}</div>
                            <div className="text-sm text-gray-500">
                              v{document.version} • {document.viewCount} views
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4 text-gray-500">
                        {formatFileSize(document.size)}
                      </td>
                      <td className="p-4 text-gray-500">
                        {new Date(document.updatedAt).toLocaleDateString()}
                      </td>
                      <td className="p-4">
                        {getAccessLevelBadge(document.accessLevel)}
                      </td>
                      <td className="p-4">
                        <div className="flex items-center justify-end gap-1">
                          {permissions.canView && (
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDocumentClick(document)
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {permissions.canDownload && (
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                onDocumentDownload?.(document)
                              }}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {permissions.canShare && (
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                onDocumentShare?.(document)
                              }}
                            >
                              <Share className="h-4 w-4" />
                            </Button>
                          )}
                          
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {sortedFolders.length === 0 && sortedDocuments.length === 0 && (
                <div className="text-center py-12">
                  <Folder className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No items found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm ? 'No files or folders match your search.' : 'This folder is empty.'}
                  </p>
                  {permissions.canUpload && !searchTerm && (
                    <Button onClick={() => setShowUploadDialog(true)}>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Files
                    </Button>
                  )}
                </div>
              )}
            </div>
          ) : (
            // Grid view would go here
            <div className="p-4">
              <div className="text-center py-12">
                <Grid className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Grid view coming soon</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Dialog */}
      {showUploadDialog && (
        <VDRUploadDialog
          open={showUploadDialog}
          onOpenChange={setShowUploadDialog}
          onUpload={handleUpload}
          currentFolder={currentFolder}
          maxFileSize={100 * 1024 * 1024} // 100MB
        />
      )}

      {/* Create Folder Dialog */}
      {showCreateFolderDialog && (
        <VDRCreateFolderDialog
          open={showCreateFolderDialog}
          onOpenChange={setShowCreateFolderDialog}
          onCreateFolder={handleCreateFolder}
          parentFolder={currentFolder}
        />
      )}

      {/* Document Preview */}
      {previewDocument && (
        <VDRDocumentPreview
          document={previewDocument}
          open={!!previewDocument}
          onOpenChange={() => setPreviewDocument(null)}
          permissions={permissions}
          onDownload={onDocumentDownload}
          onShare={onDocumentShare}
        />
      )}
    </div>
  )
}
