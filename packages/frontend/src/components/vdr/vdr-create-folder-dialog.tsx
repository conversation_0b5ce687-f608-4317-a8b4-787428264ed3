import React, { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  FolderPlus, 
  AlertCircle,
  Folder
} from 'lucide-react'
import { VDRFolder, VDRAccessLevel } from '@/types/vdr'

interface VDRCreateFolderDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateFolder: (name: string, description?: string, accessLevel?: VDRAccessLevel) => void
  parentFolder?: VDRFolder | null
}

export function VDRCreateFolderDialog({
  open,
  onOpenChange,
  onCreateFolder,
  parentFolder
}: VDRCreateFolderDialogProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [accessLevel, setAccessLevel] = useState<VDRAccessLevel>(VDRAccessLevel.RESTRICTED)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isCreating, setIsCreating] = useState(false)

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!name.trim()) {
      newErrors.name = 'Folder name is required'
    } else if (name.trim().length < 2) {
      newErrors.name = 'Folder name must be at least 2 characters'
    } else if (name.trim().length > 100) {
      newErrors.name = 'Folder name must be less than 100 characters'
    } else if (!/^[a-zA-Z0-9\s\-_().]+$/.test(name.trim())) {
      newErrors.name = 'Folder name contains invalid characters'
    }

    if (description && description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsCreating(true)

    try {
      await onCreateFolder(
        name.trim(),
        description.trim() || undefined,
        accessLevel
      )

      // Reset form
      setName('')
      setDescription('')
      setAccessLevel(VDRAccessLevel.RESTRICTED)
      setErrors({})
      
      onOpenChange(false)
    } catch (error) {
      console.error('Failed to create folder:', error)
      setErrors({ submit: 'Failed to create folder. Please try again.' })
    } finally {
      setIsCreating(false)
    }
  }

  const handleCancel = () => {
    setName('')
    setDescription('')
    setAccessLevel(VDRAccessLevel.RESTRICTED)
    setErrors({})
    setIsCreating(false)
    onOpenChange(false)
  }

  const handleNameChange = (value: string) => {
    setName(value)
    if (errors.name) {
      setErrors(prev => ({ ...prev, name: '' }))
    }
  }

  const handleDescriptionChange = (value: string) => {
    setDescription(value)
    if (errors.description) {
      setErrors(prev => ({ ...prev, description: '' }))
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FolderPlus className="h-5 w-5" />
            Create New Folder
          </DialogTitle>
          <DialogDescription>
            Create a new folder {parentFolder ? `in "${parentFolder.name}"` : 'in the root directory'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Folder Name */}
          <div className="space-y-2">
            <Label htmlFor="folderName">
              Folder Name *
            </Label>
            <Input
              id="folderName"
              value={name}
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder="Enter folder name"
              className={errors.name ? 'border-red-500' : ''}
              disabled={isCreating}
            />
            {errors.name && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.name}
              </p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="folderDescription">
              Description (Optional)
            </Label>
            <Textarea
              id="folderDescription"
              value={description}
              onChange={(e) => handleDescriptionChange(e.target.value)}
              placeholder="Enter folder description"
              rows={3}
              className={errors.description ? 'border-red-500' : ''}
              disabled={isCreating}
            />
            {errors.description && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.description}
              </p>
            )}
            <p className="text-xs text-gray-500">
              {description.length}/500 characters
            </p>
          </div>

          {/* Access Level */}
          <div className="space-y-2">
            <Label htmlFor="accessLevel">
              Access Level
            </Label>
            <Select 
              value={accessLevel} 
              onValueChange={(value) => setAccessLevel(value as VDRAccessLevel)}
              disabled={isCreating}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={VDRAccessLevel.PUBLIC}>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Public - Anyone can access
                  </div>
                </SelectItem>
                <SelectItem value={VDRAccessLevel.INTERNAL}>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Internal - Team members only
                  </div>
                </SelectItem>
                <SelectItem value={VDRAccessLevel.RESTRICTED}>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    Restricted - Limited access
                  </div>
                </SelectItem>
                <SelectItem value={VDRAccessLevel.CONFIDENTIAL}>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    Confidential - Highly restricted
                  </div>
                </SelectItem>
                <SelectItem value={VDRAccessLevel.ADMIN_ONLY}>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-gray-800 rounded-full"></div>
                    Admin Only - Administrators only
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Parent Folder Info */}
          {parentFolder && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Folder className="h-4 w-4" />
                <span>Creating in: <strong>{parentFolder.name}</strong></span>
              </div>
              {parentFolder.path && (
                <div className="text-xs text-gray-500 mt-1">
                  Path: {parentFolder.path}
                </div>
              )}
            </div>
          )}

          {/* Submit Error */}
          {errors.submit && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.submit}
              </p>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isCreating || !name.trim()}
            >
              {isCreating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <FolderPlus className="h-4 w-4 mr-2" />
                  Create Folder
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
