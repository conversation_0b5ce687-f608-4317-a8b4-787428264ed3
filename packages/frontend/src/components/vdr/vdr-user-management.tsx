import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  UserPlus, 
  Search, 
  MoreHorizontal,
  Mail,
  Calendar,
  Shield,
  Settings,
  Trash2,
  Edit,
  Eye,
  Download,
  MessageSquare,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { 
  VirtualDataRoom, 
  VDRUser, 
  VDRRole, 
  VDRUserStatus,
  VDR_ROLE_COLORS,
  VDR_STATUS_COLORS
} from '@/types/vdr'
import { VDRInviteUserDialog } from './vdr-invite-user-dialog'
import { VDREditUserDialog } from './vdr-edit-user-dialog'

interface VDRUserManagementProps {
  vdr: VirtualDataRoom
  users: VDRUser[]
  currentUserId: string
  onInviteUser?: (invitation: any) => void
  onUpdateUser?: (userId: string, updates: any) => void
  onRevokeUser?: (userId: string) => void
  onResendInvitation?: (userId: string) => void
}

export function VDRUserManagement({
  vdr,
  users,
  currentUserId,
  onInviteUser,
  onUpdateUser,
  onRevokeUser,
  onResendInvitation
}: VDRUserManagementProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<VDRUserStatus | 'all'>('all')
  const [roleFilter, setRoleFilter] = useState<VDRRole | 'all'>('all')
  const [showInviteDialog, setShowInviteDialog] = useState(false)
  const [editingUser, setEditingUser] = useState<VDRUser | null>(null)

  // Filter users
  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${user.firstName || ''} ${user.lastName || ''}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.company || '').toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || user.status === statusFilter
    const matchesRole = roleFilter === 'all' || user.role === roleFilter

    return matchesSearch && matchesStatus && matchesRole
  })

  // Group users by status
  const usersByStatus = {
    active: filteredUsers.filter(u => u.status === VDRUserStatus.ACTIVE),
    pending: filteredUsers.filter(u => u.status === VDRUserStatus.PENDING),
    suspended: filteredUsers.filter(u => u.status === VDRUserStatus.SUSPENDED),
    expired: filteredUsers.filter(u => u.status === VDRUserStatus.EXPIRED),
    revoked: filteredUsers.filter(u => u.status === VDRUserStatus.REVOKED)
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString()
  }

  const getRoleBadge = (role: VDRRole) => {
    return (
      <Badge 
        variant="outline" 
        style={{ 
          borderColor: VDR_ROLE_COLORS[role],
          color: VDR_ROLE_COLORS[role]
        }}
      >
        {role}
      </Badge>
    )
  }

  const getStatusBadge = (status: VDRUserStatus) => {
    return (
      <Badge 
        variant="outline" 
        style={{ 
          borderColor: VDR_STATUS_COLORS[status],
          color: VDR_STATUS_COLORS[status]
        }}
      >
        {status}
      </Badge>
    )
  }

  const getPermissionIcons = (user: VDRUser) => {
    const permissions = []
    
    if (user.canDownload) {
      permissions.push(
        <Download key="download" className="h-4 w-4 text-green-600" title="Can Download" />
      )
    }
    
    if (user.canComment) {
      permissions.push(
        <MessageSquare key="comment" className="h-4 w-4 text-blue-600" title="Can Comment" />
      )
    }
    
    if (user.canPrint) {
      permissions.push(
        <Eye key="print" className="h-4 w-4 text-purple-600" title="Can Print" />
      )
    }

    return permissions
  }

  const canManageUser = (user: VDRUser) => {
    // Users can't manage themselves or users with higher privileges
    if (user.userId === currentUserId) return false
    
    const currentUser = users.find(u => u.userId === currentUserId)
    if (!currentUser) return false
    
    // Only admins and managers can manage users
    if (!['ADMIN', 'MANAGER'].includes(currentUser.role)) return false
    
    // Managers can't manage admins
    if (currentUser.role === 'MANAGER' && user.role === 'ADMIN') return false
    
    return true
  }

  const handleInviteUser = (invitation: any) => {
    onInviteUser?.(invitation)
    setShowInviteDialog(false)
  }

  const handleEditUser = (user: VDRUser) => {
    setEditingUser(user)
  }

  const handleUpdateUser = (updates: any) => {
    if (editingUser) {
      onUpdateUser?.(editingUser.id, updates)
      setEditingUser(null)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Management
              </CardTitle>
              <CardDescription>
                Manage access and permissions for {vdr.name}
              </CardDescription>
            </div>
            
            <Button onClick={() => setShowInviteDialog(true)}>
              <UserPlus className="h-4 w-4 mr-2" />
              Invite User
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{usersByStatus.active.length}</div>
            <div className="text-sm text-gray-600">Active Users</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">{usersByStatus.pending.length}</div>
            <div className="text-sm text-gray-600">Pending</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">{usersByStatus.suspended.length}</div>
            <div className="text-sm text-gray-600">Suspended</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-600">{usersByStatus.expired.length}</div>
            <div className="text-sm text-gray-600">Expired</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{usersByStatus.revoked.length}</div>
            <div className="text-sm text-gray-600">Revoked</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as VDRUserStatus | 'all')}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Status</option>
              {Object.values(VDRUserStatus).map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value as VDRRole | 'all')}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Roles</option>
              {Object.values(VDRRole).map(role => (
                <option key={role} value={role}>{role}</option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b bg-gray-50">
                <tr>
                  <th className="text-left p-4 font-medium">User</th>
                  <th className="text-left p-4 font-medium">Role</th>
                  <th className="text-left p-4 font-medium">Status</th>
                  <th className="text-left p-4 font-medium">Permissions</th>
                  <th className="text-left p-4 font-medium">Last Login</th>
                  <th className="text-left p-4 font-medium">Invited</th>
                  <th className="text-right p-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="border-b hover:bg-gray-50">
                    <td className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium">
                            {user.firstName?.[0] || user.email[0].toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium">
                            {user.firstName && user.lastName 
                              ? `${user.firstName} ${user.lastName}`
                              : user.email
                            }
                          </div>
                          <div className="text-sm text-gray-500 flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {user.email}
                          </div>
                          {user.company && (
                            <div className="text-xs text-gray-500">{user.company}</div>
                          )}
                        </div>
                      </div>
                    </td>
                    
                    <td className="p-4">
                      {getRoleBadge(user.role)}
                    </td>
                    
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        {getStatusBadge(user.status)}
                        {user.status === VDRUserStatus.PENDING && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onResendInvitation?.(user.id)}
                            title="Resend invitation"
                          >
                            <Mail className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </td>
                    
                    <td className="p-4">
                      <div className="flex items-center gap-1">
                        {getPermissionIcons(user)}
                        {getPermissionIcons(user).length === 0 && (
                          <span className="text-sm text-gray-500">View only</span>
                        )}
                      </div>
                    </td>
                    
                    <td className="p-4">
                      <div className="flex items-center gap-1 text-sm text-gray-600">
                        <Clock className="h-3 w-3" />
                        {formatDate(user.lastLoginAt)}
                      </div>
                    </td>
                    
                    <td className="p-4">
                      <div className="flex items-center gap-1 text-sm text-gray-600">
                        <Calendar className="h-3 w-3" />
                        {formatDate(user.invitedAt)}
                      </div>
                    </td>
                    
                    <td className="p-4">
                      <div className="flex items-center justify-end gap-1">
                        {canManageUser(user) && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditUser(user)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onRevokeUser?.(user.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                        
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {filteredUsers.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No users found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm ? 'No users match your search criteria.' : 'No users have been invited yet.'}
                </p>
                <Button onClick={() => setShowInviteDialog(true)}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Invite First User
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Invite User Dialog */}
      {showInviteDialog && (
        <VDRInviteUserDialog
          open={showInviteDialog}
          onOpenChange={setShowInviteDialog}
          onInvite={handleInviteUser}
          vdr={vdr}
        />
      )}

      {/* Edit User Dialog */}
      {editingUser && (
        <VDREditUserDialog
          open={!!editingUser}
          onOpenChange={() => setEditingUser(null)}
          onUpdate={handleUpdateUser}
          user={editingUser}
          vdr={vdr}
        />
      )}
    </div>
  )
}
