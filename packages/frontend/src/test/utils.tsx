import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Mock data factories
export const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  avatar: null,
  status: 'ACTIVE' as const,
  tenantId: 'tenant-1',
}

export const mockTenant = {
  id: 'tenant-1',
  name: 'Test Company',
  domain: 'test.example.com',
  subdomain: 'test',
  status: 'ACTIVE' as const,
}

export const mockDeal = {
  id: 'deal-1',
  title: 'Test Acquisition',
  description: 'Test deal description',
  status: 'PIPELINE' as const,
  stage: 'Initial Review',
  value: 1000000,
  currency: 'USD',
  priority: 'HIGH' as const,
  targetCompany: 'Target Corp',
  tenantId: 'tenant-1',
  createdById: 'user-1',
}

// Test helpers
export const waitForLoadingToFinish = () =>
  new Promise(resolve => setTimeout(resolve, 0))

export const createMockQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  })
