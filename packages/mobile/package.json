{"name": "mna-platform-mobile", "version": "1.0.0", "description": "M&A Platform Mobile Application", "main": "index.js", "scripts": {"start": "react-native start", "android": "react-native run-android", "ios": "react-native run-ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace MNAPlatform.xcworkspace -scheme MNAPlatform -configuration Release -destination generic/platform=iOS -archivePath MNAPlatform.xcarchive archive", "bundle:android": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "bundle:ios": "react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle --assets-dest ios", "clean": "react-native clean", "clean:android": "cd android && ./gradlew clean", "clean:ios": "cd ios && xcodebuild clean", "postinstall": "cd ios && pod install"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/netinfo": "^9.4.1", "@react-native-firebase/app": "^18.3.0", "@react-native-firebase/messaging": "^18.3.0", "@react-native-firebase/analytics": "^18.3.0", "@react-native-firebase/crashlytics": "^18.3.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "@reduxjs/toolkit": "^1.9.5", "react": "18.2.0", "react-native": "0.72.3", "react-native-biometrics": "^3.0.1", "react-native-config": "^1.5.1", "react-native-device-info": "^10.8.0", "react-native-encrypted-storage": "^4.0.3", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.12.1", "react-native-keychain": "^8.1.2", "react-native-paper": "^5.9.1", "react-native-permissions": "^3.8.4", "react-native-reanimated": "^3.3.0", "react-native-safe-area-context": "^4.7.1", "react-native-screens": "^3.22.1", "react-native-splash-screen": "^3.3.0", "react-native-sqlite-storage": "^6.0.1", "react-native-svg": "^13.10.0", "react-native-vector-icons": "^10.0.0", "react-native-webview": "^13.2.2", "react-redux": "^8.1.2", "redux-persist": "^6.0.0", "axios": "^1.4.0", "date-fns": "^2.30.0", "formik": "^2.4.3", "yup": "^1.2.0"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@babel/runtime": "^7.22.6", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.9", "@types/jest": "^29.5.3", "@types/react": "^18.2.15", "@types/react-native": "^0.72.2", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "babel-jest": "^29.6.1", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.0.0", "jest": "^29.6.1", "metro-react-native-babel-preset": "^0.76.7", "prettier": "^3.0.0", "react-test-renderer": "18.2.0", "typescript": "^5.1.6", "detox": "^20.10.1", "@testing-library/react-native": "^12.1.3", "@testing-library/jest-native": "^5.4.2"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|react-native-vector-icons|react-native-paper|react-native-reanimated|react-native-gesture-handler|@react-navigation)/)"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/**/*.test.{js,jsx,ts,tsx}", "!src/**/__tests__/**", "!src/**/index.{js,jsx,ts,tsx}"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "eslintConfig": {"root": true, "extends": ["@react-native", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react", "react-hooks", "react-native"], "rules": {"react-native/no-unused-styles": "error", "react-native/split-platform-components": "error", "react-native/no-inline-styles": "warn", "react-native/no-color-literals": "warn", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn"}}, "prettier": {"arrowParens": "avoid", "bracketSameLine": true, "bracketSpacing": false, "singleQuote": true, "trailingComma": "all", "semi": true, "tabWidth": 2, "useTabs": false, "printWidth": 100}, "detox": {"test-runner": "jest", "runner-config": "e2e/config.json", "configurations": {"ios.sim.debug": {"device": "simulator", "app": "ios.debug"}, "ios.sim.release": {"device": "simulator", "app": "ios.release"}, "android.emu.debug": {"device": "emulator", "app": "android.debug"}, "android.emu.release": {"device": "emulator", "app": "android.release"}}, "devices": {"simulator": {"type": "ios.simulator", "device": {"type": "iPhone 14"}}, "emulator": {"type": "android.emulator", "device": {"avdName": "Pixel_4_API_30"}}}, "apps": {"ios.debug": {"type": "ios.app", "binaryPath": "ios/build/Build/Products/Debug-iphonesimulator/MNAPlatform.app"}, "ios.release": {"type": "ios.app", "binaryPath": "ios/build/Build/Products/Release-iphonesimulator/MNAPlatform.app"}, "android.debug": {"type": "android.apk", "binaryPath": "android/app/build/outputs/apk/debug/app-debug.apk"}, "android.release": {"type": "android.apk", "binaryPath": "android/app/build/outputs/apk/release/app-release.apk"}}}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["react-native", "mobile", "ios", "android", "mna", "mergers", "acquisitions", "finance"], "author": "M&A Platform Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mna-platform/mobile.git"}, "bugs": {"url": "https://github.com/mna-platform/mobile/issues"}, "homepage": "https://github.com/mna-platform/mobile#readme"}