const baseConfig = require('./jest.config.js')

module.exports = {
  ...baseConfig,
  displayName: 'Performance Tests',
  testMatch: [
    '<rootDir>/src/test/performance/**/*.test.{js,ts}',
    '<rootDir>/src/test/**/*.performance.test.{js,ts}'
  ],
  testTimeout: 60000, // 60 seconds for performance tests
  setupFilesAfterEnv: [
    '<rootDir>/src/test/setup/performance-setup.ts'
  ],
  collectCoverageFrom: [
    'src/**/*.{js,ts}',
    '!src/test/**',
    '!src/**/*.d.ts',
    '!src/**/*.config.{js,ts}',
    '!src/migrations/**'
  ],
  coverageDirectory: 'coverage/performance',
  coverageReporters: ['text', 'lcov', 'html'],
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: 'test-results/performance',
      outputName: 'performance-results.xml'
    }]
  ],
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json'
    }
  }
}
