#!/usr/bin/env node

/**
 * Staging Environment Integration Tests
 * 
 * This script runs integration tests against the staging environment
 * to verify that the deployment was successful and all features work correctly.
 */

const axios = require('axios')
const { performance } = require('perf_hooks')

const STAGING_API_URL = process.env.STAGING_API_URL || 'https://api-staging.ma-platform.com'
const TEST_USER_EMAIL = process.env.TEST_USER_EMAIL || '<EMAIL>'
const TEST_USER_PASSWORD = process.env.TEST_USER_PASSWORD || 'staging123'

class StagingTestRunner {
  constructor() {
    this.baseURL = STAGING_API_URL
    this.authToken = null
    this.testResults = []
  }

  async runAllTests() {
    console.log('🚀 Starting staging integration tests...')
    console.log(`📍 Testing against: ${this.baseURL}`)

    try {
      await this.testHealthEndpoints()
      await this.testAuthentication()
      await this.testTenantOperations()
      await this.testDealOperations()
      await this.testDocumentOperations()
      await this.testPerformance()

      this.printResults()
      
      const failedTests = this.testResults.filter(test => !test.passed)
      if (failedTests.length > 0) {
        console.error(`❌ ${failedTests.length} tests failed`)
        process.exit(1)
      } else {
        console.log('✅ All staging tests passed!')
        process.exit(0)
      }
    } catch (error) {
      console.error('💥 Test suite failed:', error.message)
      process.exit(1)
    }
  }

  async testHealthEndpoints() {
    console.log('\n🏥 Testing health endpoints...')

    await this.runTest('Health Check', async () => {
      const response = await axios.get(`${this.baseURL}/health`)
      if (response.status !== 200) {
        throw new Error(`Health check failed: ${response.status}`)
      }
      if (!response.data.status || response.data.status !== 'ok') {
        throw new Error('Health check returned invalid status')
      }
    })

    await this.runTest('API Health Check', async () => {
      const response = await axios.get(`${this.baseURL}/api/health`)
      if (response.status !== 200) {
        throw new Error(`API health check failed: ${response.status}`)
      }
    })

    await this.runTest('Database Health Check', async () => {
      const response = await axios.get(`${this.baseURL}/api/health/database`)
      if (response.status !== 200) {
        throw new Error(`Database health check failed: ${response.status}`)
      }
    })
  }

  async testAuthentication() {
    console.log('\n🔐 Testing authentication...')

    await this.runTest('User Login', async () => {
      const response = await axios.post(`${this.baseURL}/api/auth/login`, {
        email: TEST_USER_EMAIL,
        password: TEST_USER_PASSWORD
      }, {
        headers: {
          'X-Subdomain': 'staging',
          'X-Tenant-ID': 'staging-tenant-id'
        }
      })

      if (response.status !== 200) {
        throw new Error(`Login failed: ${response.status}`)
      }

      if (!response.data.success || !response.data.data.token) {
        throw new Error('Login response missing token')
      }

      this.authToken = response.data.data.token
    })

    await this.runTest('Token Validation', async () => {
      if (!this.authToken) {
        throw new Error('No auth token available')
      }

      const response = await axios.get(`${this.baseURL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'X-Subdomain': 'staging',
          'X-Tenant-ID': 'staging-tenant-id'
        }
      })

      if (response.status !== 200) {
        throw new Error(`Token validation failed: ${response.status}`)
      }

      if (!response.data.success || !response.data.data.email) {
        throw new Error('Token validation response invalid')
      }
    })
  }

  async testTenantOperations() {
    console.log('\n🏢 Testing tenant operations...')

    await this.runTest('Get Current Tenant', async () => {
      const response = await axios.get(`${this.baseURL}/api/tenants/current`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'X-Subdomain': 'staging',
          'X-Tenant-ID': 'staging-tenant-id'
        }
      })

      if (response.status !== 200) {
        throw new Error(`Get tenant failed: ${response.status}`)
      }

      if (!response.data.success || !response.data.data.id) {
        throw new Error('Get tenant response invalid')
      }
    })

    await this.runTest('Get Tenant Stats', async () => {
      const response = await axios.get(`${this.baseURL}/api/tenants/stats`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'X-Subdomain': 'staging',
          'X-Tenant-ID': 'staging-tenant-id'
        }
      })

      if (response.status !== 200) {
        throw new Error(`Get tenant stats failed: ${response.status}`)
      }

      if (!response.data.success) {
        throw new Error('Get tenant stats response invalid')
      }
    })
  }

  async testDealOperations() {
    console.log('\n💼 Testing deal operations...')

    let createdDealId = null

    await this.runTest('List Deals', async () => {
      const response = await axios.get(`${this.baseURL}/api/deals`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'X-Subdomain': 'staging',
          'X-Tenant-ID': 'staging-tenant-id'
        }
      })

      if (response.status !== 200) {
        throw new Error(`List deals failed: ${response.status}`)
      }

      if (!response.data.success || !Array.isArray(response.data.data.items)) {
        throw new Error('List deals response invalid')
      }
    })

    await this.runTest('Create Deal', async () => {
      const response = await axios.post(`${this.baseURL}/api/deals`, {
        title: 'Staging Test Deal',
        type: 'ACQUISITION',
        status: 'ACTIVE',
        targetCompany: 'Staging Target Corp',
        dealValue: 1000000,
        currency: 'USD'
      }, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'X-Subdomain': 'staging',
          'X-Tenant-ID': 'staging-tenant-id'
        }
      })

      if (response.status !== 201) {
        throw new Error(`Create deal failed: ${response.status}`)
      }

      if (!response.data.success || !response.data.data.id) {
        throw new Error('Create deal response invalid')
      }

      createdDealId = response.data.data.id
    })

    if (createdDealId) {
      await this.runTest('Get Deal', async () => {
        const response = await axios.get(`${this.baseURL}/api/deals/${createdDealId}`, {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'X-Subdomain': 'staging',
            'X-Tenant-ID': 'staging-tenant-id'
          }
        })

        if (response.status !== 200) {
          throw new Error(`Get deal failed: ${response.status}`)
        }

        if (!response.data.success || response.data.data.id !== createdDealId) {
          throw new Error('Get deal response invalid')
        }
      })

      await this.runTest('Update Deal', async () => {
        const response = await axios.put(`${this.baseURL}/api/deals/${createdDealId}`, {
          status: 'UNDER_REVIEW',
          notes: 'Updated via staging test'
        }, {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'X-Subdomain': 'staging',
            'X-Tenant-ID': 'staging-tenant-id'
          }
        })

        if (response.status !== 200) {
          throw new Error(`Update deal failed: ${response.status}`)
        }

        if (!response.data.success || response.data.data.status !== 'UNDER_REVIEW') {
          throw new Error('Update deal response invalid')
        }
      })
    }
  }

  async testDocumentOperations() {
    console.log('\n📄 Testing document operations...')

    await this.runTest('List Documents', async () => {
      const response = await axios.get(`${this.baseURL}/api/documents`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'X-Subdomain': 'staging',
          'X-Tenant-ID': 'staging-tenant-id'
        }
      })

      if (response.status !== 200) {
        throw new Error(`List documents failed: ${response.status}`)
      }

      if (!response.data.success || !Array.isArray(response.data.data.items)) {
        throw new Error('List documents response invalid')
      }
    })
  }

  async testPerformance() {
    console.log('\n⚡ Testing performance...')

    await this.runTest('Response Time Check', async () => {
      const startTime = performance.now()
      
      const response = await axios.get(`${this.baseURL}/api/deals`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'X-Subdomain': 'staging',
          'X-Tenant-ID': 'staging-tenant-id'
        }
      })

      const endTime = performance.now()
      const responseTime = endTime - startTime

      if (response.status !== 200) {
        throw new Error(`Performance test failed: ${response.status}`)
      }

      if (responseTime > 2000) { // 2 seconds
        throw new Error(`Response time too slow: ${responseTime}ms`)
      }

      console.log(`    Response time: ${responseTime.toFixed(2)}ms`)
    })
  }

  async runTest(testName, testFunction) {
    try {
      await testFunction()
      this.testResults.push({ name: testName, passed: true })
      console.log(`  ✅ ${testName}`)
    } catch (error) {
      this.testResults.push({ name: testName, passed: false, error: error.message })
      console.log(`  ❌ ${testName}: ${error.message}`)
    }
  }

  printResults() {
    console.log('\n📊 Test Results Summary:')
    console.log('========================')
    
    const passed = this.testResults.filter(test => test.passed).length
    const failed = this.testResults.filter(test => !test.passed).length
    
    console.log(`Total tests: ${this.testResults.length}`)
    console.log(`Passed: ${passed}`)
    console.log(`Failed: ${failed}`)
    
    if (failed > 0) {
      console.log('\nFailed tests:')
      this.testResults
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.error}`)
        })
    }
  }
}

// Run the tests
const testRunner = new StagingTestRunner()
testRunner.runAllTests().catch(error => {
  console.error('Test runner failed:', error)
  process.exit(1)
})
