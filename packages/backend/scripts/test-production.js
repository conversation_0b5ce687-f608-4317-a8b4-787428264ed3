#!/usr/bin/env node

/**
 * Production Environment Smoke Tests
 * 
 * This script runs critical smoke tests against the production environment
 * to verify that the deployment was successful and core functionality works.
 * 
 * These tests are designed to be non-destructive and only test read operations
 * and basic functionality without creating or modifying data.
 */

const axios = require('axios')
const { performance } = require('perf_hooks')

const PRODUCTION_API_URL = process.env.PRODUCTION_API_URL || 'https://api.ma-platform.com'
const TEST_USER_EMAIL = process.env.PROD_TEST_USER_EMAIL || '<EMAIL>'
const TEST_USER_PASSWORD = process.env.PROD_TEST_USER_PASSWORD || 'production123'

class ProductionTestRunner {
  constructor() {
    this.baseURL = PRODUCTION_API_URL
    this.authToken = null
    this.testResults = []
    this.performanceMetrics = []
  }

  async runAllTests() {
    console.log('🚀 Starting production smoke tests...')
    console.log(`📍 Testing against: ${this.baseURL}`)
    console.log('⚠️  Running in READ-ONLY mode for production safety')

    try {
      await this.testCriticalEndpoints()
      await this.testAuthentication()
      await this.testReadOnlyOperations()
      await this.testPerformanceMetrics()
      await this.testSecurityHeaders()

      this.printResults()
      this.printPerformanceMetrics()
      
      const failedTests = this.testResults.filter(test => !test.passed)
      if (failedTests.length > 0) {
        console.error(`❌ ${failedTests.length} critical tests failed`)
        process.exit(1)
      } else {
        console.log('✅ All production smoke tests passed!')
        process.exit(0)
      }
    } catch (error) {
      console.error('💥 Production test suite failed:', error.message)
      process.exit(1)
    }
  }

  async testCriticalEndpoints() {
    console.log('\n🏥 Testing critical endpoints...')

    await this.runTest('Health Check', async () => {
      const startTime = performance.now()
      const response = await axios.get(`${this.baseURL}/health`, {
        timeout: 5000
      })
      const responseTime = performance.now() - startTime

      if (response.status !== 200) {
        throw new Error(`Health check failed: ${response.status}`)
      }
      
      if (!response.data.status || response.data.status !== 'ok') {
        throw new Error('Health check returned invalid status')
      }

      this.recordPerformanceMetric('health-check', responseTime)
    })

    await this.runTest('API Health Check', async () => {
      const startTime = performance.now()
      const response = await axios.get(`${this.baseURL}/api/health`, {
        timeout: 5000
      })
      const responseTime = performance.now() - startTime

      if (response.status !== 200) {
        throw new Error(`API health check failed: ${response.status}`)
      }

      this.recordPerformanceMetric('api-health-check', responseTime)
    })

    await this.runTest('Database Connectivity', async () => {
      const startTime = performance.now()
      const response = await axios.get(`${this.baseURL}/api/health/database`, {
        timeout: 10000
      })
      const responseTime = performance.now() - startTime

      if (response.status !== 200) {
        throw new Error(`Database health check failed: ${response.status}`)
      }

      this.recordPerformanceMetric('database-health-check', responseTime)
    })

    await this.runTest('Redis Connectivity', async () => {
      const response = await axios.get(`${this.baseURL}/api/health/redis`, {
        timeout: 5000
      })

      if (response.status !== 200) {
        throw new Error(`Redis health check failed: ${response.status}`)
      }
    })
  }

  async testAuthentication() {
    console.log('\n🔐 Testing authentication (read-only)...')

    await this.runTest('Login Endpoint Available', async () => {
      // Test that login endpoint is accessible (without actually logging in)
      try {
        await axios.post(`${this.baseURL}/api/auth/login`, {
          email: '<EMAIL>',
          password: 'invalid'
        }, {
          headers: {
            'X-Subdomain': 'test',
            'X-Tenant-ID': 'test-tenant-id'
          },
          timeout: 5000
        })
      } catch (error) {
        // We expect this to fail with 401, not a connection error
        if (error.response && error.response.status === 401) {
          // This is expected - endpoint is working
          return
        }
        throw new Error(`Login endpoint not accessible: ${error.message}`)
      }
    })

    // Only test actual login if credentials are provided
    if (TEST_USER_EMAIL && TEST_USER_PASSWORD) {
      await this.runTest('Test User Login', async () => {
        const response = await axios.post(`${this.baseURL}/api/auth/login`, {
          email: TEST_USER_EMAIL,
          password: TEST_USER_PASSWORD
        }, {
          headers: {
            'X-Subdomain': 'production',
            'X-Tenant-ID': 'production-tenant-id'
          },
          timeout: 10000
        })

        if (response.status !== 200) {
          throw new Error(`Test user login failed: ${response.status}`)
        }

        if (!response.data.success || !response.data.data.token) {
          throw new Error('Login response missing token')
        }

        this.authToken = response.data.data.token
      })
    }
  }

  async testReadOnlyOperations() {
    console.log('\n👀 Testing read-only operations...')

    if (!this.authToken) {
      console.log('  ⚠️  Skipping authenticated tests (no auth token)')
      return
    }

    await this.runTest('Get Current User', async () => {
      const response = await axios.get(`${this.baseURL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'X-Subdomain': 'production',
          'X-Tenant-ID': 'production-tenant-id'
        },
        timeout: 5000
      })

      if (response.status !== 200) {
        throw new Error(`Get current user failed: ${response.status}`)
      }

      if (!response.data.success || !response.data.data.email) {
        throw new Error('Get current user response invalid')
      }
    })

    await this.runTest('List Deals (Read-Only)', async () => {
      const startTime = performance.now()
      const response = await axios.get(`${this.baseURL}/api/deals?limit=10`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'X-Subdomain': 'production',
          'X-Tenant-ID': 'production-tenant-id'
        },
        timeout: 10000
      })
      const responseTime = performance.now() - startTime

      if (response.status !== 200) {
        throw new Error(`List deals failed: ${response.status}`)
      }

      if (!response.data.success || !Array.isArray(response.data.data.items)) {
        throw new Error('List deals response invalid')
      }

      this.recordPerformanceMetric('list-deals', responseTime)
    })

    await this.runTest('Get Tenant Info', async () => {
      const response = await axios.get(`${this.baseURL}/api/tenants/current`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'X-Subdomain': 'production',
          'X-Tenant-ID': 'production-tenant-id'
        },
        timeout: 5000
      })

      if (response.status !== 200) {
        throw new Error(`Get tenant info failed: ${response.status}`)
      }

      if (!response.data.success || !response.data.data.id) {
        throw new Error('Get tenant info response invalid')
      }
    })
  }

  async testPerformanceMetrics() {
    console.log('\n⚡ Testing performance metrics...')

    await this.runTest('Response Time Benchmark', async () => {
      const requests = []
      const targetEndpoint = `${this.baseURL}/api/health`

      // Make 5 concurrent requests
      for (let i = 0; i < 5; i++) {
        requests.push(
          axios.get(targetEndpoint, { timeout: 5000 })
            .then(response => {
              if (response.status !== 200) {
                throw new Error(`Request ${i} failed: ${response.status}`)
              }
              return response
            })
        )
      }

      const startTime = performance.now()
      await Promise.all(requests)
      const totalTime = performance.now() - startTime

      const averageTime = totalTime / 5
      if (averageTime > 1000) { // 1 second average
        throw new Error(`Average response time too slow: ${averageTime.toFixed(2)}ms`)
      }

      this.recordPerformanceMetric('concurrent-requests', averageTime)
    })
  }

  async testSecurityHeaders() {
    console.log('\n🔒 Testing security headers...')

    await this.runTest('Security Headers Present', async () => {
      const response = await axios.get(`${this.baseURL}/api/health`, {
        timeout: 5000
      })

      const headers = response.headers
      const requiredHeaders = [
        'x-content-type-options',
        'x-frame-options',
        'x-xss-protection'
      ]

      const missingHeaders = requiredHeaders.filter(header => !headers[header])
      
      if (missingHeaders.length > 0) {
        console.warn(`  ⚠️  Missing security headers: ${missingHeaders.join(', ')}`)
        // Don't fail the test, just warn
      }
    })

    await this.runTest('CORS Headers', async () => {
      try {
        const response = await axios.options(`${this.baseURL}/api/health`, {
          headers: {
            'Origin': 'https://app.ma-platform.com',
            'Access-Control-Request-Method': 'GET'
          },
          timeout: 5000
        })

        // CORS preflight should return appropriate headers
        if (!response.headers['access-control-allow-origin']) {
          console.warn('  ⚠️  CORS headers may not be configured properly')
        }
      } catch (error) {
        // Some servers don't support OPTIONS, which is okay
        if (error.response && error.response.status === 405) {
          return // Method not allowed is acceptable
        }
        throw error
      }
    })
  }

  async runTest(testName, testFunction) {
    try {
      await testFunction()
      this.testResults.push({ name: testName, passed: true })
      console.log(`  ✅ ${testName}`)
    } catch (error) {
      this.testResults.push({ name: testName, passed: false, error: error.message })
      console.log(`  ❌ ${testName}: ${error.message}`)
    }
  }

  recordPerformanceMetric(operation, responseTime) {
    this.performanceMetrics.push({
      operation,
      responseTime: Math.round(responseTime),
      timestamp: new Date().toISOString()
    })
  }

  printResults() {
    console.log('\n📊 Test Results Summary:')
    console.log('========================')
    
    const passed = this.testResults.filter(test => test.passed).length
    const failed = this.testResults.filter(test => !test.passed).length
    
    console.log(`Total tests: ${this.testResults.length}`)
    console.log(`Passed: ${passed}`)
    console.log(`Failed: ${failed}`)
    
    if (failed > 0) {
      console.log('\nFailed tests:')
      this.testResults
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.error}`)
        })
    }
  }

  printPerformanceMetrics() {
    if (this.performanceMetrics.length === 0) {
      return
    }

    console.log('\n⚡ Performance Metrics:')
    console.log('======================')
    
    this.performanceMetrics.forEach(metric => {
      console.log(`${metric.operation}: ${metric.responseTime}ms`)
    })

    const avgResponseTime = this.performanceMetrics.reduce((sum, metric) => sum + metric.responseTime, 0) / this.performanceMetrics.length
    console.log(`\nAverage response time: ${avgResponseTime.toFixed(2)}ms`)
  }
}

// Run the tests
const testRunner = new ProductionTestRunner()
testRunner.runAllTests().catch(error => {
  console.error('Production test runner failed:', error)
  process.exit(1)
})
