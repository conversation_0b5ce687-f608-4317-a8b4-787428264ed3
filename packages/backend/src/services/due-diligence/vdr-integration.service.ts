import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { VDRService } from '@/services/vdr/vdr.service'
import { VDRFileStorageService } from '@/services/vdr/file-storage.service'
import { EventEmitter } from 'events'

export interface DDVDRIntegration {
  id: string
  checklistId: string
  vdrId: string
  dealId: string
  
  // Integration settings
  autoSync: boolean
  syncDirection: 'DD_TO_VDR' | 'VDR_TO_DD' | 'BIDIRECTIONAL'
  
  // Mapping configuration
  categoryMapping: DDCategoryMapping[]
  documentMapping: DDDocumentMapping[]
  
  // Sync status
  lastSyncAt?: Date
  syncStatus: 'ACTIVE' | 'PAUSED' | 'ERROR' | 'DISABLED'
  syncErrors: string[]
  
  // Statistics
  documentsLinked: number
  documentsSynced: number
  lastSyncDuration: number
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt: Date
  tenantId: string
}

export interface DDCategoryMapping {
  categoryId: string
  categoryName: string
  vdrFolderId: string
  vdrFolderPath: string
  autoCreateFolder: boolean
  syncSubfolders: boolean
}

export interface DDDocumentMapping {
  itemId: string
  itemTitle: string
  vdrDocumentId?: string
  vdrDocumentPath?: string
  linkType: 'REQUIRED' | 'OPTIONAL' | 'REFERENCE'
  uploadStatus: 'PENDING' | 'UPLOADED' | 'FAILED' | 'NOT_REQUIRED'
  lastSyncAt?: Date
}

export interface DDVDRSyncResult {
  success: boolean
  documentsProcessed: number
  documentsLinked: number
  documentsUploaded: number
  errors: string[]
  duration: number
  timestamp: Date
}

export interface DDDocumentRequirement {
  itemId: string
  itemTitle: string
  categoryId: string
  categoryName: string
  description?: string
  isRequired: boolean
  acceptedFormats: string[]
  maxFileSize?: number
  examples?: string[]
  instructions?: string
  dueDate?: Date
  assignedTo?: string
}

export class DDVDRIntegrationService extends EventEmitter {
  private prisma: PrismaClient
  private cache: CacheService
  private vdrService: VDRService
  private vdrFileService: VDRFileStorageService
  private logger: Logger

  constructor(
    prisma: PrismaClient, 
    cache: CacheService,
    vdrService: VDRService,
    vdrFileService: VDRFileStorageService
  ) {
    super()
    this.prisma = prisma
    this.cache = cache
    this.vdrService = vdrService
    this.vdrFileService = vdrFileService
    this.logger = new Logger('DDVDRIntegrationService')
  }

  /**
   * Create VDR integration for checklist
   */
  async createIntegration(
    checklistId: string,
    vdrId: string,
    dealId: string,
    userId: string,
    tenantId: string,
    options: {
      autoSync?: boolean
      syncDirection?: DDVDRIntegration['syncDirection']
      categoryMapping?: DDCategoryMapping[]
    } = {}
  ): Promise<DDVDRIntegration> {
    try {
      this.logger.info('Creating VDR integration', {
        checklistId,
        vdrId,
        dealId,
        userId
      })

      // Verify checklist and VDR exist
      const [checklist, vdr] = await Promise.all([
        this.getChecklist(checklistId, tenantId),
        this.vdrService.getVDR(vdrId, tenantId)
      ])

      if (!checklist) {
        throw new Error('Checklist not found')
      }

      if (!vdr) {
        throw new Error('VDR not found')
      }

      // Create default category mapping if not provided
      const categoryMapping = options.categoryMapping || 
        await this.createDefaultCategoryMapping(checklist, vdr)

      // Create integration
      const integration = await this.prisma.dDVDRIntegration.create({
        data: {
          checklistId,
          vdrId,
          dealId,
          autoSync: options.autoSync ?? true,
          syncDirection: options.syncDirection || 'BIDIRECTIONAL',
          categoryMapping,
          documentMapping: [],
          syncStatus: 'ACTIVE',
          syncErrors: [],
          documentsLinked: 0,
          documentsSynced: 0,
          lastSyncDuration: 0,
          createdBy: userId,
          tenantId
        }
      })

      // Perform initial sync
      if (options.autoSync !== false) {
        await this.performSync(integration.id)
      }

      this.logger.info('VDR integration created successfully', {
        integrationId: integration.id,
        checklistId,
        vdrId
      })

      return integration as DDVDRIntegration
    } catch (error) {
      this.logger.error('Failed to create VDR integration', error)
      throw error
    }
  }

  /**
   * Sync checklist with VDR
   */
  async performSync(integrationId: string): Promise<DDVDRSyncResult> {
    const startTime = Date.now()
    
    try {
      this.logger.info('Starting VDR sync', { integrationId })

      // Get integration
      const integration = await this.getIntegration(integrationId)
      if (!integration) {
        throw new Error('Integration not found')
      }

      if (integration.syncStatus !== 'ACTIVE') {
        throw new Error('Integration is not active')
      }

      // Update sync status
      await this.updateIntegrationStatus(integrationId, 'ACTIVE', [])

      const result: DDVDRSyncResult = {
        success: false,
        documentsProcessed: 0,
        documentsLinked: 0,
        documentsUploaded: 0,
        errors: [],
        duration: 0,
        timestamp: new Date()
      }

      // Sync based on direction
      switch (integration.syncDirection) {
        case 'DD_TO_VDR':
          await this.syncDDToVDR(integration, result)
          break
        case 'VDR_TO_DD':
          await this.syncVDRToDD(integration, result)
          break
        case 'BIDIRECTIONAL':
          await this.syncDDToVDR(integration, result)
          await this.syncVDRToDD(integration, result)
          break
      }

      result.success = result.errors.length === 0
      result.duration = Date.now() - startTime

      // Update integration statistics
      await this.updateIntegrationStats(integrationId, result)

      // Emit sync completed event
      this.emit('sync:completed', {
        integrationId,
        result
      })

      this.logger.info('VDR sync completed', {
        integrationId,
        success: result.success,
        duration: result.duration,
        documentsProcessed: result.documentsProcessed
      })

      return result
    } catch (error) {
      const duration = Date.now() - startTime
      
      this.logger.error('VDR sync failed', error, { integrationId })

      // Update integration with error
      await this.updateIntegrationStatus(integrationId, 'ERROR', [error.message])

      return {
        success: false,
        documentsProcessed: 0,
        documentsLinked: 0,
        documentsUploaded: 0,
        errors: [error.message],
        duration,
        timestamp: new Date()
      }
    }
  }

  /**
   * Link document to VDR
   */
  async linkDocument(
    integrationId: string,
    itemId: string,
    vdrDocumentId: string,
    linkType: DDDocumentMapping['linkType'] = 'REQUIRED'
  ): Promise<void> {
    try {
      this.logger.info('Linking document to VDR', {
        integrationId,
        itemId,
        vdrDocumentId
      })

      const integration = await this.getIntegration(integrationId)
      if (!integration) {
        throw new Error('Integration not found')
      }

      // Get VDR document info
      const vdrDocument = await this.vdrService.getDocument(vdrDocumentId, integration.tenantId)
      if (!vdrDocument) {
        throw new Error('VDR document not found')
      }

      // Update document mapping
      const updatedMapping = [...integration.documentMapping]
      const existingIndex = updatedMapping.findIndex(m => m.itemId === itemId)

      const documentMapping: DDDocumentMapping = {
        itemId,
        itemTitle: await this.getItemTitle(itemId),
        vdrDocumentId,
        vdrDocumentPath: vdrDocument.path,
        linkType,
        uploadStatus: 'UPLOADED',
        lastSyncAt: new Date()
      }

      if (existingIndex >= 0) {
        updatedMapping[existingIndex] = documentMapping
      } else {
        updatedMapping.push(documentMapping)
      }

      // Update integration
      await this.prisma.dDVDRIntegration.update({
        where: { id: integrationId },
        data: {
          documentMapping: updatedMapping,
          documentsLinked: updatedMapping.filter(m => m.vdrDocumentId).length,
          updatedAt: new Date()
        }
      })

      // Emit document linked event
      this.emit('document:linked', {
        integrationId,
        itemId,
        vdrDocumentId
      })

      this.logger.info('Document linked successfully', {
        integrationId,
        itemId,
        vdrDocumentId
      })
    } catch (error) {
      this.logger.error('Failed to link document', error)
      throw error
    }
  }

  /**
   * Get document requirements for VDR
   */
  async getDocumentRequirements(
    checklistId: string,
    tenantId: string
  ): Promise<DDDocumentRequirement[]> {
    try {
      // Get checklist with items
      const checklist = await this.getChecklistWithItems(checklistId, tenantId)
      if (!checklist) {
        throw new Error('Checklist not found')
      }

      const requirements: DDDocumentRequirement[] = []

      // Process each category and item
      for (const category of checklist.categories || []) {
        for (const item of category.items || []) {
          // Check if item requires documents
          if (this.itemRequiresDocuments(item)) {
            requirements.push({
              itemId: item.id,
              itemTitle: item.title,
              categoryId: category.id,
              categoryName: category.name,
              description: item.description,
              isRequired: item.isRequired,
              acceptedFormats: this.getAcceptedFormats(item),
              maxFileSize: this.getMaxFileSize(item),
              examples: item.examples,
              instructions: item.instructions,
              dueDate: item.dueDate ? new Date(item.dueDate) : undefined,
              assignedTo: item.assignedTo
            })
          }
        }
      }

      return requirements
    } catch (error) {
      this.logger.error('Failed to get document requirements', error)
      throw error
    }
  }

  /**
   * Upload document to VDR for checklist item
   */
  async uploadDocumentForItem(
    integrationId: string,
    itemId: string,
    file: Buffer,
    filename: string,
    mimeType: string,
    userId: string
  ): Promise<string> {
    try {
      this.logger.info('Uploading document for item', {
        integrationId,
        itemId,
        filename
      })

      const integration = await this.getIntegration(integrationId)
      if (!integration) {
        throw new Error('Integration not found')
      }

      // Find category mapping for item
      const item = await this.getItem(itemId)
      if (!item) {
        throw new Error('Item not found')
      }

      const categoryMapping = integration.categoryMapping.find(
        m => m.categoryId === item.categoryId
      )

      if (!categoryMapping) {
        throw new Error('Category mapping not found')
      }

      // Upload to VDR
      const vdrDocument = await this.vdrFileService.uploadFile(
        integration.vdrId,
        file,
        filename,
        mimeType,
        {
          folderId: categoryMapping.vdrFolderId,
          uploadedBy: userId,
          metadata: {
            checklistId: integration.checklistId,
            itemId,
            itemTitle: item.title
          }
        }
      )

      // Link document
      await this.linkDocument(integrationId, itemId, vdrDocument.id, 'REQUIRED')

      this.logger.info('Document uploaded and linked successfully', {
        integrationId,
        itemId,
        vdrDocumentId: vdrDocument.id
      })

      return vdrDocument.id
    } catch (error) {
      this.logger.error('Failed to upload document for item', error)
      throw error
    }
  }

  /**
   * Get integration status
   */
  async getIntegrationStatus(integrationId: string): Promise<{
    integration: DDVDRIntegration
    syncHistory: DDVDRSyncResult[]
    documentStatus: Array<{
      itemId: string
      itemTitle: string
      hasDocument: boolean
      isRequired: boolean
      uploadStatus: string
    }>
  }> {
    try {
      const integration = await this.getIntegration(integrationId)
      if (!integration) {
        throw new Error('Integration not found')
      }

      // Get sync history (mock for now)
      const syncHistory: DDVDRSyncResult[] = []

      // Get document status
      const requirements = await this.getDocumentRequirements(
        integration.checklistId,
        integration.tenantId
      )

      const documentStatus = requirements.map(req => {
        const mapping = integration.documentMapping.find(m => m.itemId === req.itemId)
        return {
          itemId: req.itemId,
          itemTitle: req.itemTitle,
          hasDocument: !!mapping?.vdrDocumentId,
          isRequired: req.isRequired,
          uploadStatus: mapping?.uploadStatus || 'PENDING'
        }
      })

      return {
        integration,
        syncHistory,
        documentStatus
      }
    } catch (error) {
      this.logger.error('Failed to get integration status', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async getIntegration(integrationId: string): Promise<DDVDRIntegration | null> {
    const integration = await this.prisma.dDVDRIntegration.findUnique({
      where: { id: integrationId }
    })

    return integration as DDVDRIntegration | null
  }

  private async getChecklist(checklistId: string, tenantId: string): Promise<any> {
    // Mock implementation - would fetch from database
    return {
      id: checklistId,
      name: 'Sample Checklist',
      tenantId,
      categories: []
    }
  }

  private async getChecklistWithItems(checklistId: string, tenantId: string): Promise<any> {
    // Mock implementation - would fetch checklist with categories and items
    return {
      id: checklistId,
      name: 'Sample Checklist',
      tenantId,
      categories: [
        {
          id: 'cat-1',
          name: 'Financial Documents',
          items: [
            {
              id: 'item-1',
              title: 'Financial Statements',
              categoryId: 'cat-1',
              isRequired: true,
              description: 'Last 3 years of audited financial statements'
            }
          ]
        }
      ]
    }
  }

  private async getItem(itemId: string): Promise<any> {
    // Mock implementation
    return {
      id: itemId,
      title: 'Sample Item',
      categoryId: 'cat-1'
    }
  }

  private async getItemTitle(itemId: string): Promise<string> {
    const item = await this.getItem(itemId)
    return item?.title || 'Unknown Item'
  }

  private async createDefaultCategoryMapping(checklist: any, vdr: any): Promise<DDCategoryMapping[]> {
    // Create default mapping between checklist categories and VDR folders
    const mapping: DDCategoryMapping[] = []

    for (const category of checklist.categories || []) {
      // Create or find VDR folder for category
      const vdrFolder = await this.vdrService.createFolder(
        vdr.id,
        category.name,
        { parentId: vdr.rootFolderId }
      )

      mapping.push({
        categoryId: category.id,
        categoryName: category.name,
        vdrFolderId: vdrFolder.id,
        vdrFolderPath: vdrFolder.path,
        autoCreateFolder: true,
        syncSubfolders: true
      })
    }

    return mapping
  }

  private async syncDDToVDR(integration: DDVDRIntegration, result: DDVDRSyncResult): Promise<void> {
    // Sync due diligence requirements to VDR structure
    this.logger.info('Syncing DD to VDR', { integrationId: integration.id })
    
    // Implementation would sync checklist structure to VDR folders
    result.documentsProcessed += 10 // Mock
  }

  private async syncVDRToDD(integration: DDVDRIntegration, result: DDVDRSyncResult): Promise<void> {
    // Sync VDR documents to due diligence items
    this.logger.info('Syncing VDR to DD', { integrationId: integration.id })
    
    // Implementation would link VDR documents to checklist items
    result.documentsLinked += 5 // Mock
  }

  private async updateIntegrationStatus(
    integrationId: string,
    status: DDVDRIntegration['syncStatus'],
    errors: string[]
  ): Promise<void> {
    await this.prisma.dDVDRIntegration.update({
      where: { id: integrationId },
      data: {
        syncStatus: status,
        syncErrors: errors,
        lastSyncAt: new Date(),
        updatedAt: new Date()
      }
    })
  }

  private async updateIntegrationStats(
    integrationId: string,
    result: DDVDRSyncResult
  ): Promise<void> {
    await this.prisma.dDVDRIntegration.update({
      where: { id: integrationId },
      data: {
        documentsLinked: result.documentsLinked,
        documentsSynced: result.documentsUploaded,
        lastSyncDuration: result.duration,
        lastSyncAt: new Date(),
        updatedAt: new Date()
      }
    })
  }

  private itemRequiresDocuments(item: any): boolean {
    // Check if item requires document uploads
    return item.fields?.some((field: any) => 
      field.type === 'FILE_UPLOAD' || field.type === 'DOCUMENT_LINK'
    ) || item.isRequired
  }

  private getAcceptedFormats(item: any): string[] {
    // Get accepted file formats for item
    return ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt']
  }

  private getMaxFileSize(item: any): number {
    // Get maximum file size for item (in bytes)
    return 50 * 1024 * 1024 // 50MB default
  }
}
