import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { EventEmitter } from 'events'

export interface WorkflowDefinition {
  id: string
  name: string
  description?: string
  version: string
  isActive: boolean
  
  // Workflow structure
  stages: WorkflowStage[]
  transitions: WorkflowTransition[]
  rules: WorkflowRule[]
  
  // Configuration
  allowParallelExecution: boolean
  requiresApproval: boolean
  autoProgressEnabled: boolean
  timeoutSettings: WorkflowTimeout[]
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt: Date
  tenantId: string
}

export interface WorkflowStage {
  id: string
  name: string
  description?: string
  order: number
  type: 'start' | 'task' | 'approval' | 'parallel' | 'end'
  
  // Stage configuration
  isRequired: boolean
  estimatedDuration: number
  maxDuration?: number
  
  // Assignment
  assigneeType: 'user' | 'role' | 'team' | 'auto'
  assigneeIds: string[]
  
  // Conditions
  entryConditions: WorkflowCondition[]
  exitConditions: WorkflowCondition[]
  
  // Actions
  onEntry: WorkflowAction[]
  onExit: WorkflowAction[]
  onTimeout: WorkflowAction[]
}

export interface WorkflowTransition {
  id: string
  name: string
  fromStageId: string
  toStageId: string
  
  // Transition conditions
  conditions: WorkflowCondition[]
  isAutomatic: boolean
  
  // Actions to execute during transition
  actions: WorkflowAction[]
}

export interface WorkflowCondition {
  id: string
  type: 'field_value' | 'approval_status' | 'time_elapsed' | 'dependency_complete' | 'custom'
  field?: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'exists'
  value: any
  customScript?: string
}

export interface WorkflowAction {
  id: string
  type: 'assign_task' | 'send_notification' | 'update_field' | 'create_task' | 'webhook' | 'custom'
  configuration: any
  isAsync: boolean
}

export interface WorkflowRule {
  id: string
  name: string
  description?: string
  priority: number
  
  // Rule definition
  when: WorkflowCondition[]
  then: WorkflowAction[]
  
  // Rule settings
  isActive: boolean
  executeOnce: boolean
}

export interface WorkflowTimeout {
  stageId: string
  timeoutMinutes: number
  action: 'escalate' | 'auto_approve' | 'reassign' | 'notify'
  configuration: any
}

export interface WorkflowInstance {
  id: string
  workflowDefinitionId: string
  checklistId: string
  dealId: string
  
  // Instance state
  status: 'running' | 'paused' | 'completed' | 'failed' | 'cancelled'
  currentStageId: string
  startedAt: Date
  completedAt?: Date
  
  // Progress tracking
  completedStages: string[]
  failedStages: string[]
  stageHistory: WorkflowStageExecution[]
  
  // Context data
  variables: Record<string, any>
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export interface WorkflowStageExecution {
  stageId: string
  stageName: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  assignedTo?: string
  startedAt: Date
  completedAt?: Date
  duration?: number
  notes?: string
  artifacts: any[]
}

export class DDWorkflowService extends EventEmitter {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    super()
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DDWorkflowService')
  }

  /**
   * Create workflow definition
   */
  async createWorkflowDefinition(
    definition: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt'>,
    userId: string
  ): Promise<WorkflowDefinition> {
    try {
      this.logger.info('Creating workflow definition', { 
        name: definition.name, 
        userId 
      })

      // Validate workflow definition
      await this.validateWorkflowDefinition(definition)

      // Create workflow definition
      const workflowDef = await this.prisma.workflowDefinition.create({
        data: {
          name: definition.name,
          description: definition.description,
          version: definition.version,
          isActive: definition.isActive,
          stages: definition.stages,
          transitions: definition.transitions,
          rules: definition.rules,
          allowParallelExecution: definition.allowParallelExecution,
          requiresApproval: definition.requiresApproval,
          autoProgressEnabled: definition.autoProgressEnabled,
          timeoutSettings: definition.timeoutSettings,
          createdBy: userId,
          tenantId: definition.tenantId
        }
      })

      this.logger.info('Workflow definition created', { 
        workflowId: workflowDef.id 
      })

      return workflowDef as WorkflowDefinition
    } catch (error) {
      this.logger.error('Failed to create workflow definition', error)
      throw error
    }
  }

  /**
   * Start workflow instance
   */
  async startWorkflow(
    workflowDefinitionId: string,
    checklistId: string,
    dealId: string,
    userId: string,
    initialVariables: Record<string, any> = {}
  ): Promise<WorkflowInstance> {
    try {
      this.logger.info('Starting workflow instance', {
        workflowDefinitionId,
        checklistId,
        dealId,
        userId
      })

      // Get workflow definition
      const workflowDef = await this.getWorkflowDefinition(workflowDefinitionId)
      if (!workflowDef) {
        throw new Error('Workflow definition not found')
      }

      // Find start stage
      const startStage = workflowDef.stages.find(stage => stage.type === 'start')
      if (!startStage) {
        throw new Error('Workflow must have a start stage')
      }

      // Create workflow instance
      const instance = await this.prisma.workflowInstance.create({
        data: {
          workflowDefinitionId,
          checklistId,
          dealId,
          status: 'running',
          currentStageId: startStage.id,
          startedAt: new Date(),
          completedStages: [],
          failedStages: [],
          stageHistory: [],
          variables: initialVariables,
          createdBy: userId
        }
      })

      // Execute start stage
      await this.executeStage(instance.id, startStage.id)

      // Emit workflow started event
      this.emit('workflow:started', {
        instanceId: instance.id,
        workflowDefinitionId,
        checklistId,
        dealId
      })

      this.logger.info('Workflow instance started', { 
        instanceId: instance.id 
      })

      return instance as WorkflowInstance
    } catch (error) {
      this.logger.error('Failed to start workflow', error)
      throw error
    }
  }

  /**
   * Execute workflow stage
   */
  async executeStage(instanceId: string, stageId: string): Promise<void> {
    try {
      this.logger.info('Executing workflow stage', { instanceId, stageId })

      // Get workflow instance and definition
      const instance = await this.getWorkflowInstance(instanceId)
      const workflowDef = await this.getWorkflowDefinition(instance.workflowDefinitionId)
      const stage = workflowDef.stages.find(s => s.id === stageId)

      if (!stage) {
        throw new Error('Stage not found')
      }

      // Check entry conditions
      const canEnter = await this.evaluateConditions(stage.entryConditions, instance)
      if (!canEnter) {
        this.logger.warn('Stage entry conditions not met', { instanceId, stageId })
        return
      }

      // Create stage execution record
      const stageExecution: WorkflowStageExecution = {
        stageId,
        stageName: stage.name,
        status: 'running',
        startedAt: new Date(),
        artifacts: []
      }

      // Execute entry actions
      await this.executeActions(stage.onEntry, instance)

      // Handle different stage types
      switch (stage.type) {
        case 'start':
          await this.handleStartStage(instance, stage)
          break
        case 'task':
          await this.handleTaskStage(instance, stage)
          break
        case 'approval':
          await this.handleApprovalStage(instance, stage)
          break
        case 'parallel':
          await this.handleParallelStage(instance, stage)
          break
        case 'end':
          await this.handleEndStage(instance, stage)
          break
      }

      // Update stage execution
      stageExecution.status = 'completed'
      stageExecution.completedAt = new Date()
      stageExecution.duration = stageExecution.completedAt.getTime() - stageExecution.startedAt.getTime()

      // Update instance
      await this.updateWorkflowInstance(instanceId, {
        currentStageId: stageId,
        stageHistory: [...instance.stageHistory, stageExecution],
        completedStages: [...instance.completedStages, stageId]
      })

      // Execute exit actions
      await this.executeActions(stage.onExit, instance)

      // Check for automatic transitions
      await this.checkTransitions(instanceId, stageId)

      this.logger.info('Stage executed successfully', { instanceId, stageId })
    } catch (error) {
      this.logger.error('Failed to execute stage', error, { instanceId, stageId })
      
      // Mark stage as failed
      await this.markStageAsFailed(instanceId, stageId, error.message)
      throw error
    }
  }

  /**
   * Progress workflow to next stage
   */
  async progressWorkflow(
    instanceId: string,
    fromStageId: string,
    toStageId: string,
    userId: string,
    data?: any
  ): Promise<void> {
    try {
      this.logger.info('Progressing workflow', {
        instanceId,
        fromStageId,
        toStageId,
        userId
      })

      // Get workflow instance and definition
      const instance = await this.getWorkflowInstance(instanceId)
      const workflowDef = await this.getWorkflowDefinition(instance.workflowDefinitionId)

      // Find transition
      const transition = workflowDef.transitions.find(t => 
        t.fromStageId === fromStageId && t.toStageId === toStageId
      )

      if (!transition) {
        throw new Error('Invalid transition')
      }

      // Check transition conditions
      const canTransition = await this.evaluateConditions(transition.conditions, instance)
      if (!canTransition) {
        throw new Error('Transition conditions not met')
      }

      // Execute transition actions
      await this.executeActions(transition.actions, instance)

      // Update instance variables if data provided
      if (data) {
        await this.updateWorkflowInstance(instanceId, {
          variables: { ...instance.variables, ...data }
        })
      }

      // Execute next stage
      await this.executeStage(instanceId, toStageId)

      // Emit transition event
      this.emit('workflow:transitioned', {
        instanceId,
        fromStageId,
        toStageId,
        transitionId: transition.id
      })

      this.logger.info('Workflow progressed successfully', {
        instanceId,
        fromStageId,
        toStageId
      })
    } catch (error) {
      this.logger.error('Failed to progress workflow', error)
      throw error
    }
  }

  /**
   * Complete workflow
   */
  async completeWorkflow(instanceId: string, userId: string): Promise<void> {
    try {
      this.logger.info('Completing workflow', { instanceId, userId })

      // Update workflow instance
      await this.updateWorkflowInstance(instanceId, {
        status: 'completed',
        completedAt: new Date()
      })

      // Emit completion event
      this.emit('workflow:completed', { instanceId })

      this.logger.info('Workflow completed successfully', { instanceId })
    } catch (error) {
      this.logger.error('Failed to complete workflow', error)
      throw error
    }
  }

  /**
   * Get workflow instance
   */
  private async getWorkflowInstance(instanceId: string): Promise<WorkflowInstance> {
    const instance = await this.prisma.workflowInstance.findUnique({
      where: { id: instanceId }
    })

    if (!instance) {
      throw new Error('Workflow instance not found')
    }

    return instance as WorkflowInstance
  }

  /**
   * Get workflow definition
   */
  private async getWorkflowDefinition(definitionId: string): Promise<WorkflowDefinition> {
    const cacheKey = `workflow:definition:${definitionId}`
    
    let definition = await this.cache.get<WorkflowDefinition>(cacheKey)
    if (definition) {
      return definition
    }

    const dbDefinition = await this.prisma.workflowDefinition.findUnique({
      where: { id: definitionId }
    })

    if (!dbDefinition) {
      throw new Error('Workflow definition not found')
    }

    definition = dbDefinition as WorkflowDefinition
    await this.cache.set(cacheKey, definition, 3600) // Cache for 1 hour

    return definition
  }

  /**
   * Update workflow instance
   */
  private async updateWorkflowInstance(
    instanceId: string,
    updates: Partial<WorkflowInstance>
  ): Promise<void> {
    await this.prisma.workflowInstance.update({
      where: { id: instanceId },
      data: {
        ...updates,
        updatedAt: new Date()
      }
    })
  }

  /**
   * Evaluate workflow conditions
   */
  private async evaluateConditions(
    conditions: WorkflowCondition[],
    instance: WorkflowInstance
  ): Promise<boolean> {
    if (!conditions || conditions.length === 0) {
      return true
    }

    for (const condition of conditions) {
      const result = await this.evaluateCondition(condition, instance)
      if (!result) {
        return false
      }
    }

    return true
  }

  /**
   * Evaluate single condition
   */
  private async evaluateCondition(
    condition: WorkflowCondition,
    instance: WorkflowInstance
  ): Promise<boolean> {
    switch (condition.type) {
      case 'field_value':
        return this.evaluateFieldCondition(condition, instance)
      case 'time_elapsed':
        return this.evaluateTimeCondition(condition, instance)
      case 'dependency_complete':
        return this.evaluateDependencyCondition(condition, instance)
      case 'custom':
        return this.evaluateCustomCondition(condition, instance)
      default:
        return true
    }
  }

  /**
   * Execute workflow actions
   */
  private async executeActions(
    actions: WorkflowAction[],
    instance: WorkflowInstance
  ): Promise<void> {
    for (const action of actions) {
      try {
        await this.executeAction(action, instance)
      } catch (error) {
        this.logger.error('Failed to execute action', error, { 
          actionId: action.id,
          instanceId: instance.id 
        })
        
        if (!action.isAsync) {
          throw error
        }
      }
    }
  }

  /**
   * Execute single action
   */
  private async executeAction(
    action: WorkflowAction,
    instance: WorkflowInstance
  ): Promise<void> {
    switch (action.type) {
      case 'assign_task':
        await this.executeAssignTaskAction(action, instance)
        break
      case 'send_notification':
        await this.executeSendNotificationAction(action, instance)
        break
      case 'update_field':
        await this.executeUpdateFieldAction(action, instance)
        break
      case 'webhook':
        await this.executeWebhookAction(action, instance)
        break
      case 'custom':
        await this.executeCustomAction(action, instance)
        break
    }
  }

  /**
   * Handle different stage types
   */
  private async handleStartStage(instance: WorkflowInstance, stage: WorkflowStage): Promise<void> {
    // Start stage just triggers the workflow
    this.logger.info('Handling start stage', { instanceId: instance.id, stageId: stage.id })
  }

  private async handleTaskStage(instance: WorkflowInstance, stage: WorkflowStage): Promise<void> {
    // Task stage assigns work to users
    this.logger.info('Handling task stage', { instanceId: instance.id, stageId: stage.id })
    
    // Assign tasks based on stage configuration
    if (stage.assigneeIds.length > 0) {
      await this.executeAction({
        id: 'auto-assign',
        type: 'assign_task',
        configuration: {
          assigneeIds: stage.assigneeIds,
          stageId: stage.id
        },
        isAsync: false
      }, instance)
    }
  }

  private async handleApprovalStage(instance: WorkflowInstance, stage: WorkflowStage): Promise<void> {
    // Approval stage waits for approval
    this.logger.info('Handling approval stage', { instanceId: instance.id, stageId: stage.id })
  }

  private async handleParallelStage(instance: WorkflowInstance, stage: WorkflowStage): Promise<void> {
    // Parallel stage executes multiple paths
    this.logger.info('Handling parallel stage', { instanceId: instance.id, stageId: stage.id })
  }

  private async handleEndStage(instance: WorkflowInstance, stage: WorkflowStage): Promise<void> {
    // End stage completes the workflow
    this.logger.info('Handling end stage', { instanceId: instance.id, stageId: stage.id })
    await this.completeWorkflow(instance.id, instance.createdBy)
  }

  /**
   * Check for automatic transitions
   */
  private async checkTransitions(instanceId: string, currentStageId: string): Promise<void> {
    const instance = await this.getWorkflowInstance(instanceId)
    const workflowDef = await this.getWorkflowDefinition(instance.workflowDefinitionId)

    const automaticTransitions = workflowDef.transitions.filter(t => 
      t.fromStageId === currentStageId && t.isAutomatic
    )

    for (const transition of automaticTransitions) {
      const canTransition = await this.evaluateConditions(transition.conditions, instance)
      if (canTransition) {
        await this.progressWorkflow(instanceId, currentStageId, transition.toStageId, 'system')
        break // Only execute first valid transition
      }
    }
  }

  /**
   * Mark stage as failed
   */
  private async markStageAsFailed(instanceId: string, stageId: string, error: string): Promise<void> {
    const instance = await this.getWorkflowInstance(instanceId)
    
    await this.updateWorkflowInstance(instanceId, {
      failedStages: [...instance.failedStages, stageId],
      status: 'failed'
    })

    this.emit('workflow:stage_failed', { instanceId, stageId, error })
  }

  /**
   * Validate workflow definition
   */
  private async validateWorkflowDefinition(definition: any): Promise<void> {
    if (!definition.stages || definition.stages.length === 0) {
      throw new Error('Workflow must have at least one stage')
    }

    const startStages = definition.stages.filter((s: any) => s.type === 'start')
    if (startStages.length !== 1) {
      throw new Error('Workflow must have exactly one start stage')
    }

    const endStages = definition.stages.filter((s: any) => s.type === 'end')
    if (endStages.length === 0) {
      throw new Error('Workflow must have at least one end stage')
    }
  }

  /**
   * Condition evaluation methods (simplified implementations)
   */
  private evaluateFieldCondition(condition: WorkflowCondition, instance: WorkflowInstance): boolean {
    const fieldValue = instance.variables[condition.field!]
    
    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value
      case 'not_equals':
        return fieldValue !== condition.value
      case 'exists':
        return fieldValue !== undefined && fieldValue !== null
      default:
        return false
    }
  }

  private evaluateTimeCondition(condition: WorkflowCondition, instance: WorkflowInstance): boolean {
    const elapsed = Date.now() - instance.startedAt.getTime()
    const requiredTime = condition.value * 60 * 1000 // Convert minutes to milliseconds
    
    switch (condition.operator) {
      case 'greater_than':
        return elapsed > requiredTime
      case 'less_than':
        return elapsed < requiredTime
      default:
        return false
    }
  }

  private async evaluateDependencyCondition(condition: WorkflowCondition, instance: WorkflowInstance): Promise<boolean> {
    // Check if dependent stages are completed
    return instance.completedStages.includes(condition.value)
  }

  private async evaluateCustomCondition(condition: WorkflowCondition, instance: WorkflowInstance): Promise<boolean> {
    // Execute custom script (simplified)
    return true
  }

  /**
   * Action execution methods (simplified implementations)
   */
  private async executeAssignTaskAction(action: WorkflowAction, instance: WorkflowInstance): Promise<void> {
    this.logger.info('Executing assign task action', { actionId: action.id, instanceId: instance.id })
    // Implementation would assign tasks to users
  }

  private async executeSendNotificationAction(action: WorkflowAction, instance: WorkflowInstance): Promise<void> {
    this.logger.info('Executing send notification action', { actionId: action.id, instanceId: instance.id })
    // Implementation would send notifications
  }

  private async executeUpdateFieldAction(action: WorkflowAction, instance: WorkflowInstance): Promise<void> {
    this.logger.info('Executing update field action', { actionId: action.id, instanceId: instance.id })
    // Implementation would update instance variables
  }

  private async executeWebhookAction(action: WorkflowAction, instance: WorkflowInstance): Promise<void> {
    this.logger.info('Executing webhook action', { actionId: action.id, instanceId: instance.id })
    // Implementation would call external webhooks
  }

  private async executeCustomAction(action: WorkflowAction, instance: WorkflowInstance): Promise<void> {
    this.logger.info('Executing custom action', { actionId: action.id, instanceId: instance.id })
    // Implementation would execute custom scripts
  }
}
