import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface DDAnalytics {
  checklistId: string
  summary: DDSummary
  categoryBreakdown: DDCategoryBreakdown[]
  teamPerformance: DDTeamPerformance[]
  timelineProgress: DDTimelineProgress[]
  bottlenecks: DDBottleneck[]
  trends: DDTrends
  predictions: DDPredictions
}

export interface DDSummary {
  totalItems: number
  completedItems: number
  inProgressItems: number
  pendingItems: number
  blockedItems: number
  overallProgress: number
  estimatedCompletion: string
  actualHours: number
  estimatedHours: number
  efficiency: number
  qualityScore: number
}

export interface DDCategoryBreakdown {
  categoryId: string
  categoryName: string
  categoryType: string
  totalItems: number
  completedItems: number
  inProgressItems: number
  pendingItems: number
  progress: number
  estimatedHours: number
  actualHours: number
  averageCompletionTime: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

export interface DDTeamPerformance {
  userId: string
  userName: string
  email: string
  assignedItems: number
  completedItems: number
  inProgressItems: number
  overdueItems: number
  averageCompletionTime: number
  efficiency: number
  qualityScore: number
  workloadUtilization: number
  contributionPercentage: number
}

export interface DDTimelineProgress {
  date: string
  completedItems: number
  totalItems: number
  cumulativeProgress: number
  dailyVelocity: number
  hoursSpent: number
  milestone?: string
}

export interface DDBottleneck {
  itemId: string
  itemTitle: string
  categoryName: string
  assignedTo: string
  daysStuck: number
  reason: string
  impact: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  suggestedAction: string
}

export interface DDTrends {
  velocityTrend: Array<{ period: string; velocity: number; trend: 'up' | 'down' | 'stable' }>
  qualityTrend: Array<{ period: string; score: number; trend: 'up' | 'down' | 'stable' }>
  efficiencyTrend: Array<{ period: string; efficiency: number; trend: 'up' | 'down' | 'stable' }>
  riskTrend: Array<{ period: string; riskScore: number; trend: 'up' | 'down' | 'stable' }>
}

export interface DDPredictions {
  estimatedCompletionDate: string
  confidenceLevel: number
  riskFactors: string[]
  recommendedActions: string[]
  resourceNeeds: {
    additionalHours: number
    skillGaps: string[]
    criticalPath: string[]
  }
}

export interface DDMetrics {
  checklistId: string
  date: string
  metrics: {
    totalItems: number
    completedItems: number
    progressPercentage: number
    velocity: number
    efficiency: number
    qualityScore: number
    riskScore: number
    teamUtilization: number
  }
}

export class DDAnalyticsService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DDAnalyticsService')
  }

  /**
   * Get comprehensive analytics for checklist
   */
  async getChecklistAnalytics(checklistId: string, tenantId: string): Promise<DDAnalytics> {
    try {
      this.logger.info('Getting checklist analytics', { checklistId })

      const cacheKey = `dd:analytics:${checklistId}`
      
      // Try cache first
      const cached = await this.cache.get<DDAnalytics>(cacheKey)
      if (cached) {
        return cached
      }

      // Get checklist data
      const checklist = await this.getChecklistData(checklistId, tenantId)
      if (!checklist) {
        throw new Error('Checklist not found')
      }

      // Calculate analytics
      const [
        summary,
        categoryBreakdown,
        teamPerformance,
        timelineProgress,
        bottlenecks,
        trends,
        predictions
      ] = await Promise.all([
        this.calculateSummary(checklist),
        this.calculateCategoryBreakdown(checklist),
        this.calculateTeamPerformance(checklist),
        this.calculateTimelineProgress(checklist),
        this.identifyBottlenecks(checklist),
        this.calculateTrends(checklist),
        this.generatePredictions(checklist)
      ])

      const analytics: DDAnalytics = {
        checklistId,
        summary,
        categoryBreakdown,
        teamPerformance,
        timelineProgress,
        bottlenecks,
        trends,
        predictions
      }

      // Cache for 15 minutes
      await this.cache.set(cacheKey, analytics, 900)

      this.logger.info('Checklist analytics calculated', { checklistId })

      return analytics
    } catch (error) {
      this.logger.error('Failed to get checklist analytics', error)
      throw error
    }
  }

  /**
   * Get real-time progress metrics
   */
  async getProgressMetrics(checklistId: string, tenantId: string): Promise<DDSummary> {
    try {
      const checklist = await this.getChecklistData(checklistId, tenantId)
      if (!checklist) {
        throw new Error('Checklist not found')
      }

      return this.calculateSummary(checklist)
    } catch (error) {
      this.logger.error('Failed to get progress metrics', error)
      throw error
    }
  }

  /**
   * Record daily metrics
   */
  async recordDailyMetrics(checklistId: string, tenantId: string): Promise<void> {
    try {
      this.logger.info('Recording daily metrics', { checklistId })

      const checklist = await this.getChecklistData(checklistId, tenantId)
      if (!checklist) {
        return
      }

      const summary = await this.calculateSummary(checklist)
      const teamPerformance = await this.calculateTeamPerformance(checklist)

      const metrics: DDMetrics = {
        checklistId,
        date: new Date().toISOString().split('T')[0],
        metrics: {
          totalItems: summary.totalItems,
          completedItems: summary.completedItems,
          progressPercentage: summary.overallProgress,
          velocity: this.calculateVelocity(checklist),
          efficiency: summary.efficiency,
          qualityScore: summary.qualityScore,
          riskScore: this.calculateRiskScore(checklist),
          teamUtilization: this.calculateTeamUtilization(teamPerformance)
        }
      }

      // Store metrics
      await this.prisma.dDMetrics.create({
        data: {
          checklistId,
          date: metrics.date,
          metrics: metrics.metrics,
          tenantId
        }
      })

      this.logger.info('Daily metrics recorded', { checklistId })
    } catch (error) {
      this.logger.error('Failed to record daily metrics', error)
    }
  }

  /**
   * Get historical metrics
   */
  async getHistoricalMetrics(
    checklistId: string,
    tenantId: string,
    days: number = 30
  ): Promise<DDMetrics[]> {
    try {
      const fromDate = new Date()
      fromDate.setDate(fromDate.getDate() - days)

      const metrics = await this.prisma.dDMetrics.findMany({
        where: {
          checklistId,
          tenantId,
          date: {
            gte: fromDate.toISOString().split('T')[0]
          }
        },
        orderBy: { date: 'asc' }
      })

      return metrics as DDMetrics[]
    } catch (error) {
      this.logger.error('Failed to get historical metrics', error)
      throw error
    }
  }

  /**
   * Calculate summary metrics
   */
  private async calculateSummary(checklist: any): Promise<DDSummary> {
    const items = checklist.items || []
    
    const totalItems = items.length
    const completedItems = items.filter((item: any) => item.status === 'COMPLETED').length
    const inProgressItems = items.filter((item: any) => item.status === 'IN_PROGRESS').length
    const pendingItems = items.filter((item: any) => item.status === 'NOT_STARTED').length
    const blockedItems = items.filter((item: any) => item.status === 'BLOCKED').length

    const overallProgress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0
    
    const estimatedHours = items.reduce((sum: number, item: any) => sum + (item.estimatedHours || 0), 0)
    const actualHours = items.reduce((sum: number, item: any) => sum + (item.actualHours || 0), 0)
    
    const efficiency = estimatedHours > 0 ? Math.round((estimatedHours / Math.max(actualHours, 1)) * 100) / 100 : 1
    const qualityScore = this.calculateQualityScore(items)

    // Estimate completion date
    const remainingItems = totalItems - completedItems
    const averageVelocity = this.calculateAverageVelocity(checklist)
    const daysToComplete = averageVelocity > 0 ? Math.ceil(remainingItems / averageVelocity) : 0
    
    const estimatedCompletion = new Date()
    estimatedCompletion.setDate(estimatedCompletion.getDate() + daysToComplete)

    return {
      totalItems,
      completedItems,
      inProgressItems,
      pendingItems,
      blockedItems,
      overallProgress,
      estimatedCompletion: estimatedCompletion.toISOString(),
      actualHours,
      estimatedHours,
      efficiency,
      qualityScore
    }
  }

  /**
   * Calculate category breakdown
   */
  private async calculateCategoryBreakdown(checklist: any): Promise<DDCategoryBreakdown[]> {
    const categories = checklist.categories || []
    
    return categories.map((category: any) => {
      const items = category.items || []
      const totalItems = items.length
      const completedItems = items.filter((item: any) => item.status === 'COMPLETED').length
      const inProgressItems = items.filter((item: any) => item.status === 'IN_PROGRESS').length
      const pendingItems = items.filter((item: any) => item.status === 'NOT_STARTED').length

      const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0
      const estimatedHours = items.reduce((sum: number, item: any) => sum + (item.estimatedHours || 0), 0)
      const actualHours = items.reduce((sum: number, item: any) => sum + (item.actualHours || 0), 0)
      
      const averageCompletionTime = this.calculateAverageCompletionTime(items)
      const riskLevel = this.calculateCategoryRisk(category)

      return {
        categoryId: category.id,
        categoryName: category.name,
        categoryType: category.categoryType,
        totalItems,
        completedItems,
        inProgressItems,
        pendingItems,
        progress,
        estimatedHours,
        actualHours,
        averageCompletionTime,
        riskLevel
      }
    })
  }

  /**
   * Calculate team performance
   */
  private async calculateTeamPerformance(checklist: any): Promise<DDTeamPerformance[]> {
    const assignments = checklist.assignments || []
    const userMap = new Map()

    // Group assignments by user
    assignments.forEach((assignment: any) => {
      const userId = assignment.assignedTo
      if (!userMap.has(userId)) {
        userMap.set(userId, {
          userId,
          userName: assignment.assignedToName || 'Unknown',
          email: assignment.assignedToEmail || '',
          assignments: []
        })
      }
      userMap.get(userId).assignments.push(assignment)
    })

    // Calculate metrics for each user
    const teamPerformance: DDTeamPerformance[] = []
    
    for (const [userId, userData] of userMap) {
      const userAssignments = userData.assignments
      const assignedItems = userAssignments.length
      const completedItems = userAssignments.filter((a: any) => a.status === 'COMPLETED').length
      const inProgressItems = userAssignments.filter((a: any) => a.status === 'IN_PROGRESS').length
      const overdueItems = userAssignments.filter((a: any) => 
        a.dueDate && new Date(a.dueDate) < new Date() && a.status !== 'COMPLETED'
      ).length

      const averageCompletionTime = this.calculateUserAverageCompletionTime(userAssignments)
      const efficiency = this.calculateUserEfficiency(userAssignments)
      const qualityScore = this.calculateUserQualityScore(userAssignments)
      const workloadUtilization = this.calculateWorkloadUtilization(userAssignments)
      const contributionPercentage = assignedItems > 0 ? Math.round((completedItems / assignedItems) * 100) : 0

      teamPerformance.push({
        userId,
        userName: userData.userName,
        email: userData.email,
        assignedItems,
        completedItems,
        inProgressItems,
        overdueItems,
        averageCompletionTime,
        efficiency,
        qualityScore,
        workloadUtilization,
        contributionPercentage
      })
    }

    return teamPerformance
  }

  /**
   * Calculate timeline progress
   */
  private async calculateTimelineProgress(checklist: any): Promise<DDTimelineProgress[]> {
    // Mock implementation - would calculate actual timeline from historical data
    const timeline: DDTimelineProgress[] = []
    const startDate = new Date(checklist.createdAt)
    const today = new Date()
    
    for (let d = new Date(startDate); d <= today; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      
      // Mock data - in real implementation, this would come from historical records
      timeline.push({
        date: dateStr,
        completedItems: Math.floor(Math.random() * 10),
        totalItems: checklist.totalItems || 100,
        cumulativeProgress: Math.min(100, timeline.length * 2),
        dailyVelocity: Math.floor(Math.random() * 5),
        hoursSpent: Math.floor(Math.random() * 20)
      })
    }

    return timeline
  }

  /**
   * Identify bottlenecks
   */
  private async identifyBottlenecks(checklist: any): Promise<DDBottleneck[]> {
    const items = checklist.items || []
    const bottlenecks: DDBottleneck[] = []

    items.forEach((item: any) => {
      if (item.status === 'IN_PROGRESS' || item.status === 'BLOCKED') {
        const startDate = new Date(item.startedAt || item.assignedAt)
        const daysStuck = Math.floor((Date.now() - startDate.getTime()) / (1000 * 60 * 60 * 24))
        
        if (daysStuck > 3) { // Consider stuck if more than 3 days
          bottlenecks.push({
            itemId: item.id,
            itemTitle: item.title,
            categoryName: item.categoryName || 'Unknown',
            assignedTo: item.assignedTo || 'Unassigned',
            daysStuck,
            reason: this.identifyBottleneckReason(item),
            impact: this.calculateBottleneckImpact(item),
            suggestedAction: this.suggestBottleneckAction(item)
          })
        }
      }
    })

    return bottlenecks.sort((a, b) => b.daysStuck - a.daysStuck)
  }

  /**
   * Calculate trends
   */
  private async calculateTrends(checklist: any): Promise<DDTrends> {
    // Mock implementation - would calculate from historical data
    return {
      velocityTrend: [
        { period: 'Week 1', velocity: 8, trend: 'up' },
        { period: 'Week 2', velocity: 12, trend: 'up' },
        { period: 'Week 3', velocity: 10, trend: 'down' },
        { period: 'Week 4', velocity: 15, trend: 'up' }
      ],
      qualityTrend: [
        { period: 'Week 1', score: 4.2, trend: 'stable' },
        { period: 'Week 2', score: 4.5, trend: 'up' },
        { period: 'Week 3', score: 4.3, trend: 'down' },
        { period: 'Week 4', score: 4.7, trend: 'up' }
      ],
      efficiencyTrend: [
        { period: 'Week 1', efficiency: 0.85, trend: 'stable' },
        { period: 'Week 2', efficiency: 0.92, trend: 'up' },
        { period: 'Week 3', efficiency: 0.88, trend: 'down' },
        { period: 'Week 4', efficiency: 0.95, trend: 'up' }
      ],
      riskTrend: [
        { period: 'Week 1', riskScore: 2.1, trend: 'stable' },
        { period: 'Week 2', riskScore: 1.8, trend: 'down' },
        { period: 'Week 3', riskScore: 2.3, trend: 'up' },
        { period: 'Week 4', riskScore: 1.9, trend: 'down' }
      ]
    }
  }

  /**
   * Generate predictions
   */
  private async generatePredictions(checklist: any): Promise<DDPredictions> {
    const summary = await this.calculateSummary(checklist)
    const bottlenecks = await this.identifyBottlenecks(checklist)
    
    // Calculate estimated completion date
    const remainingItems = summary.totalItems - summary.completedItems
    const averageVelocity = this.calculateAverageVelocity(checklist)
    const daysToComplete = averageVelocity > 0 ? Math.ceil(remainingItems / averageVelocity) : 30
    
    const estimatedCompletion = new Date()
    estimatedCompletion.setDate(estimatedCompletion.getDate() + daysToComplete)

    // Calculate confidence level
    const confidenceLevel = this.calculateConfidenceLevel(checklist, bottlenecks)

    // Identify risk factors
    const riskFactors = this.identifyRiskFactors(checklist, bottlenecks)

    // Generate recommendations
    const recommendedActions = this.generateRecommendations(checklist, bottlenecks)

    // Calculate resource needs
    const resourceNeeds = this.calculateResourceNeeds(checklist)

    return {
      estimatedCompletionDate: estimatedCompletion.toISOString(),
      confidenceLevel,
      riskFactors,
      recommendedActions,
      resourceNeeds
    }
  }

  /**
   * Helper methods for calculations
   */
  private getChecklistData(checklistId: string, tenantId: string): Promise<any> {
    // Mock implementation - would fetch from database
    return Promise.resolve({
      id: checklistId,
      name: 'Sample Checklist',
      createdAt: '2024-02-01T00:00:00Z',
      totalItems: 100,
      categories: [],
      items: [],
      assignments: []
    })
  }

  private calculateQualityScore(items: any[]): number {
    // Mock implementation
    return 4.5
  }

  private calculateAverageVelocity(checklist: any): number {
    // Mock implementation
    return 5 // items per day
  }

  private calculateAverageCompletionTime(items: any[]): number {
    // Mock implementation
    return 2.5 // days
  }

  private calculateCategoryRisk(category: any): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    // Mock implementation
    return 'MEDIUM'
  }

  private calculateUserAverageCompletionTime(assignments: any[]): number {
    return 2.0
  }

  private calculateUserEfficiency(assignments: any[]): number {
    return 0.92
  }

  private calculateUserQualityScore(assignments: any[]): number {
    return 4.3
  }

  private calculateWorkloadUtilization(assignments: any[]): number {
    return 0.85
  }

  private calculateVelocity(checklist: any): number {
    return 5
  }

  private calculateRiskScore(checklist: any): number {
    return 2.1
  }

  private calculateTeamUtilization(teamPerformance: DDTeamPerformance[]): number {
    const avgUtilization = teamPerformance.reduce((sum, member) => sum + member.workloadUtilization, 0) / teamPerformance.length
    return Math.round(avgUtilization * 100) / 100
  }

  private identifyBottleneckReason(item: any): string {
    const reasons = [
      'Waiting for external dependencies',
      'Resource constraints',
      'Technical complexity',
      'Unclear requirements',
      'Approval delays'
    ]
    return reasons[Math.floor(Math.random() * reasons.length)]
  }

  private calculateBottleneckImpact(item: any): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    return item.priority === 'CRITICAL' ? 'CRITICAL' : 'MEDIUM'
  }

  private suggestBottleneckAction(item: any): string {
    const actions = [
      'Reassign to available team member',
      'Break down into smaller tasks',
      'Escalate to management',
      'Request additional resources',
      'Clarify requirements with stakeholders'
    ]
    return actions[Math.floor(Math.random() * actions.length)]
  }

  private calculateConfidenceLevel(checklist: any, bottlenecks: DDBottleneck[]): number {
    const baseConfidence = 0.8
    const bottleneckPenalty = bottlenecks.length * 0.05
    return Math.max(0.3, Math.min(0.95, baseConfidence - bottleneckPenalty))
  }

  private identifyRiskFactors(checklist: any, bottlenecks: DDBottleneck[]): string[] {
    const risks = []
    
    if (bottlenecks.length > 5) {
      risks.push('Multiple bottlenecks identified')
    }
    
    if (bottlenecks.some(b => b.impact === 'CRITICAL')) {
      risks.push('Critical path items at risk')
    }
    
    return risks
  }

  private generateRecommendations(checklist: any, bottlenecks: DDBottleneck[]): string[] {
    const recommendations = []
    
    if (bottlenecks.length > 0) {
      recommendations.push('Address identified bottlenecks immediately')
    }
    
    recommendations.push('Increase team communication frequency')
    recommendations.push('Consider parallel execution where possible')
    
    return recommendations
  }

  private calculateResourceNeeds(checklist: any): DDPredictions['resourceNeeds'] {
    return {
      additionalHours: 40,
      skillGaps: ['Financial Analysis', 'Legal Review'],
      criticalPath: ['item-1', 'item-5', 'item-12']
    }
  }
}
