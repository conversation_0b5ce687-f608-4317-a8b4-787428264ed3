import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { EventEmitter } from 'events'

export interface DDNotification {
  id: string
  type: DDNotificationType
  title: string
  message: string
  
  // Recipients
  userId: string
  userEmail: string
  
  // Context
  checklistId?: string
  dealId?: string
  itemId?: string
  assignmentId?: string
  
  // Notification settings
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  channels: DDNotificationChannel[]
  
  // Status
  status: 'PENDING' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED'
  sentAt?: Date
  deliveredAt?: Date
  readAt?: Date
  
  // Scheduling
  scheduledFor?: Date
  expiresAt?: Date
  
  // Metadata
  data: any
  createdAt: Date
  updatedAt: Date
  tenantId: string
}

export enum DDNotificationType {
  TASK_ASSIGNED = 'TASK_ASSIGNED',
  TASK_REASSIGNED = 'TASK_REASSIGNED',
  TASK_DUE_SOON = 'TASK_DUE_SOON',
  TASK_OVERDUE = 'TASK_OVERDUE',
  TASK_COMPLETED = 'TASK_COMPLETED',
  TASK_REJECTED = 'TASK_REJECTED',
  TASK_BLOCKED = 'TASK_BLOCKED',
  CHECKLIST_COMPLETED = 'CHECKLIST_COMPLETED',
  MILESTONE_REACHED = 'MILESTONE_REACHED',
  DEADLINE_APPROACHING = 'DEADLINE_APPROACHING',
  QUALITY_ISSUE = 'QUALITY_ISSUE',
  APPROVAL_REQUIRED = 'APPROVAL_REQUIRED',
  COMMENT_ADDED = 'COMMENT_ADDED',
  MENTION_RECEIVED = 'MENTION_RECEIVED',
  WORKFLOW_STARTED = 'WORKFLOW_STARTED',
  WORKFLOW_COMPLETED = 'WORKFLOW_COMPLETED',
  SYSTEM_ALERT = 'SYSTEM_ALERT'
}

export enum DDNotificationChannel {
  IN_APP = 'IN_APP',
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  SLACK = 'SLACK',
  TEAMS = 'TEAMS',
  WEBHOOK = 'WEBHOOK',
  PUSH = 'PUSH'
}

export interface DDNotificationTemplate {
  id: string
  type: DDNotificationType
  name: string
  description?: string
  
  // Template content
  titleTemplate: string
  messageTemplate: string
  emailTemplate?: string
  smsTemplate?: string
  
  // Default settings
  defaultPriority: DDNotification['priority']
  defaultChannels: DDNotificationChannel[]
  
  // Conditions
  conditions?: DDNotificationCondition[]
  
  // Metadata
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  tenantId: string
}

export interface DDNotificationCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than'
  value: any
}

export interface DDNotificationPreference {
  userId: string
  type: DDNotificationType
  channels: DDNotificationChannel[]
  isEnabled: boolean
  quietHours?: {
    start: string // HH:mm format
    end: string   // HH:mm format
    timezone: string
  }
  frequency?: 'IMMEDIATE' | 'HOURLY' | 'DAILY' | 'WEEKLY'
}

export interface DDNotificationRule {
  id: string
  name: string
  description?: string
  isActive: boolean
  priority: number
  
  // Trigger conditions
  triggers: DDNotificationTrigger[]
  
  // Notification settings
  notificationType: DDNotificationType
  templateId?: string
  customMessage?: string
  
  // Recipients
  recipientType: 'USER' | 'ROLE' | 'TEAM' | 'ASSIGNEE' | 'WATCHERS'
  recipientIds?: string[]
  
  // Scheduling
  delay?: number // minutes
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt: Date
  tenantId: string
}

export interface DDNotificationTrigger {
  event: string
  conditions: DDNotificationCondition[]
}

export class DDNotificationService extends EventEmitter {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    super()
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DDNotificationService')
  }

  /**
   * Send notification
   */
  async sendNotification(notification: Omit<DDNotification, 'id' | 'createdAt' | 'updatedAt'>): Promise<DDNotification> {
    try {
      this.logger.info('Sending notification', {
        type: notification.type,
        userId: notification.userId,
        priority: notification.priority
      })

      // Check user preferences
      const preferences = await this.getUserPreferences(notification.userId, notification.type)
      if (!preferences.isEnabled) {
        this.logger.info('Notification disabled by user preferences', {
          userId: notification.userId,
          type: notification.type
        })
        return null
      }

      // Filter channels based on preferences
      const allowedChannels = notification.channels.filter(channel => 
        preferences.channels.includes(channel)
      )

      if (allowedChannels.length === 0) {
        this.logger.info('No allowed channels for notification', {
          userId: notification.userId,
          type: notification.type
        })
        return null
      }

      // Check quiet hours
      if (this.isInQuietHours(preferences)) {
        // Schedule for later if not urgent
        if (notification.priority !== 'URGENT') {
          return this.scheduleNotification(notification, this.getNextActiveTime(preferences))
        }
      }

      // Create notification record
      const createdNotification = await this.prisma.dDNotification.create({
        data: {
          type: notification.type,
          title: notification.title,
          message: notification.message,
          userId: notification.userId,
          userEmail: notification.userEmail,
          checklistId: notification.checklistId,
          dealId: notification.dealId,
          itemId: notification.itemId,
          assignmentId: notification.assignmentId,
          priority: notification.priority,
          channels: allowedChannels,
          status: 'PENDING',
          scheduledFor: notification.scheduledFor,
          expiresAt: notification.expiresAt,
          data: notification.data,
          tenantId: notification.tenantId
        }
      })

      // Send through each channel
      const sendPromises = allowedChannels.map(channel => 
        this.sendThroughChannel(createdNotification as DDNotification, channel)
      )

      await Promise.allSettled(sendPromises)

      // Update status
      await this.updateNotificationStatus(createdNotification.id, 'SENT')

      // Emit notification sent event
      this.emit('notification:sent', {
        notificationId: createdNotification.id,
        type: notification.type,
        userId: notification.userId
      })

      this.logger.info('Notification sent successfully', {
        notificationId: createdNotification.id,
        type: notification.type,
        channels: allowedChannels
      })

      return createdNotification as DDNotification
    } catch (error) {
      this.logger.error('Failed to send notification', error)
      throw error
    }
  }

  /**
   * Send notification from template
   */
  async sendFromTemplate(
    templateId: string,
    userId: string,
    context: any,
    options: {
      checklistId?: string
      dealId?: string
      itemId?: string
      assignmentId?: string
      priority?: DDNotification['priority']
      scheduledFor?: Date
    } = {}
  ): Promise<DDNotification> {
    try {
      // Get template
      const template = await this.getTemplate(templateId)
      if (!template) {
        throw new Error('Notification template not found')
      }

      // Get user info
      const user = await this.getUserInfo(userId)
      if (!user) {
        throw new Error('User not found')
      }

      // Render template
      const title = this.renderTemplate(template.titleTemplate, context)
      const message = this.renderTemplate(template.messageTemplate, context)

      // Create notification
      const notification: Omit<DDNotification, 'id' | 'createdAt' | 'updatedAt'> = {
        type: template.type,
        title,
        message,
        userId,
        userEmail: user.email,
        checklistId: options.checklistId,
        dealId: options.dealId,
        itemId: options.itemId,
        assignmentId: options.assignmentId,
        priority: options.priority || template.defaultPriority,
        channels: template.defaultChannels,
        status: 'PENDING',
        scheduledFor: options.scheduledFor,
        data: context,
        tenantId: user.tenantId
      }

      return this.sendNotification(notification)
    } catch (error) {
      this.logger.error('Failed to send notification from template', error)
      throw error
    }
  }

  /**
   * Send bulk notifications
   */
  async sendBulkNotifications(
    notifications: Array<Omit<DDNotification, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<DDNotification[]> {
    try {
      this.logger.info('Sending bulk notifications', { count: notifications.length })

      const results = await Promise.allSettled(
        notifications.map(notification => this.sendNotification(notification))
      )

      const successful = results
        .filter(result => result.status === 'fulfilled')
        .map(result => (result as PromiseFulfilledResult<DDNotification>).value)
        .filter(Boolean)

      this.logger.info('Bulk notifications sent', {
        total: notifications.length,
        successful: successful.length,
        failed: notifications.length - successful.length
      })

      return successful
    } catch (error) {
      this.logger.error('Failed to send bulk notifications', error)
      throw error
    }
  }

  /**
   * Schedule notification
   */
  async scheduleNotification(
    notification: Omit<DDNotification, 'id' | 'createdAt' | 'updatedAt'>,
    scheduledFor: Date
  ): Promise<DDNotification> {
    try {
      this.logger.info('Scheduling notification', {
        type: notification.type,
        userId: notification.userId,
        scheduledFor
      })

      const scheduledNotification = {
        ...notification,
        scheduledFor,
        status: 'PENDING' as const
      }

      return this.sendNotification(scheduledNotification)
    } catch (error) {
      this.logger.error('Failed to schedule notification', error)
      throw error
    }
  }

  /**
   * Process scheduled notifications
   */
  async processScheduledNotifications(): Promise<void> {
    try {
      const now = new Date()
      
      const scheduledNotifications = await this.prisma.dDNotification.findMany({
        where: {
          status: 'PENDING',
          scheduledFor: {
            lte: now
          }
        }
      })

      this.logger.info('Processing scheduled notifications', {
        count: scheduledNotifications.length
      })

      for (const notification of scheduledNotifications) {
        try {
          // Send through each channel
          const sendPromises = notification.channels.map(channel => 
            this.sendThroughChannel(notification as DDNotification, channel)
          )

          await Promise.allSettled(sendPromises)

          // Update status
          await this.updateNotificationStatus(notification.id, 'SENT')
        } catch (error) {
          this.logger.error('Failed to process scheduled notification', error, {
            notificationId: notification.id
          })
          
          await this.updateNotificationStatus(notification.id, 'FAILED')
        }
      }
    } catch (error) {
      this.logger.error('Failed to process scheduled notifications', error)
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: string): Promise<void> {
    try {
      await this.prisma.dDNotification.updateMany({
        where: {
          id: notificationId,
          userId
        },
        data: {
          status: 'READ',
          readAt: new Date()
        }
      })

      this.emit('notification:read', { notificationId, userId })
    } catch (error) {
      this.logger.error('Failed to mark notification as read', error)
      throw error
    }
  }

  /**
   * Get user notifications
   */
  async getUserNotifications(
    userId: string,
    tenantId: string,
    options: {
      unreadOnly?: boolean
      limit?: number
      offset?: number
      types?: DDNotificationType[]
    } = {}
  ): Promise<{ notifications: DDNotification[]; total: number }> {
    try {
      const where: any = {
        userId,
        tenantId
      }

      if (options.unreadOnly) {
        where.status = { not: 'READ' }
      }

      if (options.types && options.types.length > 0) {
        where.type = { in: options.types }
      }

      const [notifications, total] = await Promise.all([
        this.prisma.dDNotification.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          take: options.limit || 50,
          skip: options.offset || 0
        }),
        this.prisma.dDNotification.count({ where })
      ])

      return {
        notifications: notifications as DDNotification[],
        total
      }
    } catch (error) {
      this.logger.error('Failed to get user notifications', error)
      throw error
    }
  }

  /**
   * Send through specific channel
   */
  private async sendThroughChannel(notification: DDNotification, channel: DDNotificationChannel): Promise<void> {
    try {
      switch (channel) {
        case DDNotificationChannel.IN_APP:
          await this.sendInAppNotification(notification)
          break
        case DDNotificationChannel.EMAIL:
          await this.sendEmailNotification(notification)
          break
        case DDNotificationChannel.SMS:
          await this.sendSMSNotification(notification)
          break
        case DDNotificationChannel.SLACK:
          await this.sendSlackNotification(notification)
          break
        case DDNotificationChannel.TEAMS:
          await this.sendTeamsNotification(notification)
          break
        case DDNotificationChannel.WEBHOOK:
          await this.sendWebhookNotification(notification)
          break
        case DDNotificationChannel.PUSH:
          await this.sendPushNotification(notification)
          break
      }
    } catch (error) {
      this.logger.error('Failed to send through channel', error, {
        notificationId: notification.id,
        channel
      })
      throw error
    }
  }

  /**
   * Channel-specific sending methods
   */
  private async sendInAppNotification(notification: DDNotification): Promise<void> {
    // In-app notifications are stored in database and shown in UI
    this.logger.info('In-app notification stored', { notificationId: notification.id })
  }

  private async sendEmailNotification(notification: DDNotification): Promise<void> {
    // Mock email sending - would integrate with email service
    this.logger.info('Email notification sent', {
      notificationId: notification.id,
      email: notification.userEmail
    })
  }

  private async sendSMSNotification(notification: DDNotification): Promise<void> {
    // Mock SMS sending - would integrate with SMS service
    this.logger.info('SMS notification sent', { notificationId: notification.id })
  }

  private async sendSlackNotification(notification: DDNotification): Promise<void> {
    // Mock Slack sending - would integrate with Slack API
    this.logger.info('Slack notification sent', { notificationId: notification.id })
  }

  private async sendTeamsNotification(notification: DDNotification): Promise<void> {
    // Mock Teams sending - would integrate with Teams API
    this.logger.info('Teams notification sent', { notificationId: notification.id })
  }

  private async sendWebhookNotification(notification: DDNotification): Promise<void> {
    // Mock webhook sending - would call external webhook
    this.logger.info('Webhook notification sent', { notificationId: notification.id })
  }

  private async sendPushNotification(notification: DDNotification): Promise<void> {
    // Mock push notification - would integrate with push service
    this.logger.info('Push notification sent', { notificationId: notification.id })
  }

  /**
   * Helper methods
   */
  private async getUserPreferences(userId: string, type: DDNotificationType): Promise<DDNotificationPreference> {
    // Mock implementation - would fetch from database
    return {
      userId,
      type,
      channels: [DDNotificationChannel.IN_APP, DDNotificationChannel.EMAIL],
      isEnabled: true,
      frequency: 'IMMEDIATE'
    }
  }

  private async getUserInfo(userId: string): Promise<any> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        tenantId: true
      }
    })

    return user
  }

  private async getTemplate(templateId: string): Promise<DDNotificationTemplate | null> {
    // Mock implementation - would fetch from database
    return {
      id: templateId,
      type: DDNotificationType.TASK_ASSIGNED,
      name: 'Task Assignment',
      titleTemplate: 'New Task Assigned: {{itemTitle}}',
      messageTemplate: 'You have been assigned a new task: {{itemTitle}} in {{checklistName}}',
      defaultPriority: 'MEDIUM',
      defaultChannels: [DDNotificationChannel.IN_APP, DDNotificationChannel.EMAIL],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      tenantId: 'tenant-1'
    }
  }

  private renderTemplate(template: string, context: any): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return context[key] || match
    })
  }

  private isInQuietHours(preferences: DDNotificationPreference): boolean {
    if (!preferences.quietHours) return false
    
    // Mock implementation - would check actual time against quiet hours
    return false
  }

  private getNextActiveTime(preferences: DDNotificationPreference): Date {
    // Mock implementation - would calculate next active time
    const nextTime = new Date()
    nextTime.setHours(nextTime.getHours() + 1)
    return nextTime
  }

  private async updateNotificationStatus(notificationId: string, status: DDNotification['status']): Promise<void> {
    const updateData: any = { status }
    
    if (status === 'SENT') {
      updateData.sentAt = new Date()
    } else if (status === 'DELIVERED') {
      updateData.deliveredAt = new Date()
    } else if (status === 'READ') {
      updateData.readAt = new Date()
    }

    await this.prisma.dDNotification.update({
      where: { id: notificationId },
      data: updateData
    })
  }
}
