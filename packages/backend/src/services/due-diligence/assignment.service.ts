import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { EventEmitter } from 'events'

export interface TaskAssignment {
  id: string
  itemId: string
  checklistId: string
  dealId: string
  
  // Assignment details
  assignedTo: string
  assignedBy: string
  assignedAt: Date
  dueDate?: Date
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  
  // Status tracking
  status: 'ASSIGNED' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'REJECTED' | 'REASSIGNED'
  acceptedAt?: Date
  startedAt?: Date
  completedAt?: Date
  
  // Assignment context
  instructions?: string
  estimatedHours: number
  actualHours?: number
  
  // Collaboration
  collaborators: string[]
  watchers: string[]
  
  // Progress tracking
  progressPercentage: number
  lastUpdateAt: Date
  lastUpdateBy: string
  
  // Metadata
  createdAt: Date
  updatedAt: Date
  tenantId: string
}

export interface AssignmentRule {
  id: string
  name: string
  description?: string
  isActive: boolean
  priority: number
  
  // Rule conditions
  conditions: AssignmentCondition[]
  
  // Assignment logic
  assignmentType: 'USER' | 'ROLE' | 'TEAM' | 'ROUND_ROBIN' | 'WORKLOAD_BASED' | 'SKILL_BASED'
  assignmentConfig: any
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt: Date
  tenantId: string
}

export interface AssignmentCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'in' | 'greater_than' | 'less_than'
  value: any
}

export interface UserWorkload {
  userId: string
  userName: string
  email: string
  
  // Current workload
  activeAssignments: number
  totalEstimatedHours: number
  overdueAssignments: number
  
  // Capacity
  maxConcurrentTasks: number
  hoursPerWeek: number
  availableHours: number
  
  // Performance metrics
  averageCompletionTime: number
  completionRate: number
  qualityScore: number
  
  // Availability
  isAvailable: boolean
  unavailableUntil?: Date
  timeZone: string
}

export interface AssignmentNotification {
  type: 'ASSIGNED' | 'REASSIGNED' | 'DUE_SOON' | 'OVERDUE' | 'COMPLETED' | 'REJECTED'
  assignmentId: string
  userId: string
  message: string
  data: any
  createdAt: Date
}

export class DDAssignmentService extends EventEmitter {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    super()
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DDAssignmentService')
  }

  /**
   * Create task assignment
   */
  async createAssignment(
    itemId: string,
    checklistId: string,
    dealId: string,
    assignedTo: string,
    assignedBy: string,
    tenantId: string,
    options: {
      dueDate?: Date
      priority?: TaskAssignment['priority']
      instructions?: string
      estimatedHours?: number
      collaborators?: string[]
      watchers?: string[]
    } = {}
  ): Promise<TaskAssignment> {
    try {
      this.logger.info('Creating task assignment', {
        itemId,
        assignedTo,
        assignedBy
      })

      // Check if user is available
      const userWorkload = await this.getUserWorkload(assignedTo, tenantId)
      if (!userWorkload.isAvailable) {
        throw new Error('User is not available for assignment')
      }

      // Check workload capacity
      if (userWorkload.activeAssignments >= userWorkload.maxConcurrentTasks) {
        this.logger.warn('User workload at capacity', {
          userId: assignedTo,
          activeAssignments: userWorkload.activeAssignments,
          maxTasks: userWorkload.maxConcurrentTasks
        })
      }

      // Create assignment
      const assignment = await this.prisma.taskAssignment.create({
        data: {
          itemId,
          checklistId,
          dealId,
          assignedTo,
          assignedBy,
          assignedAt: new Date(),
          dueDate: options.dueDate,
          priority: options.priority || 'MEDIUM',
          status: 'ASSIGNED',
          instructions: options.instructions,
          estimatedHours: options.estimatedHours || 1,
          collaborators: options.collaborators || [],
          watchers: options.watchers || [],
          progressPercentage: 0,
          lastUpdateAt: new Date(),
          lastUpdateBy: assignedBy,
          tenantId
        }
      })

      // Send notification
      await this.sendAssignmentNotification({
        type: 'ASSIGNED',
        assignmentId: assignment.id,
        userId: assignedTo,
        message: `You have been assigned a new task: ${itemId}`,
        data: { assignment },
        createdAt: new Date()
      })

      // Update user workload cache
      await this.invalidateUserWorkloadCache(assignedTo)

      // Emit assignment event
      this.emit('assignment:created', {
        assignmentId: assignment.id,
        itemId,
        assignedTo,
        assignedBy
      })

      this.logger.info('Task assignment created successfully', {
        assignmentId: assignment.id,
        itemId,
        assignedTo
      })

      return assignment as TaskAssignment
    } catch (error) {
      this.logger.error('Failed to create task assignment', error)
      throw error
    }
  }

  /**
   * Auto-assign task based on rules
   */
  async autoAssignTask(
    itemId: string,
    checklistId: string,
    dealId: string,
    tenantId: string,
    context: any = {}
  ): Promise<TaskAssignment | null> {
    try {
      this.logger.info('Auto-assigning task', { itemId, checklistId })

      // Get assignment rules
      const rules = await this.getAssignmentRules(tenantId)
      
      // Find matching rule
      const matchingRule = await this.findMatchingRule(rules, context)
      if (!matchingRule) {
        this.logger.info('No matching assignment rule found', { itemId })
        return null
      }

      // Get assignee based on rule
      const assignee = await this.getAssigneeFromRule(matchingRule, tenantId, context)
      if (!assignee) {
        this.logger.warn('No suitable assignee found', { itemId, ruleId: matchingRule.id })
        return null
      }

      // Create assignment
      const assignment = await this.createAssignment(
        itemId,
        checklistId,
        dealId,
        assignee,
        'system',
        tenantId,
        {
          priority: context.priority,
          estimatedHours: context.estimatedHours,
          dueDate: context.dueDate
        }
      )

      this.logger.info('Task auto-assigned successfully', {
        assignmentId: assignment.id,
        itemId,
        assignee,
        ruleId: matchingRule.id
      })

      return assignment
    } catch (error) {
      this.logger.error('Failed to auto-assign task', error)
      return null
    }
  }

  /**
   * Reassign task
   */
  async reassignTask(
    assignmentId: string,
    newAssignee: string,
    reassignedBy: string,
    reason?: string
  ): Promise<TaskAssignment> {
    try {
      this.logger.info('Reassigning task', {
        assignmentId,
        newAssignee,
        reassignedBy
      })

      // Get current assignment
      const currentAssignment = await this.getAssignment(assignmentId)
      if (!currentAssignment) {
        throw new Error('Assignment not found')
      }

      // Check if new assignee is available
      const userWorkload = await this.getUserWorkload(newAssignee, currentAssignment.tenantId)
      if (!userWorkload.isAvailable) {
        throw new Error('New assignee is not available')
      }

      // Update assignment
      const updatedAssignment = await this.prisma.taskAssignment.update({
        where: { id: assignmentId },
        data: {
          assignedTo: newAssignee,
          assignedBy: reassignedBy,
          assignedAt: new Date(),
          status: 'REASSIGNED',
          lastUpdateAt: new Date(),
          lastUpdateBy: reassignedBy,
          progressPercentage: 0 // Reset progress
        }
      })

      // Send notifications
      await Promise.all([
        // Notify new assignee
        this.sendAssignmentNotification({
          type: 'REASSIGNED',
          assignmentId,
          userId: newAssignee,
          message: `Task has been reassigned to you: ${currentAssignment.itemId}`,
          data: { assignment: updatedAssignment, reason },
          createdAt: new Date()
        }),
        // Notify previous assignee
        this.sendAssignmentNotification({
          type: 'REASSIGNED',
          assignmentId,
          userId: currentAssignment.assignedTo,
          message: `Task has been reassigned: ${currentAssignment.itemId}`,
          data: { assignment: updatedAssignment, reason },
          createdAt: new Date()
        })
      ])

      // Update workload caches
      await Promise.all([
        this.invalidateUserWorkloadCache(currentAssignment.assignedTo),
        this.invalidateUserWorkloadCache(newAssignee)
      ])

      // Emit reassignment event
      this.emit('assignment:reassigned', {
        assignmentId,
        previousAssignee: currentAssignment.assignedTo,
        newAssignee,
        reassignedBy
      })

      this.logger.info('Task reassigned successfully', {
        assignmentId,
        previousAssignee: currentAssignment.assignedTo,
        newAssignee
      })

      return updatedAssignment as TaskAssignment
    } catch (error) {
      this.logger.error('Failed to reassign task', error)
      throw error
    }
  }

  /**
   * Update assignment status
   */
  async updateAssignmentStatus(
    assignmentId: string,
    status: TaskAssignment['status'],
    userId: string,
    data: {
      progressPercentage?: number
      actualHours?: number
      notes?: string
    } = {}
  ): Promise<TaskAssignment> {
    try {
      this.logger.info('Updating assignment status', {
        assignmentId,
        status,
        userId
      })

      const updateData: any = {
        status,
        lastUpdateAt: new Date(),
        lastUpdateBy: userId
      }

      // Set timestamps based on status
      switch (status) {
        case 'ACCEPTED':
          updateData.acceptedAt = new Date()
          break
        case 'IN_PROGRESS':
          updateData.startedAt = new Date()
          break
        case 'COMPLETED':
          updateData.completedAt = new Date()
          updateData.progressPercentage = 100
          break
      }

      // Add optional data
      if (data.progressPercentage !== undefined) {
        updateData.progressPercentage = data.progressPercentage
      }
      if (data.actualHours !== undefined) {
        updateData.actualHours = data.actualHours
      }

      // Update assignment
      const updatedAssignment = await this.prisma.taskAssignment.update({
        where: { id: assignmentId },
        data: updateData
      })

      // Send notification for status changes
      if (status === 'COMPLETED') {
        await this.sendAssignmentNotification({
          type: 'COMPLETED',
          assignmentId,
          userId: updatedAssignment.assignedBy,
          message: `Task completed: ${updatedAssignment.itemId}`,
          data: { assignment: updatedAssignment },
          createdAt: new Date()
        })
      }

      // Update workload cache
      await this.invalidateUserWorkloadCache(updatedAssignment.assignedTo)

      // Emit status update event
      this.emit('assignment:status_updated', {
        assignmentId,
        status,
        userId
      })

      this.logger.info('Assignment status updated successfully', {
        assignmentId,
        status
      })

      return updatedAssignment as TaskAssignment
    } catch (error) {
      this.logger.error('Failed to update assignment status', error)
      throw error
    }
  }

  /**
   * Get user workload
   */
  async getUserWorkload(userId: string, tenantId: string): Promise<UserWorkload> {
    try {
      const cacheKey = `workload:${userId}:${tenantId}`
      
      // Try cache first
      const cached = await this.cache.get<UserWorkload>(cacheKey)
      if (cached) {
        return cached
      }

      // Calculate workload from database
      const [user, activeAssignments, userSettings] = await Promise.all([
        this.prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }),
        this.prisma.taskAssignment.findMany({
          where: {
            assignedTo: userId,
            tenantId,
            status: { in: ['ASSIGNED', 'ACCEPTED', 'IN_PROGRESS'] }
          }
        }),
        this.getUserSettings(userId, tenantId)
      ])

      if (!user) {
        throw new Error('User not found')
      }

      // Calculate metrics
      const totalEstimatedHours = activeAssignments.reduce(
        (sum, assignment) => sum + assignment.estimatedHours,
        0
      )

      const overdueAssignments = activeAssignments.filter(
        assignment => assignment.dueDate && assignment.dueDate < new Date()
      ).length

      const workload: UserWorkload = {
        userId,
        userName: `${user.firstName} ${user.lastName}`,
        email: user.email,
        activeAssignments: activeAssignments.length,
        totalEstimatedHours,
        overdueAssignments,
        maxConcurrentTasks: userSettings.maxConcurrentTasks || 5,
        hoursPerWeek: userSettings.hoursPerWeek || 40,
        availableHours: Math.max(0, (userSettings.hoursPerWeek || 40) - totalEstimatedHours),
        averageCompletionTime: await this.calculateAverageCompletionTime(userId, tenantId),
        completionRate: await this.calculateCompletionRate(userId, tenantId),
        qualityScore: await this.calculateQualityScore(userId, tenantId),
        isAvailable: userSettings.isAvailable !== false,
        unavailableUntil: userSettings.unavailableUntil,
        timeZone: userSettings.timeZone || 'UTC'
      }

      // Cache for 15 minutes
      await this.cache.set(cacheKey, workload, 900)

      return workload
    } catch (error) {
      this.logger.error('Failed to get user workload', error)
      throw error
    }
  }

  /**
   * Get team workload summary
   */
  async getTeamWorkload(teamId: string, tenantId: string): Promise<UserWorkload[]> {
    try {
      // Get team members
      const teamMembers = await this.getTeamMembers(teamId, tenantId)
      
      // Get workload for each member
      const workloads = await Promise.all(
        teamMembers.map(member => this.getUserWorkload(member.userId, tenantId))
      )

      return workloads
    } catch (error) {
      this.logger.error('Failed to get team workload', error)
      throw error
    }
  }

  /**
   * Get assignment by ID
   */
  private async getAssignment(assignmentId: string): Promise<TaskAssignment | null> {
    const assignment = await this.prisma.taskAssignment.findUnique({
      where: { id: assignmentId }
    })

    return assignment as TaskAssignment | null
  }

  /**
   * Get assignment rules
   */
  private async getAssignmentRules(tenantId: string): Promise<AssignmentRule[]> {
    const rules = await this.prisma.assignmentRule.findMany({
      where: {
        tenantId,
        isActive: true
      },
      orderBy: { priority: 'asc' }
    })

    return rules as AssignmentRule[]
  }

  /**
   * Find matching assignment rule
   */
  private async findMatchingRule(rules: AssignmentRule[], context: any): Promise<AssignmentRule | null> {
    for (const rule of rules) {
      const matches = rule.conditions.every(condition => 
        this.evaluateCondition(condition, context)
      )
      
      if (matches) {
        return rule
      }
    }
    
    return null
  }

  /**
   * Evaluate assignment condition
   */
  private evaluateCondition(condition: AssignmentCondition, context: any): boolean {
    const fieldValue = context[condition.field]
    
    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value
      case 'not_equals':
        return fieldValue !== condition.value
      case 'contains':
        return String(fieldValue).includes(condition.value)
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(fieldValue)
      case 'greater_than':
        return Number(fieldValue) > Number(condition.value)
      case 'less_than':
        return Number(fieldValue) < Number(condition.value)
      default:
        return false
    }
  }

  /**
   * Get assignee from rule
   */
  private async getAssigneeFromRule(
    rule: AssignmentRule,
    tenantId: string,
    context: any
  ): Promise<string | null> {
    switch (rule.assignmentType) {
      case 'USER':
        return rule.assignmentConfig.userId
      case 'ROUND_ROBIN':
        return this.getRoundRobinAssignee(rule.assignmentConfig.userIds, tenantId)
      case 'WORKLOAD_BASED':
        return this.getWorkloadBasedAssignee(rule.assignmentConfig.userIds, tenantId)
      case 'SKILL_BASED':
        return this.getSkillBasedAssignee(rule.assignmentConfig.skills, tenantId, context)
      default:
        return null
    }
  }

  /**
   * Assignment strategies
   */
  private async getRoundRobinAssignee(userIds: string[], tenantId: string): Promise<string | null> {
    // Simple round-robin implementation
    const lastAssignmentIndex = await this.getLastRoundRobinIndex(userIds, tenantId)
    const nextIndex = (lastAssignmentIndex + 1) % userIds.length
    
    await this.setLastRoundRobinIndex(userIds, tenantId, nextIndex)
    
    return userIds[nextIndex]
  }

  private async getWorkloadBasedAssignee(userIds: string[], tenantId: string): Promise<string | null> {
    const workloads = await Promise.all(
      userIds.map(userId => this.getUserWorkload(userId, tenantId))
    )

    // Find user with lowest workload
    const availableUsers = workloads.filter(w => w.isAvailable && w.activeAssignments < w.maxConcurrentTasks)
    
    if (availableUsers.length === 0) {
      return null
    }

    availableUsers.sort((a, b) => a.activeAssignments - b.activeAssignments)
    
    return availableUsers[0].userId
  }

  private async getSkillBasedAssignee(skills: string[], tenantId: string, context: any): Promise<string | null> {
    // Find users with required skills
    const usersWithSkills = await this.getUsersWithSkills(skills, tenantId)
    
    if (usersWithSkills.length === 0) {
      return null
    }

    // Use workload-based assignment among skilled users
    return this.getWorkloadBasedAssignee(usersWithSkills.map(u => u.userId), tenantId)
  }

  /**
   * Helper methods
   */
  private async getUserSettings(userId: string, tenantId: string): Promise<any> {
    // Mock implementation - would fetch from user settings table
    return {
      maxConcurrentTasks: 5,
      hoursPerWeek: 40,
      isAvailable: true,
      timeZone: 'UTC'
    }
  }

  private async getTeamMembers(teamId: string, tenantId: string): Promise<any[]> {
    // Mock implementation - would fetch from team members table
    return []
  }

  private async calculateAverageCompletionTime(userId: string, tenantId: string): Promise<number> {
    // Mock implementation - would calculate from completed assignments
    return 24 // hours
  }

  private async calculateCompletionRate(userId: string, tenantId: string): Promise<number> {
    // Mock implementation - would calculate completion rate
    return 0.95 // 95%
  }

  private async calculateQualityScore(userId: string, tenantId: string): Promise<number> {
    // Mock implementation - would calculate quality score
    return 4.5 // out of 5
  }

  private async getUsersWithSkills(skills: string[], tenantId: string): Promise<any[]> {
    // Mock implementation - would fetch users with required skills
    return []
  }

  private async getLastRoundRobinIndex(userIds: string[], tenantId: string): Promise<number> {
    const key = `round_robin:${userIds.join(',')}:${tenantId}`
    const index = await this.cache.get<number>(key)
    return index || 0
  }

  private async setLastRoundRobinIndex(userIds: string[], tenantId: string, index: number): Promise<void> {
    const key = `round_robin:${userIds.join(',')}:${tenantId}`
    await this.cache.set(key, index, 86400) // 24 hours
  }

  private async sendAssignmentNotification(notification: AssignmentNotification): Promise<void> {
    // Mock implementation - would send actual notifications
    this.logger.info('Sending assignment notification', {
      type: notification.type,
      userId: notification.userId,
      assignmentId: notification.assignmentId
    })
  }

  private async invalidateUserWorkloadCache(userId: string): Promise<void> {
    await this.cache.deletePattern(`workload:${userId}:*`)
  }
}
