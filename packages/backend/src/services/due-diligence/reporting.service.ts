import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface DDReport {
  id: string
  name: string
  description?: string
  type: DDReportType
  format: DDReportFormat
  
  // Report configuration
  templateId?: string
  parameters: DDReportParameters
  filters: DDReportFilter[]
  
  // Scheduling
  isScheduled: boolean
  schedule?: DDReportSchedule
  
  // Output
  status: 'PENDING' | 'GENERATING' | 'COMPLETED' | 'FAILED'
  generatedAt?: Date
  fileUrl?: string
  fileSize?: number
  
  // Access control
  visibility: 'PRIVATE' | 'TEAM' | 'PUBLIC'
  allowedUsers: string[]
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt: Date
  tenantId: string
}

export enum DDReportType {
  PROGRESS_SUMMARY = 'PROGRESS_SUMMARY',
  DETAILED_STATUS = 'DETAILED_STATUS',
  TEAM_PERFORMANCE = 'TEAM_PERFORMANCE',
  CATEGORY_BREAKDOWN = 'CATEGORY_BREAKDOWN',
  TIME_TRACKING = 'TIME_TRACKING',
  QUALITY_METRICS = 'QUALITY_METRICS',
  BOTTLENECK_ANALYSIS = 'BOTTLENECK_ANALYSIS',
  COMPLIANCE_REPORT = 'COMPLIANCE_REPORT',
  EXECUTIVE_SUMMARY = 'EXECUTIVE_SUMMARY',
  CUSTOM = 'CUSTOM'
}

export enum DDReportFormat {
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  CSV = 'CSV',
  JSON = 'JSON',
  HTML = 'HTML'
}

export interface DDReportParameters {
  checklistIds?: string[]
  dealIds?: string[]
  dateRange?: {
    from: Date
    to: Date
  }
  categories?: string[]
  assignees?: string[]
  includeSubtasks?: boolean
  includeComments?: boolean
  includeAttachments?: boolean
  groupBy?: 'category' | 'assignee' | 'status' | 'priority'
  sortBy?: 'name' | 'date' | 'progress' | 'priority'
  sortOrder?: 'asc' | 'desc'
}

export interface DDReportFilter {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'in' | 'greater_than' | 'less_than' | 'between'
  value: any
}

export interface DDReportSchedule {
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
  dayOfWeek?: number // 0-6 for weekly
  dayOfMonth?: number // 1-31 for monthly
  time: string // HH:mm format
  timezone: string
  recipients: string[]
  isActive: boolean
}

export interface DDReportTemplate {
  id: string
  name: string
  description?: string
  type: DDReportType
  format: DDReportFormat
  
  // Template structure
  sections: DDReportSection[]
  styling: DDReportStyling
  
  // Default parameters
  defaultParameters: DDReportParameters
  defaultFilters: DDReportFilter[]
  
  // Metadata
  isPublic: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
  tenantId: string
}

export interface DDReportSection {
  id: string
  name: string
  type: 'CHART' | 'TABLE' | 'TEXT' | 'METRICS' | 'LIST'
  order: number
  
  // Content configuration
  dataSource: string
  query?: string
  chartType?: 'bar' | 'line' | 'pie' | 'donut' | 'area'
  columns?: DDReportColumn[]
  
  // Styling
  styling?: any
}

export interface DDReportColumn {
  field: string
  label: string
  type: 'text' | 'number' | 'date' | 'boolean' | 'progress' | 'status'
  format?: string
  width?: number
  sortable?: boolean
}

export interface DDReportStyling {
  theme: 'default' | 'corporate' | 'minimal' | 'colorful'
  colors: {
    primary: string
    secondary: string
    accent: string
  }
  fonts: {
    heading: string
    body: string
  }
  logo?: string
  header?: string
  footer?: string
}

export class DDReportingService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DDReportingService')
  }

  /**
   * Generate report
   */
  async generateReport(
    reportConfig: Omit<DDReport, 'id' | 'status' | 'createdAt' | 'updatedAt'>,
    userId: string
  ): Promise<DDReport> {
    try {
      this.logger.info('Generating report', {
        type: reportConfig.type,
        format: reportConfig.format,
        userId
      })

      // Create report record
      const report = await this.prisma.dDReport.create({
        data: {
          name: reportConfig.name,
          description: reportConfig.description,
          type: reportConfig.type,
          format: reportConfig.format,
          templateId: reportConfig.templateId,
          parameters: reportConfig.parameters,
          filters: reportConfig.filters,
          isScheduled: reportConfig.isScheduled,
          schedule: reportConfig.schedule,
          status: 'GENERATING',
          visibility: reportConfig.visibility,
          allowedUsers: reportConfig.allowedUsers,
          createdBy: userId,
          tenantId: reportConfig.tenantId
        }
      })

      // Generate report content
      const reportData = await this.collectReportData(report as DDReport)
      const generatedFile = await this.generateReportFile(report as DDReport, reportData)

      // Update report with file information
      const updatedReport = await this.prisma.dDReport.update({
        where: { id: report.id },
        data: {
          status: 'COMPLETED',
          generatedAt: new Date(),
          fileUrl: generatedFile.url,
          fileSize: generatedFile.size
        }
      })

      this.logger.info('Report generated successfully', {
        reportId: report.id,
        fileUrl: generatedFile.url
      })

      return updatedReport as DDReport
    } catch (error) {
      this.logger.error('Failed to generate report', error)
      
      // Update report status to failed
      if (reportConfig.name) {
        await this.prisma.dDReport.updateMany({
          where: {
            name: reportConfig.name,
            createdBy: userId,
            status: 'GENERATING'
          },
          data: { status: 'FAILED' }
        })
      }
      
      throw error
    }
  }

  /**
   * Generate report from template
   */
  async generateFromTemplate(
    templateId: string,
    parameters: DDReportParameters,
    userId: string,
    tenantId: string
  ): Promise<DDReport> {
    try {
      // Get template
      const template = await this.getReportTemplate(templateId, tenantId)
      if (!template) {
        throw new Error('Report template not found')
      }

      // Merge parameters with template defaults
      const mergedParameters = { ...template.defaultParameters, ...parameters }
      const mergedFilters = [...template.defaultFilters, ...(parameters as any).filters || []]

      // Create report configuration
      const reportConfig: Omit<DDReport, 'id' | 'status' | 'createdAt' | 'updatedAt'> = {
        name: `${template.name} - ${new Date().toLocaleDateString()}`,
        description: template.description,
        type: template.type,
        format: template.format,
        templateId,
        parameters: mergedParameters,
        filters: mergedFilters,
        isScheduled: false,
        visibility: 'PRIVATE',
        allowedUsers: [userId],
        createdBy: userId,
        tenantId
      }

      return this.generateReport(reportConfig, userId)
    } catch (error) {
      this.logger.error('Failed to generate report from template', error)
      throw error
    }
  }

  /**
   * Schedule report
   */
  async scheduleReport(
    reportConfig: Omit<DDReport, 'id' | 'status' | 'createdAt' | 'updatedAt'>,
    schedule: DDReportSchedule,
    userId: string
  ): Promise<DDReport> {
    try {
      this.logger.info('Scheduling report', {
        name: reportConfig.name,
        frequency: schedule.frequency,
        userId
      })

      const scheduledReportConfig = {
        ...reportConfig,
        isScheduled: true,
        schedule
      }

      // Create scheduled report record
      const report = await this.prisma.dDReport.create({
        data: {
          name: scheduledReportConfig.name,
          description: scheduledReportConfig.description,
          type: scheduledReportConfig.type,
          format: scheduledReportConfig.format,
          templateId: scheduledReportConfig.templateId,
          parameters: scheduledReportConfig.parameters,
          filters: scheduledReportConfig.filters,
          isScheduled: true,
          schedule: schedule,
          status: 'PENDING',
          visibility: scheduledReportConfig.visibility,
          allowedUsers: scheduledReportConfig.allowedUsers,
          createdBy: userId,
          tenantId: scheduledReportConfig.tenantId
        }
      })

      this.logger.info('Report scheduled successfully', { reportId: report.id })

      return report as DDReport
    } catch (error) {
      this.logger.error('Failed to schedule report', error)
      throw error
    }
  }

  /**
   * Process scheduled reports
   */
  async processScheduledReports(): Promise<void> {
    try {
      this.logger.info('Processing scheduled reports')

      const scheduledReports = await this.prisma.dDReport.findMany({
        where: {
          isScheduled: true,
          status: 'PENDING'
        }
      })

      for (const report of scheduledReports) {
        try {
          if (this.shouldGenerateReport(report as DDReport)) {
            await this.generateScheduledReport(report as DDReport)
          }
        } catch (error) {
          this.logger.error('Failed to process scheduled report', error, {
            reportId: report.id
          })
        }
      }
    } catch (error) {
      this.logger.error('Failed to process scheduled reports', error)
    }
  }

  /**
   * Get user reports
   */
  async getUserReports(
    userId: string,
    tenantId: string,
    options: {
      includeScheduled?: boolean
      limit?: number
      offset?: number
    } = {}
  ): Promise<{ reports: DDReport[]; total: number }> {
    try {
      const where: any = {
        tenantId,
        OR: [
          { createdBy: userId },
          { allowedUsers: { has: userId } },
          { visibility: 'PUBLIC' }
        ]
      }

      if (!options.includeScheduled) {
        where.isScheduled = false
      }

      const [reports, total] = await Promise.all([
        this.prisma.dDReport.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          take: options.limit || 50,
          skip: options.offset || 0
        }),
        this.prisma.dDReport.count({ where })
      ])

      return {
        reports: reports as DDReport[],
        total
      }
    } catch (error) {
      this.logger.error('Failed to get user reports', error)
      throw error
    }
  }

  /**
   * Get report templates
   */
  async getReportTemplates(tenantId: string): Promise<DDReportTemplate[]> {
    try {
      const templates = await this.prisma.dDReportTemplate.findMany({
        where: {
          OR: [
            { tenantId },
            { isPublic: true }
          ]
        },
        orderBy: { name: 'asc' }
      })

      return templates as DDReportTemplate[]
    } catch (error) {
      this.logger.error('Failed to get report templates', error)
      throw error
    }
  }

  /**
   * Create report template
   */
  async createReportTemplate(
    template: Omit<DDReportTemplate, 'id' | 'createdAt' | 'updatedAt'>,
    userId: string
  ): Promise<DDReportTemplate> {
    try {
      const createdTemplate = await this.prisma.dDReportTemplate.create({
        data: {
          name: template.name,
          description: template.description,
          type: template.type,
          format: template.format,
          sections: template.sections,
          styling: template.styling,
          defaultParameters: template.defaultParameters,
          defaultFilters: template.defaultFilters,
          isPublic: template.isPublic,
          createdBy: userId,
          tenantId: template.tenantId
        }
      })

      return createdTemplate as DDReportTemplate
    } catch (error) {
      this.logger.error('Failed to create report template', error)
      throw error
    }
  }

  /**
   * Export report data
   */
  async exportReportData(
    reportId: string,
    format: DDReportFormat,
    userId: string
  ): Promise<{ url: string; filename: string }> {
    try {
      this.logger.info('Exporting report data', { reportId, format, userId })

      // Get report
      const report = await this.getReport(reportId, userId)
      if (!report) {
        throw new Error('Report not found')
      }

      // Collect fresh data
      const reportData = await this.collectReportData(report)

      // Generate export file
      const exportFile = await this.generateExportFile(reportData, format, report.name)

      this.logger.info('Report data exported successfully', {
        reportId,
        format,
        filename: exportFile.filename
      })

      return exportFile
    } catch (error) {
      this.logger.error('Failed to export report data', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async getReport(reportId: string, userId: string): Promise<DDReport | null> {
    const report = await this.prisma.dDReport.findFirst({
      where: {
        id: reportId,
        OR: [
          { createdBy: userId },
          { allowedUsers: { has: userId } },
          { visibility: 'PUBLIC' }
        ]
      }
    })

    return report as DDReport | null
  }

  private async getReportTemplate(templateId: string, tenantId: string): Promise<DDReportTemplate | null> {
    const template = await this.prisma.dDReportTemplate.findFirst({
      where: {
        id: templateId,
        OR: [
          { tenantId },
          { isPublic: true }
        ]
      }
    })

    return template as DDReportTemplate | null
  }

  private async collectReportData(report: DDReport): Promise<any> {
    // Mock implementation - would collect actual data based on report parameters
    this.logger.info('Collecting report data', { reportId: report.id })

    const mockData = {
      summary: {
        totalItems: 156,
        completedItems: 89,
        progressPercentage: 57,
        totalHours: 342,
        efficiency: 1.4
      },
      categories: [
        { name: 'Financial Analysis', progress: 71, items: 45 },
        { name: 'Legal Review', progress: 47, items: 38 },
        { name: 'Technical Assessment', progress: 60, items: 42 }
      ],
      team: [
        { name: 'John Doe', completed: 22, assigned: 28, efficiency: 1.2 },
        { name: 'Jane Smith', completed: 24, assigned: 35, efficiency: 0.9 }
      ]
    }

    return mockData
  }

  private async generateReportFile(report: DDReport, data: any): Promise<{ url: string; size: number }> {
    // Mock implementation - would generate actual file
    this.logger.info('Generating report file', {
      reportId: report.id,
      format: report.format
    })

    const mockFile = {
      url: `/reports/${report.id}.${report.format.toLowerCase()}`,
      size: 1024 * 1024 // 1MB
    }

    return mockFile
  }

  private async generateExportFile(
    data: any,
    format: DDReportFormat,
    filename: string
  ): Promise<{ url: string; filename: string }> {
    // Mock implementation - would generate actual export file
    const exportFilename = `${filename}_export_${Date.now()}.${format.toLowerCase()}`
    
    return {
      url: `/exports/${exportFilename}`,
      filename: exportFilename
    }
  }

  private shouldGenerateReport(report: DDReport): boolean {
    if (!report.schedule || !report.schedule.isActive) {
      return false
    }

    // Mock implementation - would check if report should be generated based on schedule
    return true
  }

  private async generateScheduledReport(report: DDReport): Promise<void> {
    try {
      // Generate the report
      const reportData = await this.collectReportData(report)
      const generatedFile = await this.generateReportFile(report, reportData)

      // Update report
      await this.prisma.dDReport.update({
        where: { id: report.id },
        data: {
          status: 'COMPLETED',
          generatedAt: new Date(),
          fileUrl: generatedFile.url,
          fileSize: generatedFile.size
        }
      })

      // Send to recipients
      if (report.schedule?.recipients) {
        await this.sendReportToRecipients(report, generatedFile.url)
      }

      this.logger.info('Scheduled report generated successfully', {
        reportId: report.id
      })
    } catch (error) {
      await this.prisma.dDReport.update({
        where: { id: report.id },
        data: { status: 'FAILED' }
      })
      
      throw error
    }
  }

  private async sendReportToRecipients(report: DDReport, fileUrl: string): Promise<void> {
    // Mock implementation - would send report to recipients via email
    this.logger.info('Sending report to recipients', {
      reportId: report.id,
      recipients: report.schedule?.recipients
    })
  }
}
