import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface DDTemplateData {
  name: string
  description?: string
  version: string
  industryType: string
  isPublic: boolean
  isDefault: boolean
  allowCustomization: boolean
  requiresApproval: boolean
  tags: string[]
  categories: DDCategoryData[]
}

export interface DDCategoryData {
  name: string
  description?: string
  categoryType: string
  order: number
  isRequired: boolean
  allowCustomItems: boolean
  estimatedHours: number
  color?: string
  icon?: string
  items: DDItemData[]
}

export interface DDItemData {
  title: string
  description?: string
  priority: string
  isRequired: boolean
  order: number
  estimatedHours: number
  instructions?: string
  examples?: string[]
  references?: string[]
  fields: DDFieldData[]
  dependencies: string[]
  validationRules: any[]
}

export interface DDFieldData {
  name: string
  label: string
  type: string
  isRequired: boolean
  order: number
  placeholder?: string
  helpText?: string
  defaultValue?: any
  options?: DDFieldOptionData[]
  validation?: any
  showWhen?: any
}

export interface DDFieldOptionData {
  label: string
  value: string
  order: number
  isDefault?: boolean
  color?: string
}

export class DDTemplateService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DDTemplateService')
  }

  /**
   * Create a new due diligence template
   */
  async createTemplate(templateData: DDTemplateData, userId: string, tenantId: string): Promise<any> {
    try {
      this.logger.info('Creating DD template', { 
        name: templateData.name, 
        userId, 
        tenantId 
      })

      // Validate template data
      await this.validateTemplateData(templateData)

      // Create template with categories and items
      const template = await this.prisma.$transaction(async (tx) => {
        // Create main template
        const createdTemplate = await tx.dDTemplate.create({
          data: {
            name: templateData.name,
            description: templateData.description,
            version: templateData.version,
            industryType: templateData.industryType,
            isPublic: templateData.isPublic,
            isDefault: templateData.isDefault,
            allowCustomization: templateData.allowCustomization,
            requiresApproval: templateData.requiresApproval,
            tags: templateData.tags,
            estimatedHours: this.calculateTotalHours(templateData.categories),
            totalItems: this.calculateTotalItems(templateData.categories),
            createdBy: userId,
            tenantId
          }
        })

        // Create categories
        for (const categoryData of templateData.categories) {
          const category = await tx.dDCategory.create({
            data: {
              templateId: createdTemplate.id,
              name: categoryData.name,
              description: categoryData.description,
              categoryType: categoryData.categoryType,
              order: categoryData.order,
              isRequired: categoryData.isRequired,
              allowCustomItems: categoryData.allowCustomItems,
              estimatedHours: categoryData.estimatedHours,
              color: categoryData.color,
              icon: categoryData.icon,
              totalItems: categoryData.items.length
            }
          })

          // Create items for this category
          for (const itemData of categoryData.items) {
            const item = await tx.dDItem.create({
              data: {
                categoryId: category.id,
                title: itemData.title,
                description: itemData.description,
                priority: itemData.priority,
                isRequired: itemData.isRequired,
                order: itemData.order,
                estimatedHours: itemData.estimatedHours,
                instructions: itemData.instructions,
                examples: itemData.examples,
                references: itemData.references,
                dependencies: itemData.dependencies,
                validationRules: itemData.validationRules
              }
            })

            // Create fields for this item
            for (const fieldData of itemData.fields) {
              const field = await tx.dDField.create({
                data: {
                  itemId: item.id,
                  name: fieldData.name,
                  label: fieldData.label,
                  type: fieldData.type,
                  isRequired: fieldData.isRequired,
                  order: fieldData.order,
                  placeholder: fieldData.placeholder,
                  helpText: fieldData.helpText,
                  defaultValue: fieldData.defaultValue,
                  validation: fieldData.validation,
                  showWhen: fieldData.showWhen
                }
              })

              // Create field options if applicable
              if (fieldData.options && fieldData.options.length > 0) {
                for (const optionData of fieldData.options) {
                  await tx.dDFieldOption.create({
                    data: {
                      fieldId: field.id,
                      label: optionData.label,
                      value: optionData.value,
                      order: optionData.order,
                      isDefault: optionData.isDefault,
                      color: optionData.color
                    }
                  })
                }
              }
            }
          }
        }

        return createdTemplate
      })

      // Clear template cache
      await this.clearTemplateCache(tenantId)

      this.logger.info('DD template created successfully', { 
        templateId: template.id,
        name: templateData.name 
      })

      return template
    } catch (error) {
      this.logger.error('Failed to create DD template', error, templateData)
      throw error
    }
  }

  /**
   * Get template by ID with full structure
   */
  async getTemplate(templateId: string, tenantId: string): Promise<any> {
    try {
      const cacheKey = `dd:template:${templateId}`
      
      // Try cache first
      const cached = await this.cache.get(cacheKey)
      if (cached) {
        return cached
      }

      const template = await this.prisma.dDTemplate.findFirst({
        where: {
          id: templateId,
          OR: [
            { tenantId },
            { isPublic: true }
          ]
        },
        include: {
          categories: {
            orderBy: { order: 'asc' },
            include: {
              items: {
                orderBy: { order: 'asc' },
                include: {
                  fields: {
                    orderBy: { order: 'asc' },
                    include: {
                      options: {
                        orderBy: { order: 'asc' }
                      }
                    }
                  }
                }
              }
            }
          },
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      if (!template) {
        throw new Error('Template not found')
      }

      // Cache for 1 hour
      await this.cache.set(cacheKey, template, 3600)

      return template
    } catch (error) {
      this.logger.error('Failed to get DD template', error, { templateId, tenantId })
      throw error
    }
  }

  /**
   * Get all templates with filtering
   */
  async getTemplates(
    tenantId: string,
    filters: {
      industryType?: string
      isPublic?: boolean
      search?: string
      tags?: string[]
    } = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{ templates: any[]; total: number; hasMore: boolean }> {
    try {
      const where: any = {
        OR: [
          { tenantId },
          { isPublic: true }
        ]
      }

      // Apply filters
      if (filters.industryType) {
        where.industryType = filters.industryType
      }

      if (filters.isPublic !== undefined) {
        where.isPublic = filters.isPublic
      }

      if (filters.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } }
        ]
      }

      if (filters.tags && filters.tags.length > 0) {
        where.tags = {
          hasSome: filters.tags
        }
      }

      const [templates, total] = await Promise.all([
        this.prisma.dDTemplate.findMany({
          where,
          skip: (page - 1) * limit,
          take: limit,
          orderBy: [
            { isDefault: 'desc' },
            { usageCount: 'desc' },
            { createdAt: 'desc' }
          ],
          include: {
            creator: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            },
            _count: {
              select: {
                categories: true
              }
            }
          }
        }),
        this.prisma.dDTemplate.count({ where })
      ])

      const hasMore = page * limit < total

      return { templates, total, hasMore }
    } catch (error) {
      this.logger.error('Failed to get DD templates', error, { tenantId, filters })
      throw error
    }
  }

  /**
   * Update template
   */
  async updateTemplate(
    templateId: string,
    updates: Partial<DDTemplateData>,
    userId: string,
    tenantId: string
  ): Promise<any> {
    try {
      this.logger.info('Updating DD template', { templateId, userId })

      // Check if user can update this template
      const template = await this.prisma.dDTemplate.findFirst({
        where: {
          id: templateId,
          tenantId,
          createdBy: userId
        }
      })

      if (!template) {
        throw new Error('Template not found or access denied')
      }

      // Update template
      const updatedTemplate = await this.prisma.dDTemplate.update({
        where: { id: templateId },
        data: {
          ...updates,
          version: this.incrementVersion(template.version),
          updatedAt: new Date()
        }
      })

      // Clear cache
      await this.clearTemplateCache(tenantId)
      await this.cache.delete(`dd:template:${templateId}`)

      this.logger.info('DD template updated successfully', { templateId })

      return updatedTemplate
    } catch (error) {
      this.logger.error('Failed to update DD template', error, { templateId, updates })
      throw error
    }
  }

  /**
   * Clone template
   */
  async cloneTemplate(
    templateId: string,
    newName: string,
    userId: string,
    tenantId: string,
    customizations?: any
  ): Promise<any> {
    try {
      this.logger.info('Cloning DD template', { templateId, newName, userId })

      // Get original template
      const originalTemplate = await this.getTemplate(templateId, tenantId)

      if (!originalTemplate) {
        throw new Error('Template not found')
      }

      // Create cloned template data
      const clonedTemplateData: DDTemplateData = {
        name: newName,
        description: `Cloned from ${originalTemplate.name}`,
        version: '1.0.0',
        industryType: originalTemplate.industryType,
        isPublic: false,
        isDefault: false,
        allowCustomization: originalTemplate.allowCustomization,
        requiresApproval: originalTemplate.requiresApproval,
        tags: [...originalTemplate.tags, 'cloned'],
        categories: originalTemplate.categories.map((category: any) => ({
          name: category.name,
          description: category.description,
          categoryType: category.categoryType,
          order: category.order,
          isRequired: category.isRequired,
          allowCustomItems: category.allowCustomItems,
          estimatedHours: category.estimatedHours,
          color: category.color,
          icon: category.icon,
          items: category.items.map((item: any) => ({
            title: item.title,
            description: item.description,
            priority: item.priority,
            isRequired: item.isRequired,
            order: item.order,
            estimatedHours: item.estimatedHours,
            instructions: item.instructions,
            examples: item.examples,
            references: item.references,
            fields: item.fields.map((field: any) => ({
              name: field.name,
              label: field.label,
              type: field.type,
              isRequired: field.isRequired,
              order: field.order,
              placeholder: field.placeholder,
              helpText: field.helpText,
              defaultValue: field.defaultValue,
              validation: field.validation,
              showWhen: field.showWhen,
              options: field.options?.map((option: any) => ({
                label: option.label,
                value: option.value,
                order: option.order,
                isDefault: option.isDefault,
                color: option.color
              })) || []
            })),
            dependencies: item.dependencies,
            validationRules: item.validationRules
          }))
        }))
      }

      // Apply customizations if provided
      if (customizations) {
        this.applyCustomizations(clonedTemplateData, customizations)
      }

      // Create the cloned template
      const clonedTemplate = await this.createTemplate(clonedTemplateData, userId, tenantId)

      this.logger.info('DD template cloned successfully', { 
        originalTemplateId: templateId,
        clonedTemplateId: clonedTemplate.id 
      })

      return clonedTemplate
    } catch (error) {
      this.logger.error('Failed to clone DD template', error, { templateId, newName })
      throw error
    }
  }

  /**
   * Delete template
   */
  async deleteTemplate(templateId: string, userId: string, tenantId: string): Promise<void> {
    try {
      this.logger.info('Deleting DD template', { templateId, userId })

      // Check if user can delete this template
      const template = await this.prisma.dDTemplate.findFirst({
        where: {
          id: templateId,
          tenantId,
          createdBy: userId
        }
      })

      if (!template) {
        throw new Error('Template not found or access denied')
      }

      // Check if template is being used
      const checklistCount = await this.prisma.dDChecklist.count({
        where: { templateId }
      })

      if (checklistCount > 0) {
        throw new Error('Cannot delete template that is being used by checklists')
      }

      // Delete template and all related data
      await this.prisma.$transaction(async (tx) => {
        // Delete field options
        await tx.dDFieldOption.deleteMany({
          where: {
            field: {
              item: {
                category: {
                  templateId
                }
              }
            }
          }
        })

        // Delete fields
        await tx.dDField.deleteMany({
          where: {
            item: {
              category: {
                templateId
              }
            }
          }
        })

        // Delete items
        await tx.dDItem.deleteMany({
          where: {
            category: {
              templateId
            }
          }
        })

        // Delete categories
        await tx.dDCategory.deleteMany({
          where: { templateId }
        })

        // Delete template
        await tx.dDTemplate.delete({
          where: { id: templateId }
        })
      })

      // Clear cache
      await this.clearTemplateCache(tenantId)
      await this.cache.delete(`dd:template:${templateId}`)

      this.logger.info('DD template deleted successfully', { templateId })
    } catch (error) {
      this.logger.error('Failed to delete DD template', error, { templateId })
      throw error
    }
  }

  /**
   * Get default templates for industry
   */
  async getDefaultTemplates(industryType: string): Promise<any[]> {
    try {
      const templates = await this.prisma.dDTemplate.findMany({
        where: {
          industryType,
          isDefault: true,
          isPublic: true
        },
        include: {
          _count: {
            select: {
              categories: true
            }
          }
        },
        orderBy: { usageCount: 'desc' }
      })

      return templates
    } catch (error) {
      this.logger.error('Failed to get default templates', error, { industryType })
      throw error
    }
  }

  /**
   * Validate template data
   */
  private async validateTemplateData(templateData: DDTemplateData): Promise<void> {
    if (!templateData.name || templateData.name.trim().length === 0) {
      throw new Error('Template name is required')
    }

    if (!templateData.categories || templateData.categories.length === 0) {
      throw new Error('Template must have at least one category')
    }

    // Validate categories
    for (const category of templateData.categories) {
      if (!category.name || category.name.trim().length === 0) {
        throw new Error('Category name is required')
      }

      if (!category.items || category.items.length === 0) {
        throw new Error('Category must have at least one item')
      }

      // Validate items
      for (const item of category.items) {
        if (!item.title || item.title.trim().length === 0) {
          throw new Error('Item title is required')
        }
      }
    }
  }

  /**
   * Calculate total hours for template
   */
  private calculateTotalHours(categories: DDCategoryData[]): number {
    return categories.reduce((total, category) => {
      return total + category.items.reduce((catTotal, item) => catTotal + item.estimatedHours, 0)
    }, 0)
  }

  /**
   * Calculate total items for template
   */
  private calculateTotalItems(categories: DDCategoryData[]): number {
    return categories.reduce((total, category) => total + category.items.length, 0)
  }

  /**
   * Increment version number
   */
  private incrementVersion(currentVersion: string): string {
    const parts = currentVersion.split('.')
    const patch = parseInt(parts[2] || '0') + 1
    return `${parts[0]}.${parts[1]}.${patch}`
  }

  /**
   * Apply customizations to template data
   */
  private applyCustomizations(templateData: DDTemplateData, customizations: any): void {
    // Implementation for applying customizations
    // This would modify the templateData based on customizations
  }

  /**
   * Clear template cache
   */
  private async clearTemplateCache(tenantId: string): Promise<void> {
    try {
      await this.cache.deletePattern(`dd:templates:${tenantId}:*`)
    } catch (error) {
      this.logger.error('Failed to clear template cache', error)
    }
  }
}
