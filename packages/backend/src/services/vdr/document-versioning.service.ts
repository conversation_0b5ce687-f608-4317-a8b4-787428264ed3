import { PrismaClient, DocumentStatus } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { VDRFileStorageService } from './file-storage.service'
import { VDRAccessControlService } from './access-control.service'

export interface DocumentVersion {
  id: string
  version: number
  name: string
  originalName: string
  size: number
  mimeType: string
  checksum: string
  uploadedBy: string
  uploadedAt: Date
  changeLog?: string
  isLatest: boolean
}

export interface VersionComparisonResult {
  documentId: string
  fromVersion: number
  toVersion: number
  changes: {
    sizeChange: number
    checksumChanged: boolean
    nameChanged: boolean
    uploadedByDifferent: boolean
  }
  metadata: {
    fromDocument: DocumentVersion
    toDocument: DocumentVersion
  }
}

export interface CreateVersionOptions {
  documentId: string
  buffer: Buffer
  originalName: string
  mimeType: string
  userId: string
  tenantId: string
  changeLog?: string
  replaceLatest?: boolean
}

export class VDRDocumentVersioningService {
  private prisma: PrismaClient
  private cache: CacheService
  private fileStorage: VDRFileStorageService
  private accessControl: VDRAccessControlService
  private logger: Logger

  constructor(
    prisma: PrismaClient,
    cache: CacheService,
    fileStorage: VDRFileStorageService,
    accessControl: VDRAccessControlService
  ) {
    this.prisma = prisma
    this.cache = cache
    this.fileStorage = fileStorage
    this.accessControl = accessControl
    this.logger = new Logger('VDRDocumentVersioningService')
  }

  /**
   * Create a new version of a document
   */
  async createVersion(options: CreateVersionOptions): Promise<any> {
    try {
      this.logger.info('Creating new document version', {
        documentId: options.documentId,
        userId: options.userId
      })

      // Get the current document
      const currentDocument = await this.prisma.vDRDocument.findUnique({
        where: { id: options.documentId },
        include: { vdr: true }
      })

      if (!currentDocument) {
        throw new Error('Document not found')
      }

      // Check permissions
      const canUpload = await this.accessControl.checkPermission({
        vdrId: currentDocument.vdrId,
        userId: options.userId,
        action: 'upload'
      })

      if (!canUpload) {
        throw new Error('Insufficient permissions to create document version')
      }

      // Calculate new version number
      const latestVersion = await this.getLatestVersionNumber(options.documentId)
      const newVersionNumber = latestVersion + 1

      // Upload new file version
      const uploadResult = await this.fileStorage.uploadFile({
        vdrId: currentDocument.vdrId,
        folderId: currentDocument.folderId || undefined,
        originalName: options.originalName,
        mimeType: options.mimeType,
        size: options.buffer.length,
        buffer: options.buffer,
        userId: options.userId,
        tenantId: options.tenantId,
        encrypt: currentDocument.isEncrypted,
        tags: currentDocument.tags,
        description: `Version ${newVersionNumber} of ${currentDocument.name}`
      })

      // Create version record
      const newVersion = await this.prisma.vDRDocument.update({
        where: { id: uploadResult.id },
        data: {
          version: newVersionNumber,
          parentId: options.documentId,
          name: options.replaceLatest ? options.originalName : `${currentDocument.name} (v${newVersionNumber})`
        }
      })

      // If replacing latest, update the parent document
      if (options.replaceLatest) {
        await this.prisma.vDRDocument.update({
          where: { id: options.documentId },
          data: {
            status: 'ARCHIVED',
            updatedAt: new Date()
          }
        })

        // Update the new version to be the main document
        await this.prisma.vDRDocument.update({
          where: { id: newVersion.id },
          data: {
            parentId: null,
            name: currentDocument.name
          }
        })

        // Update all child versions to point to the new main document
        await this.prisma.vDRDocument.updateMany({
          where: { parentId: options.documentId },
          data: { parentId: newVersion.id }
        })
      }

      // Log version creation
      await this.logVersionActivity(
        currentDocument.vdrId,
        options.userId,
        'VERSION_CREATED',
        {
          documentId: options.documentId,
          newVersionId: newVersion.id,
          versionNumber: newVersionNumber,
          changeLog: options.changeLog,
          replaceLatest: options.replaceLatest
        }
      )

      // Clear cache
      await this.clearDocumentCache(options.documentId)

      this.logger.info('Document version created successfully', {
        documentId: options.documentId,
        newVersionId: newVersion.id,
        versionNumber: newVersionNumber
      })

      return newVersion
    } catch (error) {
      this.logger.error('Failed to create document version', error, options)
      throw error
    }
  }

  /**
   * Get all versions of a document
   */
  async getDocumentVersions(documentId: string, userId: string, tenantId: string): Promise<DocumentVersion[]> {
    try {
      const cacheKey = `vdr:versions:${documentId}:${userId}`
      
      // Try cache first
      const cached = await this.cache.get<DocumentVersion[]>(cacheKey)
      if (cached) {
        return cached
      }

      // Get the main document
      const mainDocument = await this.prisma.vDRDocument.findFirst({
        where: {
          id: documentId,
          tenantId,
          status: { not: 'DELETED' }
        },
        include: { vdr: true }
      })

      if (!mainDocument) {
        throw new Error('Document not found')
      }

      // Check permissions
      const canView = await this.accessControl.checkPermission({
        vdrId: mainDocument.vdrId,
        userId,
        action: 'view'
      })

      if (!canView) {
        throw new Error('Access denied')
      }

      // Get all versions (including the main document and its children)
      const allVersions = await this.prisma.vDRDocument.findMany({
        where: {
          OR: [
            { id: documentId },
            { parentId: documentId }
          ],
          status: { not: 'DELETED' }
        },
        include: {
          uploader: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        },
        orderBy: { version: 'desc' }
      })

      // Transform to DocumentVersion format
      const versions: DocumentVersion[] = allVersions.map(doc => ({
        id: doc.id,
        version: doc.version,
        name: doc.name,
        originalName: doc.originalName,
        size: doc.size,
        mimeType: doc.mimeType,
        checksum: doc.checksum || '',
        uploadedBy: doc.uploadedBy,
        uploadedAt: doc.createdAt,
        isLatest: doc.version === Math.max(...allVersions.map(v => v.version))
      }))

      // Cache for 5 minutes
      await this.cache.set(cacheKey, versions, 300)

      return versions
    } catch (error) {
      this.logger.error('Failed to get document versions', error, { documentId, userId })
      throw error
    }
  }

  /**
   * Get specific version of a document
   */
  async getDocumentVersion(documentId: string, version: number, userId: string, tenantId: string): Promise<any> {
    try {
      // Find the specific version
      const versionDocument = await this.prisma.vDRDocument.findFirst({
        where: {
          OR: [
            { id: documentId, version },
            { parentId: documentId, version }
          ],
          tenantId,
          status: { not: 'DELETED' }
        },
        include: {
          vdr: true,
          uploader: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      if (!versionDocument) {
        throw new Error('Document version not found')
      }

      // Check permissions
      const canView = await this.accessControl.checkPermission({
        vdrId: versionDocument.vdrId,
        userId,
        action: 'view'
      })

      if (!canView) {
        throw new Error('Access denied')
      }

      return versionDocument
    } catch (error) {
      this.logger.error('Failed to get document version', error, { documentId, version, userId })
      throw error
    }
  }

  /**
   * Compare two versions of a document
   */
  async compareVersions(
    documentId: string,
    fromVersion: number,
    toVersion: number,
    userId: string,
    tenantId: string
  ): Promise<VersionComparisonResult> {
    try {
      this.logger.info('Comparing document versions', {
        documentId,
        fromVersion,
        toVersion,
        userId
      })

      // Get both versions
      const [fromDoc, toDoc] = await Promise.all([
        this.getDocumentVersion(documentId, fromVersion, userId, tenantId),
        this.getDocumentVersion(documentId, toVersion, userId, tenantId)
      ])

      if (!fromDoc || !toDoc) {
        throw new Error('One or both document versions not found')
      }

      // Calculate changes
      const changes = {
        sizeChange: toDoc.size - fromDoc.size,
        checksumChanged: fromDoc.checksum !== toDoc.checksum,
        nameChanged: fromDoc.originalName !== toDoc.originalName,
        uploadedByDifferent: fromDoc.uploadedBy !== toDoc.uploadedBy
      }

      const comparison: VersionComparisonResult = {
        documentId,
        fromVersion,
        toVersion,
        changes,
        metadata: {
          fromDocument: {
            id: fromDoc.id,
            version: fromDoc.version,
            name: fromDoc.name,
            originalName: fromDoc.originalName,
            size: fromDoc.size,
            mimeType: fromDoc.mimeType,
            checksum: fromDoc.checksum || '',
            uploadedBy: fromDoc.uploadedBy,
            uploadedAt: fromDoc.createdAt,
            isLatest: false
          },
          toDocument: {
            id: toDoc.id,
            version: toDoc.version,
            name: toDoc.name,
            originalName: toDoc.originalName,
            size: toDoc.size,
            mimeType: toDoc.mimeType,
            checksum: toDoc.checksum || '',
            uploadedBy: toDoc.uploadedBy,
            uploadedAt: toDoc.createdAt,
            isLatest: false
          }
        }
      }

      return comparison
    } catch (error) {
      this.logger.error('Failed to compare document versions', error, {
        documentId,
        fromVersion,
        toVersion,
        userId
      })
      throw error
    }
  }

  /**
   * Restore a specific version as the latest
   */
  async restoreVersion(
    documentId: string,
    version: number,
    userId: string,
    tenantId: string,
    changeLog?: string
  ): Promise<any> {
    try {
      this.logger.info('Restoring document version', {
        documentId,
        version,
        userId
      })

      // Get the version to restore
      const versionToRestore = await this.getDocumentVersion(documentId, version, userId, tenantId)

      if (!versionToRestore) {
        throw new Error('Version not found')
      }

      // Check permissions
      const canUpload = await this.accessControl.checkPermission({
        vdrId: versionToRestore.vdrId,
        userId,
        action: 'upload'
      })

      if (!canUpload) {
        throw new Error('Insufficient permissions to restore version')
      }

      // Download the version file
      const { buffer } = await this.fileStorage.downloadFile({
        documentId: versionToRestore.id,
        userId,
        tenantId,
        trackAccess: false
      })

      // Create new version from the restored content
      const restoredVersion = await this.createVersion({
        documentId,
        buffer,
        originalName: versionToRestore.originalName,
        mimeType: versionToRestore.mimeType,
        userId,
        tenantId,
        changeLog: changeLog || `Restored from version ${version}`,
        replaceLatest: true
      })

      // Log restoration
      await this.logVersionActivity(
        versionToRestore.vdrId,
        userId,
        'VERSION_RESTORED',
        {
          documentId,
          restoredFromVersion: version,
          newVersionId: restoredVersion.id,
          changeLog
        }
      )

      this.logger.info('Document version restored successfully', {
        documentId,
        restoredFromVersion: version,
        newVersionId: restoredVersion.id
      })

      return restoredVersion
    } catch (error) {
      this.logger.error('Failed to restore document version', error, {
        documentId,
        version,
        userId
      })
      throw error
    }
  }

  /**
   * Delete a specific version
   */
  async deleteVersion(
    documentId: string,
    version: number,
    userId: string,
    tenantId: string
  ): Promise<void> {
    try {
      this.logger.info('Deleting document version', {
        documentId,
        version,
        userId
      })

      // Get the version to delete
      const versionToDelete = await this.getDocumentVersion(documentId, version, userId, tenantId)

      if (!versionToDelete) {
        throw new Error('Version not found')
      }

      // Check permissions
      const canDelete = await this.accessControl.checkPermission({
        vdrId: versionToDelete.vdrId,
        userId,
        action: 'delete'
      })

      if (!canDelete) {
        throw new Error('Insufficient permissions to delete version')
      }

      // Don't allow deleting the latest version if it's the only version
      const allVersions = await this.getDocumentVersions(documentId, userId, tenantId)
      if (allVersions.length === 1) {
        throw new Error('Cannot delete the only version of a document')
      }

      // Soft delete the version
      await this.prisma.vDRDocument.update({
        where: { id: versionToDelete.id },
        data: {
          status: 'DELETED',
          updatedAt: new Date()
        }
      })

      // Log deletion
      await this.logVersionActivity(
        versionToDelete.vdrId,
        userId,
        'VERSION_DELETED',
        {
          documentId,
          deletedVersion: version,
          versionId: versionToDelete.id
        }
      )

      // Clear cache
      await this.clearDocumentCache(documentId)

      this.logger.info('Document version deleted successfully', {
        documentId,
        version,
        versionId: versionToDelete.id
      })
    } catch (error) {
      this.logger.error('Failed to delete document version', error, {
        documentId,
        version,
        userId
      })
      throw error
    }
  }

  /**
   * Get latest version number for a document
   */
  private async getLatestVersionNumber(documentId: string): Promise<number> {
    const latestVersion = await this.prisma.vDRDocument.findFirst({
      where: {
        OR: [
          { id: documentId },
          { parentId: documentId }
        ],
        status: { not: 'DELETED' }
      },
      orderBy: { version: 'desc' },
      select: { version: true }
    })

    return latestVersion?.version || 0
  }

  /**
   * Log version-related activity
   */
  private async logVersionActivity(
    vdrId: string,
    userId: string,
    action: string,
    details: any
  ): Promise<void> {
    try {
      // Get VDR user ID
      const vdrUser = await this.prisma.vDRUser.findFirst({
        where: {
          vdrId,
          OR: [
            { userId },
            { email: await this.getUserEmail(userId) }
          ]
        }
      })

      if (vdrUser) {
        await this.prisma.vDRActivity.create({
          data: {
            vdrId,
            userId: vdrUser.id,
            action: action as any,
            details,
            documentId: details.documentId
          }
        })
      }
    } catch (error) {
      this.logger.error('Failed to log version activity', error)
    }
  }

  /**
   * Get user email
   */
  private async getUserEmail(userId: string): Promise<string> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { email: true }
    })
    return user?.email || ''
  }

  /**
   * Clear document cache
   */
  private async clearDocumentCache(documentId: string): Promise<void> {
    try {
      await this.cache.deletePattern(`vdr:versions:${documentId}:*`)
      await this.cache.deletePattern(`vdr:document:${documentId}:*`)
    } catch (error) {
      this.logger.error('Failed to clear document cache', error)
    }
  }
}
