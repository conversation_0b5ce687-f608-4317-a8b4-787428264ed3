import { PrismaClient, VDR<PERSON><PERSON>, VDRAccessLevel, VDRUserStatus } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import * as crypto from 'crypto'

export interface VDRUserInvitation {
  vdrId: string
  email: string
  firstName?: string
  lastName?: string
  company?: string
  title?: string
  role: VDRRole
  accessLevel: VDRAccessLevel
  canDownload?: boolean
  canPrint?: boolean
  canComment?: boolean
  expiresAt?: Date
  invitedBy: string
  message?: string
}

export interface VDRPermissionCheck {
  vdrId: string
  userId: string
  action: 'view' | 'download' | 'upload' | 'delete' | 'share' | 'comment' | 'admin'
  resourceId?: string
  resourceType?: 'document' | 'folder'
}

export interface VDRAccessToken {
  token: string
  vdrId: string
  userId: string
  expiresAt: Date
  permissions: string[]
}

export class VDRAccessControlService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('VDRAccessControlService')
  }

  /**
   * Invite user to VDR
   */
  async inviteUser(invitation: VDRUserInvitation): Promise<any> {
    try {
      this.logger.info('Inviting user to VDR', { 
        vdrId: invitation.vdrId, 
        email: invitation.email,
        role: invitation.role 
      })

      // Check if VDR exists and inviter has permission
      const vdr = await this.prisma.virtualDataRoom.findUnique({
        where: { id: invitation.vdrId },
        include: {
          users: {
            where: {
              OR: [
                { userId: invitation.invitedBy },
                { email: await this.getUserEmail(invitation.invitedBy) }
              ]
            }
          }
        }
      })

      if (!vdr) {
        throw new Error('VDR not found')
      }

      // Check if inviter has admin/manager permissions
      const inviterUser = vdr.users.find(u => 
        u.userId === invitation.invitedBy || 
        u.email === await this.getUserEmail(invitation.invitedBy)
      )

      if (!inviterUser || !['ADMIN', 'MANAGER'].includes(inviterUser.role)) {
        throw new Error('Insufficient permissions to invite users')
      }

      // Check if user is already invited
      const existingUser = await this.prisma.vDRUser.findUnique({
        where: {
          vdrId_email: {
            vdrId: invitation.vdrId,
            email: invitation.email
          }
        }
      })

      if (existingUser) {
        if (existingUser.status === 'ACTIVE') {
          throw new Error('User is already active in this VDR')
        }
        
        // Update existing invitation
        const updatedUser = await this.prisma.vDRUser.update({
          where: { id: existingUser.id },
          data: {
            firstName: invitation.firstName,
            lastName: invitation.lastName,
            company: invitation.company,
            title: invitation.title,
            role: invitation.role,
            accessLevel: invitation.accessLevel,
            canDownload: invitation.canDownload || false,
            canPrint: invitation.canPrint || false,
            canComment: invitation.canComment || false,
            expiresAt: invitation.expiresAt,
            invitedBy: invitation.invitedBy,
            invitedAt: new Date(),
            status: 'PENDING'
          }
        })

        await this.sendInvitationEmail(updatedUser, invitation.message)
        return updatedUser
      }

      // Create new invitation
      const vdrUser = await this.prisma.vDRUser.create({
        data: {
          vdrId: invitation.vdrId,
          email: invitation.email,
          firstName: invitation.firstName,
          lastName: invitation.lastName,
          company: invitation.company,
          title: invitation.title,
          role: invitation.role,
          accessLevel: invitation.accessLevel,
          canDownload: invitation.canDownload || false,
          canPrint: invitation.canPrint || false,
          canComment: invitation.canComment || false,
          expiresAt: invitation.expiresAt,
          invitedBy: invitation.invitedBy,
          invitedAt: new Date(),
          status: 'PENDING'
        }
      })

      // Send invitation email
      await this.sendInvitationEmail(vdrUser, invitation.message)

      // Log activity
      await this.logActivity(invitation.vdrId, invitation.invitedBy, 'USER_INVITED', {
        invitedEmail: invitation.email,
        role: invitation.role
      })

      this.logger.info('User invited successfully', { 
        vdrUserId: vdrUser.id,
        email: invitation.email 
      })

      return vdrUser
    } catch (error) {
      this.logger.error('Failed to invite user', error, invitation)
      throw error
    }
  }

  /**
   * Accept VDR invitation
   */
  async acceptInvitation(token: string, userId?: string): Promise<any> {
    try {
      this.logger.info('Accepting VDR invitation', { token: token.substring(0, 8) + '...' })

      // Decode invitation token
      const invitation = await this.decodeInvitationToken(token)
      
      if (!invitation) {
        throw new Error('Invalid or expired invitation token')
      }

      // Get VDR user
      const vdrUser = await this.prisma.vDRUser.findUnique({
        where: { id: invitation.vdrUserId },
        include: { vdr: true }
      })

      if (!vdrUser || vdrUser.status !== 'PENDING') {
        throw new Error('Invitation not found or already processed')
      }

      // Check expiration
      if (vdrUser.expiresAt && vdrUser.expiresAt < new Date()) {
        await this.prisma.vDRUser.update({
          where: { id: vdrUser.id },
          data: { status: 'EXPIRED' }
        })
        throw new Error('Invitation has expired')
      }

      // Update user status
      const updatedUser = await this.prisma.vDRUser.update({
        where: { id: vdrUser.id },
        data: {
          userId,
          status: 'ACTIVE',
          acceptedAt: new Date(),
          lastLoginAt: new Date()
        }
      })

      // Log activity
      await this.logActivity(vdrUser.vdrId, vdrUser.id, 'USER_ACCEPTED', {
        email: vdrUser.email
      })

      this.logger.info('Invitation accepted successfully', { 
        vdrUserId: vdrUser.id,
        email: vdrUser.email 
      })

      return updatedUser
    } catch (error) {
      this.logger.error('Failed to accept invitation', error, { token: token.substring(0, 8) + '...' })
      throw error
    }
  }

  /**
   * Check VDR permissions
   */
  async checkPermission(check: VDRPermissionCheck): Promise<boolean> {
    try {
      const cacheKey = `vdr:permission:${check.vdrId}:${check.userId}:${check.action}:${check.resourceId || 'global'}`
      
      // Try cache first
      const cached = await this.cache.get<boolean>(cacheKey)
      if (cached !== null) {
        return cached
      }

      // Get VDR user
      const vdrUser = await this.prisma.vDRUser.findFirst({
        where: {
          vdrId: check.vdrId,
          OR: [
            { userId: check.userId },
            { email: await this.getUserEmail(check.userId) }
          ],
          status: 'ACTIVE'
        }
      })

      if (!vdrUser) {
        await this.cache.set(cacheKey, false, 300) // Cache for 5 minutes
        return false
      }

      // Check session expiration
      if (vdrUser.expiresAt && vdrUser.expiresAt < new Date()) {
        await this.prisma.vDRUser.update({
          where: { id: vdrUser.id },
          data: { status: 'EXPIRED' }
        })
        await this.cache.set(cacheKey, false, 300)
        return false
      }

      let hasPermission = false

      // Check global permissions based on role
      switch (check.action) {
        case 'view':
          hasPermission = true // All active users can view
          break
        case 'download':
          hasPermission = vdrUser.canDownload
          break
        case 'upload':
          hasPermission = ['ADMIN', 'MANAGER', 'CONTRIBUTOR'].includes(vdrUser.role)
          break
        case 'delete':
          hasPermission = ['ADMIN', 'MANAGER'].includes(vdrUser.role)
          break
        case 'share':
          hasPermission = ['ADMIN', 'MANAGER'].includes(vdrUser.role)
          break
        case 'comment':
          hasPermission = vdrUser.canComment
          break
        case 'admin':
          hasPermission = vdrUser.role === 'ADMIN'
          break
        default:
          hasPermission = false
      }

      // Check resource-specific permissions if applicable
      if (hasPermission && check.resourceId && check.resourceType) {
        const resourcePermission = await this.prisma.vDRPermission.findFirst({
          where: {
            vdrId: check.vdrId,
            userId: vdrUser.id,
            ...(check.resourceType === 'document' 
              ? { documentId: check.resourceId }
              : { folderId: check.resourceId }
            )
          }
        })

        if (resourcePermission) {
          // Override with specific permissions
          switch (check.action) {
            case 'view':
              hasPermission = resourcePermission.canView
              break
            case 'download':
              hasPermission = resourcePermission.canDownload
              break
            case 'comment':
              hasPermission = resourcePermission.canComment
              break
            case 'share':
              hasPermission = resourcePermission.canShare
              break
          }

          // Check time-based permissions
          if (resourcePermission.validFrom && resourcePermission.validFrom > new Date()) {
            hasPermission = false
          }
          if (resourcePermission.validUntil && resourcePermission.validUntil < new Date()) {
            hasPermission = false
          }
        }
      }

      // Cache result
      await this.cache.set(cacheKey, hasPermission, 300) // Cache for 5 minutes

      return hasPermission
    } catch (error) {
      this.logger.error('Failed to check permission', error, check)
      return false
    }
  }

  /**
   * Generate access token for VDR
   */
  async generateAccessToken(vdrId: string, userId: string, expiresIn: number = 3600): Promise<VDRAccessToken> {
    try {
      // Get user permissions
      const vdrUser = await this.prisma.vDRUser.findFirst({
        where: {
          vdrId,
          OR: [
            { userId },
            { email: await this.getUserEmail(userId) }
          ],
          status: 'ACTIVE'
        }
      })

      if (!vdrUser) {
        throw new Error('User not found in VDR')
      }

      // Generate token
      const tokenData = {
        vdrId,
        userId: vdrUser.id,
        role: vdrUser.role,
        permissions: this.getRolePermissions(vdrUser.role),
        expiresAt: new Date(Date.now() + expiresIn * 1000)
      }

      const token = this.encodeToken(tokenData)

      // Store token in cache
      await this.cache.set(`vdr:token:${token}`, tokenData, expiresIn)

      return {
        token,
        vdrId,
        userId: vdrUser.id,
        expiresAt: tokenData.expiresAt,
        permissions: tokenData.permissions
      }
    } catch (error) {
      this.logger.error('Failed to generate access token', error, { vdrId, userId })
      throw error
    }
  }

  /**
   * Revoke user access
   */
  async revokeAccess(vdrId: string, userEmail: string, revokedBy: string): Promise<void> {
    try {
      this.logger.info('Revoking VDR access', { vdrId, userEmail, revokedBy })

      // Check if revoker has permission
      if (!await this.checkPermission({ vdrId, userId: revokedBy, action: 'admin' })) {
        throw new Error('Insufficient permissions to revoke access')
      }

      // Update user status
      const vdrUser = await this.prisma.vDRUser.updateMany({
        where: {
          vdrId,
          email: userEmail,
          status: { in: ['PENDING', 'ACTIVE'] }
        },
        data: {
          status: 'REVOKED',
          updatedAt: new Date()
        }
      })

      if (vdrUser.count === 0) {
        throw new Error('User not found or already revoked')
      }

      // Clear user cache
      await this.cache.deletePattern(`vdr:permission:${vdrId}:*`)

      // Log activity
      await this.logActivity(vdrId, revokedBy, 'USER_REVOKED', {
        revokedEmail: userEmail
      })

      this.logger.info('Access revoked successfully', { vdrId, userEmail })
    } catch (error) {
      this.logger.error('Failed to revoke access', error, { vdrId, userEmail, revokedBy })
      throw error
    }
  }

  /**
   * Update user permissions
   */
  async updateUserPermissions(
    vdrId: string, 
    userEmail: string, 
    permissions: Partial<{
      role: VDRRole
      accessLevel: VDRAccessLevel
      canDownload: boolean
      canPrint: boolean
      canComment: boolean
      expiresAt: Date
    }>,
    updatedBy: string
  ): Promise<any> {
    try {
      this.logger.info('Updating user permissions', { vdrId, userEmail, updatedBy })

      // Check if updater has permission
      if (!await this.checkPermission({ vdrId, userId: updatedBy, action: 'admin' })) {
        throw new Error('Insufficient permissions to update user permissions')
      }

      // Update user
      const vdrUser = await this.prisma.vDRUser.updateMany({
        where: {
          vdrId,
          email: userEmail,
          status: { in: ['PENDING', 'ACTIVE'] }
        },
        data: {
          ...permissions,
          updatedAt: new Date()
        }
      })

      if (vdrUser.count === 0) {
        throw new Error('User not found')
      }

      // Clear user cache
      await this.cache.deletePattern(`vdr:permission:${vdrId}:*`)

      // Log activity
      await this.logActivity(vdrId, updatedBy, 'USER_PERMISSIONS_UPDATED', {
        targetEmail: userEmail,
        permissions
      })

      this.logger.info('User permissions updated successfully', { vdrId, userEmail })

      return vdrUser
    } catch (error) {
      this.logger.error('Failed to update user permissions', error, { vdrId, userEmail, updatedBy })
      throw error
    }
  }

  /**
   * Get role permissions
   */
  private getRolePermissions(role: VDRRole): string[] {
    const permissions: Record<VDRRole, string[]> = {
      ADMIN: ['view', 'download', 'upload', 'delete', 'share', 'comment', 'admin'],
      MANAGER: ['view', 'download', 'upload', 'delete', 'share', 'comment'],
      CONTRIBUTOR: ['view', 'download', 'upload', 'comment'],
      VIEWER: ['view']
    }

    return permissions[role] || []
  }

  /**
   * Generate invitation token
   */
  private generateInvitationToken(vdrUserId: string): string {
    const data = {
      vdrUserId,
      timestamp: Date.now(),
      random: crypto.randomBytes(16).toString('hex')
    }

    return Buffer.from(JSON.stringify(data)).toString('base64url')
  }

  /**
   * Decode invitation token
   */
  private async decodeInvitationToken(token: string): Promise<{ vdrUserId: string } | null> {
    try {
      const data = JSON.parse(Buffer.from(token, 'base64url').toString())
      
      // Check if token is not too old (24 hours)
      if (Date.now() - data.timestamp > 24 * 60 * 60 * 1000) {
        return null
      }

      return { vdrUserId: data.vdrUserId }
    } catch {
      return null
    }
  }

  /**
   * Encode access token
   */
  private encodeToken(data: any): string {
    return Buffer.from(JSON.stringify(data)).toString('base64url')
  }

  /**
   * Send invitation email
   */
  private async sendInvitationEmail(vdrUser: any, message?: string): Promise<void> {
    try {
      // Generate invitation token
      const token = this.generateInvitationToken(vdrUser.id)
      
      // In a real implementation, this would send an email
      this.logger.info('Invitation email would be sent', {
        email: vdrUser.email,
        token: token.substring(0, 8) + '...',
        message
      })

      // Store token for later use
      await this.cache.set(`vdr:invitation:${token}`, vdrUser.id, 24 * 60 * 60) // 24 hours
    } catch (error) {
      this.logger.error('Failed to send invitation email', error)
    }
  }

  /**
   * Get user email
   */
  private async getUserEmail(userId: string): Promise<string> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { email: true }
    })
    return user?.email || ''
  }

  /**
   * Log VDR activity
   */
  private async logActivity(vdrId: string, userId: string, action: string, details?: any): Promise<void> {
    try {
      await this.prisma.vDRActivity.create({
        data: {
          vdrId,
          userId,
          action: action as any,
          details
        }
      })
    } catch (error) {
      this.logger.error('Failed to log VDR activity', error)
    }
  }
}
