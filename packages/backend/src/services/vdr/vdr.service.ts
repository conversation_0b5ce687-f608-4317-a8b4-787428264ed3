import { PrismaClient, VDRRole, VDRAccessLevel } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { VDRFileStorageService } from './file-storage.service'
import { VDRAccessControlService } from './access-control.service'

export interface CreateVDRRequest {
  name: string
  description?: string
  dealId?: string
  expiresAt?: Date
  maxUsers?: number
  requiresApproval?: boolean
  allowDownload?: boolean
  allowPrint?: boolean
  allowCopy?: boolean
  watermarkEnabled?: boolean
  sessionTimeout?: number
  logoUrl?: string
  primaryColor?: string
  customDomain?: string
}

export interface VDRFilters {
  search?: string
  dealId?: string
  isActive?: boolean
  createdBy?: string
}

export interface VDRAnalytics {
  totalViews: number
  totalDownloads: number
  uniqueVisitors: number
  topDocuments: Array<{
    id: string
    name: string
    viewCount: number
    downloadCount: number
  }>
  userActivity: Array<{
    userId: string
    email: string
    lastLogin: Date
    totalViews: number
    totalDownloads: number
  }>
  activityTimeline: Array<{
    date: string
    views: number
    downloads: number
    uploads: number
  }>
}

export class VDRService {
  private prisma: PrismaClient
  private cache: CacheService
  private fileStorage: VDRFileStorageService
  private accessControl: VDRAccessControlService
  private logger: Logger

  constructor(
    prisma: PrismaClient, 
    cache: CacheService,
    fileStorage: VDRFileStorageService,
    accessControl: VDRAccessControlService
  ) {
    this.prisma = prisma
    this.cache = cache
    this.fileStorage = fileStorage
    this.accessControl = accessControl
    this.logger = new Logger('VDRService')
  }

  /**
   * Create a new VDR
   */
  async createVDR(data: CreateVDRRequest, userId: string, tenantId: string): Promise<any> {
    try {
      this.logger.info('Creating new VDR', { name: data.name, userId, tenantId })

      const vdr = await this.prisma.virtualDataRoom.create({
        data: {
          name: data.name,
          description: data.description,
          dealId: data.dealId,
          expiresAt: data.expiresAt,
          maxUsers: data.maxUsers || 50,
          requiresApproval: data.requiresApproval !== false,
          allowDownload: data.allowDownload || false,
          allowPrint: data.allowPrint || false,
          allowCopy: data.allowCopy || false,
          watermarkEnabled: data.watermarkEnabled !== false,
          sessionTimeout: data.sessionTimeout || 30,
          logoUrl: data.logoUrl,
          primaryColor: data.primaryColor,
          customDomain: data.customDomain,
          tenantId,
          createdBy: userId
        },
        include: {
          deal: true,
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      // Create default folders
      await this.createDefaultFolders(vdr.id)

      // Add creator as admin
      await this.accessControl.inviteUser({
        vdrId: vdr.id,
        email: await this.getUserEmail(userId),
        role: VDRRole.ADMIN,
        accessLevel: VDRAccessLevel.ADMIN_ONLY,
        canDownload: true,
        canPrint: true,
        canComment: true,
        invitedBy: userId
      })

      this.logger.info('VDR created successfully', { vdrId: vdr.id, name: data.name })

      return vdr
    } catch (error) {
      this.logger.error('Failed to create VDR', error, { data, userId, tenantId })
      throw error
    }
  }

  /**
   * Get VDR by ID
   */
  async getVDRById(vdrId: string, userId: string, tenantId: string): Promise<any> {
    try {
      const cacheKey = `vdr:${vdrId}:${userId}`
      
      // Try cache first
      const cached = await this.cache.get(cacheKey)
      if (cached) {
        return cached
      }

      // Check access
      const hasAccess = await this.accessControl.checkPermission({
        vdrId,
        userId,
        action: 'view'
      })

      if (!hasAccess) {
        throw new Error('Access denied')
      }

      const vdr = await this.prisma.virtualDataRoom.findFirst({
        where: { id: vdrId, tenantId },
        include: {
          deal: true,
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          folders: {
            where: { parentId: null },
            orderBy: { order: 'asc' },
            include: {
              children: {
                orderBy: { order: 'asc' }
              },
              _count: {
                select: { documents: true }
              }
            }
          },
          users: {
            where: { status: 'ACTIVE' },
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            }
          }
        }
      })

      if (!vdr) {
        throw new Error('VDR not found')
      }

      // Cache for 5 minutes
      await this.cache.set(cacheKey, vdr, 300)

      return vdr
    } catch (error) {
      this.logger.error('Failed to get VDR', error, { vdrId, userId, tenantId })
      throw error
    }
  }

  /**
   * Get VDRs with filtering
   */
  async getVDRs(
    tenantId: string,
    userId: string,
    filters: VDRFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{ vdrs: any[]; total: number; hasMore: boolean }> {
    try {
      const where: any = { tenantId }

      // Apply filters
      if (filters.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } }
        ]
      }

      if (filters.dealId) {
        where.dealId = filters.dealId
      }

      if (filters.isActive !== undefined) {
        where.isActive = filters.isActive
      }

      if (filters.createdBy) {
        where.createdBy = filters.createdBy
      }

      // Only show VDRs user has access to
      where.users = {
        some: {
          OR: [
            { userId },
            { email: await this.getUserEmail(userId) }
          ],
          status: 'ACTIVE'
        }
      }

      const [vdrs, total] = await Promise.all([
        this.prisma.virtualDataRoom.findMany({
          where,
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            deal: {
              select: {
                id: true,
                title: true,
                targetCompany: true
              }
            },
            creator: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            },
            _count: {
              select: {
                documents: true,
                users: { where: { status: 'ACTIVE' } }
              }
            }
          }
        }),
        this.prisma.virtualDataRoom.count({ where })
      ])

      const hasMore = page * limit < total

      return { vdrs, total, hasMore }
    } catch (error) {
      this.logger.error('Failed to get VDRs', error, { tenantId, userId, filters })
      throw error
    }
  }

  /**
   * Update VDR
   */
  async updateVDR(
    vdrId: string, 
    data: Partial<CreateVDRRequest>, 
    userId: string, 
    tenantId: string
  ): Promise<any> {
    try {
      this.logger.info('Updating VDR', { vdrId, userId })

      // Check admin permission
      const hasPermission = await this.accessControl.checkPermission({
        vdrId,
        userId,
        action: 'admin'
      })

      if (!hasPermission) {
        throw new Error('Insufficient permissions')
      }

      const vdr = await this.prisma.virtualDataRoom.update({
        where: { id: vdrId, tenantId },
        data: {
          ...data,
          updatedAt: new Date()
        },
        include: {
          deal: true,
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      // Clear cache
      await this.cache.deletePattern(`vdr:${vdrId}:*`)

      this.logger.info('VDR updated successfully', { vdrId })

      return vdr
    } catch (error) {
      this.logger.error('Failed to update VDR', error, { vdrId, data, userId })
      throw error
    }
  }

  /**
   * Delete VDR
   */
  async deleteVDR(vdrId: string, userId: string, tenantId: string): Promise<void> {
    try {
      this.logger.info('Deleting VDR', { vdrId, userId })

      // Check admin permission
      const hasPermission = await this.accessControl.checkPermission({
        vdrId,
        userId,
        action: 'admin'
      })

      if (!hasPermission) {
        throw new Error('Insufficient permissions')
      }

      // Soft delete - mark as inactive
      await this.prisma.virtualDataRoom.update({
        where: { id: vdrId, tenantId },
        data: { 
          isActive: false,
          updatedAt: new Date()
        }
      })

      // Clear cache
      await this.cache.deletePattern(`vdr:${vdrId}:*`)

      this.logger.info('VDR deleted successfully', { vdrId })
    } catch (error) {
      this.logger.error('Failed to delete VDR', error, { vdrId, userId })
      throw error
    }
  }

  /**
   * Get VDR analytics
   */
  async getVDRAnalytics(vdrId: string, userId: string, tenantId: string): Promise<VDRAnalytics> {
    try {
      // Check access
      const hasAccess = await this.accessControl.checkPermission({
        vdrId,
        userId,
        action: 'view'
      })

      if (!hasAccess) {
        throw new Error('Access denied')
      }

      const cacheKey = `vdr:analytics:${vdrId}`
      
      // Try cache first
      const cached = await this.cache.get<VDRAnalytics>(cacheKey)
      if (cached) {
        return cached
      }

      // Get basic stats
      const vdr = await this.prisma.virtualDataRoom.findUnique({
        where: { id: vdrId },
        select: {
          totalViews: true,
          totalDownloads: true,
          uniqueVisitors: true
        }
      })

      if (!vdr) {
        throw new Error('VDR not found')
      }

      // Get top documents
      const topDocuments = await this.prisma.vDRDocument.findMany({
        where: { vdrId, status: 'ACTIVE' },
        orderBy: { viewCount: 'desc' },
        take: 10,
        select: {
          id: true,
          name: true,
          viewCount: true,
          downloadCount: true
        }
      })

      // Get user activity
      const userActivity = await this.prisma.vDRUser.findMany({
        where: { vdrId, status: 'ACTIVE' },
        select: {
          id: true,
          email: true,
          lastLoginAt: true,
          activities: {
            where: {
              action: { in: ['VIEW_DOCUMENT', 'DOWNLOAD_DOCUMENT'] }
            },
            select: {
              action: true
            }
          }
        }
      })

      // Get activity timeline (last 30 days)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      const activities = await this.prisma.vDRActivity.findMany({
        where: {
          vdrId,
          createdAt: { gte: thirtyDaysAgo }
        },
        select: {
          action: true,
          createdAt: true
        }
      })

      // Process activity timeline
      const activityTimeline = this.processActivityTimeline(activities)

      const analytics: VDRAnalytics = {
        totalViews: vdr.totalViews,
        totalDownloads: vdr.totalDownloads,
        uniqueVisitors: vdr.uniqueVisitors,
        topDocuments,
        userActivity: userActivity.map(user => ({
          userId: user.id,
          email: user.email,
          lastLogin: user.lastLoginAt || new Date(),
          totalViews: user.activities.filter(a => a.action === 'VIEW_DOCUMENT').length,
          totalDownloads: user.activities.filter(a => a.action === 'DOWNLOAD_DOCUMENT').length
        })),
        activityTimeline
      }

      // Cache for 10 minutes
      await this.cache.set(cacheKey, analytics, 600)

      return analytics
    } catch (error) {
      this.logger.error('Failed to get VDR analytics', error, { vdrId, userId })
      throw error
    }
  }

  /**
   * Create default folders for VDR
   */
  private async createDefaultFolders(vdrId: string): Promise<void> {
    const defaultFolders = [
      { name: 'Financial Information', path: '/financial', order: 1 },
      { name: 'Legal Documents', path: '/legal', order: 2 },
      { name: 'Commercial Information', path: '/commercial', order: 3 },
      { name: 'Technical Documentation', path: '/technical', order: 4 },
      { name: 'HR & Personnel', path: '/hr', order: 5 },
      { name: 'Operational Data', path: '/operations', order: 6 }
    ]

    for (const folder of defaultFolders) {
      await this.prisma.vDRFolder.create({
        data: {
          ...folder,
          vdrId,
          accessLevel: VDRAccessLevel.RESTRICTED
        }
      })
    }
  }

  /**
   * Process activity timeline data
   */
  private processActivityTimeline(activities: any[]): Array<{
    date: string
    views: number
    downloads: number
    uploads: number
  }> {
    const timeline: Record<string, { views: number; downloads: number; uploads: number }> = {}

    activities.forEach(activity => {
      const date = activity.createdAt.toISOString().split('T')[0]
      
      if (!timeline[date]) {
        timeline[date] = { views: 0, downloads: 0, uploads: 0 }
      }

      switch (activity.action) {
        case 'VIEW_DOCUMENT':
          timeline[date].views++
          break
        case 'DOWNLOAD_DOCUMENT':
          timeline[date].downloads++
          break
        case 'UPLOAD_DOCUMENT':
          timeline[date].uploads++
          break
      }
    })

    return Object.entries(timeline).map(([date, data]) => ({
      date,
      ...data
    })).sort((a, b) => a.date.localeCompare(b.date))
  }

  /**
   * Get user email
   */
  private async getUserEmail(userId: string): Promise<string> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { email: true }
    })
    return user?.email || ''
  }
}
