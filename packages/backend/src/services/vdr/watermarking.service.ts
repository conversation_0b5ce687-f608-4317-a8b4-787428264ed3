import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import * as crypto from 'crypto'

export interface WatermarkConfig {
  text: string
  userEmail: string
  timestamp: Date
  documentId: string
  vdrId: string
  opacity: number
  fontSize: number
  color: string
  position: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'diagonal'
  rotation: number
  pattern: 'single' | 'repeated' | 'background'
  includeMetadata: boolean
}

export interface WatermarkMetadata {
  documentId: string
  userId: string
  userEmail: string
  accessTime: Date
  ipAddress?: string
  sessionId: string
  vdrId: string
  watermarkId: string
}

export interface ProcessedDocument {
  buffer: Buffer
  mimeType: string
  watermarkId: string
  metadata: WatermarkMetadata
}

export class VDRWatermarkingService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('VDRWatermarkingService')
  }

  /**
   * Apply watermark to document
   */
  async applyWatermark(
    documentBuffer: Buffer,
    mimeType: string,
    config: WatermarkConfig,
    metadata: Partial<WatermarkMetadata>
  ): Promise<ProcessedDocument> {
    try {
      this.logger.info('Applying watermark to document', {
        documentId: config.documentId,
        userEmail: config.userEmail,
        mimeType
      })

      // Generate unique watermark ID
      const watermarkId = this.generateWatermarkId(config, metadata)

      // Create watermark metadata
      const watermarkMetadata: WatermarkMetadata = {
        documentId: config.documentId,
        userId: metadata.userId || '',
        userEmail: config.userEmail,
        accessTime: new Date(),
        ipAddress: metadata.ipAddress,
        sessionId: metadata.sessionId || '',
        vdrId: config.vdrId,
        watermarkId
      }

      // Apply watermark based on file type
      let watermarkedBuffer: Buffer

      switch (mimeType) {
        case 'application/pdf':
          watermarkedBuffer = await this.watermarkPDF(documentBuffer, config, watermarkMetadata)
          break
        case 'image/jpeg':
        case 'image/png':
        case 'image/gif':
          watermarkedBuffer = await this.watermarkImage(documentBuffer, config, watermarkMetadata)
          break
        case 'application/msword':
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          watermarkedBuffer = await this.watermarkDocument(documentBuffer, config, watermarkMetadata)
          break
        default:
          // For unsupported types, return original with metadata injection
          watermarkedBuffer = await this.injectMetadata(documentBuffer, config, watermarkMetadata)
          break
      }

      // Store watermark record
      await this.storeWatermarkRecord(watermarkMetadata, config)

      // Cache watermarked document temporarily
      await this.cache.set(
        `watermark:${watermarkId}`,
        {
          buffer: watermarkedBuffer.toString('base64'),
          metadata: watermarkMetadata
        },
        3600 // 1 hour
      )

      this.logger.info('Watermark applied successfully', {
        watermarkId,
        documentId: config.documentId
      })

      return {
        buffer: watermarkedBuffer,
        mimeType,
        watermarkId,
        metadata: watermarkMetadata
      }
    } catch (error) {
      this.logger.error('Failed to apply watermark', error, config)
      throw error
    }
  }

  /**
   * Watermark PDF documents
   */
  private async watermarkPDF(
    buffer: Buffer,
    config: WatermarkConfig,
    metadata: WatermarkMetadata
  ): Promise<Buffer> {
    try {
      // In a real implementation, this would use pdf-lib or similar
      this.logger.info('Watermarking PDF document', {
        watermarkId: metadata.watermarkId,
        config: {
          text: config.text,
          position: config.position,
          opacity: config.opacity
        }
      })

      // Simulate PDF watermarking
      // In production, use pdf-lib:
      /*
      import { PDFDocument, rgb } from 'pdf-lib'
      
      const pdfDoc = await PDFDocument.load(buffer)
      const pages = pdfDoc.getPages()
      
      for (const page of pages) {
        const { width, height } = page.getSize()
        
        // Add watermark text
        page.drawText(config.text, {
          x: this.getXPosition(config.position, width),
          y: this.getYPosition(config.position, height),
          size: config.fontSize,
          color: rgb(...this.hexToRgb(config.color)),
          opacity: config.opacity,
          rotate: degrees(config.rotation)
        })
        
        // Add metadata
        if (config.includeMetadata) {
          page.drawText(`Accessed by: ${metadata.userEmail} at ${metadata.accessTime.toISOString()}`, {
            x: 10,
            y: 10,
            size: 8,
            color: rgb(0.5, 0.5, 0.5),
            opacity: 0.5
          })
        }
      }
      
      return Buffer.from(await pdfDoc.save())
      */

      // For now, return original buffer with simulated processing
      return buffer
    } catch (error) {
      this.logger.error('Failed to watermark PDF', error)
      throw error
    }
  }

  /**
   * Watermark image files
   */
  private async watermarkImage(
    buffer: Buffer,
    config: WatermarkConfig,
    metadata: WatermarkMetadata
  ): Promise<Buffer> {
    try {
      // In a real implementation, this would use sharp or canvas
      this.logger.info('Watermarking image document', {
        watermarkId: metadata.watermarkId,
        config: {
          text: config.text,
          position: config.position,
          opacity: config.opacity
        }
      })

      // Simulate image watermarking
      // In production, use sharp:
      /*
      import sharp from 'sharp'
      
      const image = sharp(buffer)
      const { width, height } = await image.metadata()
      
      // Create watermark SVG
      const watermarkSvg = `
        <svg width="${width}" height="${height}">
          <text x="${this.getXPosition(config.position, width)}" 
                y="${this.getYPosition(config.position, height)}"
                font-family="Arial" 
                font-size="${config.fontSize}"
                fill="${config.color}"
                opacity="${config.opacity}"
                transform="rotate(${config.rotation} ${width/2} ${height/2})">
            ${config.text}
          </text>
        </svg>
      `
      
      return await image
        .composite([{ input: Buffer.from(watermarkSvg), blend: 'over' }])
        .toBuffer()
      */

      // For now, return original buffer
      return buffer
    } catch (error) {
      this.logger.error('Failed to watermark image', error)
      throw error
    }
  }

  /**
   * Watermark Word documents
   */
  private async watermarkDocument(
    buffer: Buffer,
    config: WatermarkConfig,
    metadata: WatermarkMetadata
  ): Promise<Buffer> {
    try {
      // In a real implementation, this would use docx or mammoth
      this.logger.info('Watermarking Word document', {
        watermarkId: metadata.watermarkId
      })

      // Simulate document watermarking
      // In production, use docx library to add watermarks
      
      return buffer
    } catch (error) {
      this.logger.error('Failed to watermark document', error)
      throw error
    }
  }

  /**
   * Inject metadata into unsupported file types
   */
  private async injectMetadata(
    buffer: Buffer,
    config: WatermarkConfig,
    metadata: WatermarkMetadata
  ): Promise<Buffer> {
    try {
      // For unsupported file types, we can inject metadata in file headers or create a wrapper
      this.logger.info('Injecting metadata into file', {
        watermarkId: metadata.watermarkId
      })

      // Create metadata header
      const metadataHeader = JSON.stringify({
        watermarkId: metadata.watermarkId,
        userEmail: metadata.userEmail,
        accessTime: metadata.accessTime.toISOString(),
        documentId: metadata.documentId
      })

      // In a real implementation, this would be more sophisticated
      // For now, just return the original buffer
      return buffer
    } catch (error) {
      this.logger.error('Failed to inject metadata', error)
      throw error
    }
  }

  /**
   * Generate unique watermark ID
   */
  private generateWatermarkId(config: WatermarkConfig, metadata: Partial<WatermarkMetadata>): string {
    const data = {
      documentId: config.documentId,
      userEmail: config.userEmail,
      timestamp: config.timestamp.toISOString(),
      sessionId: metadata.sessionId,
      random: crypto.randomBytes(8).toString('hex')
    }

    return crypto
      .createHash('sha256')
      .update(JSON.stringify(data))
      .digest('hex')
      .substring(0, 16)
  }

  /**
   * Store watermark record for audit trail
   */
  private async storeWatermarkRecord(
    metadata: WatermarkMetadata,
    config: WatermarkConfig
  ): Promise<void> {
    try {
      // In a real implementation, this would store in database
      const record = {
        id: metadata.watermarkId,
        documentId: metadata.documentId,
        userId: metadata.userId,
        userEmail: metadata.userEmail,
        vdrId: metadata.vdrId,
        accessTime: metadata.accessTime,
        ipAddress: metadata.ipAddress,
        sessionId: metadata.sessionId,
        watermarkConfig: {
          text: config.text,
          position: config.position,
          opacity: config.opacity,
          fontSize: config.fontSize,
          color: config.color,
          rotation: config.rotation,
          pattern: config.pattern
        },
        createdAt: new Date()
      }

      // Store in cache for now (in production, use database)
      await this.cache.set(
        `watermark:record:${metadata.watermarkId}`,
        record,
        86400 // 24 hours
      )

      this.logger.info('Watermark record stored', {
        watermarkId: metadata.watermarkId,
        documentId: metadata.documentId
      })
    } catch (error) {
      this.logger.error('Failed to store watermark record', error)
    }
  }

  /**
   * Verify watermark authenticity
   */
  async verifyWatermark(watermarkId: string): Promise<{
    isValid: boolean
    metadata?: WatermarkMetadata
    record?: any
  }> {
    try {
      // Get watermark record
      const record = await this.cache.get(`watermark:record:${watermarkId}`)
      
      if (!record) {
        return { isValid: false }
      }

      // Verify timestamp (watermarks expire after 24 hours)
      const accessTime = new Date(record.accessTime)
      const now = new Date()
      const hoursDiff = (now.getTime() - accessTime.getTime()) / (1000 * 60 * 60)

      if (hoursDiff > 24) {
        return { isValid: false }
      }

      return {
        isValid: true,
        metadata: {
          documentId: record.documentId,
          userId: record.userId,
          userEmail: record.userEmail,
          accessTime: accessTime,
          ipAddress: record.ipAddress,
          sessionId: record.sessionId,
          vdrId: record.vdrId,
          watermarkId: record.id
        },
        record
      }
    } catch (error) {
      this.logger.error('Failed to verify watermark', error, { watermarkId })
      return { isValid: false }
    }
  }

  /**
   * Get watermark analytics
   */
  async getWatermarkAnalytics(vdrId: string, dateFrom?: Date, dateTo?: Date): Promise<{
    totalWatermarks: number
    uniqueUsers: number
    topDocuments: Array<{ documentId: string; count: number }>
    accessByHour: Array<{ hour: number; count: number }>
  }> {
    try {
      // In a real implementation, this would query the database
      // For now, return mock data
      return {
        totalWatermarks: 150,
        uniqueUsers: 25,
        topDocuments: [
          { documentId: 'doc1', count: 45 },
          { documentId: 'doc2', count: 32 },
          { documentId: 'doc3', count: 28 }
        ],
        accessByHour: Array.from({ length: 24 }, (_, hour) => ({
          hour,
          count: Math.floor(Math.random() * 20)
        }))
      }
    } catch (error) {
      this.logger.error('Failed to get watermark analytics', error)
      throw error
    }
  }

  /**
   * Clean up expired watermarks
   */
  async cleanupExpiredWatermarks(): Promise<void> {
    try {
      this.logger.info('Cleaning up expired watermarks')

      // In a real implementation, this would clean up database records
      // and cached watermarked documents older than 24 hours

      // For now, just log the cleanup
      this.logger.info('Watermark cleanup completed')
    } catch (error) {
      this.logger.error('Failed to cleanup expired watermarks', error)
    }
  }

  /**
   * Helper methods for positioning
   */
  private getXPosition(position: string, width: number): number {
    switch (position) {
      case 'top-left':
      case 'bottom-left':
        return 50
      case 'top-right':
      case 'bottom-right':
        return width - 200
      case 'center':
      case 'diagonal':
      default:
        return width / 2
    }
  }

  private getYPosition(position: string, height: number): number {
    switch (position) {
      case 'top-left':
      case 'top-right':
        return height - 50
      case 'bottom-left':
      case 'bottom-right':
        return 50
      case 'center':
      case 'diagonal':
      default:
        return height / 2
    }
  }

  private hexToRgb(hex: string): [number, number, number] {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result
      ? [
          parseInt(result[1], 16) / 255,
          parseInt(result[2], 16) / 255,
          parseInt(result[3], 16) / 255
        ]
      : [0, 0, 0]
  }
}
