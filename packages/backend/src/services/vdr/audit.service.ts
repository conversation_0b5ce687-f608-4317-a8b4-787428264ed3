import { PrismaClient, VDRActivityType } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface AuditLogEntry {
  id: string
  vdrId: string
  userId: string
  userEmail: string
  userName: string
  action: VDRActivityType
  resourceType?: 'document' | 'folder' | 'user' | 'vdr'
  resourceId?: string
  resourceName?: string
  details?: any
  ipAddress?: string
  userAgent?: string
  location?: string
  timestamp: Date
  duration?: number
}

export interface AuditFilters {
  vdrId?: string
  userId?: string
  userEmail?: string
  actions?: VDRActivityType[]
  resourceType?: 'document' | 'folder' | 'user' | 'vdr'
  resourceId?: string
  dateFrom?: Date
  dateTo?: Date
  ipAddress?: string
  search?: string
}

export interface AuditReport {
  summary: {
    totalActivities: number
    uniqueUsers: number
    mostActiveUser: {
      email: string
      activityCount: number
    }
    topActions: Array<{
      action: VDRActivityType
      count: number
    }>
    timeRange: {
      from: Date
      to: Date
    }
  }
  activities: AuditLogEntry[]
  analytics: {
    activitiesByDay: Array<{
      date: string
      count: number
    }>
    activitiesByAction: Array<{
      action: VDRActivityType
      count: number
    }>
    activitiesByUser: Array<{
      userEmail: string
      count: number
    }>
    documentsAccessed: Array<{
      documentId: string
      documentName: string
      viewCount: number
      downloadCount: number
    }>
  }
}

export interface SecurityAlert {
  id: string
  vdrId: string
  type: 'SUSPICIOUS_ACTIVITY' | 'MULTIPLE_FAILED_LOGINS' | 'UNUSUAL_DOWNLOAD_PATTERN' | 'UNAUTHORIZED_ACCESS_ATTEMPT' | 'DATA_EXFILTRATION_RISK'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  description: string
  userId?: string
  userEmail?: string
  ipAddress?: string
  details: any
  createdAt: Date
  resolved: boolean
  resolvedAt?: Date
  resolvedBy?: string
}

export class VDRAuditService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('VDRAuditService')
  }

  /**
   * Log VDR activity
   */
  async logActivity(
    vdrId: string,
    userId: string,
    action: VDRActivityType,
    details?: {
      documentId?: string
      folderId?: string
      resourceName?: string
      ipAddress?: string
      userAgent?: string
      location?: string
      duration?: number
      additionalData?: any
    }
  ): Promise<void> {
    try {
      // Get VDR user
      const vdrUser = await this.prisma.vDRUser.findFirst({
        where: {
          vdrId,
          OR: [
            { userId },
            { email: await this.getUserEmail(userId) }
          ]
        }
      })

      if (!vdrUser) {
        this.logger.warn('VDR user not found for activity logging', { vdrId, userId, action })
        return
      }

      // Create activity log
      await this.prisma.vDRActivity.create({
        data: {
          vdrId,
          userId: vdrUser.id,
          action,
          documentId: details?.documentId,
          folderId: details?.folderId,
          ipAddress: details?.ipAddress,
          userAgent: details?.userAgent,
          location: details?.location,
          duration: details?.duration,
          details: {
            resourceName: details?.resourceName,
            ...details?.additionalData
          }
        }
      })

      // Check for security alerts
      await this.checkSecurityAlerts(vdrId, vdrUser.id, action, details)

      // Update VDR statistics
      await this.updateVDRStatistics(vdrId, action)

    } catch (error) {
      this.logger.error('Failed to log VDR activity', error, { vdrId, userId, action })
    }
  }

  /**
   * Get audit logs with filtering
   */
  async getAuditLogs(
    filters: AuditFilters,
    page: number = 1,
    limit: number = 50
  ): Promise<{ logs: AuditLogEntry[]; total: number; hasMore: boolean }> {
    try {
      const where = this.buildAuditWhereClause(filters)

      const [activities, total] = await Promise.all([
        this.prisma.vDRActivity.findMany({
          where,
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            vdr: {
              select: { name: true }
            },
            vdrUser: {
              select: {
                email: true,
                firstName: true,
                lastName: true,
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true
                  }
                }
              }
            },
            document: {
              select: {
                name: true,
                originalName: true
              }
            },
            folder: {
              select: {
                name: true
              }
            }
          }
        }),
        this.prisma.vDRActivity.count({ where })
      ])

      const logs: AuditLogEntry[] = activities.map(activity => ({
        id: activity.id,
        vdrId: activity.vdrId,
        userId: activity.userId,
        userEmail: activity.vdrUser.email,
        userName: activity.vdrUser.user 
          ? `${activity.vdrUser.user.firstName} ${activity.vdrUser.user.lastName}`
          : `${activity.vdrUser.firstName || ''} ${activity.vdrUser.lastName || ''}`.trim(),
        action: activity.action,
        resourceType: activity.documentId ? 'document' : activity.folderId ? 'folder' : undefined,
        resourceId: activity.documentId || activity.folderId || undefined,
        resourceName: activity.document?.name || activity.folder?.name || undefined,
        details: activity.details,
        ipAddress: activity.ipAddress || undefined,
        userAgent: activity.userAgent || undefined,
        location: activity.location || undefined,
        timestamp: activity.createdAt,
        duration: activity.duration || undefined
      }))

      const hasMore = page * limit < total

      return { logs, total, hasMore }
    } catch (error) {
      this.logger.error('Failed to get audit logs', error, filters)
      throw error
    }
  }

  /**
   * Generate comprehensive audit report
   */
  async generateAuditReport(
    vdrId: string,
    dateFrom: Date,
    dateTo: Date,
    userId: string
  ): Promise<AuditReport> {
    try {
      this.logger.info('Generating audit report', { vdrId, dateFrom, dateTo, userId })

      const filters: AuditFilters = {
        vdrId,
        dateFrom,
        dateTo
      }

      // Get all activities for the period
      const { logs: activities, total } = await this.getAuditLogs(filters, 1, 10000)

      // Calculate summary statistics
      const uniqueUsers = new Set(activities.map(a => a.userEmail)).size
      
      const userActivityCounts = activities.reduce((acc, activity) => {
        acc[activity.userEmail] = (acc[activity.userEmail] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const mostActiveUser = Object.entries(userActivityCounts)
        .sort(([,a], [,b]) => b - a)[0]

      const actionCounts = activities.reduce((acc, activity) => {
        acc[activity.action] = (acc[activity.action] || 0) + 1
        return acc
      }, {} as Record<VDRActivityType, number>)

      const topActions = Object.entries(actionCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([action, count]) => ({ action: action as VDRActivityType, count }))

      // Generate analytics
      const activitiesByDay = this.groupActivitiesByDay(activities, dateFrom, dateTo)
      const activitiesByAction = topActions
      const activitiesByUser = Object.entries(userActivityCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20)
        .map(([userEmail, count]) => ({ userEmail, count }))

      const documentsAccessed = this.getDocumentAccessStats(activities)

      const report: AuditReport = {
        summary: {
          totalActivities: total,
          uniqueUsers,
          mostActiveUser: {
            email: mostActiveUser?.[0] || '',
            activityCount: mostActiveUser?.[1] || 0
          },
          topActions,
          timeRange: { from: dateFrom, to: dateTo }
        },
        activities: activities.slice(0, 100), // Limit to first 100 for report
        analytics: {
          activitiesByDay,
          activitiesByAction,
          activitiesByUser,
          documentsAccessed
        }
      }

      return report
    } catch (error) {
      this.logger.error('Failed to generate audit report', error, { vdrId, dateFrom, dateTo })
      throw error
    }
  }

  /**
   * Get security alerts
   */
  async getSecurityAlerts(
    vdrId: string,
    resolved?: boolean,
    severity?: SecurityAlert['severity']
  ): Promise<SecurityAlert[]> {
    try {
      // In a real implementation, this would fetch from a security_alerts table
      // For now, we'll return a mock implementation
      const alerts: SecurityAlert[] = []

      // Check for suspicious patterns in recent activities
      const recentActivities = await this.prisma.vDRActivity.findMany({
        where: {
          vdrId,
          createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
        },
        include: {
          vdrUser: {
            select: { email: true }
          }
        }
      })

      // Detect unusual download patterns
      const downloadsByUser = recentActivities
        .filter(a => a.action === 'DOWNLOAD_DOCUMENT')
        .reduce((acc, activity) => {
          const email = activity.vdrUser.email
          acc[email] = (acc[email] || 0) + 1
          return acc
        }, {} as Record<string, number>)

      Object.entries(downloadsByUser).forEach(([email, count]) => {
        if (count > 20) { // More than 20 downloads in 24 hours
          alerts.push({
            id: `alert_${Date.now()}_${email}`,
            vdrId,
            type: 'UNUSUAL_DOWNLOAD_PATTERN',
            severity: count > 50 ? 'HIGH' : 'MEDIUM',
            description: `User ${email} has downloaded ${count} documents in the last 24 hours`,
            userEmail: email,
            details: { downloadCount: count, timeframe: '24h' },
            createdAt: new Date(),
            resolved: false
          })
        }
      })

      return alerts.filter(alert => {
        if (resolved !== undefined && alert.resolved !== resolved) return false
        if (severity && alert.severity !== severity) return false
        return true
      })
    } catch (error) {
      this.logger.error('Failed to get security alerts', error, { vdrId })
      throw error
    }
  }

  /**
   * Export audit logs
   */
  async exportAuditLogs(
    filters: AuditFilters,
    format: 'csv' | 'json' | 'xlsx' = 'csv'
  ): Promise<Buffer> {
    try {
      const { logs } = await this.getAuditLogs(filters, 1, 10000)

      switch (format) {
        case 'csv':
          return this.exportToCSV(logs)
        case 'json':
          return Buffer.from(JSON.stringify(logs, null, 2))
        case 'xlsx':
          return this.exportToExcel(logs)
        default:
          throw new Error(`Unsupported export format: ${format}`)
      }
    } catch (error) {
      this.logger.error('Failed to export audit logs', error, { filters, format })
      throw error
    }
  }

  /**
   * Check for security alerts based on activity
   */
  private async checkSecurityAlerts(
    vdrId: string,
    vdrUserId: string,
    action: VDRActivityType,
    details?: any
  ): Promise<void> {
    try {
      // Check for multiple failed login attempts
      if (action === 'LOGIN') {
        const recentFailedLogins = await this.prisma.vDRActivity.count({
          where: {
            vdrId,
            userId: vdrUserId,
            action: 'LOGIN',
            createdAt: { gte: new Date(Date.now() - 60 * 60 * 1000) }, // Last hour
            details: { path: ['success'], equals: false }
          }
        })

        if (recentFailedLogins >= 5) {
          this.logger.warn('Multiple failed login attempts detected', {
            vdrId,
            vdrUserId,
            failedAttempts: recentFailedLogins
          })
        }
      }

      // Check for rapid document downloads
      if (action === 'DOWNLOAD_DOCUMENT') {
        const recentDownloads = await this.prisma.vDRActivity.count({
          where: {
            vdrId,
            userId: vdrUserId,
            action: 'DOWNLOAD_DOCUMENT',
            createdAt: { gte: new Date(Date.now() - 10 * 60 * 1000) } // Last 10 minutes
          }
        })

        if (recentDownloads >= 10) {
          this.logger.warn('Rapid document downloads detected', {
            vdrId,
            vdrUserId,
            downloadCount: recentDownloads
          })
        }
      }
    } catch (error) {
      this.logger.error('Failed to check security alerts', error)
    }
  }

  /**
   * Update VDR statistics
   */
  private async updateVDRStatistics(vdrId: string, action: VDRActivityType): Promise<void> {
    try {
      const updates: any = {}

      switch (action) {
        case 'VIEW_DOCUMENT':
          updates.totalViews = { increment: 1 }
          break
        case 'DOWNLOAD_DOCUMENT':
          updates.totalDownloads = { increment: 1 }
          break
        case 'LOGIN':
          // Update unique visitors (simplified - would need more sophisticated tracking)
          updates.uniqueVisitors = { increment: 1 }
          break
      }

      if (Object.keys(updates).length > 0) {
        await this.prisma.virtualDataRoom.update({
          where: { id: vdrId },
          data: updates
        })
      }
    } catch (error) {
      this.logger.error('Failed to update VDR statistics', error)
    }
  }

  /**
   * Build where clause for audit filtering
   */
  private buildAuditWhereClause(filters: AuditFilters): any {
    const where: any = {}

    if (filters.vdrId) {
      where.vdrId = filters.vdrId
    }

    if (filters.userId) {
      where.userId = filters.userId
    }

    if (filters.userEmail) {
      where.vdrUser = {
        email: { contains: filters.userEmail, mode: 'insensitive' }
      }
    }

    if (filters.actions?.length) {
      where.action = { in: filters.actions }
    }

    if (filters.resourceId) {
      where.OR = [
        { documentId: filters.resourceId },
        { folderId: filters.resourceId }
      ]
    }

    if (filters.dateFrom || filters.dateTo) {
      where.createdAt = {}
      if (filters.dateFrom) where.createdAt.gte = filters.dateFrom
      if (filters.dateTo) where.createdAt.lte = filters.dateTo
    }

    if (filters.ipAddress) {
      where.ipAddress = filters.ipAddress
    }

    if (filters.search) {
      where.OR = [
        { vdrUser: { email: { contains: filters.search, mode: 'insensitive' } } },
        { document: { name: { contains: filters.search, mode: 'insensitive' } } },
        { folder: { name: { contains: filters.search, mode: 'insensitive' } } }
      ]
    }

    return where
  }

  /**
   * Group activities by day
   */
  private groupActivitiesByDay(activities: AuditLogEntry[], from: Date, to: Date): Array<{ date: string; count: number }> {
    const dayMap: Record<string, number> = {}
    
    // Initialize all days in range
    const currentDate = new Date(from)
    while (currentDate <= to) {
      const dateStr = currentDate.toISOString().split('T')[0]
      dayMap[dateStr] = 0
      currentDate.setDate(currentDate.getDate() + 1)
    }

    // Count activities by day
    activities.forEach(activity => {
      const dateStr = activity.timestamp.toISOString().split('T')[0]
      if (dayMap.hasOwnProperty(dateStr)) {
        dayMap[dateStr]++
      }
    })

    return Object.entries(dayMap).map(([date, count]) => ({ date, count }))
  }

  /**
   * Get document access statistics
   */
  private getDocumentAccessStats(activities: AuditLogEntry[]): Array<{
    documentId: string
    documentName: string
    viewCount: number
    downloadCount: number
  }> {
    const docStats: Record<string, { name: string; views: number; downloads: number }> = {}

    activities.forEach(activity => {
      if (activity.resourceType === 'document' && activity.resourceId) {
        if (!docStats[activity.resourceId]) {
          docStats[activity.resourceId] = {
            name: activity.resourceName || 'Unknown',
            views: 0,
            downloads: 0
          }
        }

        if (activity.action === 'VIEW_DOCUMENT') {
          docStats[activity.resourceId].views++
        } else if (activity.action === 'DOWNLOAD_DOCUMENT') {
          docStats[activity.resourceId].downloads++
        }
      }
    })

    return Object.entries(docStats)
      .map(([documentId, stats]) => ({
        documentId,
        documentName: stats.name,
        viewCount: stats.views,
        downloadCount: stats.downloads
      }))
      .sort((a, b) => (b.viewCount + b.downloadCount) - (a.viewCount + a.downloadCount))
      .slice(0, 20)
  }

  /**
   * Export logs to CSV
   */
  private exportToCSV(logs: AuditLogEntry[]): Buffer {
    const headers = [
      'Timestamp',
      'User Email',
      'User Name',
      'Action',
      'Resource Type',
      'Resource Name',
      'IP Address',
      'Location',
      'Duration (seconds)'
    ]

    const rows = logs.map(log => [
      log.timestamp.toISOString(),
      log.userEmail,
      log.userName,
      log.action,
      log.resourceType || '',
      log.resourceName || '',
      log.ipAddress || '',
      log.location || '',
      log.duration?.toString() || ''
    ])

    const csv = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n')

    return Buffer.from(csv)
  }

  /**
   * Export logs to Excel (placeholder)
   */
  private exportToExcel(logs: AuditLogEntry[]): Buffer {
    // In a real implementation, this would use a library like exceljs
    return Buffer.from('Excel export not implemented')
  }

  /**
   * Get user email
   */
  private async getUserEmail(userId: string): Promise<string> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { email: true }
    })
    return user?.email || ''
  }
}
