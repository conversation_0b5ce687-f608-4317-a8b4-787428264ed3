import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import * as crypto from 'crypto'
import * as jwt from 'jsonwebtoken'

export interface WatermarkOptions {
  text: string
  opacity: number
  fontSize: number
  color: string
  position: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  rotation: number
}

export interface SecurityPolicy {
  id: string
  vdrId: string
  name: string
  description?: string
  
  // Access controls
  requiresApproval: boolean
  allowDownload: boolean
  allowPrint: boolean
  allowCopy: boolean
  allowScreenshot: boolean
  
  // Session management
  sessionTimeout: number // minutes
  maxConcurrentSessions: number
  requireReauth: boolean
  
  // Document protection
  watermarkEnabled: boolean
  watermarkOptions?: WatermarkOptions
  encryptionRequired: boolean
  
  // IP restrictions
  allowedIpRanges: string[]
  blockedIpRanges: string[]
  
  // Time restrictions
  accessStartTime?: string // HH:MM format
  accessEndTime?: string   // HH:MM format
  allowedDays: number[]    // 0-6 (Sunday-Saturday)
  
  // Compliance
  dataRetentionDays?: number
  requiresAuditLog: boolean
  complianceLevel: 'STANDARD' | 'HIGH' | 'CRITICAL'
  
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface SecurityViolation {
  id: string
  vdrId: string
  userId: string
  violationType: 'UNAUTHORIZED_ACCESS' | 'IP_RESTRICTION' | 'TIME_RESTRICTION' | 'DOWNLOAD_LIMIT' | 'SESSION_LIMIT' | 'SUSPICIOUS_ACTIVITY'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  description: string
  details: any
  ipAddress?: string
  userAgent?: string
  timestamp: Date
  resolved: boolean
  resolvedAt?: Date
  resolvedBy?: string
}

export interface SessionInfo {
  sessionId: string
  vdrId: string
  userId: string
  ipAddress: string
  userAgent: string
  startTime: Date
  lastActivity: Date
  expiresAt: Date
  isActive: boolean
}

export class VDRSecurityService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger
  private jwtSecret: string

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('VDRSecurityService')
    this.jwtSecret = process.env.VDR_JWT_SECRET || 'vdr-secret-key'
  }

  /**
   * Create or update security policy for VDR
   */
  async createSecurityPolicy(vdrId: string, policy: Partial<SecurityPolicy>, userId: string): Promise<SecurityPolicy> {
    try {
      this.logger.info('Creating security policy', { vdrId, userId })

      // Check if user has admin permissions
      const hasPermission = await this.checkAdminPermission(vdrId, userId)
      if (!hasPermission) {
        throw new Error('Insufficient permissions to create security policy')
      }

      const securityPolicy: SecurityPolicy = {
        id: crypto.randomUUID(),
        vdrId,
        name: policy.name || 'Default Security Policy',
        description: policy.description,
        requiresApproval: policy.requiresApproval ?? true,
        allowDownload: policy.allowDownload ?? false,
        allowPrint: policy.allowPrint ?? false,
        allowCopy: policy.allowCopy ?? false,
        allowScreenshot: policy.allowScreenshot ?? false,
        sessionTimeout: policy.sessionTimeout ?? 30,
        maxConcurrentSessions: policy.maxConcurrentSessions ?? 3,
        requireReauth: policy.requireReauth ?? false,
        watermarkEnabled: policy.watermarkEnabled ?? true,
        watermarkOptions: policy.watermarkOptions || {
          text: 'CONFIDENTIAL',
          opacity: 0.3,
          fontSize: 24,
          color: '#666666',
          position: 'center',
          rotation: -45
        },
        encryptionRequired: policy.encryptionRequired ?? true,
        allowedIpRanges: policy.allowedIpRanges || [],
        blockedIpRanges: policy.blockedIpRanges || [],
        accessStartTime: policy.accessStartTime,
        accessEndTime: policy.accessEndTime,
        allowedDays: policy.allowedDays || [1, 2, 3, 4, 5], // Monday-Friday
        dataRetentionDays: policy.dataRetentionDays,
        requiresAuditLog: policy.requiresAuditLog ?? true,
        complianceLevel: policy.complianceLevel || 'STANDARD',
        isActive: policy.isActive ?? true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Store in cache (in a real implementation, this would be in database)
      await this.cache.set(`vdr:security:${vdrId}`, securityPolicy, 3600)

      this.logger.info('Security policy created', { vdrId, policyId: securityPolicy.id })

      return securityPolicy
    } catch (error) {
      this.logger.error('Failed to create security policy', error, { vdrId, userId })
      throw error
    }
  }

  /**
   * Validate access request against security policy
   */
  async validateAccess(
    vdrId: string,
    userId: string,
    ipAddress: string,
    userAgent: string,
    action: 'view' | 'download' | 'print' | 'copy'
  ): Promise<{ allowed: boolean; reason?: string; violations?: SecurityViolation[] }> {
    try {
      const policy = await this.getSecurityPolicy(vdrId)
      const violations: SecurityViolation[] = []

      if (!policy || !policy.isActive) {
        return { allowed: true } // No policy or inactive policy allows access
      }

      // Check IP restrictions
      if (!this.isIpAllowed(ipAddress, policy)) {
        const violation = await this.createSecurityViolation(
          vdrId,
          userId,
          'IP_RESTRICTION',
          'HIGH',
          `Access denied from IP address: ${ipAddress}`,
          { ipAddress, userAgent }
        )
        violations.push(violation)
        return { allowed: false, reason: 'IP address not allowed', violations }
      }

      // Check time restrictions
      if (!this.isTimeAllowed(policy)) {
        const violation = await this.createSecurityViolation(
          vdrId,
          userId,
          'TIME_RESTRICTION',
          'MEDIUM',
          'Access denied outside allowed time window',
          { currentTime: new Date(), policy: { accessStartTime: policy.accessStartTime, accessEndTime: policy.accessEndTime } }
        )
        violations.push(violation)
        return { allowed: false, reason: 'Access not allowed at this time', violations }
      }

      // Check action-specific permissions
      switch (action) {
        case 'download':
          if (!policy.allowDownload) {
            return { allowed: false, reason: 'Download not permitted' }
          }
          break
        case 'print':
          if (!policy.allowPrint) {
            return { allowed: false, reason: 'Print not permitted' }
          }
          break
        case 'copy':
          if (!policy.allowCopy) {
            return { allowed: false, reason: 'Copy not permitted' }
          }
          break
      }

      // Check session limits
      const activeSessions = await this.getActiveSessionCount(vdrId, userId)
      if (activeSessions >= policy.maxConcurrentSessions) {
        const violation = await this.createSecurityViolation(
          vdrId,
          userId,
          'SESSION_LIMIT',
          'MEDIUM',
          `Maximum concurrent sessions exceeded: ${activeSessions}/${policy.maxConcurrentSessions}`,
          { activeSessions, maxAllowed: policy.maxConcurrentSessions }
        )
        violations.push(violation)
        return { allowed: false, reason: 'Maximum concurrent sessions exceeded', violations }
      }

      return { allowed: true, violations: violations.length > 0 ? violations : undefined }
    } catch (error) {
      this.logger.error('Failed to validate access', error, { vdrId, userId, action })
      return { allowed: false, reason: 'Security validation failed' }
    }
  }

  /**
   * Create secure session
   */
  async createSession(
    vdrId: string,
    userId: string,
    ipAddress: string,
    userAgent: string
  ): Promise<{ sessionToken: string; expiresAt: Date }> {
    try {
      const policy = await this.getSecurityPolicy(vdrId)
      const sessionTimeout = policy?.sessionTimeout || 30

      const sessionId = crypto.randomUUID()
      const expiresAt = new Date(Date.now() + sessionTimeout * 60 * 1000)

      const sessionInfo: SessionInfo = {
        sessionId,
        vdrId,
        userId,
        ipAddress,
        userAgent,
        startTime: new Date(),
        lastActivity: new Date(),
        expiresAt,
        isActive: true
      }

      // Store session
      await this.cache.set(`vdr:session:${sessionId}`, sessionInfo, sessionTimeout * 60)

      // Create JWT token
      const sessionToken = jwt.sign(
        {
          sessionId,
          vdrId,
          userId,
          exp: Math.floor(expiresAt.getTime() / 1000)
        },
        this.jwtSecret
      )

      this.logger.info('Secure session created', { vdrId, userId, sessionId })

      return { sessionToken, expiresAt }
    } catch (error) {
      this.logger.error('Failed to create session', error, { vdrId, userId })
      throw error
    }
  }

  /**
   * Validate session token
   */
  async validateSession(sessionToken: string): Promise<SessionInfo | null> {
    try {
      // Verify JWT
      const decoded = jwt.verify(sessionToken, this.jwtSecret) as any
      
      // Get session from cache
      const sessionInfo = await this.cache.get<SessionInfo>(`vdr:session:${decoded.sessionId}`)
      
      if (!sessionInfo || !sessionInfo.isActive) {
        return null
      }

      // Check expiration
      if (sessionInfo.expiresAt < new Date()) {
        await this.invalidateSession(decoded.sessionId)
        return null
      }

      // Update last activity
      sessionInfo.lastActivity = new Date()
      await this.cache.set(`vdr:session:${decoded.sessionId}`, sessionInfo, 
        Math.floor((sessionInfo.expiresAt.getTime() - Date.now()) / 1000))

      return sessionInfo
    } catch (error) {
      this.logger.error('Failed to validate session', error)
      return null
    }
  }

  /**
   * Invalidate session
   */
  async invalidateSession(sessionId: string): Promise<void> {
    try {
      const sessionInfo = await this.cache.get<SessionInfo>(`vdr:session:${sessionId}`)
      
      if (sessionInfo) {
        sessionInfo.isActive = false
        await this.cache.set(`vdr:session:${sessionId}`, sessionInfo, 60) // Keep for 1 minute for audit
      }

      this.logger.info('Session invalidated', { sessionId })
    } catch (error) {
      this.logger.error('Failed to invalidate session', error, { sessionId })
    }
  }

  /**
   * Apply watermark to document
   */
  async applyWatermark(
    documentBuffer: Buffer,
    mimeType: string,
    vdrId: string,
    userEmail: string
  ): Promise<Buffer> {
    try {
      const policy = await this.getSecurityPolicy(vdrId)
      
      if (!policy?.watermarkEnabled) {
        return documentBuffer
      }

      // In a real implementation, this would use image processing libraries
      // to apply watermarks to PDFs, images, etc.
      this.logger.info('Watermark would be applied', {
        vdrId,
        userEmail,
        mimeType,
        watermarkOptions: policy.watermarkOptions
      })

      // For now, return the original buffer
      // In production, use libraries like pdf-lib, sharp, or canvas
      return documentBuffer
    } catch (error) {
      this.logger.error('Failed to apply watermark', error, { vdrId, userEmail })
      return documentBuffer
    }
  }

  /**
   * Get security violations
   */
  async getSecurityViolations(
    vdrId: string,
    resolved?: boolean,
    severity?: SecurityViolation['severity']
  ): Promise<SecurityViolation[]> {
    try {
      // In a real implementation, this would fetch from database
      const cacheKey = `vdr:violations:${vdrId}`
      const violations = await this.cache.get<SecurityViolation[]>(cacheKey) || []

      return violations.filter(violation => {
        if (resolved !== undefined && violation.resolved !== resolved) return false
        if (severity && violation.severity !== severity) return false
        return true
      })
    } catch (error) {
      this.logger.error('Failed to get security violations', error, { vdrId })
      return []
    }
  }

  /**
   * Get security policy for VDR
   */
  private async getSecurityPolicy(vdrId: string): Promise<SecurityPolicy | null> {
    try {
      return await this.cache.get<SecurityPolicy>(`vdr:security:${vdrId}`)
    } catch (error) {
      this.logger.error('Failed to get security policy', error, { vdrId })
      return null
    }
  }

  /**
   * Check if IP address is allowed
   */
  private isIpAllowed(ipAddress: string, policy: SecurityPolicy): boolean {
    // Check blocked IPs first
    if (policy.blockedIpRanges.length > 0) {
      for (const range of policy.blockedIpRanges) {
        if (this.isIpInRange(ipAddress, range)) {
          return false
        }
      }
    }

    // If no allowed ranges specified, allow all (except blocked)
    if (policy.allowedIpRanges.length === 0) {
      return true
    }

    // Check allowed ranges
    for (const range of policy.allowedIpRanges) {
      if (this.isIpInRange(ipAddress, range)) {
        return true
      }
    }

    return false
  }

  /**
   * Check if current time is within allowed window
   */
  private isTimeAllowed(policy: SecurityPolicy): boolean {
    const now = new Date()
    const currentDay = now.getDay() // 0 = Sunday, 1 = Monday, etc.
    const currentTime = now.getHours() * 60 + now.getMinutes()

    // Check allowed days
    if (!policy.allowedDays.includes(currentDay)) {
      return false
    }

    // Check time window
    if (policy.accessStartTime && policy.accessEndTime) {
      const [startHour, startMin] = policy.accessStartTime.split(':').map(Number)
      const [endHour, endMin] = policy.accessEndTime.split(':').map(Number)
      
      const startTime = startHour * 60 + startMin
      const endTime = endHour * 60 + endMin

      if (currentTime < startTime || currentTime > endTime) {
        return false
      }
    }

    return true
  }

  /**
   * Check if IP is in range (simplified implementation)
   */
  private isIpInRange(ip: string, range: string): boolean {
    // Simplified IP range checking
    // In production, use a proper CIDR library
    if (range.includes('/')) {
      // CIDR notation
      return ip.startsWith(range.split('/')[0].split('.').slice(0, 3).join('.'))
    } else {
      // Exact match or wildcard
      return ip === range || range === '*'
    }
  }

  /**
   * Get active session count for user
   */
  private async getActiveSessionCount(vdrId: string, userId: string): Promise<number> {
    try {
      // In a real implementation, this would query active sessions from cache/database
      const pattern = `vdr:session:*`
      const sessionKeys = await this.cache.getKeys(pattern)
      
      let count = 0
      for (const key of sessionKeys) {
        const session = await this.cache.get<SessionInfo>(key)
        if (session && session.vdrId === vdrId && session.userId === userId && session.isActive) {
          count++
        }
      }
      
      return count
    } catch (error) {
      this.logger.error('Failed to get active session count', error)
      return 0
    }
  }

  /**
   * Create security violation record
   */
  private async createSecurityViolation(
    vdrId: string,
    userId: string,
    violationType: SecurityViolation['violationType'],
    severity: SecurityViolation['severity'],
    description: string,
    details: any
  ): Promise<SecurityViolation> {
    const violation: SecurityViolation = {
      id: crypto.randomUUID(),
      vdrId,
      userId,
      violationType,
      severity,
      description,
      details,
      ipAddress: details.ipAddress,
      userAgent: details.userAgent,
      timestamp: new Date(),
      resolved: false
    }

    // Store violation (in real implementation, this would be in database)
    const existingViolations = await this.cache.get<SecurityViolation[]>(`vdr:violations:${vdrId}`) || []
    existingViolations.push(violation)
    await this.cache.set(`vdr:violations:${vdrId}`, existingViolations, 86400) // 24 hours

    this.logger.warn('Security violation created', violation)

    return violation
  }

  /**
   * Check admin permission
   */
  private async checkAdminPermission(vdrId: string, userId: string): Promise<boolean> {
    try {
      const vdrUser = await this.prisma.vDRUser.findFirst({
        where: {
          vdrId,
          OR: [
            { userId },
            { email: await this.getUserEmail(userId) }
          ],
          role: 'ADMIN',
          status: 'ACTIVE'
        }
      })

      return !!vdrUser
    } catch (error) {
      this.logger.error('Failed to check admin permission', error)
      return false
    }
  }

  /**
   * Get user email
   */
  private async getUserEmail(userId: string): Promise<string> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { email: true }
    })
    return user?.email || ''
  }
}
