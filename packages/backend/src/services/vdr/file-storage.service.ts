import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import * as fs from 'fs/promises'
import * as path from 'path'
import * as crypto from 'crypto'
import { Readable } from 'stream'

export interface FileUploadOptions {
  vdrId: string
  folderId?: string
  originalName: string
  mimeType: string
  size: number
  buffer: Buffer
  userId: string
  tenantId: string
  encrypt?: boolean
  tags?: string[]
  description?: string
}

export interface FileDownloadOptions {
  documentId: string
  userId: string
  tenantId: string
  trackAccess?: boolean
}

export interface StorageProvider {
  upload(key: string, buffer: Buffer, options?: any): Promise<string>
  download(key: string): Promise<Buffer>
  delete(key: string): Promise<void>
  getSignedUrl(key: string, expiresIn?: number): Promise<string>
}

export class LocalStorageProvider implements StorageProvider {
  private basePath: string

  constructor(basePath: string = './uploads') {
    this.basePath = basePath
  }

  async upload(key: string, buffer: Buffer): Promise<string> {
    const filePath = path.join(this.basePath, key)
    const dir = path.dirname(filePath)
    
    // Ensure directory exists
    await fs.mkdir(dir, { recursive: true })
    
    // Write file
    await fs.writeFile(filePath, buffer)
    
    return filePath
  }

  async download(key: string): Promise<Buffer> {
    const filePath = path.join(this.basePath, key)
    return fs.readFile(filePath)
  }

  async delete(key: string): Promise<void> {
    const filePath = path.join(this.basePath, key)
    await fs.unlink(filePath)
  }

  async getSignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    // For local storage, return a simple path
    // In production, this would generate a signed URL
    return `/api/vdr/files/${key}`
  }
}

export class VDRFileStorageService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger
  private storageProvider: StorageProvider
  private encryptionKey: string

  constructor(
    prisma: PrismaClient, 
    cache: CacheService,
    storageProvider?: StorageProvider,
    encryptionKey?: string
  ) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('VDRFileStorageService')
    this.storageProvider = storageProvider || new LocalStorageProvider()
    this.encryptionKey = encryptionKey || process.env.VDR_ENCRYPTION_KEY || 'default-key'
  }

  /**
   * Upload a file to VDR
   */
  async uploadFile(options: FileUploadOptions): Promise<any> {
    try {
      this.logger.info('Uploading file to VDR', { 
        vdrId: options.vdrId, 
        fileName: options.originalName,
        size: options.size 
      })

      // Validate VDR access
      await this.validateVDRAccess(options.vdrId, options.userId, options.tenantId)

      // Generate unique storage key
      const fileExtension = path.extname(options.originalName)
      const storageKey = this.generateStorageKey(options.vdrId, fileExtension)
      
      // Encrypt file if required
      let fileBuffer = options.buffer
      let isEncrypted = false
      
      if (options.encrypt !== false) { // Default to encrypted
        fileBuffer = this.encryptBuffer(options.buffer)
        isEncrypted = true
      }

      // Calculate checksum for integrity
      const checksum = this.calculateChecksum(options.buffer)

      // Upload to storage provider
      const storagePath = await this.storageProvider.upload(storageKey, fileBuffer)

      // Create database record
      const document = await this.prisma.vDRDocument.create({
        data: {
          name: this.sanitizeFileName(options.originalName),
          originalName: options.originalName,
          mimeType: options.mimeType,
          size: options.size,
          storageProvider: 'local', // This would be configurable
          storagePath,
          storageKey,
          checksum,
          isEncrypted,
          vdrId: options.vdrId,
          folderId: options.folderId,
          uploadedBy: options.userId,
          tenantId: options.tenantId,
          tags: options.tags || [],
          description: options.description,
          accessLevel: 'RESTRICTED' // Default access level
        },
        include: {
          vdr: true,
          folder: true,
          uploader: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      // Log activity
      await this.logActivity(options.vdrId, options.userId, 'UPLOAD_DOCUMENT', {
        documentId: document.id,
        fileName: options.originalName,
        size: options.size
      })

      // Clear cache
      await this.clearVDRCache(options.vdrId)

      this.logger.info('File uploaded successfully', { 
        documentId: document.id,
        storageKey 
      })

      return document
    } catch (error) {
      this.logger.error('Failed to upload file', error, options)
      throw error
    }
  }

  /**
   * Download a file from VDR
   */
  async downloadFile(options: FileDownloadOptions): Promise<{ buffer: Buffer; document: any }> {
    try {
      this.logger.info('Downloading file from VDR', { 
        documentId: options.documentId,
        userId: options.userId 
      })

      // Get document with permissions check
      const document = await this.getDocumentWithPermissions(
        options.documentId, 
        options.userId, 
        options.tenantId
      )

      if (!document) {
        throw new Error('Document not found or access denied')
      }

      // Check download permissions
      if (!await this.canDownload(document.vdrId, options.userId)) {
        throw new Error('Download not permitted')
      }

      // Download from storage
      let fileBuffer = await this.storageProvider.download(document.storageKey)

      // Decrypt if encrypted
      if (document.isEncrypted) {
        fileBuffer = this.decryptBuffer(fileBuffer)
      }

      // Verify integrity
      const checksum = this.calculateChecksum(fileBuffer)
      if (checksum !== document.checksum) {
        this.logger.error('File integrity check failed', { 
          documentId: options.documentId,
          expectedChecksum: document.checksum,
          actualChecksum: checksum
        })
        throw new Error('File integrity verification failed')
      }

      // Update download count and last accessed
      await this.prisma.vDRDocument.update({
        where: { id: options.documentId },
        data: {
          downloadCount: { increment: 1 },
          lastViewedAt: new Date()
        }
      })

      // Log activity if tracking enabled
      if (options.trackAccess !== false) {
        await this.logActivity(document.vdrId, options.userId, 'DOWNLOAD_DOCUMENT', {
          documentId: options.documentId,
          fileName: document.originalName
        })
      }

      this.logger.info('File downloaded successfully', { 
        documentId: options.documentId 
      })

      return { buffer: fileBuffer, document }
    } catch (error) {
      this.logger.error('Failed to download file', error, options)
      throw error
    }
  }

  /**
   * Delete a file from VDR
   */
  async deleteFile(documentId: string, userId: string, tenantId: string): Promise<void> {
    try {
      this.logger.info('Deleting file from VDR', { documentId, userId })

      // Get document with permissions check
      const document = await this.getDocumentWithPermissions(documentId, userId, tenantId)

      if (!document) {
        throw new Error('Document not found or access denied')
      }

      // Check if user can delete (admin or uploader)
      if (!await this.canDelete(document.vdrId, userId) && document.uploadedBy !== userId) {
        throw new Error('Delete not permitted')
      }

      // Delete from storage
      await this.storageProvider.delete(document.storageKey)

      // Soft delete from database (mark as deleted)
      await this.prisma.vDRDocument.update({
        where: { id: documentId },
        data: { 
          status: 'DELETED',
          updatedAt: new Date()
        }
      })

      // Log activity
      await this.logActivity(document.vdrId, userId, 'DELETE_DOCUMENT', {
        documentId,
        fileName: document.originalName
      })

      // Clear cache
      await this.clearVDRCache(document.vdrId)

      this.logger.info('File deleted successfully', { documentId })
    } catch (error) {
      this.logger.error('Failed to delete file', error, { documentId, userId })
      throw error
    }
  }

  /**
   * Get signed URL for file access
   */
  async getSignedUrl(documentId: string, userId: string, tenantId: string, expiresIn: number = 3600): Promise<string> {
    try {
      // Get document with permissions check
      const document = await this.getDocumentWithPermissions(documentId, userId, tenantId)

      if (!document) {
        throw new Error('Document not found or access denied')
      }

      // Generate signed URL
      const signedUrl = await this.storageProvider.getSignedUrl(document.storageKey, expiresIn)

      // Log activity
      await this.logActivity(document.vdrId, userId, 'VIEW_DOCUMENT', {
        documentId,
        fileName: document.originalName,
        accessMethod: 'signed_url'
      })

      return signedUrl
    } catch (error) {
      this.logger.error('Failed to generate signed URL', error, { documentId, userId })
      throw error
    }
  }

  /**
   * Validate VDR access for user
   */
  private async validateVDRAccess(vdrId: string, userId: string, tenantId: string): Promise<void> {
    const vdrUser = await this.prisma.vDRUser.findFirst({
      where: {
        vdrId,
        OR: [
          { userId },
          { email: await this.getUserEmail(userId) }
        ],
        status: 'ACTIVE'
      }
    })

    if (!vdrUser) {
      throw new Error('VDR access denied')
    }
  }

  /**
   * Get document with permission validation
   */
  private async getDocumentWithPermissions(documentId: string, userId: string, tenantId: string) {
    return this.prisma.vDRDocument.findFirst({
      where: {
        id: documentId,
        tenantId,
        status: { not: 'DELETED' },
        vdr: {
          users: {
            some: {
              OR: [
                { userId },
                { email: await this.getUserEmail(userId) }
              ],
              status: 'ACTIVE'
            }
          }
        }
      },
      include: {
        vdr: true,
        folder: true,
        uploader: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })
  }

  /**
   * Check if user can download files
   */
  private async canDownload(vdrId: string, userId: string): Promise<boolean> {
    const vdrUser = await this.prisma.vDRUser.findFirst({
      where: {
        vdrId,
        OR: [
          { userId },
          { email: await this.getUserEmail(userId) }
        ],
        status: 'ACTIVE'
      }
    })

    return vdrUser?.canDownload || false
  }

  /**
   * Check if user can delete files
   */
  private async canDelete(vdrId: string, userId: string): Promise<boolean> {
    const vdrUser = await this.prisma.vDRUser.findFirst({
      where: {
        vdrId,
        OR: [
          { userId },
          { email: await this.getUserEmail(userId) }
        ],
        status: 'ACTIVE',
        role: { in: ['ADMIN', 'MANAGER'] }
      }
    })

    return !!vdrUser
  }

  /**
   * Get user email
   */
  private async getUserEmail(userId: string): Promise<string> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { email: true }
    })
    return user?.email || ''
  }

  /**
   * Generate unique storage key
   */
  private generateStorageKey(vdrId: string, extension: string): string {
    const timestamp = Date.now()
    const random = crypto.randomBytes(8).toString('hex')
    return `vdr/${vdrId}/${timestamp}-${random}${extension}`
  }

  /**
   * Sanitize file name
   */
  private sanitizeFileName(fileName: string): string {
    return fileName.replace(/[^a-zA-Z0-9.-]/g, '_')
  }

  /**
   * Calculate file checksum
   */
  private calculateChecksum(buffer: Buffer): string {
    return crypto.createHash('sha256').update(buffer).digest('hex')
  }

  /**
   * Encrypt buffer
   */
  private encryptBuffer(buffer: Buffer): Buffer {
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey)
    return Buffer.concat([cipher.update(buffer), cipher.final()])
  }

  /**
   * Decrypt buffer
   */
  private decryptBuffer(buffer: Buffer): Buffer {
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey)
    return Buffer.concat([decipher.update(buffer), decipher.final()])
  }

  /**
   * Log VDR activity
   */
  private async logActivity(vdrId: string, userId: string, action: string, details?: any): Promise<void> {
    try {
      // Get VDR user ID
      const vdrUser = await this.prisma.vDRUser.findFirst({
        where: {
          vdrId,
          OR: [
            { userId },
            { email: await this.getUserEmail(userId) }
          ]
        }
      })

      if (vdrUser) {
        await this.prisma.vDRActivity.create({
          data: {
            vdrId,
            userId: vdrUser.id,
            action: action as any,
            details,
            documentId: details?.documentId,
            folderId: details?.folderId
          }
        })
      }
    } catch (error) {
      this.logger.error('Failed to log VDR activity', error)
    }
  }

  /**
   * Clear VDR cache
   */
  private async clearVDRCache(vdrId: string): Promise<void> {
    try {
      await this.cache.deletePattern(`vdr:${vdrId}:*`)
    } catch (error) {
      this.logger.error('Failed to clear VDR cache', error)
    }
  }
}
