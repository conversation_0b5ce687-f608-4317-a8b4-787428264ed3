import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { PrismaClient } from '@prisma/client'
import { EventEmitter } from 'events'
import { Server as SocketIOServer } from 'socket.io'

export interface Message {
  id: string
  conversationId: string
  senderId: string
  content: string
  type: 'TEXT' | 'FILE' | 'IMAGE' | 'SYSTEM' | 'ANNOUNCEMENT'
  
  // Message metadata
  timestamp: Date
  editedAt?: Date
  deletedAt?: Date
  
  // Delivery tracking
  deliveryStatus: 'SENT' | 'DELIVERED' | 'READ'
  readBy: Array<{
    userId: string
    readAt: Date
  }>
  
  // File attachments
  attachments?: Array<{
    id: string
    name: string
    url: string
    size: number
    mimeType: string
  }>
  
  // Message threading
  replyToId?: string
  threadId?: string
  
  // Reactions
  reactions: Array<{
    userId: string
    emoji: string
    timestamp: Date
  }>
  
  // Metadata
  metadata?: Record<string, any>
  tenantId: string
}

export interface Conversation {
  id: string
  type: 'DIRECT' | 'GROUP' | 'CHANNEL' | 'DEAL_ROOM'
  name?: string
  description?: string
  
  // Participants
  participants: Array<{
    userId: string
    role: 'ADMIN' | 'MODERATOR' | 'MEMBER'
    joinedAt: Date
    lastReadAt?: Date
    notificationSettings: {
      muted: boolean
      muteUntil?: Date
    }
  }>
  
  // Deal association
  dealId?: string
  
  // Conversation settings
  settings: {
    isPrivate: boolean
    allowFileSharing: boolean
    allowExternalUsers: boolean
    retentionDays?: number
    requireApproval: boolean
  }
  
  // Status
  isActive: boolean
  isArchived: boolean
  
  // Last activity
  lastMessageId?: string
  lastActivityAt: Date
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt?: Date
  tenantId: string
}

export interface TypingIndicator {
  conversationId: string
  userId: string
  timestamp: Date
}

export interface MessageDeliveryReceipt {
  messageId: string
  userId: string
  status: 'DELIVERED' | 'READ'
  timestamp: Date
}

export class MessagingService extends EventEmitter {
  private logger: Logger
  private cache: CacheService
  private prisma: PrismaClient
  private io?: SocketIOServer
  private activeConnections: Map<string, Set<string>> // userId -> socketIds
  private typingIndicators: Map<string, TypingIndicator[]> // conversationId -> typing users

  constructor(cache: CacheService, prisma: PrismaClient) {
    super()
    this.logger = new Logger('MessagingService')
    this.cache = cache
    this.prisma = prisma
    this.activeConnections = new Map()
    this.typingIndicators = new Map()
  }

  /**
   * Initialize WebSocket server
   */
  initializeWebSocket(io: SocketIOServer): void {
    this.io = io
    
    io.on('connection', (socket) => {
      this.handleConnection(socket)
    })

    this.logger.info('WebSocket server initialized for messaging')
  }

  /**
   * Send message
   */
  async sendMessage(
    conversationId: string,
    senderId: string,
    content: string,
    type: Message['type'] = 'TEXT',
    attachments?: Message['attachments'],
    replyToId?: string
  ): Promise<Message> {
    try {
      // Validate conversation and permissions
      const conversation = await this.getConversation(conversationId)
      if (!conversation) {
        throw new Error('Conversation not found')
      }

      if (!this.canUserSendMessage(senderId, conversation)) {
        throw new Error('User not authorized to send messages in this conversation')
      }

      // Create message
      const message: Message = {
        id: this.generateId(),
        conversationId,
        senderId,
        content,
        type,
        timestamp: new Date(),
        deliveryStatus: 'SENT',
        readBy: [],
        attachments: attachments || [],
        replyToId,
        reactions: [],
        tenantId: conversation.tenantId
      }

      // Save message
      await this.saveMessage(message)

      // Update conversation
      await this.updateConversationActivity(conversationId, message.id)

      // Send real-time notifications
      await this.broadcastMessage(message, conversation)

      // Send push notifications to offline users
      await this.sendPushNotifications(message, conversation)

      this.logger.info('Message sent', {
        messageId: message.id,
        conversationId,
        senderId,
        type
      })

      return message
    } catch (error) {
      this.logger.error('Failed to send message', error)
      throw error
    }
  }

  /**
   * Create conversation
   */
  async createConversation(
    type: Conversation['type'],
    creatorId: string,
    tenantId: string,
    options: {
      name?: string
      description?: string
      participantIds: string[]
      dealId?: string
      settings?: Partial<Conversation['settings']>
    }
  ): Promise<Conversation> {
    try {
      const conversation: Conversation = {
        id: this.generateId(),
        type,
        name: options.name,
        description: options.description,
        participants: [
          {
            userId: creatorId,
            role: 'ADMIN',
            joinedAt: new Date(),
            notificationSettings: { muted: false }
          },
          ...options.participantIds.map(userId => ({
            userId,
            role: 'MEMBER' as const,
            joinedAt: new Date(),
            notificationSettings: { muted: false }
          }))
        ],
        dealId: options.dealId,
        settings: {
          isPrivate: type === 'DIRECT',
          allowFileSharing: true,
          allowExternalUsers: false,
          requireApproval: false,
          ...options.settings
        },
        isActive: true,
        isArchived: false,
        lastActivityAt: new Date(),
        createdBy: creatorId,
        createdAt: new Date(),
        tenantId
      }

      // Save conversation
      await this.saveConversation(conversation)

      // Notify participants
      await this.notifyConversationCreated(conversation)

      this.logger.info('Conversation created', {
        conversationId: conversation.id,
        type,
        creatorId,
        participantCount: conversation.participants.length
      })

      return conversation
    } catch (error) {
      this.logger.error('Failed to create conversation', error)
      throw error
    }
  }

  /**
   * Get user conversations
   */
  async getUserConversations(userId: string, tenantId: string): Promise<Conversation[]> {
    try {
      const cacheKey = `user-conversations:${userId}:${tenantId}`
      const cached = await this.cache.get<Conversation[]>(cacheKey)
      
      if (cached) {
        return cached
      }

      // In a real implementation, this would query the database
      const conversations: Conversation[] = []

      await this.cache.set(cacheKey, conversations, 300) // Cache for 5 minutes
      
      return conversations
    } catch (error) {
      this.logger.error('Failed to get user conversations', error)
      return []
    }
  }

  /**
   * Get conversation messages
   */
  async getConversationMessages(
    conversationId: string,
    userId: string,
    options: {
      limit?: number
      offset?: number
      before?: Date
      after?: Date
    } = {}
  ): Promise<{ messages: Message[]; hasMore: boolean }> {
    try {
      // Validate user access
      const conversation = await this.getConversation(conversationId)
      if (!conversation || !this.canUserAccessConversation(userId, conversation)) {
        throw new Error('Access denied')
      }

      const limit = options.limit || 50
      const offset = options.offset || 0

      // In a real implementation, this would query the database
      const messages: Message[] = []

      return {
        messages,
        hasMore: messages.length === limit
      }
    } catch (error) {
      this.logger.error('Failed to get conversation messages', error)
      throw error
    }
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(messageId: string, userId: string): Promise<void> {
    try {
      const message = await this.getMessage(messageId)
      if (!message) {
        throw new Error('Message not found')
      }

      // Check if already read
      const alreadyRead = message.readBy.some(r => r.userId === userId)
      if (alreadyRead) {
        return
      }

      // Add read receipt
      message.readBy.push({
        userId,
        readAt: new Date()
      })

      // Update delivery status if all participants have read
      const conversation = await this.getConversation(message.conversationId)
      if (conversation) {
        const allParticipants = conversation.participants.map(p => p.userId)
        const allRead = allParticipants.every(participantId => 
          participantId === message.senderId || 
          message.readBy.some(r => r.userId === participantId)
        )

        if (allRead) {
          message.deliveryStatus = 'READ'
        }
      }

      await this.saveMessage(message)

      // Send read receipt
      await this.sendReadReceipt(message, userId)

      this.logger.debug('Message marked as read', {
        messageId,
        userId
      })
    } catch (error) {
      this.logger.error('Failed to mark message as read', error)
      throw error
    }
  }

  /**
   * Handle typing indicators
   */
  async handleTyping(conversationId: string, userId: string, isTyping: boolean): Promise<void> {
    try {
      const indicators = this.typingIndicators.get(conversationId) || []
      
      if (isTyping) {
        // Add or update typing indicator
        const existingIndex = indicators.findIndex(i => i.userId === userId)
        const indicator: TypingIndicator = {
          conversationId,
          userId,
          timestamp: new Date()
        }

        if (existingIndex >= 0) {
          indicators[existingIndex] = indicator
        } else {
          indicators.push(indicator)
        }
      } else {
        // Remove typing indicator
        const filteredIndicators = indicators.filter(i => i.userId !== userId)
        this.typingIndicators.set(conversationId, filteredIndicators)
      }

      this.typingIndicators.set(conversationId, indicators)

      // Broadcast typing status
      await this.broadcastTypingStatus(conversationId, userId, isTyping)

      // Auto-remove typing indicator after 5 seconds
      if (isTyping) {
        setTimeout(() => {
          this.handleTyping(conversationId, userId, false)
        }, 5000)
      }
    } catch (error) {
      this.logger.error('Failed to handle typing indicator', error)
    }
  }

  /**
   * Private helper methods
   */
  private handleConnection(socket: any): void {
    const userId = socket.handshake.auth.userId
    const tenantId = socket.handshake.auth.tenantId

    if (!userId || !tenantId) {
      socket.disconnect()
      return
    }

    // Track connection
    if (!this.activeConnections.has(userId)) {
      this.activeConnections.set(userId, new Set())
    }
    this.activeConnections.get(userId)!.add(socket.id)

    // Join user to their conversation rooms
    this.joinUserRooms(socket, userId, tenantId)

    // Handle events
    socket.on('send_message', async (data: any) => {
      try {
        await this.sendMessage(
          data.conversationId,
          userId,
          data.content,
          data.type,
          data.attachments,
          data.replyToId
        )
      } catch (error) {
        socket.emit('error', { message: 'Failed to send message' })
      }
    })

    socket.on('typing', async (data: any) => {
      await this.handleTyping(data.conversationId, userId, data.isTyping)
    })

    socket.on('mark_read', async (data: any) => {
      await this.markMessageAsRead(data.messageId, userId)
    })

    socket.on('disconnect', () => {
      // Remove connection
      const userConnections = this.activeConnections.get(userId)
      if (userConnections) {
        userConnections.delete(socket.id)
        if (userConnections.size === 0) {
          this.activeConnections.delete(userId)
        }
      }
    })

    this.logger.debug('User connected to messaging', { userId, socketId: socket.id })
  }

  private async joinUserRooms(socket: any, userId: string, tenantId: string): Promise<void> {
    try {
      const conversations = await this.getUserConversations(userId, tenantId)
      
      for (const conversation of conversations) {
        socket.join(`conversation:${conversation.id}`)
      }
    } catch (error) {
      this.logger.error('Failed to join user rooms', error)
    }
  }

  private async broadcastMessage(message: Message, conversation: Conversation): Promise<void> {
    if (!this.io) return

    this.io.to(`conversation:${message.conversationId}`).emit('new_message', {
      message,
      conversation
    })
  }

  private async broadcastTypingStatus(conversationId: string, userId: string, isTyping: boolean): Promise<void> {
    if (!this.io) return

    this.io.to(`conversation:${conversationId}`).emit('typing_status', {
      conversationId,
      userId,
      isTyping
    })
  }

  private async sendReadReceipt(message: Message, userId: string): Promise<void> {
    if (!this.io) return

    this.io.to(`conversation:${message.conversationId}`).emit('message_read', {
      messageId: message.id,
      userId,
      readAt: new Date()
    })
  }

  private async sendPushNotifications(message: Message, conversation: Conversation): Promise<void> {
    // Send push notifications to offline users
    for (const participant of conversation.participants) {
      if (participant.userId === message.senderId) continue
      if (participant.notificationSettings.muted) continue

      const isOnline = this.activeConnections.has(participant.userId)
      if (!isOnline) {
        // Send push notification
        this.emit('push_notification', {
          userId: participant.userId,
          title: conversation.name || 'New Message',
          body: message.content,
          data: {
            conversationId: conversation.id,
            messageId: message.id
          }
        })
      }
    }
  }

  private canUserSendMessage(userId: string, conversation: Conversation): boolean {
    const participant = conversation.participants.find(p => p.userId === userId)
    return !!participant && conversation.isActive
  }

  private canUserAccessConversation(userId: string, conversation: Conversation): boolean {
    return conversation.participants.some(p => p.userId === userId)
  }

  private async saveMessage(message: Message): Promise<void> {
    // In a real implementation, this would save to database
    await this.cache.set(`message:${message.id}`, message, 86400)
  }

  private async saveConversation(conversation: Conversation): Promise<void> {
    // In a real implementation, this would save to database
    await this.cache.set(`conversation:${conversation.id}`, conversation, 86400)
  }

  private async getMessage(messageId: string): Promise<Message | null> {
    return await this.cache.get<Message>(`message:${messageId}`)
  }

  private async getConversation(conversationId: string): Promise<Conversation | null> {
    return await this.cache.get<Conversation>(`conversation:${conversationId}`)
  }

  private async updateConversationActivity(conversationId: string, lastMessageId: string): Promise<void> {
    const conversation = await this.getConversation(conversationId)
    if (conversation) {
      conversation.lastMessageId = lastMessageId
      conversation.lastActivityAt = new Date()
      await this.saveConversation(conversation)
    }
  }

  private async notifyConversationCreated(conversation: Conversation): Promise<void> {
    if (!this.io) return

    for (const participant of conversation.participants) {
      this.io.to(`user:${participant.userId}`).emit('conversation_created', conversation)
    }
  }

  private generateId(): string {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
