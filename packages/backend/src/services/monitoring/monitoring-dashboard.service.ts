import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { EventEmitter } from 'events'
import { APMService, PerformanceMetrics } from './apm.service'
import { InfrastructureMonitoringService, SystemMetrics } from './infrastructure.service'
import { ErrorTrackingService, ErrorStats } from './error-tracking.service'
import { LogAggregationService, LogStats } from './log-aggregation.service'
import { HealthCheckService, SystemHealth } from './health-check.service'

export interface MonitoringDashboard {
  timestamp: Date
  
  // Overall system status
  systemStatus: 'healthy' | 'degraded' | 'unhealthy'
  uptime: number
  
  // Performance metrics
  performance: PerformanceMetrics
  
  // Infrastructure metrics
  infrastructure: SystemMetrics
  
  // Error tracking
  errors: ErrorStats
  
  // Logging stats
  logs: LogStats
  
  // Health checks
  health: SystemHealth
  
  // Alerts summary
  alerts: {
    critical: number
    high: number
    medium: number
    low: number
    total: number
  }
  
  // Trends
  trends: {
    responseTime: Array<{ timestamp: Date; value: number }>
    errorRate: Array<{ timestamp: Date; value: number }>
    throughput: Array<{ timestamp: Date; value: number }>
    cpuUsage: Array<{ timestamp: Date; value: number }>
    memoryUsage: Array<{ timestamp: Date; value: number }>
  }
}

export interface AlertRule {
  id: string
  name: string
  metric: string
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte'
  threshold: number
  duration: number // seconds
  severity: 'low' | 'medium' | 'high' | 'critical'
  enabled: boolean
  channels: string[]
  tags: Record<string, string>
}

export interface Alert {
  id: string
  ruleId: string
  ruleName: string
  metric: string
  value: number
  threshold: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  timestamp: Date
  status: 'active' | 'resolved' | 'acknowledged'
  acknowledgedBy?: string
  acknowledgedAt?: Date
  resolvedAt?: Date
  tags: Record<string, string>
}

export class MonitoringDashboardService extends EventEmitter {
  private logger: Logger
  private cache: CacheService
  private apmService: APMService
  private infrastructureService: InfrastructureMonitoringService
  private errorTrackingService: ErrorTrackingService
  private logAggregationService: LogAggregationService
  private healthCheckService: HealthCheckService
  
  private alertRules: Map<string, AlertRule>
  private activeAlerts: Map<string, Alert>
  private dashboardHistory: MonitoringDashboard[]

  constructor(
    cache: CacheService,
    apmService: APMService,
    infrastructureService: InfrastructureMonitoringService,
    errorTrackingService: ErrorTrackingService,
    logAggregationService: LogAggregationService,
    healthCheckService: HealthCheckService
  ) {
    super()
    this.logger = new Logger('MonitoringDashboardService')
    this.cache = cache
    this.apmService = apmService
    this.infrastructureService = infrastructureService
    this.errorTrackingService = errorTrackingService
    this.logAggregationService = logAggregationService
    this.healthCheckService = healthCheckService
    
    this.alertRules = new Map()
    this.activeAlerts = new Map()
    this.dashboardHistory = []

    // Initialize default alert rules
    this.initializeDefaultAlertRules()
  }

  /**
   * Get current monitoring dashboard
   */
  async getDashboard(): Promise<MonitoringDashboard> {
    try {
      const [
        performance,
        infrastructure,
        errors,
        logs,
        health
      ] = await Promise.all([
        this.apmService.getCurrentMetrics(),
        this.infrastructureService.getCurrentMetrics(),
        this.errorTrackingService.getErrorStats(),
        this.logAggregationService.getLogStats(),
        this.healthCheckService.performHealthChecks()
      ])

      // Calculate overall system status
      const systemStatus = this.calculateSystemStatus(health, performance, infrastructure, errors)

      // Get alerts summary
      const alerts = this.getAlertsSummary()

      // Get trends
      const trends = await this.getTrends()

      const dashboard: MonitoringDashboard = {
        timestamp: new Date(),
        systemStatus,
        uptime: process.uptime(),
        performance,
        infrastructure,
        errors,
        logs,
        health,
        alerts,
        trends
      }

      // Store in history
      this.dashboardHistory.push(dashboard)
      if (this.dashboardHistory.length > 288) { // Keep 24 hours of 5-minute intervals
        this.dashboardHistory.shift()
      }

      // Cache dashboard
      await this.cache.set('monitoring:dashboard', dashboard, 300) // 5 minutes

      return dashboard
    } catch (error) {
      this.logger.error('Failed to get monitoring dashboard', error)
      throw error
    }
  }

  /**
   * Get dashboard history
   */
  getDashboardHistory(hours: number = 24): MonitoringDashboard[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000)
    return this.dashboardHistory.filter(d => d.timestamp >= cutoff)
  }

  /**
   * Create alert rule
   */
  createAlertRule(rule: Omit<AlertRule, 'id'>): string {
    const id = this.generateId()
    const alertRule: AlertRule = {
      id,
      ...rule
    }

    this.alertRules.set(id, alertRule)
    this.emit('alert:rule_created', alertRule)

    this.logger.info('Created alert rule', {
      id,
      name: rule.name,
      metric: rule.metric,
      threshold: rule.threshold
    })

    return id
  }

  /**
   * Update alert rule
   */
  updateAlertRule(id: string, updates: Partial<AlertRule>): boolean {
    const rule = this.alertRules.get(id)
    if (!rule) return false

    const updatedRule = { ...rule, ...updates }
    this.alertRules.set(id, updatedRule)
    this.emit('alert:rule_updated', updatedRule)

    return true
  }

  /**
   * Delete alert rule
   */
  deleteAlertRule(id: string): boolean {
    const rule = this.alertRules.get(id)
    if (!rule) return false

    this.alertRules.delete(id)
    this.emit('alert:rule_deleted', rule)

    return true
  }

  /**
   * Get all alert rules
   */
  getAlertRules(): AlertRule[] {
    return Array.from(this.alertRules.values())
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.activeAlerts.values()).filter(alert => alert.status === 'active')
  }

  /**
   * Acknowledge alert
   */
  acknowledgeAlert(alertId: string, acknowledgedBy: string): boolean {
    const alert = this.activeAlerts.get(alertId)
    if (!alert) return false

    alert.status = 'acknowledged'
    alert.acknowledgedBy = acknowledgedBy
    alert.acknowledgedAt = new Date()

    this.emit('alert:acknowledged', alert)
    return true
  }

  /**
   * Resolve alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.activeAlerts.get(alertId)
    if (!alert) return false

    alert.status = 'resolved'
    alert.resolvedAt = new Date()

    this.emit('alert:resolved', alert)
    return true
  }

  /**
   * Check alert rules against current metrics
   */
  async checkAlertRules(): Promise<void> {
    try {
      const dashboard = await this.getDashboard()

      for (const rule of this.alertRules.values()) {
        if (!rule.enabled) continue

        const value = this.extractMetricValue(dashboard, rule.metric)
        if (value === undefined) continue

        const shouldAlert = this.evaluateAlertCondition(value, rule.operator, rule.threshold)

        if (shouldAlert) {
          await this.createAlert(rule, value)
        }
      }
    } catch (error) {
      this.logger.error('Failed to check alert rules', error)
    }
  }

  /**
   * Private helper methods
   */
  private calculateSystemStatus(
    health: SystemHealth,
    performance: PerformanceMetrics,
    infrastructure: SystemMetrics,
    errors: ErrorStats
  ): MonitoringDashboard['systemStatus'] {
    // Start with health check status
    let status = health.status

    // Consider performance metrics
    if (performance.averageResponseTime > 2000 || performance.errorRate > 0.1) {
      status = status === 'healthy' ? 'degraded' : status
    }

    // Consider infrastructure metrics
    if (infrastructure.cpuUsage > 90 || infrastructure.memoryUsagePercent > 95) {
      status = 'unhealthy'
    } else if (infrastructure.cpuUsage > 80 || infrastructure.memoryUsagePercent > 85) {
      status = status === 'healthy' ? 'degraded' : status
    }

    // Consider error rate
    if (errors.errorRate > 0.2) {
      status = 'unhealthy'
    } else if (errors.errorRate > 0.05) {
      status = status === 'healthy' ? 'degraded' : status
    }

    return status
  }

  private getAlertsSummary(): MonitoringDashboard['alerts'] {
    const alerts = this.getActiveAlerts()
    
    return {
      critical: alerts.filter(a => a.severity === 'critical').length,
      high: alerts.filter(a => a.severity === 'high').length,
      medium: alerts.filter(a => a.severity === 'medium').length,
      low: alerts.filter(a => a.severity === 'low').length,
      total: alerts.length
    }
  }

  private async getTrends(): Promise<MonitoringDashboard['trends']> {
    const history = this.getDashboardHistory(24)
    
    return {
      responseTime: history.map(d => ({
        timestamp: d.timestamp,
        value: d.performance.averageResponseTime
      })),
      errorRate: history.map(d => ({
        timestamp: d.timestamp,
        value: d.performance.errorRate
      })),
      throughput: history.map(d => ({
        timestamp: d.timestamp,
        value: d.performance.requestsPerSecond
      })),
      cpuUsage: history.map(d => ({
        timestamp: d.timestamp,
        value: d.infrastructure.cpuUsage
      })),
      memoryUsage: history.map(d => ({
        timestamp: d.timestamp,
        value: d.infrastructure.memoryUsagePercent
      }))
    }
  }

  private extractMetricValue(dashboard: MonitoringDashboard, metric: string): number | undefined {
    const parts = metric.split('.')
    let value: any = dashboard

    for (const part of parts) {
      value = value?.[part]
      if (value === undefined) return undefined
    }

    return typeof value === 'number' ? value : undefined
  }

  private evaluateAlertCondition(value: number, operator: string, threshold: number): boolean {
    switch (operator) {
      case 'gt': return value > threshold
      case 'gte': return value >= threshold
      case 'lt': return value < threshold
      case 'lte': return value <= threshold
      case 'eq': return value === threshold
      default: return false
    }
  }

  private async createAlert(rule: AlertRule, value: number): Promise<void> {
    // Check if alert already exists for this rule
    const existingAlert = Array.from(this.activeAlerts.values()).find(
      alert => alert.ruleId === rule.id && alert.status === 'active'
    )

    if (existingAlert) {
      // Update existing alert
      existingAlert.value = value
      existingAlert.timestamp = new Date()
      return
    }

    // Create new alert
    const alert: Alert = {
      id: this.generateId(),
      ruleId: rule.id,
      ruleName: rule.name,
      metric: rule.metric,
      value,
      threshold: rule.threshold,
      severity: rule.severity,
      message: `${rule.name}: ${rule.metric} is ${value} (threshold: ${rule.threshold})`,
      timestamp: new Date(),
      status: 'active',
      tags: rule.tags
    }

    this.activeAlerts.set(alert.id, alert)
    this.emit('alert:created', alert)

    this.logger.warn('Alert created', {
      id: alert.id,
      rule: rule.name,
      metric: rule.metric,
      value,
      threshold: rule.threshold
    })
  }

  private initializeDefaultAlertRules(): void {
    // High response time alert
    this.createAlertRule({
      name: 'High Response Time',
      metric: 'performance.averageResponseTime',
      operator: 'gt',
      threshold: 1000,
      duration: 300,
      severity: 'medium',
      enabled: true,
      channels: ['email'],
      tags: { type: 'performance' }
    })

    // High error rate alert
    this.createAlertRule({
      name: 'High Error Rate',
      metric: 'performance.errorRate',
      operator: 'gt',
      threshold: 0.05,
      duration: 60,
      severity: 'high',
      enabled: true,
      channels: ['email', 'slack'],
      tags: { type: 'errors' }
    })

    // High CPU usage alert
    this.createAlertRule({
      name: 'High CPU Usage',
      metric: 'infrastructure.cpuUsage',
      operator: 'gt',
      threshold: 80,
      duration: 300,
      severity: 'medium',
      enabled: true,
      channels: ['email'],
      tags: { type: 'infrastructure' }
    })

    // High memory usage alert
    this.createAlertRule({
      name: 'High Memory Usage',
      metric: 'infrastructure.memoryUsagePercent',
      operator: 'gt',
      threshold: 85,
      duration: 300,
      severity: 'medium',
      enabled: true,
      channels: ['email'],
      tags: { type: 'infrastructure' }
    })

    // System unhealthy alert
    this.createAlertRule({
      name: 'System Unhealthy',
      metric: 'health.summary.unhealthy',
      operator: 'gt',
      threshold: 0,
      duration: 60,
      severity: 'critical',
      enabled: true,
      channels: ['email', 'slack', 'sms'],
      tags: { type: 'health' }
    })
  }

  private generateId(): string {
    return `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
