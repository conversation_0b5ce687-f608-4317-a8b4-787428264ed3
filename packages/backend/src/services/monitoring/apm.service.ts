import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { EventEmitter } from 'events'

export interface APMMetrics {
  timestamp: Date
  requestId: string
  method: string
  url: string
  statusCode: number
  responseTime: number
  memoryUsage: NodeJS.MemoryUsage
  cpuUsage: number
  userAgent?: string
  userId?: string
  tenantId?: string
  errorMessage?: string
  stackTrace?: string
}

export interface TransactionTrace {
  id: string
  name: string
  type: 'web' | 'background' | 'database' | 'external'
  startTime: Date
  endTime?: Date
  duration?: number
  status: 'running' | 'completed' | 'failed'
  metadata: Record<string, any>
  spans: TransactionSpan[]
}

export interface TransactionSpan {
  id: string
  parentId?: string
  operationName: string
  startTime: Date
  endTime?: Date
  duration?: number
  tags: Record<string, any>
  logs: SpanLog[]
}

export interface SpanLog {
  timestamp: Date
  level: 'info' | 'warn' | 'error' | 'debug'
  message: string
  fields?: Record<string, any>
}

export interface PerformanceMetrics {
  timestamp: Date
  
  // Response time metrics
  averageResponseTime: number
  p50ResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  
  // Throughput metrics
  requestsPerSecond: number
  requestsPerMinute: number
  
  // Error metrics
  errorRate: number
  errorCount: number
  
  // Resource metrics
  memoryUsage: number
  cpuUsage: number
  
  // Database metrics
  dbConnectionCount: number
  dbQueryTime: number
  
  // Cache metrics
  cacheHitRate: number
  cacheMissRate: number
}

export interface AlertThreshold {
  metric: string
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte'
  value: number
  duration: number // seconds
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export class APMService extends EventEmitter {
  private logger: Logger
  private cache: CacheService
  private activeTransactions: Map<string, TransactionTrace>
  private metricsBuffer: APMMetrics[]
  private performanceHistory: PerformanceMetrics[]
  private alertThresholds: AlertThreshold[]
  private isEnabled: boolean

  constructor(cache: CacheService) {
    super()
    this.logger = new Logger('APMService')
    this.cache = cache
    this.activeTransactions = new Map()
    this.metricsBuffer = []
    this.performanceHistory = []
    this.alertThresholds = []
    this.isEnabled = process.env.NODE_ENV !== 'test'

    // Initialize default alert thresholds
    this.initializeDefaultThresholds()

    // Start metrics collection
    if (this.isEnabled) {
      this.startMetricsCollection()
    }
  }

  /**
   * Record request metrics
   */
  recordRequest(metrics: Omit<APMMetrics, 'timestamp' | 'memoryUsage' | 'cpuUsage'>): void {
    if (!this.isEnabled) return

    try {
      const fullMetrics: APMMetrics = {
        ...metrics,
        timestamp: new Date(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: this.getCPUUsage()
      }

      this.metricsBuffer.push(fullMetrics)

      // Emit real-time metric event
      this.emit('metric:recorded', fullMetrics)

      // Check for performance issues
      this.checkPerformanceThresholds(fullMetrics)

      // Flush buffer if it gets too large
      if (this.metricsBuffer.length > 1000) {
        this.flushMetrics()
      }
    } catch (error) {
      this.logger.error('Failed to record request metrics', error)
    }
  }

  /**
   * Start a new transaction trace
   */
  startTransaction(name: string, type: TransactionTrace['type'], metadata: Record<string, any> = {}): string {
    if (!this.isEnabled) return 'disabled'

    try {
      const transactionId = this.generateId()
      const transaction: TransactionTrace = {
        id: transactionId,
        name,
        type,
        startTime: new Date(),
        status: 'running',
        metadata,
        spans: []
      }

      this.activeTransactions.set(transactionId, transaction)

      this.logger.debug('Started transaction', {
        transactionId,
        name,
        type
      })

      return transactionId
    } catch (error) {
      this.logger.error('Failed to start transaction', error)
      return 'error'
    }
  }

  /**
   * End a transaction trace
   */
  endTransaction(transactionId: string, status: 'completed' | 'failed' = 'completed'): void {
    if (!this.isEnabled || transactionId === 'disabled' || transactionId === 'error') return

    try {
      const transaction = this.activeTransactions.get(transactionId)
      if (!transaction) {
        this.logger.warn('Transaction not found', { transactionId })
        return
      }

      transaction.endTime = new Date()
      transaction.duration = transaction.endTime.getTime() - transaction.startTime.getTime()
      transaction.status = status

      // Store completed transaction
      this.storeTransaction(transaction)

      // Remove from active transactions
      this.activeTransactions.delete(transactionId)

      this.logger.debug('Ended transaction', {
        transactionId,
        duration: transaction.duration,
        status
      })

      // Emit transaction completed event
      this.emit('transaction:completed', transaction)
    } catch (error) {
      this.logger.error('Failed to end transaction', error)
    }
  }

  /**
   * Add a span to a transaction
   */
  addSpan(transactionId: string, operationName: string, tags: Record<string, any> = {}): string {
    if (!this.isEnabled || transactionId === 'disabled' || transactionId === 'error') return 'disabled'

    try {
      const transaction = this.activeTransactions.get(transactionId)
      if (!transaction) {
        this.logger.warn('Transaction not found for span', { transactionId, operationName })
        return 'not-found'
      }

      const spanId = this.generateId()
      const span: TransactionSpan = {
        id: spanId,
        operationName,
        startTime: new Date(),
        tags,
        logs: []
      }

      transaction.spans.push(span)

      return spanId
    } catch (error) {
      this.logger.error('Failed to add span', error)
      return 'error'
    }
  }

  /**
   * End a span
   */
  endSpan(transactionId: string, spanId: string): void {
    if (!this.isEnabled || transactionId === 'disabled' || spanId === 'disabled') return

    try {
      const transaction = this.activeTransactions.get(transactionId)
      if (!transaction) return

      const span = transaction.spans.find(s => s.id === spanId)
      if (!span) return

      span.endTime = new Date()
      span.duration = span.endTime.getTime() - span.startTime.getTime()
    } catch (error) {
      this.logger.error('Failed to end span', error)
    }
  }

  /**
   * Add log to span
   */
  addSpanLog(transactionId: string, spanId: string, level: SpanLog['level'], message: string, fields?: Record<string, any>): void {
    if (!this.isEnabled || transactionId === 'disabled' || spanId === 'disabled') return

    try {
      const transaction = this.activeTransactions.get(transactionId)
      if (!transaction) return

      const span = transaction.spans.find(s => s.id === spanId)
      if (!span) return

      span.logs.push({
        timestamp: new Date(),
        level,
        message,
        fields
      })
    } catch (error) {
      this.logger.error('Failed to add span log', error)
    }
  }

  /**
   * Get current performance metrics
   */
  async getCurrentMetrics(): Promise<PerformanceMetrics> {
    try {
      const now = new Date()
      const recentMetrics = this.metricsBuffer.filter(
        m => now.getTime() - m.timestamp.getTime() < 60000 // Last minute
      )

      if (recentMetrics.length === 0) {
        return this.getDefaultMetrics()
      }

      const responseTimes = recentMetrics.map(m => m.responseTime)
      const errors = recentMetrics.filter(m => m.statusCode >= 400)

      const metrics: PerformanceMetrics = {
        timestamp: now,
        averageResponseTime: this.calculateAverage(responseTimes),
        p50ResponseTime: this.calculatePercentile(responseTimes, 50),
        p95ResponseTime: this.calculatePercentile(responseTimes, 95),
        p99ResponseTime: this.calculatePercentile(responseTimes, 99),
        requestsPerSecond: recentMetrics.length / 60,
        requestsPerMinute: recentMetrics.length,
        errorRate: errors.length / recentMetrics.length,
        errorCount: errors.length,
        memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024, // MB
        cpuUsage: this.getCPUUsage(),
        dbConnectionCount: await this.getDBConnectionCount(),
        dbQueryTime: await this.getAverageDBQueryTime(),
        cacheHitRate: await this.getCacheHitRate(),
        cacheMissRate: await this.getCacheMissRate()
      }

      // Store in history
      this.performanceHistory.push(metrics)
      if (this.performanceHistory.length > 1440) { // Keep 24 hours of minute-by-minute data
        this.performanceHistory.shift()
      }

      return metrics
    } catch (error) {
      this.logger.error('Failed to get current metrics', error)
      return this.getDefaultMetrics()
    }
  }

  /**
   * Get performance history
   */
  getPerformanceHistory(hours: number = 24): PerformanceMetrics[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000)
    return this.performanceHistory.filter(m => m.timestamp >= cutoff)
  }

  /**
   * Get active transactions
   */
  getActiveTransactions(): TransactionTrace[] {
    return Array.from(this.activeTransactions.values())
  }

  /**
   * Configure alert thresholds
   */
  setAlertThresholds(thresholds: AlertThreshold[]): void {
    this.alertThresholds = thresholds
    this.logger.info('Updated alert thresholds', { count: thresholds.length })
  }

  /**
   * Private helper methods
   */
  private initializeDefaultThresholds(): void {
    this.alertThresholds = [
      {
        metric: 'averageResponseTime',
        operator: 'gt',
        value: 1000, // 1 second
        duration: 300, // 5 minutes
        severity: 'medium'
      },
      {
        metric: 'errorRate',
        operator: 'gt',
        value: 0.05, // 5%
        duration: 60, // 1 minute
        severity: 'high'
      },
      {
        metric: 'memoryUsage',
        operator: 'gt',
        value: 512, // 512 MB
        duration: 300, // 5 minutes
        severity: 'medium'
      },
      {
        metric: 'cpuUsage',
        operator: 'gt',
        value: 80, // 80%
        duration: 300, // 5 minutes
        severity: 'high'
      }
    ]
  }

  private startMetricsCollection(): void {
    // Collect metrics every minute
    setInterval(async () => {
      try {
        const metrics = await this.getCurrentMetrics()
        this.emit('metrics:collected', metrics)
      } catch (error) {
        this.logger.error('Failed to collect metrics', error)
      }
    }, 60000)

    // Flush metrics buffer every 5 minutes
    setInterval(() => {
      this.flushMetrics()
    }, 300000)
  }

  private flushMetrics(): void {
    if (this.metricsBuffer.length === 0) return

    try {
      // Store metrics in cache for short-term access
      this.cache.set('apm:recent_metrics', this.metricsBuffer.slice(-100), 3600)

      // Clear buffer
      this.metricsBuffer = []

      this.logger.debug('Flushed metrics buffer')
    } catch (error) {
      this.logger.error('Failed to flush metrics', error)
    }
  }

  private checkPerformanceThresholds(metrics: APMMetrics): void {
    // Check response time threshold
    if (metrics.responseTime > 2000) { // 2 seconds
      this.emit('alert:performance', {
        type: 'slow_response',
        severity: 'medium',
        message: `Slow response time: ${metrics.responseTime}ms`,
        metrics
      })
    }

    // Check error threshold
    if (metrics.statusCode >= 500) {
      this.emit('alert:error', {
        type: 'server_error',
        severity: 'high',
        message: `Server error: ${metrics.statusCode}`,
        metrics
      })
    }
  }

  private async storeTransaction(transaction: TransactionTrace): Promise<void> {
    try {
      // Store in cache for recent access
      const recentTransactions = await this.cache.get<TransactionTrace[]>('apm:recent_transactions') || []
      recentTransactions.push(transaction)
      
      // Keep only last 100 transactions
      if (recentTransactions.length > 100) {
        recentTransactions.shift()
      }
      
      await this.cache.set('apm:recent_transactions', recentTransactions, 3600)
    } catch (error) {
      this.logger.error('Failed to store transaction', error)
    }
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  private getCPUUsage(): number {
    // Simplified CPU usage calculation
    const usage = process.cpuUsage()
    return (usage.user + usage.system) / 1000000 // Convert to percentage
  }

  private calculateAverage(numbers: number[]): number {
    if (numbers.length === 0) return 0
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length
  }

  private calculatePercentile(numbers: number[], percentile: number): number {
    if (numbers.length === 0) return 0
    
    const sorted = numbers.slice().sort((a, b) => a - b)
    const index = Math.ceil((percentile / 100) * sorted.length) - 1
    return sorted[Math.max(0, index)]
  }

  private async getDBConnectionCount(): Promise<number> {
    // Mock implementation - would integrate with actual DB monitoring
    return 10
  }

  private async getAverageDBQueryTime(): Promise<number> {
    // Mock implementation - would integrate with actual DB monitoring
    return 50 // ms
  }

  private async getCacheHitRate(): Promise<number> {
    // Mock implementation - would integrate with actual cache monitoring
    return 0.85 // 85%
  }

  private async getCacheMissRate(): Promise<number> {
    return 1 - await this.getCacheHitRate()
  }

  private getDefaultMetrics(): PerformanceMetrics {
    return {
      timestamp: new Date(),
      averageResponseTime: 0,
      p50ResponseTime: 0,
      p95ResponseTime: 0,
      p99ResponseTime: 0,
      requestsPerSecond: 0,
      requestsPerMinute: 0,
      errorRate: 0,
      errorCount: 0,
      memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024,
      cpuUsage: 0,
      dbConnectionCount: 0,
      dbQueryTime: 0,
      cacheHitRate: 0,
      cacheMissRate: 0
    }
  }
}
