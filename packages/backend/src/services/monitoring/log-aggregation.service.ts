import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { EventEmitter } from 'events'
import * as fs from 'fs/promises'
import * as path from 'path'

export interface LogEntry {
  id: string
  timestamp: Date
  level: 'error' | 'warn' | 'info' | 'debug' | 'trace'
  message: string
  service: string
  environment: string
  
  // Context
  userId?: string
  tenantId?: string
  requestId?: string
  sessionId?: string
  
  // Technical details
  hostname: string
  pid: number
  version?: string
  
  // Structured data
  data?: Record<string, any>
  tags?: Record<string, string>
  
  // Error details (if applicable)
  error?: {
    name: string
    message: string
    stack?: string
  }
  
  // Performance metrics
  duration?: number
  memoryUsage?: number
  cpuUsage?: number
}

export interface LogQuery {
  // Time range
  startTime?: Date
  endTime?: Date
  
  // Filters
  level?: LogEntry['level'] | LogEntry['level'][]
  service?: string | string[]
  environment?: string | string[]
  userId?: string
  tenantId?: string
  requestId?: string
  
  // Search
  search?: string
  
  // Pagination
  limit?: number
  offset?: number
  
  // Sorting
  sortBy?: 'timestamp' | 'level' | 'service'
  sortOrder?: 'asc' | 'desc'
}

export interface LogStats {
  totalLogs: number
  logsByLevel: Record<string, number>
  logsByService: Record<string, number>
  logsByEnvironment: Record<string, number>
  
  // Time-based stats
  logsLast1h: number
  logsLast24h: number
  logsLast7d: number
  
  // Error stats
  errorRate: number
  topErrors: Array<{
    message: string
    count: number
    lastSeen: Date
  }>
  
  // Performance stats
  averageResponseTime: number
  slowestRequests: Array<{
    requestId: string
    duration: number
    endpoint: string
    timestamp: Date
  }>
}

export interface LogAggregationConfig {
  enabled: boolean
  
  // Storage
  storageType: 'file' | 'elasticsearch' | 'memory'
  storagePath?: string
  retentionDays: number
  
  // Buffering
  bufferSize: number
  flushInterval: number // seconds
  
  // Filtering
  minLevel: LogEntry['level']
  excludeServices: string[]
  
  // Performance
  enableMetrics: boolean
  enableTracing: boolean
  
  // Elasticsearch config (if using)
  elasticsearch?: {
    host: string
    index: string
    username?: string
    password?: string
  }
}

export class LogAggregationService extends EventEmitter {
  private logger: Logger
  private cache: CacheService
  private config: LogAggregationConfig
  private logBuffer: LogEntry[]
  private flushInterval?: NodeJS.Timeout
  private isRunning: boolean

  constructor(cache: CacheService, config?: Partial<LogAggregationConfig>) {
    super()
    this.logger = new Logger('LogAggregationService')
    this.cache = cache
    this.logBuffer = []
    this.isRunning = false

    // Initialize configuration
    this.config = {
      enabled: true,
      storageType: 'file',
      storagePath: './logs',
      retentionDays: 30,
      bufferSize: 1000,
      flushInterval: 30, // 30 seconds
      minLevel: 'info',
      excludeServices: [],
      enableMetrics: true,
      enableTracing: true,
      ...config
    }
  }

  /**
   * Start log aggregation service
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Log aggregation service is already running')
      return
    }

    if (!this.config.enabled) {
      this.logger.info('Log aggregation service is disabled')
      return
    }

    try {
      this.logger.info('Starting log aggregation service', {
        storageType: this.config.storageType,
        bufferSize: this.config.bufferSize,
        flushInterval: this.config.flushInterval
      })

      // Initialize storage
      await this.initializeStorage()

      // Start buffer flushing
      this.flushInterval = setInterval(async () => {
        await this.flushBuffer()
      }, this.config.flushInterval * 1000)

      this.isRunning = true
      this.emit('service:started')

      this.logger.info('Log aggregation service started successfully')
    } catch (error) {
      this.logger.error('Failed to start log aggregation service', error)
      throw error
    }
  }

  /**
   * Stop log aggregation service
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('Log aggregation service is not running')
      return
    }

    try {
      this.logger.info('Stopping log aggregation service')

      // Stop flushing
      if (this.flushInterval) {
        clearInterval(this.flushInterval)
        this.flushInterval = undefined
      }

      // Flush remaining logs
      await this.flushBuffer()

      this.isRunning = false
      this.emit('service:stopped')

      this.logger.info('Log aggregation service stopped')
    } catch (error) {
      this.logger.error('Failed to stop log aggregation service', error)
      throw error
    }
  }

  /**
   * Add log entry
   */
  addLog(entry: Omit<LogEntry, 'id' | 'timestamp' | 'hostname' | 'pid'>): void {
    if (!this.config.enabled || !this.isRunning) return

    try {
      // Check if service should be excluded
      if (this.config.excludeServices.includes(entry.service)) {
        return
      }

      // Check minimum level
      if (!this.shouldLogLevel(entry.level)) {
        return
      }

      // Create full log entry
      const logEntry: LogEntry = {
        id: this.generateId(),
        timestamp: new Date(),
        hostname: require('os').hostname(),
        pid: process.pid,
        ...entry
      }

      // Add to buffer
      this.logBuffer.push(logEntry)

      // Emit event
      this.emit('log:added', logEntry)

      // Flush if buffer is full
      if (this.logBuffer.length >= this.config.bufferSize) {
        this.flushBuffer()
      }
    } catch (error) {
      this.logger.error('Failed to add log entry', error)
    }
  }

  /**
   * Query logs
   */
  async queryLogs(query: LogQuery): Promise<{ logs: LogEntry[]; total: number }> {
    try {
      switch (this.config.storageType) {
        case 'file':
          return await this.queryFileStorage(query)
        case 'elasticsearch':
          return await this.queryElasticsearch(query)
        case 'memory':
          return await this.queryMemoryStorage(query)
        default:
          throw new Error(`Unsupported storage type: ${this.config.storageType}`)
      }
    } catch (error) {
      this.logger.error('Failed to query logs', error)
      throw error
    }
  }

  /**
   * Get log statistics
   */
  async getLogStats(timeRange: '1h' | '24h' | '7d' = '24h'): Promise<LogStats> {
    try {
      const endTime = new Date()
      const startTime = this.getTimeRangeStart(timeRange, endTime)

      const query: LogQuery = {
        startTime,
        endTime,
        limit: 10000 // Get enough logs for stats
      }

      const { logs } = await this.queryLogs(query)

      const stats: LogStats = {
        totalLogs: logs.length,
        logsByLevel: this.groupLogsByLevel(logs),
        logsByService: this.groupLogsByService(logs),
        logsByEnvironment: this.groupLogsByEnvironment(logs),
        logsLast1h: this.countLogsInTimeRange(logs, '1h'),
        logsLast24h: this.countLogsInTimeRange(logs, '24h'),
        logsLast7d: this.countLogsInTimeRange(logs, '7d'),
        errorRate: this.calculateErrorRate(logs),
        topErrors: this.getTopErrors(logs),
        averageResponseTime: this.calculateAverageResponseTime(logs),
        slowestRequests: this.getSlowestRequests(logs)
      }

      return stats
    } catch (error) {
      this.logger.error('Failed to get log stats', error)
      throw error
    }
  }

  /**
   * Search logs by text
   */
  async searchLogs(searchTerm: string, options: Partial<LogQuery> = {}): Promise<LogEntry[]> {
    try {
      const query: LogQuery = {
        search: searchTerm,
        limit: 100,
        sortBy: 'timestamp',
        sortOrder: 'desc',
        ...options
      }

      const { logs } = await this.queryLogs(query)
      return logs
    } catch (error) {
      this.logger.error('Failed to search logs', error)
      throw error
    }
  }

  /**
   * Get logs for specific request
   */
  async getRequestLogs(requestId: string): Promise<LogEntry[]> {
    try {
      const { logs } = await this.queryLogs({
        requestId,
        sortBy: 'timestamp',
        sortOrder: 'asc'
      })

      return logs
    } catch (error) {
      this.logger.error('Failed to get request logs', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async initializeStorage(): Promise<void> {
    switch (this.config.storageType) {
      case 'file':
        await this.initializeFileStorage()
        break
      case 'elasticsearch':
        await this.initializeElasticsearch()
        break
      case 'memory':
        // No initialization needed for memory storage
        break
      default:
        throw new Error(`Unsupported storage type: ${this.config.storageType}`)
    }
  }

  private async initializeFileStorage(): Promise<void> {
    if (!this.config.storagePath) {
      throw new Error('Storage path is required for file storage')
    }

    try {
      await fs.mkdir(this.config.storagePath, { recursive: true })
      this.logger.info('File storage initialized', { path: this.config.storagePath })
    } catch (error) {
      this.logger.error('Failed to initialize file storage', error)
      throw error
    }
  }

  private async initializeElasticsearch(): Promise<void> {
    // Elasticsearch initialization would go here
    this.logger.info('Elasticsearch storage initialized')
  }

  private async flushBuffer(): Promise<void> {
    if (this.logBuffer.length === 0) return

    try {
      const logsToFlush = [...this.logBuffer]
      this.logBuffer = []

      switch (this.config.storageType) {
        case 'file':
          await this.writeToFileStorage(logsToFlush)
          break
        case 'elasticsearch':
          await this.writeToElasticsearch(logsToFlush)
          break
        case 'memory':
          await this.writeToMemoryStorage(logsToFlush)
          break
      }

      this.emit('logs:flushed', { count: logsToFlush.length })
      this.logger.debug('Flushed log buffer', { count: logsToFlush.length })
    } catch (error) {
      this.logger.error('Failed to flush log buffer', error)
      // Put logs back in buffer on failure
      this.logBuffer.unshift(...this.logBuffer)
    }
  }

  private async writeToFileStorage(logs: LogEntry[]): Promise<void> {
    if (!this.config.storagePath) return

    try {
      const today = new Date().toISOString().split('T')[0]
      const filename = `logs-${today}.jsonl`
      const filepath = path.join(this.config.storagePath, filename)

      const logLines = logs.map(log => JSON.stringify(log)).join('\n') + '\n'
      await fs.appendFile(filepath, logLines, 'utf8')
    } catch (error) {
      this.logger.error('Failed to write to file storage', error)
      throw error
    }
  }

  private async writeToElasticsearch(logs: LogEntry[]): Promise<void> {
    // Elasticsearch bulk write would go here
    this.logger.debug('Writing to Elasticsearch', { count: logs.length })
  }

  private async writeToMemoryStorage(logs: LogEntry[]): Promise<void> {
    // Store in cache for memory storage
    const existingLogs = await this.cache.get<LogEntry[]>('logs:memory') || []
    const allLogs = [...existingLogs, ...logs]
    
    // Keep only recent logs to prevent memory issues
    const maxLogs = 10000
    const recentLogs = allLogs.slice(-maxLogs)
    
    await this.cache.set('logs:memory', recentLogs, 86400) // 24 hours
  }

  private async queryFileStorage(query: LogQuery): Promise<{ logs: LogEntry[]; total: number }> {
    // Simplified file storage query - would implement proper file reading and filtering
    const logs: LogEntry[] = []
    return { logs, total: logs.length }
  }

  private async queryElasticsearch(query: LogQuery): Promise<{ logs: LogEntry[]; total: number }> {
    // Elasticsearch query would go here
    const logs: LogEntry[] = []
    return { logs, total: logs.length }
  }

  private async queryMemoryStorage(query: LogQuery): Promise<{ logs: LogEntry[]; total: number }> {
    const allLogs = await this.cache.get<LogEntry[]>('logs:memory') || []
    let filteredLogs = allLogs

    // Apply filters
    if (query.level) {
      const levels = Array.isArray(query.level) ? query.level : [query.level]
      filteredLogs = filteredLogs.filter(log => levels.includes(log.level))
    }

    if (query.service) {
      const services = Array.isArray(query.service) ? query.service : [query.service]
      filteredLogs = filteredLogs.filter(log => services.includes(log.service))
    }

    if (query.startTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= query.startTime!)
    }

    if (query.endTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp <= query.endTime!)
    }

    if (query.search) {
      const searchTerm = query.search.toLowerCase()
      filteredLogs = filteredLogs.filter(log => 
        log.message.toLowerCase().includes(searchTerm) ||
        log.service.toLowerCase().includes(searchTerm)
      )
    }

    // Sort
    const sortBy = query.sortBy || 'timestamp'
    const sortOrder = query.sortOrder || 'desc'
    filteredLogs.sort((a, b) => {
      const aVal = a[sortBy as keyof LogEntry]
      const bVal = b[sortBy as keyof LogEntry]
      
      if (sortOrder === 'asc') {
        return aVal < bVal ? -1 : aVal > bVal ? 1 : 0
      } else {
        return aVal > bVal ? -1 : aVal < bVal ? 1 : 0
      }
    })

    // Pagination
    const offset = query.offset || 0
    const limit = query.limit || 100
    const paginatedLogs = filteredLogs.slice(offset, offset + limit)

    return { logs: paginatedLogs, total: filteredLogs.length }
  }

  private shouldLogLevel(level: LogEntry['level']): boolean {
    const levels = ['error', 'warn', 'info', 'debug', 'trace']
    const minLevelIndex = levels.indexOf(this.config.minLevel)
    const currentLevelIndex = levels.indexOf(level)
    
    return currentLevelIndex <= minLevelIndex
  }

  private generateId(): string {
    return `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  private getTimeRangeStart(timeRange: string, endTime: Date): Date {
    switch (timeRange) {
      case '1h':
        return new Date(endTime.getTime() - 60 * 60 * 1000)
      case '24h':
        return new Date(endTime.getTime() - 24 * 60 * 60 * 1000)
      case '7d':
        return new Date(endTime.getTime() - 7 * 24 * 60 * 60 * 1000)
      default:
        return new Date(endTime.getTime() - 24 * 60 * 60 * 1000)
    }
  }

  private groupLogsByLevel(logs: LogEntry[]): Record<string, number> {
    const groups: Record<string, number> = {}
    for (const log of logs) {
      groups[log.level] = (groups[log.level] || 0) + 1
    }
    return groups
  }

  private groupLogsByService(logs: LogEntry[]): Record<string, number> {
    const groups: Record<string, number> = {}
    for (const log of logs) {
      groups[log.service] = (groups[log.service] || 0) + 1
    }
    return groups
  }

  private groupLogsByEnvironment(logs: LogEntry[]): Record<string, number> {
    const groups: Record<string, number> = {}
    for (const log of logs) {
      groups[log.environment] = (groups[log.environment] || 0) + 1
    }
    return groups
  }

  private countLogsInTimeRange(logs: LogEntry[], timeRange: string): number {
    const now = new Date()
    const cutoff = this.getTimeRangeStart(timeRange, now)
    return logs.filter(log => log.timestamp >= cutoff).length
  }

  private calculateErrorRate(logs: LogEntry[]): number {
    const errorLogs = logs.filter(log => log.level === 'error')
    return logs.length > 0 ? errorLogs.length / logs.length : 0
  }

  private getTopErrors(logs: LogEntry[]): Array<{ message: string; count: number; lastSeen: Date }> {
    const errorCounts: Record<string, { count: number; lastSeen: Date }> = {}
    
    for (const log of logs.filter(l => l.level === 'error')) {
      if (!errorCounts[log.message]) {
        errorCounts[log.message] = { count: 0, lastSeen: log.timestamp }
      }
      errorCounts[log.message].count++
      if (log.timestamp > errorCounts[log.message].lastSeen) {
        errorCounts[log.message].lastSeen = log.timestamp
      }
    }

    return Object.entries(errorCounts)
      .map(([message, data]) => ({ message, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
  }

  private calculateAverageResponseTime(logs: LogEntry[]): number {
    const logsWithDuration = logs.filter(log => log.duration !== undefined)
    if (logsWithDuration.length === 0) return 0
    
    const totalDuration = logsWithDuration.reduce((sum, log) => sum + (log.duration || 0), 0)
    return totalDuration / logsWithDuration.length
  }

  private getSlowestRequests(logs: LogEntry[]): Array<{ requestId: string; duration: number; endpoint: string; timestamp: Date }> {
    return logs
      .filter(log => log.duration !== undefined && log.requestId)
      .map(log => ({
        requestId: log.requestId!,
        duration: log.duration!,
        endpoint: log.data?.endpoint || 'unknown',
        timestamp: log.timestamp
      }))
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10)
  }
}
