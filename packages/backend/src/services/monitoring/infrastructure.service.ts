import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { EventEmitter } from 'events'
import * as os from 'os'
import * as fs from 'fs/promises'
import { PrismaClient } from '@prisma/client'

export interface SystemMetrics {
  timestamp: Date
  
  // CPU metrics
  cpuUsage: number
  cpuLoadAverage: number[]
  cpuCores: number
  
  // Memory metrics
  memoryTotal: number
  memoryUsed: number
  memoryFree: number
  memoryUsagePercent: number
  
  // Disk metrics
  diskUsage: DiskUsage[]
  
  // Network metrics
  networkInterfaces: NetworkInterface[]
  
  // Process metrics
  processCount: number
  nodeProcessMemory: NodeJS.MemoryUsage
  nodeProcessUptime: number
  
  // Database metrics
  databaseConnections: number
  databaseActiveQueries: number
  databaseSlowQueries: number
  
  // Cache metrics
  cacheMemoryUsage: number
  cacheHitRate: number
  cacheConnections: number
}

export interface DiskUsage {
  filesystem: string
  size: number
  used: number
  available: number
  usagePercent: number
  mountPoint: string
}

export interface NetworkInterface {
  name: string
  address: string
  family: string
  internal: boolean
  bytesReceived?: number
  bytesSent?: number
  packetsReceived?: number
  packetsSent?: number
}

export interface ServiceHealth {
  name: string
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown'
  lastCheck: Date
  responseTime?: number
  errorMessage?: string
  metadata?: Record<string, any>
}

export interface InfrastructureAlert {
  id: string
  type: 'cpu' | 'memory' | 'disk' | 'network' | 'database' | 'service'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  value: number
  threshold: number
  timestamp: Date
  resolved: boolean
  resolvedAt?: Date
}

export interface MonitoringConfig {
  enabled: boolean
  collectInterval: number // seconds
  retentionPeriod: number // hours
  
  // Alert thresholds
  thresholds: {
    cpu: { warning: number; critical: number }
    memory: { warning: number; critical: number }
    disk: { warning: number; critical: number }
    database: { connections: number; slowQueries: number }
  }
  
  // Services to monitor
  services: Array<{
    name: string
    url: string
    method: 'GET' | 'POST' | 'HEAD'
    timeout: number
    expectedStatus: number
    headers?: Record<string, string>
  }>
}

export class InfrastructureMonitoringService extends EventEmitter {
  private logger: Logger
  private cache: CacheService
  private prisma: PrismaClient
  private config: MonitoringConfig
  private isRunning: boolean
  private collectInterval?: NodeJS.Timeout
  private metricsHistory: SystemMetrics[]
  private activeAlerts: Map<string, InfrastructureAlert>

  constructor(cache: CacheService, prisma: PrismaClient, config?: Partial<MonitoringConfig>) {
    super()
    this.logger = new Logger('InfrastructureMonitoringService')
    this.cache = cache
    this.prisma = prisma
    this.isRunning = false
    this.metricsHistory = []
    this.activeAlerts = new Map()

    // Initialize configuration
    this.config = {
      enabled: true,
      collectInterval: 60, // 1 minute
      retentionPeriod: 24, // 24 hours
      thresholds: {
        cpu: { warning: 70, critical: 90 },
        memory: { warning: 80, critical: 95 },
        disk: { warning: 80, critical: 95 },
        database: { connections: 80, slowQueries: 10 }
      },
      services: [
        {
          name: 'API Health',
          url: '/api/health',
          method: 'GET',
          timeout: 5000,
          expectedStatus: 200
        }
      ],
      ...config
    }
  }

  /**
   * Start infrastructure monitoring
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Infrastructure monitoring is already running')
      return
    }

    if (!this.config.enabled) {
      this.logger.info('Infrastructure monitoring is disabled')
      return
    }

    try {
      this.logger.info('Starting infrastructure monitoring', {
        interval: this.config.collectInterval,
        retention: this.config.retentionPeriod
      })

      this.isRunning = true

      // Start metrics collection
      this.collectInterval = setInterval(async () => {
        try {
          await this.collectMetrics()
        } catch (error) {
          this.logger.error('Failed to collect metrics', error)
        }
      }, this.config.collectInterval * 1000)

      // Collect initial metrics
      await this.collectMetrics()

      this.emit('monitoring:started')
      this.logger.info('Infrastructure monitoring started successfully')
    } catch (error) {
      this.logger.error('Failed to start infrastructure monitoring', error)
      throw error
    }
  }

  /**
   * Stop infrastructure monitoring
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('Infrastructure monitoring is not running')
      return
    }

    try {
      this.logger.info('Stopping infrastructure monitoring')

      this.isRunning = false

      if (this.collectInterval) {
        clearInterval(this.collectInterval)
        this.collectInterval = undefined
      }

      this.emit('monitoring:stopped')
      this.logger.info('Infrastructure monitoring stopped')
    } catch (error) {
      this.logger.error('Failed to stop infrastructure monitoring', error)
      throw error
    }
  }

  /**
   * Get current system metrics
   */
  async getCurrentMetrics(): Promise<SystemMetrics> {
    try {
      const metrics: SystemMetrics = {
        timestamp: new Date(),
        
        // CPU metrics
        cpuUsage: await this.getCPUUsage(),
        cpuLoadAverage: os.loadavg(),
        cpuCores: os.cpus().length,
        
        // Memory metrics
        memoryTotal: os.totalmem(),
        memoryFree: os.freemem(),
        memoryUsed: os.totalmem() - os.freemem(),
        memoryUsagePercent: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100,
        
        // Disk metrics
        diskUsage: await this.getDiskUsage(),
        
        // Network metrics
        networkInterfaces: this.getNetworkInterfaces(),
        
        // Process metrics
        processCount: await this.getProcessCount(),
        nodeProcessMemory: process.memoryUsage(),
        nodeProcessUptime: process.uptime(),
        
        // Database metrics
        databaseConnections: await this.getDatabaseConnections(),
        databaseActiveQueries: await this.getDatabaseActiveQueries(),
        databaseSlowQueries: await this.getDatabaseSlowQueries(),
        
        // Cache metrics
        cacheMemoryUsage: await this.getCacheMemoryUsage(),
        cacheHitRate: await this.getCacheHitRate(),
        cacheConnections: await this.getCacheConnections()
      }

      return metrics
    } catch (error) {
      this.logger.error('Failed to get current metrics', error)
      throw error
    }
  }

  /**
   * Get metrics history
   */
  getMetricsHistory(hours: number = 24): SystemMetrics[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000)
    return this.metricsHistory.filter(m => m.timestamp >= cutoff)
  }

  /**
   * Check service health
   */
  async checkServiceHealth(): Promise<ServiceHealth[]> {
    const healthChecks: ServiceHealth[] = []

    for (const service of this.config.services) {
      try {
        const startTime = Date.now()
        
        // Perform health check (simplified - would use actual HTTP client)
        const isHealthy = await this.performHealthCheck(service)
        const responseTime = Date.now() - startTime

        healthChecks.push({
          name: service.name,
          status: isHealthy ? 'healthy' : 'unhealthy',
          lastCheck: new Date(),
          responseTime,
          metadata: {
            url: service.url,
            method: service.method,
            expectedStatus: service.expectedStatus
          }
        })
      } catch (error) {
        healthChecks.push({
          name: service.name,
          status: 'unhealthy',
          lastCheck: new Date(),
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
          metadata: {
            url: service.url,
            method: service.method
          }
        })
      }
    }

    return healthChecks
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): InfrastructureAlert[] {
    return Array.from(this.activeAlerts.values()).filter(alert => !alert.resolved)
  }

  /**
   * Resolve alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.activeAlerts.get(alertId)
    if (!alert) return false

    alert.resolved = true
    alert.resolvedAt = new Date()

    this.emit('alert:resolved', alert)
    return true
  }

  /**
   * Update monitoring configuration
   */
  updateConfig(config: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...config }
    this.logger.info('Updated monitoring configuration', config)
  }

  /**
   * Private helper methods
   */
  private async collectMetrics(): Promise<void> {
    try {
      const metrics = await this.getCurrentMetrics()
      
      // Store metrics
      this.metricsHistory.push(metrics)
      
      // Cleanup old metrics
      const cutoff = new Date(Date.now() - this.config.retentionPeriod * 60 * 60 * 1000)
      this.metricsHistory = this.metricsHistory.filter(m => m.timestamp >= cutoff)
      
      // Cache recent metrics
      await this.cache.set('infrastructure:current_metrics', metrics, 300)
      
      // Check thresholds and generate alerts
      await this.checkThresholds(metrics)
      
      // Emit metrics collected event
      this.emit('metrics:collected', metrics)
      
      this.logger.debug('Collected infrastructure metrics', {
        timestamp: metrics.timestamp,
        cpuUsage: metrics.cpuUsage,
        memoryUsage: metrics.memoryUsagePercent
      })
    } catch (error) {
      this.logger.error('Failed to collect metrics', error)
    }
  }

  private async checkThresholds(metrics: SystemMetrics): Promise<void> {
    // Check CPU threshold
    if (metrics.cpuUsage > this.config.thresholds.cpu.critical) {
      await this.createAlert('cpu', 'critical', 'Critical CPU usage', metrics.cpuUsage, this.config.thresholds.cpu.critical)
    } else if (metrics.cpuUsage > this.config.thresholds.cpu.warning) {
      await this.createAlert('cpu', 'medium', 'High CPU usage', metrics.cpuUsage, this.config.thresholds.cpu.warning)
    }

    // Check memory threshold
    if (metrics.memoryUsagePercent > this.config.thresholds.memory.critical) {
      await this.createAlert('memory', 'critical', 'Critical memory usage', metrics.memoryUsagePercent, this.config.thresholds.memory.critical)
    } else if (metrics.memoryUsagePercent > this.config.thresholds.memory.warning) {
      await this.createAlert('memory', 'medium', 'High memory usage', metrics.memoryUsagePercent, this.config.thresholds.memory.warning)
    }

    // Check disk usage
    for (const disk of metrics.diskUsage) {
      if (disk.usagePercent > this.config.thresholds.disk.critical) {
        await this.createAlert('disk', 'critical', `Critical disk usage on ${disk.mountPoint}`, disk.usagePercent, this.config.thresholds.disk.critical)
      } else if (disk.usagePercent > this.config.thresholds.disk.warning) {
        await this.createAlert('disk', 'medium', `High disk usage on ${disk.mountPoint}`, disk.usagePercent, this.config.thresholds.disk.warning)
      }
    }

    // Check database metrics
    if (metrics.databaseConnections > this.config.thresholds.database.connections) {
      await this.createAlert('database', 'high', 'High database connections', metrics.databaseConnections, this.config.thresholds.database.connections)
    }

    if (metrics.databaseSlowQueries > this.config.thresholds.database.slowQueries) {
      await this.createAlert('database', 'medium', 'High number of slow queries', metrics.databaseSlowQueries, this.config.thresholds.database.slowQueries)
    }
  }

  private async createAlert(
    type: InfrastructureAlert['type'],
    severity: InfrastructureAlert['severity'],
    message: string,
    value: number,
    threshold: number
  ): Promise<void> {
    const alertId = `${type}-${severity}-${Date.now()}`
    
    // Check if similar alert already exists
    const existingAlert = Array.from(this.activeAlerts.values()).find(
      alert => alert.type === type && alert.severity === severity && !alert.resolved
    )

    if (existingAlert) {
      // Update existing alert
      existingAlert.value = value
      existingAlert.timestamp = new Date()
      return
    }

    const alert: InfrastructureAlert = {
      id: alertId,
      type,
      severity,
      message,
      value,
      threshold,
      timestamp: new Date(),
      resolved: false
    }

    this.activeAlerts.set(alertId, alert)
    this.emit('alert:created', alert)

    this.logger.warn('Infrastructure alert created', {
      type,
      severity,
      message,
      value,
      threshold
    })
  }

  private async getCPUUsage(): Promise<number> {
    // Simplified CPU usage calculation
    const cpus = os.cpus()
    let totalIdle = 0
    let totalTick = 0

    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type as keyof typeof cpu.times]
      }
      totalIdle += cpu.times.idle
    }

    return 100 - (totalIdle / totalTick) * 100
  }

  private async getDiskUsage(): Promise<DiskUsage[]> {
    try {
      // Simplified disk usage - would use actual disk monitoring library
      const stats = await fs.stat('/')
      return [
        {
          filesystem: '/dev/disk1',
          size: 1000000000000, // 1TB
          used: 500000000000,  // 500GB
          available: 500000000000, // 500GB
          usagePercent: 50,
          mountPoint: '/'
        }
      ]
    } catch (error) {
      this.logger.error('Failed to get disk usage', error)
      return []
    }
  }

  private getNetworkInterfaces(): NetworkInterface[] {
    const interfaces = os.networkInterfaces()
    const result: NetworkInterface[] = []

    for (const [name, addresses] of Object.entries(interfaces)) {
      if (!addresses) continue
      
      for (const address of addresses) {
        result.push({
          name,
          address: address.address,
          family: address.family,
          internal: address.internal
        })
      }
    }

    return result
  }

  private async getProcessCount(): Promise<number> {
    // Simplified process count
    return 150
  }

  private async getDatabaseConnections(): Promise<number> {
    try {
      // Would query actual database connection pool
      return 10
    } catch (error) {
      this.logger.error('Failed to get database connections', error)
      return 0
    }
  }

  private async getDatabaseActiveQueries(): Promise<number> {
    try {
      // Would query actual database for active queries
      return 5
    } catch (error) {
      this.logger.error('Failed to get active queries', error)
      return 0
    }
  }

  private async getDatabaseSlowQueries(): Promise<number> {
    try {
      // Would query actual database for slow queries
      return 2
    } catch (error) {
      this.logger.error('Failed to get slow queries', error)
      return 0
    }
  }

  private async getCacheMemoryUsage(): Promise<number> {
    try {
      // Would get actual cache memory usage
      return 128 * 1024 * 1024 // 128MB
    } catch (error) {
      this.logger.error('Failed to get cache memory usage', error)
      return 0
    }
  }

  private async getCacheHitRate(): Promise<number> {
    try {
      // Would get actual cache hit rate
      return 0.85 // 85%
    } catch (error) {
      this.logger.error('Failed to get cache hit rate', error)
      return 0
    }
  }

  private async getCacheConnections(): Promise<number> {
    try {
      // Would get actual cache connections
      return 5
    } catch (error) {
      this.logger.error('Failed to get cache connections', error)
      return 0
    }
  }

  private async performHealthCheck(service: any): Promise<boolean> {
    // Simplified health check - would use actual HTTP client
    return Math.random() > 0.1 // 90% success rate
  }
}
