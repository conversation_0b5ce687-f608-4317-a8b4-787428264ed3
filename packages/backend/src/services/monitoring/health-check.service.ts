import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { PrismaClient } from '@prisma/client'
import { EventEmitter } from 'events'

export interface HealthCheck {
  name: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  responseTime: number
  timestamp: Date
  details?: Record<string, any>
  error?: string
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: Date
  uptime: number
  version: string
  environment: string
  checks: HealthCheck[]
  summary: {
    total: number
    healthy: number
    degraded: number
    unhealthy: number
  }
}

export class HealthCheckService extends EventEmitter {
  private logger: Logger
  private cache: CacheService
  private prisma: PrismaClient
  private checks: Map<string, () => Promise<HealthCheck>>
  private lastHealthCheck?: SystemHealth

  constructor(cache: CacheService, prisma: PrismaClient) {
    super()
    this.logger = new Logger('HealthCheckService')
    this.cache = cache
    this.prisma = prisma
    this.checks = new Map()

    // Register default health checks
    this.registerDefaultChecks()
  }

  /**
   * Register a health check
   */
  registerCheck(name: string, checkFn: () => Promise<HealthCheck>): void {
    this.checks.set(name, checkFn)
    this.logger.debug('Registered health check', { name })
  }

  /**
   * Perform all health checks
   */
  async performHealthChecks(): Promise<SystemHealth> {
    const startTime = Date.now()
    const checks: HealthCheck[] = []

    // Run all health checks
    for (const [name, checkFn] of this.checks) {
      try {
        const check = await checkFn()
        checks.push(check)
      } catch (error) {
        checks.push({
          name,
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          timestamp: new Date(),
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Calculate overall status
    const summary = {
      total: checks.length,
      healthy: checks.filter(c => c.status === 'healthy').length,
      degraded: checks.filter(c => c.status === 'degraded').length,
      unhealthy: checks.filter(c => c.status === 'unhealthy').length
    }

    let overallStatus: SystemHealth['status'] = 'healthy'
    if (summary.unhealthy > 0) {
      overallStatus = 'unhealthy'
    } else if (summary.degraded > 0) {
      overallStatus = 'degraded'
    }

    const systemHealth: SystemHealth = {
      status: overallStatus,
      timestamp: new Date(),
      uptime: process.uptime(),
      version: process.env.APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks,
      summary
    }

    this.lastHealthCheck = systemHealth
    this.emit('health:checked', systemHealth)

    return systemHealth
  }

  /**
   * Get last health check result
   */
  getLastHealthCheck(): SystemHealth | undefined {
    return this.lastHealthCheck
  }

  /**
   * Get specific health check
   */
  async getHealthCheck(name: string): Promise<HealthCheck | null> {
    const checkFn = this.checks.get(name)
    if (!checkFn) return null

    try {
      return await checkFn()
    } catch (error) {
      return {
        name,
        status: 'unhealthy',
        responseTime: 0,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Register default health checks
   */
  private registerDefaultChecks(): void {
    // Database health check
    this.registerCheck('database', async () => {
      const startTime = Date.now()
      try {
        await this.prisma.$queryRaw`SELECT 1`
        return {
          name: 'database',
          status: 'healthy',
          responseTime: Date.now() - startTime,
          timestamp: new Date(),
          details: { type: 'postgresql' }
        }
      } catch (error) {
        return {
          name: 'database',
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          timestamp: new Date(),
          error: error instanceof Error ? error.message : 'Database connection failed'
        }
      }
    })

    // Cache health check
    this.registerCheck('cache', async () => {
      const startTime = Date.now()
      try {
        const testKey = 'health-check-test'
        const testValue = Date.now().toString()
        
        await this.cache.set(testKey, testValue, 10)
        const retrieved = await this.cache.get(testKey)
        await this.cache.delete(testKey)

        if (retrieved === testValue) {
          return {
            name: 'cache',
            status: 'healthy',
            responseTime: Date.now() - startTime,
            timestamp: new Date(),
            details: { type: 'redis' }
          }
        } else {
          throw new Error('Cache read/write test failed')
        }
      } catch (error) {
        return {
          name: 'cache',
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          timestamp: new Date(),
          error: error instanceof Error ? error.message : 'Cache connection failed'
        }
      }
    })

    // Memory health check
    this.registerCheck('memory', async () => {
      const startTime = Date.now()
      const memUsage = process.memoryUsage()
      const totalMem = require('os').totalmem()
      const freeMem = require('os').freemem()
      const usedMem = totalMem - freeMem
      const memoryUsagePercent = (usedMem / totalMem) * 100

      let status: HealthCheck['status'] = 'healthy'
      if (memoryUsagePercent > 90) {
        status = 'unhealthy'
      } else if (memoryUsagePercent > 80) {
        status = 'degraded'
      }

      return {
        name: 'memory',
        status,
        responseTime: Date.now() - startTime,
        timestamp: new Date(),
        details: {
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
          systemUsagePercent: Math.round(memoryUsagePercent),
          rss: Math.round(memUsage.rss / 1024 / 1024)
        }
      }
    })

    // CPU health check
    this.registerCheck('cpu', async () => {
      const startTime = Date.now()
      const cpus = require('os').cpus()
      const loadAvg = require('os').loadavg()
      
      // Calculate CPU usage (simplified)
      let totalIdle = 0
      let totalTick = 0
      
      for (const cpu of cpus) {
        for (const type in cpu.times) {
          totalTick += cpu.times[type as keyof typeof cpu.times]
        }
        totalIdle += cpu.times.idle
      }
      
      const cpuUsage = 100 - (totalIdle / totalTick) * 100
      
      let status: HealthCheck['status'] = 'healthy'
      if (cpuUsage > 90) {
        status = 'unhealthy'
      } else if (cpuUsage > 80) {
        status = 'degraded'
      }

      return {
        name: 'cpu',
        status,
        responseTime: Date.now() - startTime,
        timestamp: new Date(),
        details: {
          usage: Math.round(cpuUsage),
          loadAverage: loadAvg.map(load => Math.round(load * 100) / 100),
          cores: cpus.length
        }
      }
    })

    // Disk health check
    this.registerCheck('disk', async () => {
      const startTime = Date.now()
      
      try {
        const fs = require('fs')
        const stats = fs.statSync('/')
        
        // Simplified disk check - would use proper disk monitoring
        const diskUsage = 45 // Mock 45% usage
        
        let status: HealthCheck['status'] = 'healthy'
        if (diskUsage > 95) {
          status = 'unhealthy'
        } else if (diskUsage > 85) {
          status = 'degraded'
        }

        return {
          name: 'disk',
          status,
          responseTime: Date.now() - startTime,
          timestamp: new Date(),
          details: {
            usage: diskUsage,
            available: '500GB',
            total: '1TB'
          }
        }
      } catch (error) {
        return {
          name: 'disk',
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          timestamp: new Date(),
          error: error instanceof Error ? error.message : 'Disk check failed'
        }
      }
    })
  }
}
