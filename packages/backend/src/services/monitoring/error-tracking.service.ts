import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { PrismaClient } from '@prisma/client'
import { EventEmitter } from 'events'
import * as crypto from 'crypto'

export interface ErrorEvent {
  id: string
  fingerprint: string
  message: string
  type: string
  level: 'error' | 'warning' | 'info' | 'debug'
  timestamp: Date
  
  // Context information
  userId?: string
  tenantId?: string
  requestId?: string
  sessionId?: string
  
  // Technical details
  stackTrace?: string
  fileName?: string
  lineNumber?: number
  columnNumber?: number
  functionName?: string
  
  // Request context
  url?: string
  method?: string
  userAgent?: string
  ip?: string
  headers?: Record<string, string>
  
  // Environment context
  environment: string
  version?: string
  platform?: string
  
  // Additional context
  tags: Record<string, string>
  extra: Record<string, any>
  
  // Grouping and tracking
  groupId: string
  firstSeen: Date
  lastSeen: Date
  count: number
  
  // Status
  status: 'unresolved' | 'resolved' | 'ignored'
  assignedTo?: string
  resolvedAt?: Date
  resolvedBy?: string
}

export interface ErrorGroup {
  id: string
  fingerprint: string
  title: string
  message: string
  type: string
  level: 'error' | 'warning' | 'info' | 'debug'
  
  // Occurrence tracking
  firstSeen: Date
  lastSeen: Date
  count: number
  
  // Recent events
  recentEvents: ErrorEvent[]
  
  // Status and assignment
  status: 'unresolved' | 'resolved' | 'ignored'
  assignedTo?: string
  resolvedAt?: Date
  resolvedBy?: string
  
  // Metadata
  tags: Record<string, string>
  affectedUsers: number
  environments: string[]
  
  // Trends
  trend: 'increasing' | 'decreasing' | 'stable'
  frequency: number // events per hour
}

export interface ErrorStats {
  totalErrors: number
  newErrors: number
  resolvedErrors: number
  errorRate: number
  
  // Time-based stats
  errorsLast24h: number
  errorsLast7d: number
  errorsLast30d: number
  
  // By level
  errorsByLevel: Record<string, number>
  
  // By environment
  errorsByEnvironment: Record<string, number>
  
  // Top error groups
  topErrorGroups: ErrorGroup[]
  
  // Trends
  errorTrend: Array<{
    timestamp: Date
    count: number
  }>
}

export interface ErrorTrackingConfig {
  enabled: boolean
  captureUnhandledRejections: boolean
  captureUncaughtExceptions: boolean
  
  // Filtering
  ignoreErrors: string[] // Error messages to ignore
  ignoreUrls: string[] // URLs to ignore
  allowUrls: string[] // Only capture errors from these URLs
  
  // Sampling
  sampleRate: number // 0.0 to 1.0
  
  // Grouping
  groupingRules: Array<{
    pattern: string
    replacement: string
  }>
  
  // Retention
  retentionDays: number
  
  // Notifications
  notifications: {
    enabled: boolean
    channels: string[]
    thresholds: {
      newError: boolean
      errorSpike: number // percentage increase
      errorRate: number // errors per minute
    }
  }
}

export class ErrorTrackingService extends EventEmitter {
  private logger: Logger
  private cache: CacheService
  private prisma: PrismaClient
  private config: ErrorTrackingConfig
  private errorGroups: Map<string, ErrorGroup>
  private recentErrors: ErrorEvent[]

  constructor(cache: CacheService, prisma: PrismaClient, config?: Partial<ErrorTrackingConfig>) {
    super()
    this.logger = new Logger('ErrorTrackingService')
    this.cache = cache
    this.prisma = prisma
    this.errorGroups = new Map()
    this.recentErrors = []

    // Initialize configuration
    this.config = {
      enabled: true,
      captureUnhandledRejections: true,
      captureUncaughtExceptions: true,
      ignoreErrors: [
        'Network Error',
        'Script error',
        'Non-Error promise rejection captured'
      ],
      ignoreUrls: [
        '/health',
        '/metrics',
        '/favicon.ico'
      ],
      allowUrls: [],
      sampleRate: 1.0,
      groupingRules: [],
      retentionDays: 30,
      notifications: {
        enabled: true,
        channels: ['email'],
        thresholds: {
          newError: true,
          errorSpike: 50, // 50% increase
          errorRate: 10 // 10 errors per minute
        }
      },
      ...config
    }

    if (this.config.enabled) {
      this.setupGlobalErrorHandlers()
    }
  }

  /**
   * Capture an error event
   */
  captureError(error: Error | string, context: Partial<ErrorEvent> = {}): string {
    if (!this.config.enabled) return 'disabled'

    try {
      // Apply sampling
      if (Math.random() > this.config.sampleRate) {
        return 'sampled'
      }

      // Create error event
      const errorEvent = this.createErrorEvent(error, context)

      // Apply filters
      if (this.shouldIgnoreError(errorEvent)) {
        return 'ignored'
      }

      // Process the error
      this.processError(errorEvent)

      return errorEvent.id
    } catch (processingError) {
      this.logger.error('Failed to capture error', processingError)
      return 'failed'
    }
  }

  /**
   * Capture exception with context
   */
  captureException(error: Error, context: Partial<ErrorEvent> = {}): string {
    return this.captureError(error, {
      ...context,
      level: 'error',
      stackTrace: error.stack,
      type: error.constructor.name
    })
  }

  /**
   * Capture message
   */
  captureMessage(message: string, level: ErrorEvent['level'] = 'info', context: Partial<ErrorEvent> = {}): string {
    return this.captureError(message, {
      ...context,
      level,
      type: 'message'
    })
  }

  /**
   * Get error statistics
   */
  async getErrorStats(timeRange: '24h' | '7d' | '30d' = '24h'): Promise<ErrorStats> {
    try {
      const now = new Date()
      const cutoff = this.getTimeRangeCutoff(timeRange)
      
      const recentErrors = this.recentErrors.filter(e => e.timestamp >= cutoff)
      const errorGroups = Array.from(this.errorGroups.values())

      const stats: ErrorStats = {
        totalErrors: this.recentErrors.length,
        newErrors: recentErrors.filter(e => e.firstSeen >= cutoff).length,
        resolvedErrors: errorGroups.filter(g => g.status === 'resolved').length,
        errorRate: this.calculateErrorRate(recentErrors),
        
        errorsLast24h: this.recentErrors.filter(e => 
          e.timestamp >= new Date(now.getTime() - 24 * 60 * 60 * 1000)
        ).length,
        errorsLast7d: this.recentErrors.filter(e => 
          e.timestamp >= new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        ).length,
        errorsLast30d: this.recentErrors.filter(e => 
          e.timestamp >= new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        ).length,
        
        errorsByLevel: this.groupErrorsByLevel(recentErrors),
        errorsByEnvironment: this.groupErrorsByEnvironment(recentErrors),
        topErrorGroups: this.getTopErrorGroups(10),
        errorTrend: this.calculateErrorTrend(timeRange)
      }

      return stats
    } catch (error) {
      this.logger.error('Failed to get error stats', error)
      throw error
    }
  }

  /**
   * Get error group by ID
   */
  getErrorGroup(groupId: string): ErrorGroup | undefined {
    return this.errorGroups.get(groupId)
  }

  /**
   * Get all error groups
   */
  getErrorGroups(filters: {
    status?: ErrorGroup['status']
    level?: ErrorEvent['level']
    environment?: string
    limit?: number
    offset?: number
  } = {}): ErrorGroup[] {
    let groups = Array.from(this.errorGroups.values())

    // Apply filters
    if (filters.status) {
      groups = groups.filter(g => g.status === filters.status)
    }
    if (filters.level) {
      groups = groups.filter(g => g.level === filters.level)
    }
    if (filters.environment) {
      groups = groups.filter(g => g.environments.includes(filters.environment))
    }

    // Sort by last seen (most recent first)
    groups.sort((a, b) => b.lastSeen.getTime() - a.lastSeen.getTime())

    // Apply pagination
    const offset = filters.offset || 0
    const limit = filters.limit || 50
    return groups.slice(offset, offset + limit)
  }

  /**
   * Resolve error group
   */
  resolveErrorGroup(groupId: string, resolvedBy: string): boolean {
    const group = this.errorGroups.get(groupId)
    if (!group) return false

    group.status = 'resolved'
    group.resolvedAt = new Date()
    group.resolvedBy = resolvedBy

    this.emit('error:resolved', group)
    return true
  }

  /**
   * Ignore error group
   */
  ignoreErrorGroup(groupId: string): boolean {
    const group = this.errorGroups.get(groupId)
    if (!group) return false

    group.status = 'ignored'
    this.emit('error:ignored', group)
    return true
  }

  /**
   * Assign error group
   */
  assignErrorGroup(groupId: string, assignedTo: string): boolean {
    const group = this.errorGroups.get(groupId)
    if (!group) return false

    group.assignedTo = assignedTo
    this.emit('error:assigned', group)
    return true
  }

  /**
   * Private helper methods
   */
  private createErrorEvent(error: Error | string, context: Partial<ErrorEvent>): ErrorEvent {
    const timestamp = new Date()
    const message = error instanceof Error ? error.message : error
    const stackTrace = error instanceof Error ? error.stack : undefined
    
    // Generate fingerprint for grouping
    const fingerprint = this.generateFingerprint(message, stackTrace)
    
    const errorEvent: ErrorEvent = {
      id: this.generateId(),
      fingerprint,
      message,
      type: error instanceof Error ? error.constructor.name : 'message',
      level: 'error',
      timestamp,
      environment: process.env.NODE_ENV || 'development',
      tags: {},
      extra: {},
      groupId: fingerprint,
      firstSeen: timestamp,
      lastSeen: timestamp,
      count: 1,
      status: 'unresolved',
      ...context
    }

    return errorEvent
  }

  private processError(errorEvent: ErrorEvent): void {
    // Add to recent errors
    this.recentErrors.push(errorEvent)
    
    // Cleanup old errors
    this.cleanupOldErrors()

    // Update or create error group
    this.updateErrorGroup(errorEvent)

    // Check for alerts
    this.checkErrorAlerts(errorEvent)

    // Emit event
    this.emit('error:captured', errorEvent)

    this.logger.debug('Processed error event', {
      id: errorEvent.id,
      message: errorEvent.message,
      level: errorEvent.level,
      groupId: errorEvent.groupId
    })
  }

  private updateErrorGroup(errorEvent: ErrorEvent): void {
    let group = this.errorGroups.get(errorEvent.groupId)

    if (!group) {
      // Create new group
      group = {
        id: errorEvent.groupId,
        fingerprint: errorEvent.fingerprint,
        title: this.generateGroupTitle(errorEvent),
        message: errorEvent.message,
        type: errorEvent.type,
        level: errorEvent.level,
        firstSeen: errorEvent.timestamp,
        lastSeen: errorEvent.timestamp,
        count: 1,
        recentEvents: [errorEvent],
        status: 'unresolved',
        tags: errorEvent.tags,
        affectedUsers: errorEvent.userId ? 1 : 0,
        environments: [errorEvent.environment],
        trend: 'stable',
        frequency: 0
      }

      this.errorGroups.set(errorEvent.groupId, group)
      this.emit('error:new_group', group)
    } else {
      // Update existing group
      group.lastSeen = errorEvent.timestamp
      group.count++
      group.recentEvents.unshift(errorEvent)
      
      // Keep only recent events
      if (group.recentEvents.length > 10) {
        group.recentEvents = group.recentEvents.slice(0, 10)
      }

      // Update affected users
      if (errorEvent.userId && !group.recentEvents.some(e => e.userId === errorEvent.userId)) {
        group.affectedUsers++
      }

      // Update environments
      if (!group.environments.includes(errorEvent.environment)) {
        group.environments.push(errorEvent.environment)
      }

      // Update trend
      group.trend = this.calculateTrend(group)
      group.frequency = this.calculateFrequency(group)
    }
  }

  private shouldIgnoreError(errorEvent: ErrorEvent): boolean {
    // Check ignore patterns
    for (const pattern of this.config.ignoreErrors) {
      if (errorEvent.message.includes(pattern)) {
        return true
      }
    }

    // Check ignore URLs
    if (errorEvent.url) {
      for (const pattern of this.config.ignoreUrls) {
        if (errorEvent.url.includes(pattern)) {
          return true
        }
      }
    }

    // Check allow URLs
    if (this.config.allowUrls.length > 0 && errorEvent.url) {
      const isAllowed = this.config.allowUrls.some(pattern => 
        errorEvent.url!.includes(pattern)
      )
      if (!isAllowed) {
        return true
      }
    }

    return false
  }

  private generateFingerprint(message: string, stackTrace?: string): string {
    // Create a fingerprint for grouping similar errors
    let content = message

    if (stackTrace) {
      // Extract the first few lines of stack trace for fingerprinting
      const lines = stackTrace.split('\n').slice(0, 3)
      content += lines.join('\n')
    }

    // Apply grouping rules
    for (const rule of this.config.groupingRules) {
      content = content.replace(new RegExp(rule.pattern, 'g'), rule.replacement)
    }

    return crypto.createHash('md5').update(content).digest('hex')
  }

  private generateGroupTitle(errorEvent: ErrorEvent): string {
    if (errorEvent.type === 'message') {
      return errorEvent.message
    }

    return `${errorEvent.type}: ${errorEvent.message}`
  }

  private generateId(): string {
    return `err-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  private setupGlobalErrorHandlers(): void {
    if (this.config.captureUncaughtExceptions) {
      process.on('uncaughtException', (error) => {
        this.captureException(error, {
          tags: { source: 'uncaughtException' }
        })
      })
    }

    if (this.config.captureUnhandledRejections) {
      process.on('unhandledRejection', (reason) => {
        const error = reason instanceof Error ? reason : new Error(String(reason))
        this.captureException(error, {
          tags: { source: 'unhandledRejection' }
        })
      })
    }
  }

  private cleanupOldErrors(): void {
    const cutoff = new Date(Date.now() - this.config.retentionDays * 24 * 60 * 60 * 1000)
    this.recentErrors = this.recentErrors.filter(e => e.timestamp >= cutoff)
  }

  private checkErrorAlerts(errorEvent: ErrorEvent): void {
    if (!this.config.notifications.enabled) return

    // Check for new error type
    if (this.config.notifications.thresholds.newError) {
      const group = this.errorGroups.get(errorEvent.groupId)
      if (group && group.count === 1) {
        this.emit('alert:new_error', { errorEvent, group })
      }
    }

    // Check error rate
    const recentErrors = this.recentErrors.filter(e => 
      e.timestamp >= new Date(Date.now() - 60000) // Last minute
    )
    if (recentErrors.length >= this.config.notifications.thresholds.errorRate) {
      this.emit('alert:error_rate', { count: recentErrors.length, threshold: this.config.notifications.thresholds.errorRate })
    }
  }

  private getTimeRangeCutoff(timeRange: string): Date {
    const now = new Date()
    switch (timeRange) {
      case '24h':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000)
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      default:
        return new Date(now.getTime() - 24 * 60 * 60 * 1000)
    }
  }

  private calculateErrorRate(errors: ErrorEvent[]): number {
    if (errors.length === 0) return 0
    
    const timeSpan = 60 * 60 * 1000 // 1 hour in milliseconds
    return (errors.length / timeSpan) * 60 * 1000 // errors per minute
  }

  private groupErrorsByLevel(errors: ErrorEvent[]): Record<string, number> {
    const groups: Record<string, number> = {}
    
    for (const error of errors) {
      groups[error.level] = (groups[error.level] || 0) + 1
    }
    
    return groups
  }

  private groupErrorsByEnvironment(errors: ErrorEvent[]): Record<string, number> {
    const groups: Record<string, number> = {}
    
    for (const error of errors) {
      groups[error.environment] = (groups[error.environment] || 0) + 1
    }
    
    return groups
  }

  private getTopErrorGroups(limit: number): ErrorGroup[] {
    return Array.from(this.errorGroups.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, limit)
  }

  private calculateErrorTrend(timeRange: string): Array<{ timestamp: Date; count: number }> {
    // Simplified trend calculation
    const now = new Date()
    const points = []
    const interval = timeRange === '24h' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000 // 1 hour or 1 day

    for (let i = 0; i < 24; i++) {
      const timestamp = new Date(now.getTime() - i * interval)
      const count = this.recentErrors.filter(e => 
        e.timestamp >= timestamp && e.timestamp < new Date(timestamp.getTime() + interval)
      ).length

      points.unshift({ timestamp, count })
    }

    return points
  }

  private calculateTrend(group: ErrorGroup): 'increasing' | 'decreasing' | 'stable' {
    // Simplified trend calculation based on recent events
    if (group.recentEvents.length < 2) return 'stable'
    
    const recent = group.recentEvents.slice(0, 5).length
    const older = group.recentEvents.slice(5, 10).length
    
    if (recent > older * 1.5) return 'increasing'
    if (recent < older * 0.5) return 'decreasing'
    return 'stable'
  }

  private calculateFrequency(group: ErrorGroup): number {
    // Calculate events per hour
    const timeSpan = Date.now() - group.firstSeen.getTime()
    const hours = timeSpan / (60 * 60 * 1000)
    return hours > 0 ? group.count / hours : 0
  }
}
