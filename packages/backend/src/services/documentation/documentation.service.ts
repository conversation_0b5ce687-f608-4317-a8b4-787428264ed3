import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { PrismaClient } from '@prisma/client'
import { FileStorageService } from '@/services/file-storage.service'

export interface DocumentationArticle {
  id: string
  title: string
  content: string
  summary?: string
  tenantId: string
  
  // Organization
  category: DocumentCategory
  subcategory?: string
  tags: string[]
  
  // Content metadata
  type: 'ARTICLE' | 'GUIDE' | 'API_DOC' | 'FAQ' | 'TUTORIAL' | 'VIDEO'
  format: 'MARKDOWN' | 'HTML' | 'RICH_TEXT'
  language: string
  
  // Visibility and access
  visibility: 'PUBLIC' | 'INTERNAL' | 'PRIVATE'
  allowedRoles: string[]
  allowedUsers: string[]
  
  // Versioning
  version: string
  parentId?: string // For versioned documents
  isLatest: boolean
  
  // SEO and discovery
  slug: string
  metaDescription?: string
  keywords: string[]
  
  // Media attachments
  attachments: DocumentAttachment[]
  
  // Collaboration
  authors: string[]
  reviewers: string[]
  lastReviewedAt?: Date
  
  // Analytics
  viewCount: number
  rating: number
  ratingCount: number
  
  // Status
  status: 'DRAFT' | 'REVIEW' | 'PUBLISHED' | 'ARCHIVED'
  publishedAt?: Date
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedBy?: string
  updatedAt?: Date
}

export interface DocumentCategory {
  id: string
  name: string
  description?: string
  icon?: string
  color?: string
  order: number
  parentId?: string
  isActive: boolean
}

export interface DocumentAttachment {
  id: string
  name: string
  type: 'IMAGE' | 'VIDEO' | 'DOCUMENT' | 'AUDIO'
  url: string
  size: number
  mimeType: string
  description?: string
  uploadedAt: Date
}

export interface FAQ {
  id: string
  question: string
  answer: string
  tenantId: string
  
  // Organization
  category: string
  subcategory?: string
  tags: string[]
  
  // Metadata
  order: number
  isPopular: boolean
  
  // Analytics
  viewCount: number
  helpfulCount: number
  notHelpfulCount: number
  
  // Related content
  relatedArticles: string[]
  relatedFAQs: string[]
  
  // Status
  isActive: boolean
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedBy?: string
  updatedAt?: Date
}

export interface VideoTutorial {
  id: string
  title: string
  description: string
  tenantId: string
  
  // Video details
  videoUrl: string
  thumbnailUrl?: string
  duration: number // seconds
  transcript?: string
  
  // Organization
  category: string
  subcategory?: string
  tags: string[]
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED'
  
  // Content structure
  chapters: VideoChapter[]
  
  // Analytics
  viewCount: number
  rating: number
  ratingCount: number
  completionRate: number
  
  // Status
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  publishedAt?: Date
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedBy?: string
  updatedAt?: Date
}

export interface VideoChapter {
  id: string
  title: string
  startTime: number // seconds
  endTime: number // seconds
  description?: string
}

export interface DocumentationSearch {
  query: string
  filters?: {
    category?: string
    type?: DocumentationArticle['type']
    language?: string
    tags?: string[]
    dateRange?: {
      start: Date
      end: Date
    }
  }
  sort?: {
    field: 'relevance' | 'date' | 'popularity' | 'rating'
    direction: 'ASC' | 'DESC'
  }
  pagination?: {
    page: number
    limit: number
  }
}

export interface SearchResult {
  articles: Array<{
    article: DocumentationArticle
    score: number
    highlights: Record<string, string[]>
  }>
  faqs: Array<{
    faq: FAQ
    score: number
    highlights: Record<string, string[]>
  }>
  videos: Array<{
    video: VideoTutorial
    score: number
    highlights: Record<string, string[]>
  }>
  total: number
  facets: SearchFacet[]
  suggestions: string[]
}

export interface SearchFacet {
  field: string
  name: string
  buckets: Array<{
    key: string
    count: number
  }>
}

export interface DocumentationAnalytics {
  period: string
  tenantId: string
  
  // Content metrics
  totalArticles: number
  totalFAQs: number
  totalVideos: number
  newContent: number
  updatedContent: number
  
  // Usage metrics
  totalViews: number
  uniqueUsers: number
  averageSessionDuration: number
  bounceRate: number
  
  // Popular content
  topArticles: Array<{
    id: string
    title: string
    views: number
    rating: number
  }>
  topSearches: Array<{
    query: string
    count: number
    resultCount: number
  }>
  
  // User behavior
  searchSuccessRate: number
  feedbackScore: number
  helpfulnessRatio: number
  
  // Content gaps
  zeroResultQueries: string[]
  lowRatedContent: string[]
  
  // Trends
  trends: {
    viewsChange: number
    engagementChange: number
    satisfactionChange: number
  }
  
  lastUpdated: Date
}

export interface DocumentVersion {
  id: string
  documentId: string
  version: string
  content: string
  changeLog: string
  
  // Metadata
  createdBy: string
  createdAt: Date
}

export class DocumentationService {
  private logger: Logger
  private cache: CacheService
  private prisma: PrismaClient
  private fileStorage: FileStorageService

  constructor(
    cache: CacheService,
    prisma: PrismaClient,
    fileStorage: FileStorageService
  ) {
    this.logger = new Logger('DocumentationService')
    this.cache = cache
    this.prisma = prisma
    this.fileStorage = fileStorage
  }

  /**
   * Create documentation article
   */
  async createArticle(
    article: Omit<DocumentationArticle, 'id' | 'createdAt' | 'viewCount' | 'rating' | 'ratingCount'>,
    userId: string
  ): Promise<DocumentationArticle> {
    try {
      const newArticle: DocumentationArticle = {
        ...article,
        id: this.generateId(),
        createdBy: userId,
        createdAt: new Date(),
        viewCount: 0,
        rating: 0,
        ratingCount: 0
      }

      // Generate slug if not provided
      if (!newArticle.slug) {
        newArticle.slug = this.generateSlug(newArticle.title)
      }

      // Save article
      await this.saveArticle(newArticle)

      // Index for search
      await this.indexArticle(newArticle)

      this.logger.info('Created documentation article', {
        articleId: newArticle.id,
        title: article.title,
        category: article.category.name,
        type: article.type,
        userId
      })

      return newArticle
    } catch (error) {
      this.logger.error('Failed to create article', error)
      throw error
    }
  }

  /**
   * Create FAQ
   */
  async createFAQ(
    faq: Omit<FAQ, 'id' | 'createdAt' | 'viewCount' | 'helpfulCount' | 'notHelpfulCount'>,
    userId: string
  ): Promise<FAQ> {
    try {
      const newFAQ: FAQ = {
        ...faq,
        id: this.generateId(),
        createdBy: userId,
        createdAt: new Date(),
        viewCount: 0,
        helpfulCount: 0,
        notHelpfulCount: 0
      }

      // Save FAQ
      await this.saveFAQ(newFAQ)

      // Index for search
      await this.indexFAQ(newFAQ)

      this.logger.info('Created FAQ', {
        faqId: newFAQ.id,
        question: faq.question.substring(0, 100),
        category: faq.category,
        userId
      })

      return newFAQ
    } catch (error) {
      this.logger.error('Failed to create FAQ', error)
      throw error
    }
  }

  /**
   * Create video tutorial
   */
  async createVideoTutorial(
    video: Omit<VideoTutorial, 'id' | 'createdAt' | 'viewCount' | 'rating' | 'ratingCount' | 'completionRate'>,
    userId: string
  ): Promise<VideoTutorial> {
    try {
      const newVideo: VideoTutorial = {
        ...video,
        id: this.generateId(),
        createdBy: userId,
        createdAt: new Date(),
        viewCount: 0,
        rating: 0,
        ratingCount: 0,
        completionRate: 0
      }

      // Save video
      await this.saveVideoTutorial(newVideo)

      // Index for search
      await this.indexVideo(newVideo)

      this.logger.info('Created video tutorial', {
        videoId: newVideo.id,
        title: video.title,
        duration: video.duration,
        category: video.category,
        userId
      })

      return newVideo
    } catch (error) {
      this.logger.error('Failed to create video tutorial', error)
      throw error
    }
  }

  /**
   * Search documentation
   */
  async searchDocumentation(search: DocumentationSearch, tenantId: string): Promise<SearchResult> {
    try {
      const startTime = Date.now()

      // Execute search across all content types
      const articles = await this.searchArticles(search, tenantId)
      const faqs = await this.searchFAQs(search, tenantId)
      const videos = await this.searchVideos(search, tenantId)

      // Generate facets
      const facets = await this.generateSearchFacets(search, tenantId)

      // Generate suggestions for empty results
      const suggestions = articles.length === 0 && faqs.length === 0 && videos.length === 0
        ? await this.generateSearchSuggestions(search.query, tenantId)
        : []

      const result: SearchResult = {
        articles,
        faqs,
        videos,
        total: articles.length + faqs.length + videos.length,
        facets,
        suggestions
      }

      // Track search analytics
      await this.trackSearchAnalytics(search, result, Date.now() - startTime)

      this.logger.info('Executed documentation search', {
        query: search.query,
        total: result.total,
        took: Date.now() - startTime,
        tenantId
      })

      return result
    } catch (error) {
      this.logger.error('Failed to search documentation', error)
      throw error
    }
  }

  /**
   * Get article by slug
   */
  async getArticleBySlug(slug: string, tenantId: string, userId?: string): Promise<DocumentationArticle | null> {
    try {
      const article = await this.findArticleBySlug(slug, tenantId)
      if (!article) {
        return null
      }

      // Check access permissions
      if (!this.canUserAccessArticle(article, userId)) {
        return null
      }

      // Increment view count
      article.viewCount++
      await this.saveArticle(article)

      // Track view analytics
      await this.trackArticleView(article, userId)

      return article
    } catch (error) {
      this.logger.error('Failed to get article by slug', error)
      return null
    }
  }

  /**
   * Rate content
   */
  async rateContent(
    contentId: string,
    contentType: 'ARTICLE' | 'FAQ' | 'VIDEO',
    rating: number,
    userId: string
  ): Promise<void> {
    try {
      if (rating < 1 || rating > 5) {
        throw new Error('Rating must be between 1 and 5')
      }

      // Update content rating
      switch (contentType) {
        case 'ARTICLE':
          await this.updateArticleRating(contentId, rating, userId)
          break
        case 'FAQ':
          await this.updateFAQHelpfulness(contentId, rating > 3, userId)
          break
        case 'VIDEO':
          await this.updateVideoRating(contentId, rating, userId)
          break
      }

      this.logger.info('Content rated', {
        contentId,
        contentType,
        rating,
        userId
      })
    } catch (error) {
      this.logger.error('Failed to rate content', error)
      throw error
    }
  }

  /**
   * Get documentation analytics
   */
  async getAnalytics(tenantId: string, period?: string): Promise<DocumentationAnalytics> {
    try {
      const currentPeriod = period || this.getCurrentPeriod()
      const cacheKey = `docs:analytics:${tenantId}:${currentPeriod}`
      
      const cached = await this.cache.get<DocumentationAnalytics>(cacheKey)
      if (cached) {
        return cached
      }

      // Calculate analytics
      const analytics = await this.calculateAnalytics(tenantId, currentPeriod)
      
      await this.cache.set(cacheKey, analytics, 3600) // Cache for 1 hour
      
      return analytics
    } catch (error) {
      this.logger.error('Failed to get documentation analytics', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async searchArticles(search: DocumentationSearch, tenantId: string): Promise<Array<{
    article: DocumentationArticle
    score: number
    highlights: Record<string, string[]>
  }>> {
    // In a real implementation, this would use a search engine like Elasticsearch
    return []
  }

  private async searchFAQs(search: DocumentationSearch, tenantId: string): Promise<Array<{
    faq: FAQ
    score: number
    highlights: Record<string, string[]>
  }>> {
    // In a real implementation, this would use a search engine
    return []
  }

  private async searchVideos(search: DocumentationSearch, tenantId: string): Promise<Array<{
    video: VideoTutorial
    score: number
    highlights: Record<string, string[]>
  }>> {
    // In a real implementation, this would use a search engine
    return []
  }

  private async generateSearchFacets(search: DocumentationSearch, tenantId: string): Promise<SearchFacet[]> {
    return [
      {
        field: 'category',
        name: 'Category',
        buckets: [
          { key: 'User Guides', count: 25 },
          { key: 'API Documentation', count: 18 },
          { key: 'Tutorials', count: 12 }
        ]
      },
      {
        field: 'type',
        name: 'Content Type',
        buckets: [
          { key: 'ARTICLE', count: 35 },
          { key: 'FAQ', count: 20 },
          { key: 'VIDEO', count: 10 }
        ]
      }
    ]
  }

  private async generateSearchSuggestions(query: string, tenantId: string): Promise<string[]> {
    // Generate search suggestions based on popular queries and content
    return [
      'getting started',
      'API authentication',
      'user management',
      'deal workflow'
    ].filter(suggestion => 
      suggestion.toLowerCase().includes(query.toLowerCase())
    )
  }

  private canUserAccessArticle(article: DocumentationArticle, userId?: string): boolean {
    if (article.visibility === 'PUBLIC') {
      return true
    }

    if (!userId) {
      return false
    }

    if (article.visibility === 'PRIVATE') {
      return article.allowedUsers.includes(userId) || article.createdBy === userId
    }

    // For INTERNAL visibility, would check user's organization
    return true
  }

  private async updateArticleRating(articleId: string, rating: number, userId: string): Promise<void> {
    const article = await this.getArticle(articleId)
    if (!article) return

    // Calculate new rating
    const totalRating = article.rating * article.ratingCount + rating
    article.ratingCount++
    article.rating = totalRating / article.ratingCount

    await this.saveArticle(article)
  }

  private async updateFAQHelpfulness(faqId: string, isHelpful: boolean, userId: string): Promise<void> {
    const faq = await this.getFAQ(faqId)
    if (!faq) return

    if (isHelpful) {
      faq.helpfulCount++
    } else {
      faq.notHelpfulCount++
    }

    await this.saveFAQ(faq)
  }

  private async updateVideoRating(videoId: string, rating: number, userId: string): Promise<void> {
    const video = await this.getVideoTutorial(videoId)
    if (!video) return

    // Calculate new rating
    const totalRating = video.rating * video.ratingCount + rating
    video.ratingCount++
    video.rating = totalRating / video.ratingCount

    await this.saveVideoTutorial(video)
  }

  private async calculateAnalytics(tenantId: string, period: string): Promise<DocumentationAnalytics> {
    // In a real implementation, this would calculate from database
    return {
      period,
      tenantId,
      totalArticles: 150,
      totalFAQs: 75,
      totalVideos: 25,
      newContent: 12,
      updatedContent: 8,
      totalViews: 5420,
      uniqueUsers: 234,
      averageSessionDuration: 180,
      bounceRate: 0.35,
      topArticles: [
        { id: '1', title: 'Getting Started Guide', views: 450, rating: 4.5 },
        { id: '2', title: 'API Authentication', views: 320, rating: 4.2 }
      ],
      topSearches: [
        { query: 'getting started', count: 89, resultCount: 15 },
        { query: 'API documentation', count: 67, resultCount: 12 }
      ],
      searchSuccessRate: 0.85,
      feedbackScore: 4.2,
      helpfulnessRatio: 0.78,
      zeroResultQueries: ['obscure feature', 'legacy system'],
      lowRatedContent: ['outdated-guide-1', 'complex-tutorial-2'],
      trends: {
        viewsChange: 0.15,
        engagementChange: 0.08,
        satisfactionChange: 0.05
      },
      lastUpdated: new Date()
    }
  }

  private async indexArticle(article: DocumentationArticle): Promise<void> {
    // Index article for search
    this.logger.debug('Indexing article for search', { articleId: article.id })
  }

  private async indexFAQ(faq: FAQ): Promise<void> {
    // Index FAQ for search
    this.logger.debug('Indexing FAQ for search', { faqId: faq.id })
  }

  private async indexVideo(video: VideoTutorial): Promise<void> {
    // Index video for search
    this.logger.debug('Indexing video for search', { videoId: video.id })
  }

  private async trackSearchAnalytics(search: DocumentationSearch, result: SearchResult, duration: number): Promise<void> {
    // Track search analytics
    this.logger.debug('Tracking search analytics', {
      query: search.query,
      resultCount: result.total,
      duration
    })
  }

  private async trackArticleView(article: DocumentationArticle, userId?: string): Promise<void> {
    // Track article view analytics
    this.logger.debug('Tracking article view', {
      articleId: article.id,
      userId
    })
  }

  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
  }

  private getCurrentPeriod(): string {
    const now = new Date()
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
  }

  private async saveArticle(article: DocumentationArticle): Promise<void> {
    await this.cache.set(`article:${article.id}`, article, 86400)
  }

  private async getArticle(articleId: string): Promise<DocumentationArticle | null> {
    return await this.cache.get<DocumentationArticle>(`article:${articleId}`)
  }

  private async findArticleBySlug(slug: string, tenantId: string): Promise<DocumentationArticle | null> {
    // In a real implementation, this would query the database
    return null
  }

  private async saveFAQ(faq: FAQ): Promise<void> {
    await this.cache.set(`faq:${faq.id}`, faq, 86400)
  }

  private async getFAQ(faqId: string): Promise<FAQ | null> {
    return await this.cache.get<FAQ>(`faq:${faqId}`)
  }

  private async saveVideoTutorial(video: VideoTutorial): Promise<void> {
    await this.cache.set(`video:${video.id}`, video, 86400)
  }

  private async getVideoTutorial(videoId: string): Promise<VideoTutorial | null> {
    return await this.cache.get<VideoTutorial>(`video:${videoId}`)
  }

  private generateId(): string {
    return `doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
