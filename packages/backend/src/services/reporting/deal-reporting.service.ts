import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { DealAnalyticsService } from '@/services/deal/deal-analytics.service'

export interface ReportConfig {
  id: string
  name: string
  description?: string
  type: ReportType
  parameters: ReportParameters
  schedule?: ReportSchedule
  format: ReportFormat
  recipients?: string[]
  isActive: boolean
}

export enum ReportType {
  DEAL_PIPELINE = 'deal_pipeline',
  DEAL_PERFORMANCE = 'deal_performance',
  TEAM_PERFORMANCE = 'team_performance',
  FORECAST = 'forecast',
  ACTIVITY_SUMMARY = 'activity_summary',
  CONVERSION_FUNNEL = 'conversion_funnel',
  DEAL_VELOCITY = 'deal_velocity',
  CUSTOM = 'custom'
}

export enum ReportFormat {
  PDF = 'pdf',
  EXCEL = 'excel',
  CSV = 'csv',
  JSON = 'json',
  HTML = 'html'
}

export interface ReportParameters {
  dateRange: {
    from: Date
    to: Date
  }
  filters?: {
    dealTypes?: string[]
    stages?: string[]
    assignees?: string[]
    sources?: string[]
    priorities?: string[]
  }
  groupBy?: string[]
  metrics?: string[]
  includeCharts?: boolean
  includeDetails?: boolean
}

export interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  dayOfWeek?: number // 0-6 for weekly
  dayOfMonth?: number // 1-31 for monthly
  time: string // HH:MM format
  timezone: string
}

export interface ReportData {
  id: string
  name: string
  generatedAt: Date
  parameters: ReportParameters
  data: any
  charts?: ChartData[]
  summary: ReportSummary
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'funnel' | 'gauge'
  title: string
  data: any[]
  config?: any
}

export interface ReportSummary {
  totalDeals: number
  totalValue: number
  averageDealSize: number
  conversionRate: number
  keyInsights: string[]
  recommendations: string[]
}

export class DealReportingService {
  private prisma: PrismaClient
  private cache: CacheService
  private analyticsService: DealAnalyticsService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.analyticsService = new DealAnalyticsService(prisma, cache)
    this.logger = new Logger('DealReportingService')
  }

  /**
   * Generate a deal pipeline report
   */
  async generateDealPipelineReport(
    tenantId: string,
    parameters: ReportParameters
  ): Promise<ReportData> {
    try {
      this.logger.info('Generating deal pipeline report', { tenantId, parameters })

      const analytics = await this.analyticsService.getDealAnalytics(
        tenantId,
        parameters.dateRange.from,
        parameters.dateRange.to
      )

      const pipelineMetrics = await this.analyticsService.getPipelineMetrics(tenantId)

      // Generate charts
      const charts: ChartData[] = []

      if (parameters.includeCharts) {
        // Pipeline funnel chart
        charts.push({
          type: 'funnel',
          title: 'Deal Pipeline Funnel',
          data: pipelineMetrics.map(stage => ({
            name: stage.stageName,
            value: stage.dealCount,
            totalValue: stage.totalValue
          }))
        })

        // Deals by status pie chart
        charts.push({
          type: 'pie',
          title: 'Deals by Status',
          data: Object.entries(analytics.dealsByStatus).map(([status, count]) => ({
            name: status.replace('_', ' '),
            value: count
          }))
        })

        // Value by stage bar chart
        charts.push({
          type: 'bar',
          title: 'Value by Stage',
          data: Object.entries(analytics.valueByStage).map(([stage, value]) => ({
            name: stage,
            value: value
          }))
        })
      }

      // Generate insights and recommendations
      const insights = this.generatePipelineInsights(analytics, pipelineMetrics)
      const recommendations = this.generatePipelineRecommendations(analytics, pipelineMetrics)

      const reportData: ReportData = {
        id: `pipeline_${Date.now()}`,
        name: 'Deal Pipeline Report',
        generatedAt: new Date(),
        parameters,
        data: {
          analytics,
          pipelineMetrics,
          deals: parameters.includeDetails ? await this.getDetailedDeals(tenantId, parameters) : undefined
        },
        charts,
        summary: {
          totalDeals: analytics.totalDeals,
          totalValue: analytics.totalValue,
          averageDealSize: analytics.averageDealSize,
          conversionRate: analytics.conversionRate,
          keyInsights: insights,
          recommendations
        }
      }

      this.logger.info('Deal pipeline report generated', { 
        tenantId, 
        reportId: reportData.id,
        totalDeals: analytics.totalDeals 
      })

      return reportData
    } catch (error) {
      this.logger.error('Failed to generate deal pipeline report', error, { tenantId, parameters })
      throw error
    }
  }

  /**
   * Generate a deal performance report
   */
  async generateDealPerformanceReport(
    tenantId: string,
    parameters: ReportParameters
  ): Promise<ReportData> {
    try {
      this.logger.info('Generating deal performance report', { tenantId, parameters })

      // Get performance metrics
      const [
        dealVelocity,
        conversionRates,
        teamPerformance,
        timeToClose
      ] = await Promise.all([
        this.getDealVelocityMetrics(tenantId, parameters),
        this.getConversionRates(tenantId, parameters),
        this.getTeamPerformance(tenantId, parameters),
        this.getTimeToCloseMetrics(tenantId, parameters)
      ])

      const charts: ChartData[] = []

      if (parameters.includeCharts) {
        // Deal velocity trend
        charts.push({
          type: 'line',
          title: 'Deal Velocity Trend',
          data: dealVelocity.trend
        })

        // Conversion rates by stage
        charts.push({
          type: 'bar',
          title: 'Conversion Rates by Stage',
          data: conversionRates.byStage
        })

        // Team performance comparison
        charts.push({
          type: 'bar',
          title: 'Team Performance',
          data: teamPerformance.comparison
        })
      }

      const insights = this.generatePerformanceInsights(dealVelocity, conversionRates, teamPerformance)
      const recommendations = this.generatePerformanceRecommendations(dealVelocity, conversionRates, teamPerformance)

      const reportData: ReportData = {
        id: `performance_${Date.now()}`,
        name: 'Deal Performance Report',
        generatedAt: new Date(),
        parameters,
        data: {
          dealVelocity,
          conversionRates,
          teamPerformance,
          timeToClose
        },
        charts,
        summary: {
          totalDeals: dealVelocity.totalDeals,
          totalValue: dealVelocity.totalValue,
          averageDealSize: dealVelocity.averageDealSize,
          conversionRate: conversionRates.overall,
          keyInsights: insights,
          recommendations
        }
      }

      return reportData
    } catch (error) {
      this.logger.error('Failed to generate deal performance report', error, { tenantId, parameters })
      throw error
    }
  }

  /**
   * Generate a forecast report
   */
  async generateForecastReport(
    tenantId: string,
    parameters: ReportParameters
  ): Promise<ReportData> {
    try {
      this.logger.info('Generating forecast report', { tenantId, parameters })

      const forecast = await this.analyticsService.getDealForecast(tenantId, 4)
      const pipelineValue = await this.getPipelineValue(tenantId)
      const riskAnalysis = await this.getRiskAnalysis(tenantId)

      const charts: ChartData[] = []

      if (parameters.includeCharts) {
        // Forecast by quarter
        charts.push({
          type: 'bar',
          title: 'Quarterly Forecast',
          data: forecast.map(q => ({
            period: q.period,
            bestCase: q.bestCase,
            commit: q.commit,
            pipeline: q.pipeline
          }))
        })

        // Risk distribution
        charts.push({
          type: 'pie',
          title: 'Deal Risk Distribution',
          data: riskAnalysis.distribution
        })
      }

      const insights = this.generateForecastInsights(forecast, pipelineValue, riskAnalysis)
      const recommendations = this.generateForecastRecommendations(forecast, riskAnalysis)

      const reportData: ReportData = {
        id: `forecast_${Date.now()}`,
        name: 'Deal Forecast Report',
        generatedAt: new Date(),
        parameters,
        data: {
          forecast,
          pipelineValue,
          riskAnalysis
        },
        charts,
        summary: {
          totalDeals: pipelineValue.totalDeals,
          totalValue: pipelineValue.totalValue,
          averageDealSize: pipelineValue.averageDealSize,
          conversionRate: pipelineValue.conversionRate,
          keyInsights: insights,
          recommendations
        }
      }

      return reportData
    } catch (error) {
      this.logger.error('Failed to generate forecast report', error, { tenantId, parameters })
      throw error
    }
  }

  /**
   * Export report to specified format
   */
  async exportReport(reportData: ReportData, format: ReportFormat): Promise<Buffer> {
    try {
      this.logger.info('Exporting report', { reportId: reportData.id, format })

      switch (format) {
        case ReportFormat.PDF:
          return this.exportToPDF(reportData)
        case ReportFormat.EXCEL:
          return this.exportToExcel(reportData)
        case ReportFormat.CSV:
          return this.exportToCSV(reportData)
        case ReportFormat.JSON:
          return Buffer.from(JSON.stringify(reportData, null, 2))
        case ReportFormat.HTML:
          return this.exportToHTML(reportData)
        default:
          throw new Error(`Unsupported export format: ${format}`)
      }
    } catch (error) {
      this.logger.error('Failed to export report', error, { reportId: reportData.id, format })
      throw error
    }
  }

  /**
   * Get detailed deals for report
   */
  private async getDetailedDeals(tenantId: string, parameters: ReportParameters) {
    const where: any = { tenantId }

    // Apply date range
    if (parameters.dateRange) {
      where.createdAt = {
        gte: parameters.dateRange.from,
        lte: parameters.dateRange.to
      }
    }

    // Apply filters
    if (parameters.filters) {
      if (parameters.filters.dealTypes?.length) {
        where.dealType = { in: parameters.filters.dealTypes }
      }
      if (parameters.filters.stages?.length) {
        where.stage = { in: parameters.filters.stages }
      }
      if (parameters.filters.assignees?.length) {
        where.assignedTo = { in: parameters.filters.assignees }
      }
      if (parameters.filters.priorities?.length) {
        where.priority = { in: parameters.filters.priorities }
      }
    }

    return this.prisma.deal.findMany({
      where,
      include: {
        currentStage: true,
        creator: { select: { firstName: true, lastName: true, email: true } },
        assignee: { select: { firstName: true, lastName: true, email: true } }
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  /**
   * Get deal velocity metrics
   */
  private async getDealVelocityMetrics(tenantId: string, parameters: ReportParameters) {
    // Simplified implementation - would calculate actual velocity metrics
    return {
      totalDeals: 100,
      totalValue: 5000000,
      averageDealSize: 50000,
      averageDaysToClose: 45,
      trend: [
        { period: '2024-01', velocity: 30 },
        { period: '2024-02', velocity: 35 },
        { period: '2024-03', velocity: 28 }
      ]
    }
  }

  /**
   * Get conversion rates
   */
  private async getConversionRates(tenantId: string, parameters: ReportParameters) {
    // Simplified implementation
    return {
      overall: 75,
      byStage: [
        { stage: 'Pipeline', rate: 90 },
        { stage: 'Due Diligence', rate: 80 },
        { stage: 'Negotiation', rate: 70 },
        { stage: 'Closing', rate: 85 }
      ]
    }
  }

  /**
   * Get team performance metrics
   */
  private async getTeamPerformance(tenantId: string, parameters: ReportParameters) {
    // Simplified implementation
    return {
      comparison: [
        { member: 'John Doe', deals: 15, value: 750000 },
        { member: 'Jane Smith', deals: 12, value: 600000 },
        { member: 'Bob Johnson', deals: 18, value: 900000 }
      ]
    }
  }

  /**
   * Get time to close metrics
   */
  private async getTimeToCloseMetrics(tenantId: string, parameters: ReportParameters) {
    // Simplified implementation
    return {
      average: 45,
      median: 42,
      byStage: [
        { stage: 'Pipeline', days: 10 },
        { stage: 'Due Diligence', days: 15 },
        { stage: 'Negotiation', days: 12 },
        { stage: 'Closing', days: 8 }
      ]
    }
  }

  /**
   * Get pipeline value metrics
   */
  private async getPipelineValue(tenantId: string) {
    // Simplified implementation
    return {
      totalDeals: 50,
      totalValue: 2500000,
      averageDealSize: 50000,
      conversionRate: 75
    }
  }

  /**
   * Get risk analysis
   */
  private async getRiskAnalysis(tenantId: string) {
    // Simplified implementation
    return {
      distribution: [
        { risk: 'Low', count: 20 },
        { risk: 'Medium', count: 25 },
        { risk: 'High', count: 8 },
        { risk: 'Critical', count: 2 }
      ]
    }
  }

  /**
   * Generate pipeline insights
   */
  private generatePipelineInsights(analytics: any, pipelineMetrics: any[]): string[] {
    const insights: string[] = []

    if (analytics.conversionRate > 80) {
      insights.push('Excellent conversion rate indicates strong deal qualification')
    } else if (analytics.conversionRate < 50) {
      insights.push('Low conversion rate suggests need for better lead qualification')
    }

    if (analytics.averageDaysInPipeline > 60) {
      insights.push('Deals are taking longer than average to close - consider process optimization')
    }

    if (analytics.dealsAtRisk > analytics.totalDeals * 0.2) {
      insights.push('High number of at-risk deals requires immediate attention')
    }

    return insights
  }

  /**
   * Generate pipeline recommendations
   */
  private generatePipelineRecommendations(analytics: any, pipelineMetrics: any[]): string[] {
    const recommendations: string[] = []

    if (analytics.conversionRate < 70) {
      recommendations.push('Implement better lead scoring and qualification processes')
    }

    if (analytics.averageDaysInPipeline > 45) {
      recommendations.push('Review and streamline deal approval processes')
    }

    if (analytics.dealsAtRisk > 5) {
      recommendations.push('Conduct risk assessment meetings for high-risk deals')
    }

    return recommendations
  }

  /**
   * Generate performance insights
   */
  private generatePerformanceInsights(velocity: any, conversion: any, team: any): string[] {
    return [
      'Deal velocity has improved by 15% compared to last quarter',
      'Conversion rates are highest in the closing stage',
      'Top performer is closing 50% more deals than average'
    ]
  }

  /**
   * Generate performance recommendations
   */
  private generatePerformanceRecommendations(velocity: any, conversion: any, team: any): string[] {
    return [
      'Provide additional training for underperforming team members',
      'Implement best practices from top performers across the team',
      'Focus on improving conversion rates in due diligence stage'
    ]
  }

  /**
   * Generate forecast insights
   */
  private generateForecastInsights(forecast: any[], pipeline: any, risk: any): string[] {
    return [
      'Q2 forecast shows 20% increase over Q1',
      'Pipeline value is sufficient to meet quarterly targets',
      'Risk distribution is within acceptable parameters'
    ]
  }

  /**
   * Generate forecast recommendations
   */
  private generateForecastRecommendations(forecast: any[], risk: any): string[] {
    return [
      'Increase prospecting activities to maintain pipeline health',
      'Focus on converting high-probability deals in current quarter',
      'Develop risk mitigation strategies for critical deals'
    ]
  }

  /**
   * Export to PDF (placeholder)
   */
  private async exportToPDF(reportData: ReportData): Promise<Buffer> {
    // Would use a PDF library like puppeteer or jsPDF
    return Buffer.from('PDF export not implemented')
  }

  /**
   * Export to Excel (placeholder)
   */
  private async exportToExcel(reportData: ReportData): Promise<Buffer> {
    // Would use a library like exceljs
    return Buffer.from('Excel export not implemented')
  }

  /**
   * Export to CSV
   */
  private async exportToCSV(reportData: ReportData): Promise<Buffer> {
    // Simple CSV export of summary data
    const csv = [
      'Metric,Value',
      `Total Deals,${reportData.summary.totalDeals}`,
      `Total Value,${reportData.summary.totalValue}`,
      `Average Deal Size,${reportData.summary.averageDealSize}`,
      `Conversion Rate,${reportData.summary.conversionRate}%`
    ].join('\n')

    return Buffer.from(csv)
  }

  /**
   * Export to HTML (placeholder)
   */
  private async exportToHTML(reportData: ReportData): Promise<Buffer> {
    // Would generate HTML report with charts
    return Buffer.from('<html><body>HTML export not implemented</body></html>')
  }
}
