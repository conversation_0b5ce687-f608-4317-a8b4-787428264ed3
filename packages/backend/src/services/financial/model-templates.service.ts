import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { PrismaClient } from '@prisma/client'
import { FinancialCalculationEngine, DCFInputs, CCAInputs } from './calculation-engine.service'

export interface FinancialModelTemplate {
  id: string
  name: string
  description: string
  category: 'DCF' | 'CCA' | 'LBO' | 'MERGER_MODEL' | 'ACCRETION_DILUTION' | 'CUSTOM'
  version: string
  
  // Template structure
  sections: ModelSection[]
  
  // Default inputs and assumptions
  defaultInputs: Record<string, any>
  assumptions: ModelAssumption[]
  
  // Calculation logic
  calculations: ModelCalculation[]
  
  // Output format
  outputs: ModelOutput[]
  
  // Metadata
  industry?: string
  complexity: 'BASIC' | 'INTERMEDIATE' | 'ADVANCED'
  estimatedTime: number // minutes
  tags: string[]
  
  // Audit trail
  createdBy: string
  createdAt: Date
  updatedBy?: string
  updatedAt?: Date
  tenantId: string
}

export interface ModelSection {
  id: string
  name: string
  description: string
  order: number
  type: 'INPUT' | 'CALCULATION' | 'OUTPUT' | 'ASSUMPTION'
  fields: ModelField[]
  dependencies?: string[] // Other section IDs this depends on
}

export interface ModelField {
  id: string
  name: string
  label: string
  type: 'NUMBER' | 'PERCENTAGE' | 'CURRENCY' | 'DATE' | 'TEXT' | 'BOOLEAN' | 'FORMULA'
  required: boolean
  defaultValue?: any
  validation?: {
    min?: number
    max?: number
    pattern?: string
    customRule?: string
  }
  formula?: string // For calculated fields
  formatting?: {
    decimals?: number
    currency?: string
    percentage?: boolean
  }
  helpText?: string
}

export interface ModelAssumption {
  id: string
  name: string
  description: string
  value: any
  type: 'FIXED' | 'VARIABLE' | 'CALCULATED'
  source?: string
  confidence: 'LOW' | 'MEDIUM' | 'HIGH'
  sensitivity: 'LOW' | 'MEDIUM' | 'HIGH'
  lastUpdated: Date
}

export interface ModelCalculation {
  id: string
  name: string
  description: string
  formula: string
  dependencies: string[] // Field IDs this calculation depends on
  order: number
  category: string
}

export interface ModelOutput {
  id: string
  name: string
  description: string
  type: 'VALUE' | 'CHART' | 'TABLE' | 'SUMMARY'
  format: 'CURRENCY' | 'PERCENTAGE' | 'NUMBER' | 'TEXT'
  calculation: string
  visualization?: {
    chartType?: 'LINE' | 'BAR' | 'PIE' | 'SCATTER'
    xAxis?: string
    yAxis?: string
    series?: string[]
  }
}

export interface ModelInstance {
  id: string
  templateId: string
  name: string
  description?: string
  
  // Input values
  inputs: Record<string, any>
  assumptions: Record<string, any>
  
  // Calculated results
  results: Record<string, any>
  
  // Scenarios
  scenarios: ModelScenario[]
  
  // Status
  status: 'DRAFT' | 'IN_PROGRESS' | 'COMPLETED' | 'ARCHIVED'
  
  // Metadata
  dealId?: string
  createdBy: string
  createdAt: Date
  updatedBy?: string
  updatedAt?: Date
  tenantId: string
}

export interface ModelScenario {
  id: string
  name: string
  description?: string
  inputs: Record<string, any>
  results: Record<string, any>
  isBaseCase: boolean
  createdAt: Date
}

export class FinancialModelTemplatesService {
  private logger: Logger
  private cache: CacheService
  private prisma: PrismaClient
  private calculationEngine: FinancialCalculationEngine

  constructor(
    cache: CacheService,
    prisma: PrismaClient,
    calculationEngine: FinancialCalculationEngine
  ) {
    this.logger = new Logger('FinancialModelTemplatesService')
    this.cache = cache
    this.prisma = prisma
    this.calculationEngine = calculationEngine
  }

  /**
   * Get all available templates
   */
  async getTemplates(filters?: {
    category?: string
    industry?: string
    complexity?: string
    tenantId?: string
  }): Promise<FinancialModelTemplate[]> {
    try {
      const cacheKey = `financial:templates:${JSON.stringify(filters || {})}`
      const cached = await this.cache.get<FinancialModelTemplate[]>(cacheKey)
      
      if (cached) {
        return cached
      }

      // In a real implementation, this would query the database
      const templates = this.getBuiltInTemplates()
      
      let filteredTemplates = templates
      
      if (filters?.category) {
        filteredTemplates = filteredTemplates.filter(t => t.category === filters.category)
      }
      
      if (filters?.industry) {
        filteredTemplates = filteredTemplates.filter(t => t.industry === filters.industry)
      }
      
      if (filters?.complexity) {
        filteredTemplates = filteredTemplates.filter(t => t.complexity === filters.complexity)
      }

      await this.cache.set(cacheKey, filteredTemplates, 3600) // Cache for 1 hour
      
      return filteredTemplates
    } catch (error) {
      this.logger.error('Failed to get templates', error)
      throw error
    }
  }

  /**
   * Get template by ID
   */
  async getTemplate(templateId: string): Promise<FinancialModelTemplate | null> {
    try {
      const templates = await this.getTemplates()
      return templates.find(t => t.id === templateId) || null
    } catch (error) {
      this.logger.error('Failed to get template', error)
      throw error
    }
  }

  /**
   * Create model instance from template
   */
  async createModelInstance(
    templateId: string,
    name: string,
    userId: string,
    tenantId: string,
    dealId?: string
  ): Promise<ModelInstance> {
    try {
      const template = await this.getTemplate(templateId)
      if (!template) {
        throw new Error('Template not found')
      }

      const instance: ModelInstance = {
        id: this.generateId(),
        templateId,
        name,
        inputs: { ...template.defaultInputs },
        assumptions: template.assumptions.reduce((acc, assumption) => {
          acc[assumption.id] = assumption.value
          return acc
        }, {} as Record<string, any>),
        results: {},
        scenarios: [],
        status: 'DRAFT',
        dealId,
        createdBy: userId,
        createdAt: new Date(),
        tenantId
      }

      // In a real implementation, this would save to database
      await this.cache.set(`financial:model:${instance.id}`, instance, 86400)

      this.logger.info('Created model instance', {
        instanceId: instance.id,
        templateId,
        name,
        userId
      })

      return instance
    } catch (error) {
      this.logger.error('Failed to create model instance', error)
      throw error
    }
  }

  /**
   * Update model instance inputs
   */
  async updateModelInputs(
    instanceId: string,
    inputs: Record<string, any>,
    userId: string
  ): Promise<ModelInstance> {
    try {
      const instance = await this.getModelInstance(instanceId)
      if (!instance) {
        throw new Error('Model instance not found')
      }

      // Update inputs
      instance.inputs = { ...instance.inputs, ...inputs }
      instance.updatedBy = userId
      instance.updatedAt = new Date()

      // Recalculate results
      await this.calculateModelResults(instance)

      // Save updated instance
      await this.cache.set(`financial:model:${instanceId}`, instance, 86400)

      this.logger.info('Updated model inputs', {
        instanceId,
        inputCount: Object.keys(inputs).length,
        userId
      })

      return instance
    } catch (error) {
      this.logger.error('Failed to update model inputs', error)
      throw error
    }
  }

  /**
   * Calculate model results
   */
  async calculateModelResults(instance: ModelInstance): Promise<void> {
    try {
      const template = await this.getTemplate(instance.templateId)
      if (!template) {
        throw new Error('Template not found')
      }

      // Execute calculations based on template type
      switch (template.category) {
        case 'DCF':
          await this.calculateDCFResults(instance, template)
          break
        case 'CCA':
          await this.calculateCCAResults(instance, template)
          break
        case 'LBO':
          await this.calculateLBOResults(instance, template)
          break
        case 'MERGER_MODEL':
          await this.calculateMergerResults(instance, template)
          break
        case 'ACCRETION_DILUTION':
          await this.calculateAccretionDilutionResults(instance, template)
          break
        default:
          await this.calculateCustomResults(instance, template)
      }

      this.logger.debug('Calculated model results', {
        instanceId: instance.id,
        category: template.category
      })
    } catch (error) {
      this.logger.error('Failed to calculate model results', error)
      throw error
    }
  }

  /**
   * Create scenario
   */
  async createScenario(
    instanceId: string,
    scenarioName: string,
    inputs: Record<string, any>,
    userId: string
  ): Promise<ModelScenario> {
    try {
      const instance = await this.getModelInstance(instanceId)
      if (!instance) {
        throw new Error('Model instance not found')
      }

      // Create temporary instance with scenario inputs
      const scenarioInstance = {
        ...instance,
        inputs: { ...instance.inputs, ...inputs }
      }

      // Calculate results for scenario
      await this.calculateModelResults(scenarioInstance)

      const scenario: ModelScenario = {
        id: this.generateId(),
        name: scenarioName,
        inputs,
        results: scenarioInstance.results,
        isBaseCase: false,
        createdAt: new Date()
      }

      instance.scenarios.push(scenario)
      instance.updatedBy = userId
      instance.updatedAt = new Date()

      await this.cache.set(`financial:model:${instanceId}`, instance, 86400)

      this.logger.info('Created scenario', {
        instanceId,
        scenarioId: scenario.id,
        scenarioName,
        userId
      })

      return scenario
    } catch (error) {
      this.logger.error('Failed to create scenario', error)
      throw error
    }
  }

  /**
   * Get model instance
   */
  async getModelInstance(instanceId: string): Promise<ModelInstance | null> {
    try {
      return await this.cache.get<ModelInstance>(`financial:model:${instanceId}`)
    } catch (error) {
      this.logger.error('Failed to get model instance', error)
      return null
    }
  }

  /**
   * Private helper methods
   */
  private async calculateDCFResults(instance: ModelInstance, template: FinancialModelTemplate): Promise<void> {
    const inputs: DCFInputs = {
      cashFlows: instance.inputs.cashFlows || [],
      discountRate: instance.inputs.discountRate || 0.1,
      terminalGrowthRate: instance.inputs.terminalGrowthRate,
      periods: instance.inputs.periods || 5
    }

    const result = this.calculationEngine.calculateDCF(inputs)
    instance.results = {
      ...instance.results,
      dcf: result,
      valuation: result.totalValue,
      npv: result.npv,
      irr: result.irr
    }
  }

  private async calculateCCAResults(instance: ModelInstance, template: FinancialModelTemplate): Promise<void> {
    const inputs: CCAInputs = {
      targetMetrics: instance.inputs.targetMetrics || {},
      comparableCompanies: instance.inputs.comparableCompanies || []
    }

    const result = this.calculationEngine.calculateCCA(inputs)
    instance.results = {
      ...instance.results,
      cca: result,
      valuation: result.summary.midValuation,
      valuationRange: result.summary.recommendedRange
    }
  }

  private async calculateLBOResults(instance: ModelInstance, template: FinancialModelTemplate): Promise<void> {
    // LBO calculation logic would go here
    instance.results = {
      ...instance.results,
      lbo: {
        entryValuation: instance.inputs.entryValuation || 0,
        exitValuation: instance.inputs.exitValuation || 0,
        irr: 0.15, // Calculated IRR
        moic: 2.5 // Multiple of invested capital
      }
    }
  }

  private async calculateMergerResults(instance: ModelInstance, template: FinancialModelTemplate): Promise<void> {
    // Merger model calculation logic would go here
    instance.results = {
      ...instance.results,
      merger: {
        proFormaRevenue: instance.inputs.acquirerRevenue + instance.inputs.targetRevenue,
        synergies: instance.inputs.synergies || 0,
        accretionDilution: 0.05 // 5% accretive
      }
    }
  }

  private async calculateAccretionDilutionResults(instance: ModelInstance, template: FinancialModelTemplate): Promise<void> {
    // Accretion/dilution calculation logic would go here
    const acquirerEPS = instance.inputs.acquirerEPS || 0
    const proFormaEPS = instance.inputs.proFormaEPS || 0
    const accretionDilution = (proFormaEPS - acquirerEPS) / acquirerEPS

    instance.results = {
      ...instance.results,
      accretionDilution: {
        acquirerEPS,
        proFormaEPS,
        accretionDilution,
        isAccretive: accretionDilution > 0
      }
    }
  }

  private async calculateCustomResults(instance: ModelInstance, template: FinancialModelTemplate): Promise<void> {
    // Custom calculation logic based on template calculations
    const results: Record<string, any> = {}

    for (const calculation of template.calculations) {
      try {
        // In a real implementation, this would evaluate the formula
        // For now, we'll just set placeholder values
        results[calculation.id] = 0
      } catch (error) {
        this.logger.error('Failed to execute calculation', { calculationId: calculation.id, error })
      }
    }

    instance.results = { ...instance.results, ...results }
  }

  private getBuiltInTemplates(): FinancialModelTemplate[] {
    return [
      {
        id: 'dcf-basic',
        name: 'Basic DCF Model',
        description: 'Standard discounted cash flow model for company valuation',
        category: 'DCF',
        version: '1.0',
        sections: [
          {
            id: 'inputs',
            name: 'Inputs',
            description: 'Financial projections and assumptions',
            order: 1,
            type: 'INPUT',
            fields: [
              {
                id: 'revenue_growth',
                name: 'revenueGrowth',
                label: 'Revenue Growth Rate',
                type: 'PERCENTAGE',
                required: true,
                defaultValue: 0.05,
                formatting: { percentage: true, decimals: 2 }
              },
              {
                id: 'discount_rate',
                name: 'discountRate',
                label: 'Discount Rate (WACC)',
                type: 'PERCENTAGE',
                required: true,
                defaultValue: 0.1,
                formatting: { percentage: true, decimals: 2 }
              }
            ]
          }
        ],
        defaultInputs: {
          revenueGrowth: 0.05,
          discountRate: 0.1,
          terminalGrowthRate: 0.025,
          periods: 5
        },
        assumptions: [
          {
            id: 'terminal_growth',
            name: 'Terminal Growth Rate',
            description: 'Long-term growth rate assumption',
            value: 0.025,
            type: 'FIXED',
            confidence: 'MEDIUM',
            sensitivity: 'HIGH',
            lastUpdated: new Date()
          }
        ],
        calculations: [
          {
            id: 'fcf_projection',
            name: 'Free Cash Flow Projection',
            description: 'Project future free cash flows',
            formula: 'revenue * (1 + revenueGrowth)^period * fcfMargin',
            dependencies: ['revenue', 'revenueGrowth', 'fcfMargin'],
            order: 1,
            category: 'projection'
          }
        ],
        outputs: [
          {
            id: 'enterprise_value',
            name: 'Enterprise Value',
            description: 'Total enterprise value from DCF',
            type: 'VALUE',
            format: 'CURRENCY',
            calculation: 'sum(presentValueCashFlows) + terminalValue'
          }
        ],
        complexity: 'BASIC',
        estimatedTime: 30,
        tags: ['valuation', 'dcf', 'basic'],
        createdBy: 'system',
        createdAt: new Date(),
        tenantId: 'system'
      },
      {
        id: 'cca-basic',
        name: 'Comparable Company Analysis',
        description: 'Market-based valuation using comparable companies',
        category: 'CCA',
        version: '1.0',
        sections: [
          {
            id: 'target_metrics',
            name: 'Target Company Metrics',
            description: 'Financial metrics for the target company',
            order: 1,
            type: 'INPUT',
            fields: [
              {
                id: 'target_revenue',
                name: 'targetRevenue',
                label: 'Revenue',
                type: 'CURRENCY',
                required: true,
                formatting: { currency: 'USD', decimals: 0 }
              },
              {
                id: 'target_ebitda',
                name: 'targetEbitda',
                label: 'EBITDA',
                type: 'CURRENCY',
                required: true,
                formatting: { currency: 'USD', decimals: 0 }
              }
            ]
          }
        ],
        defaultInputs: {
          targetRevenue: *********,
          targetEbitda: 20000000
        },
        assumptions: [],
        calculations: [],
        outputs: [
          {
            id: 'implied_valuation',
            name: 'Implied Valuation',
            description: 'Valuation based on comparable multiples',
            type: 'VALUE',
            format: 'CURRENCY',
            calculation: 'median(comparableMultiples) * targetMetric'
          }
        ],
        complexity: 'BASIC',
        estimatedTime: 45,
        tags: ['valuation', 'comparables', 'market'],
        createdBy: 'system',
        createdAt: new Date(),
        tenantId: 'system'
      }
    ]
  }

  private generateId(): string {
    return `model-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
