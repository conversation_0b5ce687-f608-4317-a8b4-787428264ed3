import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface CashFlow {
  period: number
  amount: number
  date?: Date
  description?: string
}

export interface DCFInputs {
  cashFlows: CashFlow[]
  discountRate: number
  terminalGrowthRate?: number
  terminalValue?: number
  periods: number
}

export interface DCFResult {
  presentValue: number
  terminalValue: number
  totalValue: number
  npv: number
  irr: number
  cashFlows: Array<{
    period: number
    cashFlow: number
    presentValue: number
    cumulativePV: number
  }>
}

export interface ComparableCompany {
  name: string
  marketCap: number
  revenue: number
  ebitda: number
  netIncome: number
  bookValue: number
  shares: number
}

export interface CCAInputs {
  targetMetrics: {
    revenue: number
    ebitda: number
    netIncome: number
    bookValue: number
  }
  comparableCompanies: ComparableCompany[]
  adjustments?: {
    sizeAdjustment?: number
    liquidityAdjustment?: number
    controlPremium?: number
  }
}

export interface CCAResult {
  valuationMultiples: {
    evRevenue: { median: number; mean: number; range: [number, number] }
    evEbitda: { median: number; mean: number; range: [number, number] }
    peRatio: { median: number; mean: number; range: [number, number] }
    pbRatio: { median: number; mean: number; range: [number, number] }
  }
  impliedValuation: {
    byRevenue: { low: number; mid: number; high: number }
    byEbitda: { low: number; mid: number; high: number }
    byEarnings: { low: number; mid: number; high: number }
    byBook: { low: number; mid: number; high: number }
  }
  adjustedValuation: {
    byRevenue: { low: number; mid: number; high: number }
    byEbitda: { low: number; mid: number; high: number }
    byEarnings: { low: number; mid: number; high: number }
    byBook: { low: number; mid: number; high: number }
  }
  summary: {
    lowValuation: number
    midValuation: number
    highValuation: number
    recommendedRange: [number, number]
  }
}

export interface SensitivityAnalysis {
  baseCase: number
  scenarios: Array<{
    name: string
    variables: Record<string, number>
    result: number
    variance: number
  }>
  sensitivityMatrix: Array<{
    variable: string
    changes: Array<{
      changePercent: number
      newValue: number
      result: number
      impact: number
    }>
  }>
}

export class FinancialCalculationEngine {
  private logger: Logger
  private cache: CacheService

  constructor(cache: CacheService) {
    this.logger = new Logger('FinancialCalculationEngine')
    this.cache = cache
  }

  /**
   * Perform Discounted Cash Flow (DCF) analysis
   */
  calculateDCF(inputs: DCFInputs): DCFResult {
    try {
      this.logger.info('Calculating DCF analysis', {
        periods: inputs.periods,
        discountRate: inputs.discountRate,
        cashFlowCount: inputs.cashFlows.length
      })

      // Calculate present value of each cash flow
      const cashFlowAnalysis = inputs.cashFlows.map((cf, index) => {
        const period = cf.period || index + 1
        const presentValue = cf.amount / Math.pow(1 + inputs.discountRate, period)
        
        return {
          period,
          cashFlow: cf.amount,
          presentValue,
          cumulativePV: 0 // Will be calculated below
        }
      })

      // Calculate cumulative present values
      let cumulative = 0
      cashFlowAnalysis.forEach(cf => {
        cumulative += cf.presentValue
        cf.cumulativePV = cumulative
      })

      // Calculate terminal value
      let terminalValue = 0
      if (inputs.terminalValue) {
        terminalValue = inputs.terminalValue / Math.pow(1 + inputs.discountRate, inputs.periods)
      } else if (inputs.terminalGrowthRate && inputs.cashFlows.length > 0) {
        const finalCashFlow = inputs.cashFlows[inputs.cashFlows.length - 1].amount
        const terminalCashFlow = finalCashFlow * (1 + inputs.terminalGrowthRate)
        terminalValue = (terminalCashFlow / (inputs.discountRate - inputs.terminalGrowthRate)) / 
                       Math.pow(1 + inputs.discountRate, inputs.periods)
      }

      // Calculate totals
      const presentValue = cashFlowAnalysis.reduce((sum, cf) => sum + cf.presentValue, 0)
      const totalValue = presentValue + terminalValue
      const npv = totalValue - (inputs.cashFlows[0]?.amount || 0) // Assuming first cash flow is initial investment
      const irr = this.calculateIRR(inputs.cashFlows.map(cf => cf.amount))

      const result: DCFResult = {
        presentValue,
        terminalValue,
        totalValue,
        npv,
        irr,
        cashFlows: cashFlowAnalysis
      }

      this.logger.info('DCF calculation completed', {
        totalValue,
        npv,
        irr
      })

      return result
    } catch (error) {
      this.logger.error('Failed to calculate DCF', error)
      throw error
    }
  }

  /**
   * Perform Comparable Company Analysis (CCA)
   */
  calculateCCA(inputs: CCAInputs): CCAResult {
    try {
      this.logger.info('Calculating CCA analysis', {
        targetRevenue: inputs.targetMetrics.revenue,
        comparableCount: inputs.comparableCompanies.length
      })

      // Calculate multiples for each comparable company
      const multiples = inputs.comparableCompanies.map(company => ({
        name: company.name,
        evRevenue: (company.marketCap) / company.revenue,
        evEbitda: (company.marketCap) / company.ebitda,
        peRatio: company.marketCap / company.netIncome,
        pbRatio: company.marketCap / company.bookValue
      }))

      // Calculate statistics for each multiple
      const valuationMultiples = {
        evRevenue: this.calculateMultipleStats(multiples.map(m => m.evRevenue)),
        evEbitda: this.calculateMultipleStats(multiples.map(m => m.evEbitda)),
        peRatio: this.calculateMultipleStats(multiples.map(m => m.peRatio)),
        pbRatio: this.calculateMultipleStats(multiples.map(m => m.pbRatio))
      }

      // Calculate implied valuations
      const impliedValuation = {
        byRevenue: {
          low: inputs.targetMetrics.revenue * valuationMultiples.evRevenue.range[0],
          mid: inputs.targetMetrics.revenue * valuationMultiples.evRevenue.median,
          high: inputs.targetMetrics.revenue * valuationMultiples.evRevenue.range[1]
        },
        byEbitda: {
          low: inputs.targetMetrics.ebitda * valuationMultiples.evEbitda.range[0],
          mid: inputs.targetMetrics.ebitda * valuationMultiples.evEbitda.median,
          high: inputs.targetMetrics.ebitda * valuationMultiples.evEbitda.range[1]
        },
        byEarnings: {
          low: inputs.targetMetrics.netIncome * valuationMultiples.peRatio.range[0],
          mid: inputs.targetMetrics.netIncome * valuationMultiples.peRatio.median,
          high: inputs.targetMetrics.netIncome * valuationMultiples.peRatio.range[1]
        },
        byBook: {
          low: inputs.targetMetrics.bookValue * valuationMultiples.pbRatio.range[0],
          mid: inputs.targetMetrics.bookValue * valuationMultiples.pbRatio.median,
          high: inputs.targetMetrics.bookValue * valuationMultiples.pbRatio.range[1]
        }
      }

      // Apply adjustments
      const adjustmentFactor = 1 + 
        (inputs.adjustments?.sizeAdjustment || 0) +
        (inputs.adjustments?.liquidityAdjustment || 0) +
        (inputs.adjustments?.controlPremium || 0)

      const adjustedValuation = {
        byRevenue: {
          low: impliedValuation.byRevenue.low * adjustmentFactor,
          mid: impliedValuation.byRevenue.mid * adjustmentFactor,
          high: impliedValuation.byRevenue.high * adjustmentFactor
        },
        byEbitda: {
          low: impliedValuation.byEbitda.low * adjustmentFactor,
          mid: impliedValuation.byEbitda.mid * adjustmentFactor,
          high: impliedValuation.byEbitda.high * adjustmentFactor
        },
        byEarnings: {
          low: impliedValuation.byEarnings.low * adjustmentFactor,
          mid: impliedValuation.byEarnings.mid * adjustmentFactor,
          high: impliedValuation.byEarnings.high * adjustmentFactor
        },
        byBook: {
          low: impliedValuation.byBook.low * adjustmentFactor,
          mid: impliedValuation.byBook.mid * adjustmentFactor,
          high: impliedValuation.byBook.high * adjustmentFactor
        }
      }

      // Calculate summary
      const allMidValues = [
        adjustedValuation.byRevenue.mid,
        adjustedValuation.byEbitda.mid,
        adjustedValuation.byEarnings.mid,
        adjustedValuation.byBook.mid
      ].filter(v => !isNaN(v) && isFinite(v))

      const allLowValues = [
        adjustedValuation.byRevenue.low,
        adjustedValuation.byEbitda.low,
        adjustedValuation.byEarnings.low,
        adjustedValuation.byBook.low
      ].filter(v => !isNaN(v) && isFinite(v))

      const allHighValues = [
        adjustedValuation.byRevenue.high,
        adjustedValuation.byEbitda.high,
        adjustedValuation.byEarnings.high,
        adjustedValuation.byBook.high
      ].filter(v => !isNaN(v) && isFinite(v))

      const summary = {
        lowValuation: Math.min(...allLowValues),
        midValuation: allMidValues.reduce((sum, val) => sum + val, 0) / allMidValues.length,
        highValuation: Math.max(...allHighValues),
        recommendedRange: [
          allMidValues.reduce((sum, val) => sum + val, 0) / allMidValues.length * 0.9,
          allMidValues.reduce((sum, val) => sum + val, 0) / allMidValues.length * 1.1
        ] as [number, number]
      }

      const result: CCAResult = {
        valuationMultiples,
        impliedValuation,
        adjustedValuation,
        summary
      }

      this.logger.info('CCA calculation completed', {
        midValuation: summary.midValuation,
        recommendedRange: summary.recommendedRange
      })

      return result
    } catch (error) {
      this.logger.error('Failed to calculate CCA', error)
      throw error
    }
  }

  /**
   * Perform sensitivity analysis
   */
  performSensitivityAnalysis(
    baseInputs: any,
    variables: Record<string, number[]>,
    calculationFunction: (inputs: any) => number
  ): SensitivityAnalysis {
    try {
      this.logger.info('Performing sensitivity analysis', {
        variableCount: Object.keys(variables).length
      })

      const baseCase = calculationFunction(baseInputs)
      const scenarios: SensitivityAnalysis['scenarios'] = []
      const sensitivityMatrix: SensitivityAnalysis['sensitivityMatrix'] = []

      // Generate scenarios for each variable
      for (const [variableName, values] of Object.entries(variables)) {
        const changes = values.map(value => {
          const modifiedInputs = { ...baseInputs, [variableName]: value }
          const result = calculationFunction(modifiedInputs)
          const changePercent = ((value - baseInputs[variableName]) / baseInputs[variableName]) * 100
          const impact = ((result - baseCase) / baseCase) * 100

          return {
            changePercent,
            newValue: value,
            result,
            impact
          }
        })

        sensitivityMatrix.push({
          variable: variableName,
          changes
        })

        // Add best and worst case scenarios
        const sortedChanges = [...changes].sort((a, b) => a.result - b.result)
        scenarios.push({
          name: `${variableName} - Best Case`,
          variables: { [variableName]: sortedChanges[sortedChanges.length - 1].newValue },
          result: sortedChanges[sortedChanges.length - 1].result,
          variance: sortedChanges[sortedChanges.length - 1].impact
        })

        scenarios.push({
          name: `${variableName} - Worst Case`,
          variables: { [variableName]: sortedChanges[0].newValue },
          result: sortedChanges[0].result,
          variance: sortedChanges[0].impact
        })
      }

      const result: SensitivityAnalysis = {
        baseCase,
        scenarios,
        sensitivityMatrix
      }

      this.logger.info('Sensitivity analysis completed', {
        baseCase,
        scenarioCount: scenarios.length
      })

      return result
    } catch (error) {
      this.logger.error('Failed to perform sensitivity analysis', error)
      throw error
    }
  }

  /**
   * Calculate Internal Rate of Return (IRR)
   */
  private calculateIRR(cashFlows: number[], guess: number = 0.1): number {
    const maxIterations = 100
    const tolerance = 1e-6

    let rate = guess
    
    for (let i = 0; i < maxIterations; i++) {
      let npv = 0
      let dnpv = 0

      for (let j = 0; j < cashFlows.length; j++) {
        npv += cashFlows[j] / Math.pow(1 + rate, j)
        dnpv -= j * cashFlows[j] / Math.pow(1 + rate, j + 1)
      }

      if (Math.abs(npv) < tolerance) {
        return rate
      }

      if (Math.abs(dnpv) < tolerance) {
        break
      }

      rate = rate - npv / dnpv
    }

    return rate
  }

  /**
   * Calculate statistics for valuation multiples
   */
  private calculateMultipleStats(values: number[]): { median: number; mean: number; range: [number, number] } {
    const validValues = values.filter(v => !isNaN(v) && isFinite(v))
    
    if (validValues.length === 0) {
      return { median: 0, mean: 0, range: [0, 0] }
    }

    const sorted = validValues.sort((a, b) => a - b)
    const median = sorted.length % 2 === 0
      ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
      : sorted[Math.floor(sorted.length / 2)]
    
    const mean = validValues.reduce((sum, val) => sum + val, 0) / validValues.length
    
    // Use 25th and 75th percentiles for range
    const q1Index = Math.floor(sorted.length * 0.25)
    const q3Index = Math.floor(sorted.length * 0.75)
    const range: [number, number] = [sorted[q1Index], sorted[q3Index]]

    return { median, mean, range }
  }

  /**
   * Calculate Net Present Value (NPV)
   */
  calculateNPV(cashFlows: CashFlow[], discountRate: number): number {
    return cashFlows.reduce((npv, cf) => {
      const period = cf.period || 1
      return npv + cf.amount / Math.pow(1 + discountRate, period)
    }, 0)
  }

  /**
   * Calculate compound annual growth rate (CAGR)
   */
  calculateCAGR(beginningValue: number, endingValue: number, periods: number): number {
    return Math.pow(endingValue / beginningValue, 1 / periods) - 1
  }

  /**
   * Calculate weighted average cost of capital (WACC)
   */
  calculateWACC(
    marketValueEquity: number,
    marketValueDebt: number,
    costOfEquity: number,
    costOfDebt: number,
    taxRate: number
  ): number {
    const totalValue = marketValueEquity + marketValueDebt
    const equityWeight = marketValueEquity / totalValue
    const debtWeight = marketValueDebt / totalValue
    
    return (equityWeight * costOfEquity) + (debtWeight * costOfDebt * (1 - taxRate))
  }

  /**
   * Calculate beta coefficient
   */
  calculateBeta(stockReturns: number[], marketReturns: number[]): number {
    if (stockReturns.length !== marketReturns.length) {
      throw new Error('Stock returns and market returns must have the same length')
    }

    const n = stockReturns.length
    const stockMean = stockReturns.reduce((sum, r) => sum + r, 0) / n
    const marketMean = marketReturns.reduce((sum, r) => sum + r, 0) / n

    let covariance = 0
    let marketVariance = 0

    for (let i = 0; i < n; i++) {
      const stockDiff = stockReturns[i] - stockMean
      const marketDiff = marketReturns[i] - marketMean
      
      covariance += stockDiff * marketDiff
      marketVariance += marketDiff * marketDiff
    }

    return covariance / marketVariance
  }
}
