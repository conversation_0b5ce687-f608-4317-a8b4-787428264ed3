import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { NotificationService } from '@/services/notification.service'
import { EventEmitter } from 'events'
import {
  ComplianceAlert,
  ComplianceStatus,
  AlertType,
  AlertSeverity,
  AlertStatus,
  ComplianceStatusType,
  RiskLevel,
  NotificationChannel
} from '@/shared/types/compliance'

export interface MonitoringRule {
  id: string
  name: string
  description: string
  isActive: boolean
  priority: number
  
  // Trigger conditions
  triggerType: 'DEADLINE' | 'STATUS_CHANGE' | 'DOCUMENT_MISSING' | 'APPROVAL_DELAY' | 'CUSTOM'
  conditions: MonitoringCondition[]
  
  // Alert configuration
  alertType: AlertType
  alertSeverity: AlertSeverity
  alertMessage: string
  
  // Notification settings
  notificationChannels: NotificationChannel[]
  recipients: MonitoringRecipient[]
  
  // Escalation rules
  escalationEnabled: boolean
  escalationRules: EscalationRule[]
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt: Date
  tenantId: string
}

export interface MonitoringCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'between'
  value: any
  logicalOperator?: 'AND' | 'OR'
}

export interface MonitoringRecipient {
  type: 'USER' | 'ROLE' | 'EMAIL'
  identifier: string
  name?: string
}

export interface EscalationRule {
  level: number
  triggerAfterMinutes: number
  recipients: MonitoringRecipient[]
  actions: EscalationAction[]
}

export interface EscalationAction {
  type: 'NOTIFY' | 'ASSIGN' | 'ESCALATE' | 'AUTO_APPROVE' | 'WEBHOOK'
  parameters: any
}

export interface MonitoringDashboard {
  dealId: string
  summary: {
    totalRequirements: number
    completedRequirements: number
    overdueRequirements: number
    criticalAlerts: number
    riskScore: number
  }
  alerts: ComplianceAlert[]
  deadlines: Array<{
    requirementId: string
    requirementName: string
    dueDate: Date
    daysRemaining: number
    riskLevel: RiskLevel
    status: ComplianceStatusType
  }>
  riskFactors: Array<{
    factor: string
    impact: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    description: string
    recommendation: string
  }>
  trends: {
    completionRate: Array<{ date: string; rate: number }>
    riskScore: Array<{ date: string; score: number }>
    alertVolume: Array<{ date: string; count: number }>
  }
}

export class ComplianceMonitoringService extends EventEmitter {
  private prisma: PrismaClient
  private cache: CacheService
  private notificationService: NotificationService
  private logger: Logger

  constructor(
    prisma: PrismaClient,
    cache: CacheService,
    notificationService: NotificationService
  ) {
    super()
    this.prisma = prisma
    this.cache = cache
    this.notificationService = notificationService
    this.logger = new Logger('ComplianceMonitoringService')
  }

  /**
   * Start monitoring for a deal
   */
  async startMonitoring(dealId: string, tenantId: string): Promise<void> {
    try {
      this.logger.info('Starting compliance monitoring', { dealId, tenantId })

      // Get monitoring rules
      const rules = await this.getMonitoringRules(tenantId)

      // Initialize monitoring state
      await this.initializeMonitoringState(dealId, rules)

      // Schedule periodic checks
      await this.scheduleMonitoringChecks(dealId, tenantId)

      this.logger.info('Compliance monitoring started', { dealId })
    } catch (error) {
      this.logger.error('Failed to start compliance monitoring', error)
      throw error
    }
  }

  /**
   * Run monitoring checks
   */
  async runMonitoringChecks(dealId: string, tenantId: string): Promise<void> {
    try {
      this.logger.info('Running compliance monitoring checks', { dealId })

      // Get compliance statuses
      const complianceStatuses = await this.getComplianceStatuses(dealId, tenantId)

      // Get monitoring rules
      const rules = await this.getMonitoringRules(tenantId)

      // Check each rule against current state
      for (const rule of rules) {
        if (!rule.isActive) continue

        await this.evaluateMonitoringRule(rule, complianceStatuses, dealId)
      }

      // Update monitoring metrics
      await this.updateMonitoringMetrics(dealId, tenantId)

      this.logger.info('Monitoring checks completed', { dealId })
    } catch (error) {
      this.logger.error('Failed to run monitoring checks', error)
    }
  }

  /**
   * Get monitoring dashboard
   */
  async getMonitoringDashboard(dealId: string, tenantId: string): Promise<MonitoringDashboard> {
    try {
      const cacheKey = `monitoring:dashboard:${dealId}`
      
      // Try cache first
      const cached = await this.cache.get<MonitoringDashboard>(cacheKey)
      if (cached) {
        return cached
      }

      // Get compliance data
      const [complianceStatuses, alerts] = await Promise.all([
        this.getComplianceStatuses(dealId, tenantId),
        this.getActiveAlerts(dealId, tenantId)
      ])

      // Calculate summary metrics
      const summary = this.calculateSummaryMetrics(complianceStatuses, alerts)

      // Get upcoming deadlines
      const deadlines = this.getUpcomingDeadlines(complianceStatuses)

      // Identify risk factors
      const riskFactors = this.identifyRiskFactors(complianceStatuses, alerts)

      // Get trends data
      const trends = await this.getTrendsData(dealId, tenantId)

      const dashboard: MonitoringDashboard = {
        dealId,
        summary,
        alerts,
        deadlines,
        riskFactors,
        trends
      }

      // Cache for 5 minutes
      await this.cache.set(cacheKey, dashboard, 300)

      return dashboard
    } catch (error) {
      this.logger.error('Failed to get monitoring dashboard', error)
      throw error
    }
  }

  /**
   * Create monitoring rule
   */
  async createMonitoringRule(
    rule: Omit<MonitoringRule, 'id' | 'createdAt' | 'updatedAt'>,
    userId: string
  ): Promise<MonitoringRule> {
    try {
      const createdRule = await this.prisma.monitoringRule.create({
        data: {
          name: rule.name,
          description: rule.description,
          isActive: rule.isActive,
          priority: rule.priority,
          triggerType: rule.triggerType,
          conditions: rule.conditions,
          alertType: rule.alertType,
          alertSeverity: rule.alertSeverity,
          alertMessage: rule.alertMessage,
          notificationChannels: rule.notificationChannels,
          recipients: rule.recipients,
          escalationEnabled: rule.escalationEnabled,
          escalationRules: rule.escalationRules,
          createdBy: userId,
          tenantId: rule.tenantId
        }
      })

      return createdRule as MonitoringRule
    } catch (error) {
      this.logger.error('Failed to create monitoring rule', error)
      throw error
    }
  }

  /**
   * Handle alert acknowledgment
   */
  async acknowledgeAlert(
    alertId: string,
    userId: string,
    notes?: string
  ): Promise<ComplianceAlert> {
    try {
      const updatedAlert = await this.prisma.complianceAlert.update({
        where: { id: alertId },
        data: {
          status: AlertStatus.ACKNOWLEDGED,
          acknowledgedBy: userId,
          acknowledgedDate: new Date(),
          updatedAt: new Date()
        }
      })

      // Log acknowledgment
      this.logger.info('Alert acknowledged', { alertId, userId })

      // Emit event
      this.emit('alert:acknowledged', {
        alertId,
        userId,
        notes
      })

      return updatedAlert as ComplianceAlert
    } catch (error) {
      this.logger.error('Failed to acknowledge alert', error)
      throw error
    }
  }

  /**
   * Resolve alert
   */
  async resolveAlert(
    alertId: string,
    userId: string,
    resolution: string
  ): Promise<ComplianceAlert> {
    try {
      const updatedAlert = await this.prisma.complianceAlert.update({
        where: { id: alertId },
        data: {
          status: AlertStatus.RESOLVED,
          resolvedDate: new Date(),
          updatedAt: new Date()
        }
      })

      // Log resolution
      this.logger.info('Alert resolved', { alertId, userId, resolution })

      // Emit event
      this.emit('alert:resolved', {
        alertId,
        userId,
        resolution
      })

      return updatedAlert as ComplianceAlert
    } catch (error) {
      this.logger.error('Failed to resolve alert', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async getMonitoringRules(tenantId: string): Promise<MonitoringRule[]> {
    const rules = await this.prisma.monitoringRule.findMany({
      where: {
        tenantId,
        isActive: true
      },
      orderBy: { priority: 'asc' }
    })

    return rules as MonitoringRule[]
  }

  private async getComplianceStatuses(dealId: string, tenantId: string): Promise<ComplianceStatus[]> {
    const statuses = await this.prisma.complianceStatus.findMany({
      where: { dealId, tenantId }
    })

    return statuses as ComplianceStatus[]
  }

  private async getActiveAlerts(dealId: string, tenantId: string): Promise<ComplianceAlert[]> {
    const alerts = await this.prisma.complianceAlert.findMany({
      where: {
        dealId,
        tenantId,
        status: { in: [AlertStatus.ACTIVE, AlertStatus.ACKNOWLEDGED] }
      },
      orderBy: { triggerDate: 'desc' }
    })

    return alerts as ComplianceAlert[]
  }

  private async initializeMonitoringState(dealId: string, rules: MonitoringRule[]): Promise<void> {
    // Initialize monitoring state for the deal
    await this.cache.set(`monitoring:state:${dealId}`, {
      dealId,
      rulesCount: rules.length,
      lastCheck: new Date(),
      isActive: true
    }, 86400) // 24 hours
  }

  private async scheduleMonitoringChecks(dealId: string, tenantId: string): Promise<void> {
    // Schedule periodic monitoring checks
    // In a real implementation, this would use a job scheduler
    this.logger.info('Scheduling monitoring checks', { dealId })
  }

  private async evaluateMonitoringRule(
    rule: MonitoringRule,
    complianceStatuses: ComplianceStatus[],
    dealId: string
  ): Promise<void> {
    try {
      // Evaluate rule conditions
      const triggeredStatuses = complianceStatuses.filter(status => 
        this.evaluateRuleConditions(rule, status)
      )

      // Create alerts for triggered conditions
      for (const status of triggeredStatuses) {
        await this.createAlert(rule, status, dealId)
      }
    } catch (error) {
      this.logger.error('Failed to evaluate monitoring rule', error, { ruleId: rule.id })
    }
  }

  private evaluateRuleConditions(rule: MonitoringRule, status: ComplianceStatus): boolean {
    return rule.conditions.every(condition => {
      const fieldValue = this.getFieldValue(status, condition.field)
      return this.evaluateCondition(condition, fieldValue)
    })
  }

  private getFieldValue(status: ComplianceStatus, field: string): any {
    switch (field) {
      case 'status':
        return status.status
      case 'daysRemaining':
        return status.daysRemaining
      case 'isOverdue':
        return status.isOverdue
      case 'completionPercentage':
        return status.completionPercentage
      case 'riskLevel':
        return status.riskLevel
      default:
        return null
    }
  }

  private evaluateCondition(condition: MonitoringCondition, fieldValue: any): boolean {
    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value
      case 'not_equals':
        return fieldValue !== condition.value
      case 'greater_than':
        return Number(fieldValue) > Number(condition.value)
      case 'less_than':
        return Number(fieldValue) < Number(condition.value)
      case 'contains':
        return String(fieldValue).includes(condition.value)
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(fieldValue)
      default:
        return false
    }
  }

  private async createAlert(
    rule: MonitoringRule,
    status: ComplianceStatus,
    dealId: string
  ): Promise<void> {
    // Check if alert already exists
    const existingAlert = await this.prisma.complianceAlert.findFirst({
      where: {
        dealId,
        complianceStatusId: status.id,
        type: rule.alertType,
        status: { in: [AlertStatus.ACTIVE, AlertStatus.ACKNOWLEDGED] }
      }
    })

    if (existingAlert) {
      return // Don't create duplicate alerts
    }

    // Create new alert
    const alert = await this.prisma.complianceAlert.create({
      data: {
        type: rule.alertType,
        severity: rule.alertSeverity,
        title: `Compliance Alert: ${rule.name}`,
        message: rule.alertMessage,
        dealId,
        complianceStatusId: status.id,
        triggerDate: new Date(),
        triggerEvent: `Monitoring rule: ${rule.name}`,
        recipients: rule.recipients.map(r => r.identifier),
        notificationChannels: rule.notificationChannels,
        status: AlertStatus.ACTIVE,
        suggestedActions: [],
        escalationRules: rule.escalationRules,
        tenantId: status.tenantId
      }
    })

    // Send notifications
    await this.sendAlertNotifications(alert as ComplianceAlert, rule)

    // Emit alert created event
    this.emit('alert:created', {
      alertId: alert.id,
      ruleId: rule.id,
      dealId,
      complianceStatusId: status.id
    })
  }

  private async sendAlertNotifications(alert: ComplianceAlert, rule: MonitoringRule): Promise<void> {
    try {
      for (const recipient of rule.recipients) {
        for (const channel of rule.notificationChannels) {
          await this.notificationService.sendNotification({
            type: 'COMPLIANCE_ALERT',
            title: alert.title,
            message: alert.message,
            recipient: recipient.identifier,
            channel,
            priority: alert.severity === AlertSeverity.CRITICAL ? 'HIGH' : 'MEDIUM',
            data: {
              alertId: alert.id,
              dealId: alert.dealId,
              complianceStatusId: alert.complianceStatusId
            }
          })
        }
      }
    } catch (error) {
      this.logger.error('Failed to send alert notifications', error)
    }
  }

  private calculateSummaryMetrics(
    complianceStatuses: ComplianceStatus[],
    alerts: ComplianceAlert[]
  ): MonitoringDashboard['summary'] {
    const totalRequirements = complianceStatuses.length
    const completedRequirements = complianceStatuses.filter(
      s => s.status === ComplianceStatusType.COMPLETED
    ).length
    const overdueRequirements = complianceStatuses.filter(s => s.isOverdue).length
    const criticalAlerts = alerts.filter(a => a.severity === AlertSeverity.CRITICAL).length

    // Calculate risk score (0-100)
    const riskScore = this.calculateRiskScore(complianceStatuses, alerts)

    return {
      totalRequirements,
      completedRequirements,
      overdueRequirements,
      criticalAlerts,
      riskScore
    }
  }

  private calculateRiskScore(
    complianceStatuses: ComplianceStatus[],
    alerts: ComplianceAlert[]
  ): number {
    let riskScore = 0

    // Factor in overdue items
    const overdueCount = complianceStatuses.filter(s => s.isOverdue).length
    riskScore += (overdueCount / complianceStatuses.length) * 40

    // Factor in critical alerts
    const criticalAlerts = alerts.filter(a => a.severity === AlertSeverity.CRITICAL).length
    riskScore += Math.min(criticalAlerts * 10, 30)

    // Factor in completion rate
    const completionRate = complianceStatuses.filter(
      s => s.status === ComplianceStatusType.COMPLETED
    ).length / complianceStatuses.length
    riskScore += (1 - completionRate) * 30

    return Math.min(Math.round(riskScore), 100)
  }

  private getUpcomingDeadlines(complianceStatuses: ComplianceStatus[]): MonitoringDashboard['deadlines'] {
    return complianceStatuses
      .filter(s => s.dueDate && s.status !== ComplianceStatusType.COMPLETED)
      .map(s => ({
        requirementId: s.requirementId,
        requirementName: s.requirementId, // Would be resolved from requirement data
        dueDate: s.dueDate!,
        daysRemaining: s.daysRemaining || 0,
        riskLevel: s.riskLevel,
        status: s.status
      }))
      .sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime())
      .slice(0, 10) // Top 10 upcoming deadlines
  }

  private identifyRiskFactors(
    complianceStatuses: ComplianceStatus[],
    alerts: ComplianceAlert[]
  ): MonitoringDashboard['riskFactors'] {
    const riskFactors: MonitoringDashboard['riskFactors'] = []

    // Check for overdue items
    const overdueCount = complianceStatuses.filter(s => s.isOverdue).length
    if (overdueCount > 0) {
      riskFactors.push({
        factor: 'Overdue Requirements',
        impact: overdueCount > 5 ? 'CRITICAL' : overdueCount > 2 ? 'HIGH' : 'MEDIUM',
        description: `${overdueCount} requirements are overdue`,
        recommendation: 'Prioritize completion of overdue requirements'
      })
    }

    // Check for critical alerts
    const criticalAlerts = alerts.filter(a => a.severity === AlertSeverity.CRITICAL).length
    if (criticalAlerts > 0) {
      riskFactors.push({
        factor: 'Critical Alerts',
        impact: 'CRITICAL',
        description: `${criticalAlerts} critical alerts require immediate attention`,
        recommendation: 'Address critical alerts immediately'
      })
    }

    return riskFactors
  }

  private async getTrendsData(dealId: string, tenantId: string): Promise<MonitoringDashboard['trends']> {
    // Mock implementation - would fetch historical data
    return {
      completionRate: [
        { date: '2024-02-01', rate: 20 },
        { date: '2024-02-08', rate: 35 },
        { date: '2024-02-15', rate: 57 }
      ],
      riskScore: [
        { date: '2024-02-01', score: 75 },
        { date: '2024-02-08', score: 60 },
        { date: '2024-02-15', score: 45 }
      ],
      alertVolume: [
        { date: '2024-02-01', count: 8 },
        { date: '2024-02-08', count: 5 },
        { date: '2024-02-15', count: 3 }
      ]
    }
  }

  private async updateMonitoringMetrics(dealId: string, tenantId: string): Promise<void> {
    // Update monitoring metrics in cache/database
    const metrics = {
      dealId,
      lastCheck: new Date(),
      checksPerformed: 1
    }

    await this.cache.set(`monitoring:metrics:${dealId}`, metrics, 86400)
  }
}
