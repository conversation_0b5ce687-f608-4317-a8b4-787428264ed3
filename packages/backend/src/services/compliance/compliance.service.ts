import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { EventEmitter } from 'events'
import {
  ComplianceFramework,
  ComplianceStatus,
  ComplianceStatusType,
  ComplianceAlert,
  ComplianceIssue,
  TransactionType,
  EntityType,
  CompliancePhase,
  RiskLevel,
  AlertType,
  AlertSeverity
} from '@/shared/types/compliance'
import { REGULATORY_FRAMEWORKS, getApplicableFrameworks } from '@/data/regulatory-frameworks'

export interface ComplianceAssessment {
  dealId: string
  applicableFrameworks: ComplianceFramework[]
  complianceStatuses: ComplianceStatus[]
  overallRiskLevel: RiskLevel
  criticalDeadlines: Array<{
    requirementId: string
    requirementName: string
    dueDate: Date
    daysRemaining: number
    status: ComplianceStatusType
  }>
  openIssues: ComplianceIssue[]
  completionPercentage: number
}

export interface ComplianceConfiguration {
  dealId: string
  transactionType: TransactionType
  transactionValue: number
  transactionCurrency: string
  jurisdictions: string[]
  industries: string[]
  entityTypes: EntityType[]
  targetCompanyName: string
  acquirerCompanyName: string
  expectedClosingDate?: Date
  customRequirements: string[]
  exemptionClaims: string[]
}

export class ComplianceService extends EventEmitter {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    super()
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('ComplianceService')
  }

  /**
   * Initialize compliance tracking for a deal
   */
  async initializeCompliance(
    config: ComplianceConfiguration,
    userId: string,
    tenantId: string
  ): Promise<ComplianceAssessment> {
    try {
      this.logger.info('Initializing compliance tracking', {
        dealId: config.dealId,
        transactionType: config.transactionType,
        transactionValue: config.transactionValue
      })

      // Get applicable regulatory frameworks
      const applicableFrameworks = getApplicableFrameworks(
        config.transactionType,
        config.transactionValue,
        config.jurisdictions,
        config.industries,
        config.entityTypes
      )

      // Create compliance statuses for each requirement
      const complianceStatuses: ComplianceStatus[] = []
      
      for (const framework of applicableFrameworks) {
        for (const requirement of framework.requirements) {
          const dueDate = this.calculateDueDate(requirement, config.expectedClosingDate)
          
          const status: ComplianceStatus = {
            id: `${config.dealId}-${framework.id}-${requirement.id}`,
            dealId: config.dealId,
            frameworkId: framework.id,
            requirementId: requirement.id,
            status: ComplianceStatusType.NOT_STARTED,
            completionPercentage: 0,
            lastUpdated: new Date(),
            updatedBy: userId,
            dueDate,
            isOverdue: dueDate ? dueDate < new Date() : false,
            daysRemaining: dueDate ? Math.ceil((dueDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : undefined,
            submittedDocuments: [],
            missingDocuments: requirement.documentationRequired.map(doc => doc.name),
            issues: [],
            riskLevel: this.assessRequirementRisk(requirement, dueDate),
            notes: [],
            tenantId,
            createdAt: new Date(),
            updatedAt: new Date()
          }

          complianceStatuses.push(status)

          // Store in database
          await this.prisma.complianceStatus.create({
            data: {
              id: status.id,
              dealId: status.dealId,
              frameworkId: status.frameworkId,
              requirementId: status.requirementId,
              status: status.status,
              completionPercentage: status.completionPercentage,
              lastUpdated: status.lastUpdated,
              updatedBy: status.updatedBy,
              dueDate: status.dueDate,
              isOverdue: status.isOverdue,
              daysRemaining: status.daysRemaining,
              submittedDocuments: status.submittedDocuments,
              missingDocuments: status.missingDocuments,
              riskLevel: status.riskLevel,
              notes: status.notes,
              tenantId: status.tenantId
            }
          })
        }
      }

      // Calculate overall assessment
      const assessment = await this.calculateComplianceAssessment(
        config.dealId,
        applicableFrameworks,
        complianceStatuses
      )

      // Generate initial alerts for critical deadlines
      await this.generateDeadlineAlerts(assessment, tenantId)

      this.logger.info('Compliance tracking initialized', {
        dealId: config.dealId,
        frameworksCount: applicableFrameworks.length,
        requirementsCount: complianceStatuses.length,
        overallRisk: assessment.overallRiskLevel
      })

      return assessment
    } catch (error) {
      this.logger.error('Failed to initialize compliance tracking', error)
      throw error
    }
  }

  /**
   * Update compliance status
   */
  async updateComplianceStatus(
    statusId: string,
    updates: Partial<ComplianceStatus>,
    userId: string
  ): Promise<ComplianceStatus> {
    try {
      this.logger.info('Updating compliance status', { statusId, userId })

      const currentStatus = await this.getComplianceStatus(statusId)
      if (!currentStatus) {
        throw new Error('Compliance status not found')
      }

      // Calculate completion percentage if documents are updated
      let completionPercentage = updates.completionPercentage
      if (updates.submittedDocuments || updates.missingDocuments) {
        completionPercentage = this.calculateCompletionPercentage(
          updates.submittedDocuments || currentStatus.submittedDocuments,
          updates.missingDocuments || currentStatus.missingDocuments
        )
      }

      // Update status
      const updatedStatus = await this.prisma.complianceStatus.update({
        where: { id: statusId },
        data: {
          ...updates,
          completionPercentage,
          lastUpdated: new Date(),
          updatedBy: userId,
          updatedAt: new Date()
        }
      })

      // Check for status changes that require alerts
      if (updates.status && updates.status !== currentStatus.status) {
        await this.handleStatusChange(updatedStatus as ComplianceStatus, currentStatus)
      }

      // Emit status update event
      this.emit('compliance:status_updated', {
        statusId,
        previousStatus: currentStatus.status,
        newStatus: updatedStatus.status,
        userId
      })

      this.logger.info('Compliance status updated', { statusId, newStatus: updatedStatus.status })

      return updatedStatus as ComplianceStatus
    } catch (error) {
      this.logger.error('Failed to update compliance status', error)
      throw error
    }
  }

  /**
   * Get compliance assessment for deal
   */
  async getComplianceAssessment(dealId: string, tenantId: string): Promise<ComplianceAssessment> {
    try {
      const cacheKey = `compliance:assessment:${dealId}`
      
      // Try cache first
      const cached = await this.cache.get<ComplianceAssessment>(cacheKey)
      if (cached) {
        return cached
      }

      // Get compliance statuses
      const complianceStatuses = await this.prisma.complianceStatus.findMany({
        where: { dealId, tenantId }
      }) as ComplianceStatus[]

      // Get applicable frameworks
      const frameworkIds = [...new Set(complianceStatuses.map(s => s.frameworkId))]
      const applicableFrameworks = REGULATORY_FRAMEWORKS.filter(f => 
        frameworkIds.includes(f.id)
      )

      // Calculate assessment
      const assessment = await this.calculateComplianceAssessment(
        dealId,
        applicableFrameworks,
        complianceStatuses
      )

      // Cache for 15 minutes
      await this.cache.set(cacheKey, assessment, 900)

      return assessment
    } catch (error) {
      this.logger.error('Failed to get compliance assessment', error)
      throw error
    }
  }

  /**
   * Monitor compliance deadlines
   */
  async monitorDeadlines(tenantId: string): Promise<void> {
    try {
      this.logger.info('Monitoring compliance deadlines', { tenantId })

      // Get all active compliance statuses with upcoming deadlines
      const upcomingDeadlines = await this.prisma.complianceStatus.findMany({
        where: {
          tenantId,
          status: {
            in: [
              ComplianceStatusType.NOT_STARTED,
              ComplianceStatusType.IN_PROGRESS,
              ComplianceStatusType.PENDING_REVIEW
            ]
          },
          dueDate: {
            gte: new Date(),
            lte: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // Next 30 days
          }
        }
      }) as ComplianceStatus[]

      // Generate alerts for approaching deadlines
      for (const status of upcomingDeadlines) {
        await this.checkDeadlineAlert(status)
      }

      // Check for overdue items
      const overdueItems = await this.prisma.complianceStatus.findMany({
        where: {
          tenantId,
          dueDate: { lt: new Date() },
          status: {
            not: ComplianceStatusType.COMPLETED
          },
          isOverdue: false
        }
      }) as ComplianceStatus[]

      // Mark as overdue and generate alerts
      for (const status of overdueItems) {
        await this.markAsOverdue(status)
      }

      this.logger.info('Deadline monitoring completed', {
        tenantId,
        upcomingCount: upcomingDeadlines.length,
        overdueCount: overdueItems.length
      })
    } catch (error) {
      this.logger.error('Failed to monitor compliance deadlines', error)
    }
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(
    dealId: string,
    tenantId: string,
    reportType: 'SUMMARY' | 'DETAILED' | 'AUDIT_TRAIL'
  ): Promise<any> {
    try {
      this.logger.info('Generating compliance report', { dealId, reportType })

      const assessment = await this.getComplianceAssessment(dealId, tenantId)
      
      const report = {
        dealId,
        reportType,
        generatedAt: new Date(),
        assessment,
        summary: {
          totalRequirements: assessment.complianceStatuses.length,
          completedRequirements: assessment.complianceStatuses.filter(
            s => s.status === ComplianceStatusType.COMPLETED
          ).length,
          overdueRequirements: assessment.complianceStatuses.filter(
            s => s.isOverdue
          ).length,
          criticalIssues: assessment.openIssues.filter(
            i => i.severity === 'CRITICAL'
          ).length,
          overallRisk: assessment.overallRiskLevel,
          completionPercentage: assessment.completionPercentage
        }
      }

      if (reportType === 'DETAILED') {
        // Add detailed breakdown by framework
        report['frameworkBreakdown'] = assessment.applicableFrameworks.map(framework => ({
          frameworkId: framework.id,
          frameworkName: framework.name,
          category: framework.category,
          requirements: assessment.complianceStatuses.filter(
            s => s.frameworkId === framework.id
          )
        }))
      }

      if (reportType === 'AUDIT_TRAIL') {
        // Add audit trail information
        report['auditTrail'] = await this.getAuditTrail(dealId, tenantId)
      }

      return report
    } catch (error) {
      this.logger.error('Failed to generate compliance report', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async getComplianceStatus(statusId: string): Promise<ComplianceStatus | null> {
    const status = await this.prisma.complianceStatus.findUnique({
      where: { id: statusId }
    })

    return status as ComplianceStatus | null
  }

  private calculateDueDate(requirement: any, expectedClosingDate?: Date): Date | undefined {
    if (!requirement.filingDeadline && !requirement.approvalTimeframe) {
      return undefined
    }

    const timeframe = requirement.filingDeadline || requirement.approvalTimeframe
    const baseDate = expectedClosingDate || new Date()
    
    // Calculate due date based on trigger event and days
    const dueDate = new Date(baseDate)
    
    if (timeframe.phase === CompliancePhase.PRE_CLOSING) {
      dueDate.setDate(dueDate.getDate() - timeframe.daysFromTrigger)
    } else {
      dueDate.setDate(dueDate.getDate() + timeframe.daysFromTrigger)
    }

    return dueDate
  }

  private assessRequirementRisk(requirement: any, dueDate?: Date): RiskLevel {
    if (requirement.priority === 'CRITICAL') {
      return RiskLevel.CRITICAL
    }
    
    if (dueDate) {
      const daysUntilDue = Math.ceil((dueDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
      if (daysUntilDue < 7) return RiskLevel.HIGH
      if (daysUntilDue < 30) return RiskLevel.MEDIUM
    }

    return RiskLevel.LOW
  }

  private calculateCompletionPercentage(submittedDocs: any[], missingDocs: string[]): number {
    const totalDocs = submittedDocs.length + missingDocs.length
    if (totalDocs === 0) return 100
    
    return Math.round((submittedDocs.length / totalDocs) * 100)
  }

  private async calculateComplianceAssessment(
    dealId: string,
    frameworks: ComplianceFramework[],
    statuses: ComplianceStatus[]
  ): Promise<ComplianceAssessment> {
    // Calculate overall risk level
    const riskLevels = statuses.map(s => s.riskLevel)
    const overallRiskLevel = riskLevels.includes(RiskLevel.CRITICAL) ? RiskLevel.CRITICAL :
                            riskLevels.includes(RiskLevel.HIGH) ? RiskLevel.HIGH :
                            riskLevels.includes(RiskLevel.MEDIUM) ? RiskLevel.MEDIUM :
                            RiskLevel.LOW

    // Get critical deadlines
    const criticalDeadlines = statuses
      .filter(s => s.dueDate && s.riskLevel === RiskLevel.CRITICAL)
      .map(s => {
        const framework = frameworks.find(f => f.id === s.frameworkId)
        const requirement = framework?.requirements.find(r => r.id === s.requirementId)
        
        return {
          requirementId: s.requirementId,
          requirementName: requirement?.name || 'Unknown Requirement',
          dueDate: s.dueDate!,
          daysRemaining: s.daysRemaining || 0,
          status: s.status
        }
      })
      .sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime())

    // Get open issues
    const openIssues = statuses.flatMap(s => s.issues.filter(i => i.status === 'OPEN'))

    // Calculate completion percentage
    const completedCount = statuses.filter(s => s.status === ComplianceStatusType.COMPLETED).length
    const completionPercentage = statuses.length > 0 ? 
      Math.round((completedCount / statuses.length) * 100) : 0

    return {
      dealId,
      applicableFrameworks: frameworks,
      complianceStatuses: statuses,
      overallRiskLevel,
      criticalDeadlines,
      openIssues,
      completionPercentage
    }
  }

  private async generateDeadlineAlerts(assessment: ComplianceAssessment, tenantId: string): Promise<void> {
    for (const deadline of assessment.criticalDeadlines) {
      if (deadline.daysRemaining <= 7) {
        await this.createAlert({
          type: AlertType.DEADLINE_APPROACHING,
          severity: deadline.daysRemaining <= 3 ? AlertSeverity.CRITICAL : AlertSeverity.WARNING,
          title: 'Critical Deadline Approaching',
          message: `${deadline.requirementName} is due in ${deadline.daysRemaining} days`,
          dealId: assessment.dealId,
          triggerDate: new Date(),
          triggerEvent: 'Deadline monitoring',
          recipients: [], // Would be populated based on deal team
          notificationChannels: [],
          status: 'ACTIVE',
          suggestedActions: ['Review requirement status', 'Prepare required documents'],
          escalationRules: [],
          tenantId
        })
      }
    }
  }

  private async handleStatusChange(newStatus: ComplianceStatus, oldStatus: ComplianceStatus): Promise<void> {
    // Handle specific status transitions
    if (newStatus.status === ComplianceStatusType.COMPLETED && 
        oldStatus.status !== ComplianceStatusType.COMPLETED) {
      this.emit('compliance:requirement_completed', {
        statusId: newStatus.id,
        dealId: newStatus.dealId,
        requirementId: newStatus.requirementId
      })
    }
  }

  private async checkDeadlineAlert(status: ComplianceStatus): Promise<void> {
    if (!status.dueDate) return

    const daysUntilDue = Math.ceil((status.dueDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
    
    if ([7, 3, 1].includes(daysUntilDue)) {
      await this.createAlert({
        type: AlertType.DEADLINE_APPROACHING,
        severity: daysUntilDue === 1 ? AlertSeverity.CRITICAL : AlertSeverity.WARNING,
        title: 'Compliance Deadline Approaching',
        message: `Requirement due in ${daysUntilDue} day(s)`,
        dealId: status.dealId,
        complianceStatusId: status.id,
        triggerDate: new Date(),
        triggerEvent: 'Deadline monitoring',
        recipients: [],
        notificationChannels: [],
        status: 'ACTIVE',
        suggestedActions: ['Review and complete requirement'],
        escalationRules: [],
        tenantId: status.tenantId
      })
    }
  }

  private async markAsOverdue(status: ComplianceStatus): Promise<void> {
    await this.prisma.complianceStatus.update({
      where: { id: status.id },
      data: {
        isOverdue: true,
        status: ComplianceStatusType.OVERDUE,
        updatedAt: new Date()
      }
    })

    await this.createAlert({
      type: AlertType.DEADLINE_MISSED,
      severity: AlertSeverity.CRITICAL,
      title: 'Compliance Deadline Missed',
      message: 'Requirement is now overdue',
      dealId: status.dealId,
      complianceStatusId: status.id,
      triggerDate: new Date(),
      triggerEvent: 'Deadline monitoring',
      recipients: [],
      notificationChannels: [],
      status: 'ACTIVE',
      suggestedActions: ['Immediate action required', 'Contact compliance team'],
      escalationRules: [],
      tenantId: status.tenantId
    })
  }

  private async createAlert(alert: Omit<ComplianceAlert, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {
    await this.prisma.complianceAlert.create({
      data: {
        ...alert,
        id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      }
    })
  }

  private async getAuditTrail(dealId: string, tenantId: string): Promise<any[]> {
    // Mock implementation - would fetch actual audit trail
    return []
  }
}
