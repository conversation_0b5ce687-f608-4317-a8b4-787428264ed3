import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { FileStorageService } from '@/services/file-storage.service'
import { EventEmitter } from 'events'
import {
  ComplianceReport,
  ReportType,
  ReportFormat,
  ReportVisibility,
  ComplianceStatus,
  ComplianceAlert,
  DateRange,
  ReportGrouping
} from '@/shared/types/compliance'

export interface ComplianceReportData {
  dealId: string
  reportType: ReportType
  generatedAt: Date
  
  // Summary data
  summary: {
    totalRequirements: number
    completedRequirements: number
    inProgressRequirements: number
    overdueRequirements: number
    completionPercentage: number
    overallRiskLevel: string
    criticalIssues: number
  }
  
  // Detailed data
  requirementsByFramework: Array<{
    frameworkId: string
    frameworkName: string
    category: string
    requirements: ComplianceStatus[]
  }>
  
  alertsSummary: {
    totalAlerts: number
    criticalAlerts: number
    resolvedAlerts: number
    averageResolutionTime: number
    alertsByType: Array<{ type: string; count: number }>
  }
  
  timelineData: Array<{
    date: string
    completedItems: number
    newAlerts: number
    riskScore: number
  }>
  
  riskAssessment: {
    currentRiskScore: number
    riskFactors: Array<{
      factor: string
      impact: string
      likelihood: string
      mitigation: string
    }>
    recommendations: string[]
  }
  
  auditTrail: Array<{
    timestamp: string
    action: string
    entity: string
    user: string
    details: string
  }>
}

export interface ReportTemplate {
  id: string
  name: string
  description: string
  reportType: ReportType
  format: ReportFormat
  sections: ReportSection[]
  styling: ReportStyling
  isPublic: boolean
  usageCount: number
}

export interface ReportSection {
  id: string
  name: string
  type: 'SUMMARY' | 'TABLE' | 'CHART' | 'TEXT' | 'METRICS'
  order: number
  configuration: any
}

export interface ReportStyling {
  theme: 'corporate' | 'minimal' | 'detailed'
  colors: {
    primary: string
    secondary: string
    accent: string
  }
  logo?: string
  header?: string
  footer?: string
}

export class ComplianceReportingService extends EventEmitter {
  private prisma: PrismaClient
  private cache: CacheService
  private fileStorage: FileStorageService
  private logger: Logger

  constructor(
    prisma: PrismaClient,
    cache: CacheService,
    fileStorage: FileStorageService
  ) {
    super()
    this.prisma = prisma
    this.cache = cache
    this.fileStorage = fileStorage
    this.logger = new Logger('ComplianceReportingService')
  }

  /**
   * Generate compliance report
   */
  async generateReport(
    reportConfig: Omit<ComplianceReport, 'id' | 'generatedDate' | 'reportUrl' | 'createdAt' | 'updatedAt'>,
    userId: string
  ): Promise<ComplianceReport> {
    try {
      this.logger.info('Generating compliance report', {
        type: reportConfig.type,
        dealId: reportConfig.dealId,
        format: reportConfig.format
      })

      // Collect report data
      const reportData = await this.collectReportData(reportConfig)

      // Generate report file
      const reportFile = await this.generateReportFile(reportData, reportConfig.format)

      // Create report record
      const report = await this.prisma.complianceReport.create({
        data: {
          name: reportConfig.name,
          type: reportConfig.type,
          dealId: reportConfig.dealId,
          frameworkIds: reportConfig.frameworkIds,
          dateRange: reportConfig.dateRange,
          includeCompleted: reportConfig.includeCompleted,
          includeInProgress: reportConfig.includeInProgress,
          includeOverdue: reportConfig.includeOverdue,
          groupBy: reportConfig.groupBy,
          generatedDate: new Date(),
          reportUrl: reportFile.url,
          format: reportConfig.format,
          isScheduled: reportConfig.isScheduled,
          schedule: reportConfig.schedule,
          visibility: reportConfig.visibility,
          allowedUsers: reportConfig.allowedUsers,
          createdBy: userId,
          tenantId: reportConfig.tenantId
        }
      })

      // Emit report generated event
      this.emit('report:generated', {
        reportId: report.id,
        type: reportConfig.type,
        dealId: reportConfig.dealId,
        userId
      })

      this.logger.info('Compliance report generated successfully', {
        reportId: report.id,
        fileUrl: reportFile.url
      })

      return report as ComplianceReport
    } catch (error) {
      this.logger.error('Failed to generate compliance report', error)
      throw error
    }
  }

  /**
   * Generate executive summary report
   */
  async generateExecutiveSummary(
    dealId: string,
    tenantId: string,
    userId: string
  ): Promise<ComplianceReport> {
    const reportConfig: Omit<ComplianceReport, 'id' | 'generatedDate' | 'reportUrl' | 'createdAt' | 'updatedAt'> = {
      name: `Executive Summary - ${new Date().toLocaleDateString()}`,
      type: ReportType.EXECUTIVE_SUMMARY,
      dealId,
      frameworkIds: [],
      dateRange: {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        endDate: new Date()
      },
      includeCompleted: true,
      includeInProgress: true,
      includeOverdue: true,
      groupBy: [ReportGrouping.FRAMEWORK, ReportGrouping.STATUS],
      format: ReportFormat.PDF,
      isScheduled: false,
      visibility: ReportVisibility.TEAM,
      allowedUsers: [userId],
      createdBy: userId,
      tenantId
    }

    return this.generateReport(reportConfig, userId)
  }

  /**
   * Generate audit trail report
   */
  async generateAuditTrail(
    dealId: string,
    dateRange: DateRange,
    tenantId: string,
    userId: string
  ): Promise<ComplianceReport> {
    const reportConfig: Omit<ComplianceReport, 'id' | 'generatedDate' | 'reportUrl' | 'createdAt' | 'updatedAt'> = {
      name: `Audit Trail - ${dateRange.startDate.toLocaleDateString()} to ${dateRange.endDate.toLocaleDateString()}`,
      type: ReportType.AUDIT_TRAIL,
      dealId,
      frameworkIds: [],
      dateRange,
      includeCompleted: true,
      includeInProgress: true,
      includeOverdue: true,
      groupBy: [ReportGrouping.DUE_DATE],
      format: ReportFormat.EXCEL,
      isScheduled: false,
      visibility: ReportVisibility.PRIVATE,
      allowedUsers: [userId],
      createdBy: userId,
      tenantId
    }

    return this.generateReport(reportConfig, userId)
  }

  /**
   * Schedule recurring report
   */
  async scheduleReport(
    reportConfig: Omit<ComplianceReport, 'id' | 'generatedDate' | 'reportUrl' | 'createdAt' | 'updatedAt'>,
    userId: string
  ): Promise<ComplianceReport> {
    try {
      this.logger.info('Scheduling compliance report', {
        name: reportConfig.name,
        type: reportConfig.type,
        schedule: reportConfig.schedule
      })

      const scheduledReport = await this.prisma.complianceReport.create({
        data: {
          ...reportConfig,
          isScheduled: true,
          createdBy: userId
        }
      })

      // Set up scheduling (would integrate with job scheduler)
      await this.setupReportSchedule(scheduledReport as ComplianceReport)

      this.logger.info('Report scheduled successfully', {
        reportId: scheduledReport.id
      })

      return scheduledReport as ComplianceReport
    } catch (error) {
      this.logger.error('Failed to schedule report', error)
      throw error
    }
  }

  /**
   * Get available report templates
   */
  async getReportTemplates(tenantId: string): Promise<ReportTemplate[]> {
    try {
      // Mock implementation - would fetch from database
      const templates: ReportTemplate[] = [
        {
          id: 'template-1',
          name: 'Standard Compliance Report',
          description: 'Comprehensive compliance status report',
          reportType: ReportType.COMPLIANCE_STATUS,
          format: ReportFormat.PDF,
          sections: [
            {
              id: 'section-1',
              name: 'Executive Summary',
              type: 'SUMMARY',
              order: 1,
              configuration: {}
            },
            {
              id: 'section-2',
              name: 'Requirements Status',
              type: 'TABLE',
              order: 2,
              configuration: {}
            }
          ],
          styling: {
            theme: 'corporate',
            colors: {
              primary: '#1f2937',
              secondary: '#6b7280',
              accent: '#3b82f6'
            }
          },
          isPublic: true,
          usageCount: 45
        },
        {
          id: 'template-2',
          name: 'Executive Dashboard',
          description: 'High-level executive summary',
          reportType: ReportType.EXECUTIVE_SUMMARY,
          format: ReportFormat.PDF,
          sections: [
            {
              id: 'section-1',
              name: 'Key Metrics',
              type: 'METRICS',
              order: 1,
              configuration: {}
            },
            {
              id: 'section-2',
              name: 'Risk Assessment',
              type: 'CHART',
              order: 2,
              configuration: {}
            }
          ],
          styling: {
            theme: 'minimal',
            colors: {
              primary: '#000000',
              secondary: '#666666',
              accent: '#0066cc'
            }
          },
          isPublic: true,
          usageCount: 32
        }
      ]

      return templates
    } catch (error) {
      this.logger.error('Failed to get report templates', error)
      throw error
    }
  }

  /**
   * Export report data
   */
  async exportReportData(
    reportId: string,
    format: ReportFormat,
    userId: string
  ): Promise<{ url: string; filename: string }> {
    try {
      this.logger.info('Exporting report data', { reportId, format, userId })

      const report = await this.getReport(reportId, userId)
      if (!report) {
        throw new Error('Report not found')
      }

      // Get fresh report data
      const reportData = await this.collectReportData(report)

      // Generate export file
      const exportFile = await this.generateExportFile(reportData, format, report.name)

      this.logger.info('Report data exported successfully', {
        reportId,
        format,
        filename: exportFile.filename
      })

      return exportFile
    } catch (error) {
      this.logger.error('Failed to export report data', error)
      throw error
    }
  }

  /**
   * Get user reports
   */
  async getUserReports(
    userId: string,
    tenantId: string,
    options: {
      dealId?: string
      type?: ReportType
      limit?: number
      offset?: number
    } = {}
  ): Promise<{ reports: ComplianceReport[]; total: number }> {
    try {
      const where: any = {
        tenantId,
        OR: [
          { createdBy: userId },
          { allowedUsers: { has: userId } },
          { visibility: ReportVisibility.ORGANIZATION }
        ]
      }

      if (options.dealId) {
        where.dealId = options.dealId
      }

      if (options.type) {
        where.type = options.type
      }

      const [reports, total] = await Promise.all([
        this.prisma.complianceReport.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          take: options.limit || 50,
          skip: options.offset || 0
        }),
        this.prisma.complianceReport.count({ where })
      ])

      return {
        reports: reports as ComplianceReport[],
        total
      }
    } catch (error) {
      this.logger.error('Failed to get user reports', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async collectReportData(reportConfig: any): Promise<ComplianceReportData> {
    // Get compliance statuses
    const complianceStatuses = await this.getComplianceStatuses(
      reportConfig.dealId,
      reportConfig.tenantId
    )

    // Get alerts
    const alerts = await this.getComplianceAlerts(
      reportConfig.dealId,
      reportConfig.tenantId,
      reportConfig.dateRange
    )

    // Calculate summary metrics
    const summary = this.calculateSummaryMetrics(complianceStatuses)

    // Group requirements by framework
    const requirementsByFramework = this.groupRequirementsByFramework(complianceStatuses)

    // Calculate alerts summary
    const alertsSummary = this.calculateAlertsSummary(alerts)

    // Get timeline data
    const timelineData = await this.getTimelineData(
      reportConfig.dealId,
      reportConfig.dateRange
    )

    // Perform risk assessment
    const riskAssessment = this.performRiskAssessment(complianceStatuses, alerts)

    // Get audit trail
    const auditTrail = await this.getAuditTrail(
      reportConfig.dealId,
      reportConfig.dateRange
    )

    return {
      dealId: reportConfig.dealId,
      reportType: reportConfig.type,
      generatedAt: new Date(),
      summary,
      requirementsByFramework,
      alertsSummary,
      timelineData,
      riskAssessment,
      auditTrail
    }
  }

  private async generateReportFile(
    reportData: ComplianceReportData,
    format: ReportFormat
  ): Promise<{ url: string; size: number }> {
    // Mock implementation - would generate actual report file
    this.logger.info('Generating report file', {
      dealId: reportData.dealId,
      format
    })

    const filename = `compliance-report-${reportData.dealId}-${Date.now()}.${format.toLowerCase()}`
    const mockContent = JSON.stringify(reportData, null, 2)
    const buffer = Buffer.from(mockContent)

    const url = await this.fileStorage.uploadFile(
      buffer,
      `reports/${filename}`,
      {
        mimeType: this.getMimeType(format),
        metadata: {
          reportType: reportData.reportType,
          dealId: reportData.dealId,
          generatedAt: reportData.generatedAt
        }
      }
    )

    return {
      url,
      size: buffer.length
    }
  }

  private async generateExportFile(
    reportData: ComplianceReportData,
    format: ReportFormat,
    reportName: string
  ): Promise<{ url: string; filename: string }> {
    const filename = `${reportName}_export_${Date.now()}.${format.toLowerCase()}`
    const mockContent = JSON.stringify(reportData, null, 2)
    const buffer = Buffer.from(mockContent)

    const url = await this.fileStorage.uploadFile(
      buffer,
      `exports/${filename}`,
      {
        mimeType: this.getMimeType(format)
      }
    )

    return { url, filename }
  }

  private getMimeType(format: ReportFormat): string {
    switch (format) {
      case ReportFormat.PDF:
        return 'application/pdf'
      case ReportFormat.EXCEL:
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      case ReportFormat.CSV:
        return 'text/csv'
      case ReportFormat.HTML:
        return 'text/html'
      case ReportFormat.JSON:
        return 'application/json'
      default:
        return 'application/octet-stream'
    }
  }

  private async getReport(reportId: string, userId: string): Promise<ComplianceReport | null> {
    const report = await this.prisma.complianceReport.findFirst({
      where: {
        id: reportId,
        OR: [
          { createdBy: userId },
          { allowedUsers: { has: userId } },
          { visibility: ReportVisibility.ORGANIZATION }
        ]
      }
    })

    return report as ComplianceReport | null
  }

  private async getComplianceStatuses(dealId: string, tenantId: string): Promise<ComplianceStatus[]> {
    const statuses = await this.prisma.complianceStatus.findMany({
      where: { dealId, tenantId }
    })

    return statuses as ComplianceStatus[]
  }

  private async getComplianceAlerts(
    dealId: string,
    tenantId: string,
    dateRange?: DateRange
  ): Promise<ComplianceAlert[]> {
    const where: any = { dealId, tenantId }

    if (dateRange) {
      where.triggerDate = {
        gte: dateRange.startDate,
        lte: dateRange.endDate
      }
    }

    const alerts = await this.prisma.complianceAlert.findMany({
      where,
      orderBy: { triggerDate: 'desc' }
    })

    return alerts as ComplianceAlert[]
  }

  private calculateSummaryMetrics(complianceStatuses: ComplianceStatus[]): ComplianceReportData['summary'] {
    const totalRequirements = complianceStatuses.length
    const completedRequirements = complianceStatuses.filter(s => s.status === 'COMPLETED').length
    const inProgressRequirements = complianceStatuses.filter(s => s.status === 'IN_PROGRESS').length
    const overdueRequirements = complianceStatuses.filter(s => s.isOverdue).length

    const completionPercentage = totalRequirements > 0 ? 
      Math.round((completedRequirements / totalRequirements) * 100) : 0

    return {
      totalRequirements,
      completedRequirements,
      inProgressRequirements,
      overdueRequirements,
      completionPercentage,
      overallRiskLevel: 'MEDIUM', // Would be calculated based on actual risk assessment
      criticalIssues: overdueRequirements
    }
  }

  private groupRequirementsByFramework(complianceStatuses: ComplianceStatus[]): ComplianceReportData['requirementsByFramework'] {
    const frameworkMap = new Map()

    complianceStatuses.forEach(status => {
      if (!frameworkMap.has(status.frameworkId)) {
        frameworkMap.set(status.frameworkId, {
          frameworkId: status.frameworkId,
          frameworkName: status.frameworkId, // Would be resolved from framework data
          category: 'UNKNOWN', // Would be resolved from framework data
          requirements: []
        })
      }
      frameworkMap.get(status.frameworkId).requirements.push(status)
    })

    return Array.from(frameworkMap.values())
  }

  private calculateAlertsSummary(alerts: ComplianceAlert[]): ComplianceReportData['alertsSummary'] {
    const totalAlerts = alerts.length
    const criticalAlerts = alerts.filter(a => a.severity === 'CRITICAL').length
    const resolvedAlerts = alerts.filter(a => a.status === 'RESOLVED').length

    // Calculate average resolution time (mock)
    const averageResolutionTime = 2.5 // days

    // Group alerts by type
    const alertsByType = alerts.reduce((acc, alert) => {
      const existing = acc.find(item => item.type === alert.type)
      if (existing) {
        existing.count++
      } else {
        acc.push({ type: alert.type, count: 1 })
      }
      return acc
    }, [] as Array<{ type: string; count: number }>)

    return {
      totalAlerts,
      criticalAlerts,
      resolvedAlerts,
      averageResolutionTime,
      alertsByType
    }
  }

  private async getTimelineData(dealId: string, dateRange?: DateRange): Promise<ComplianceReportData['timelineData']> {
    // Mock implementation - would fetch actual timeline data
    return [
      { date: '2024-02-01', completedItems: 5, newAlerts: 2, riskScore: 75 },
      { date: '2024-02-08', completedItems: 8, newAlerts: 1, riskScore: 60 },
      { date: '2024-02-15', completedItems: 12, newAlerts: 3, riskScore: 45 }
    ]
  }

  private performRiskAssessment(
    complianceStatuses: ComplianceStatus[],
    alerts: ComplianceAlert[]
  ): ComplianceReportData['riskAssessment'] {
    // Mock implementation - would perform actual risk assessment
    return {
      currentRiskScore: 45,
      riskFactors: [
        {
          factor: 'Overdue Requirements',
          impact: 'HIGH',
          likelihood: 'MEDIUM',
          mitigation: 'Prioritize completion of overdue items'
        }
      ],
      recommendations: [
        'Address overdue requirements immediately',
        'Increase monitoring frequency for critical items',
        'Consider additional resources for document review'
      ]
    }
  }

  private async getAuditTrail(dealId: string, dateRange?: DateRange): Promise<ComplianceReportData['auditTrail']> {
    const where: any = { dealId }

    if (dateRange) {
      where.timestamp = {
        gte: dateRange.startDate,
        lte: dateRange.endDate
      }
    }

    const auditLogs = await this.prisma.complianceAuditLog.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: 100 // Limit to recent entries
    })

    return auditLogs.map(log => ({
      timestamp: log.timestamp.toISOString(),
      action: log.action,
      entity: log.entityType,
      user: log.userEmail,
      details: `${log.action} ${log.entityType} ${log.entityId}`
    }))
  }

  private async setupReportSchedule(report: ComplianceReport): Promise<void> {
    // Mock implementation - would set up actual job scheduling
    this.logger.info('Setting up report schedule', {
      reportId: report.id,
      schedule: report.schedule
    })
  }
}
