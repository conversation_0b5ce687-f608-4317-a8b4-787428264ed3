import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { EventEmitter } from 'events'

// Import compliance services
import { ComplianceService } from './compliance.service'
import { ComplianceDocumentService } from './document-management.service'
import { ComplianceMonitoringService } from './monitoring.service'
import { ComplianceReportingService } from './reporting.service'

// Import other system services
import { DealService } from '@/services/deal/deal.service'
import { UserService } from '@/services/user/user.service'
import { NotificationService } from '@/services/notification.service'
import { VDRService } from '@/services/vdr/vdr.service'
import { DDVDRIntegrationService } from '@/services/due-diligence/vdr-integration.service'

import {
  ComplianceConfiguration,
  ComplianceAssessment,
  ComplianceStatus,
  ComplianceAlert,
  ComplianceReport,
  TransactionType,
  EntityType
} from '@/shared/types/compliance'

export interface ComplianceIntegrationConfig {
  dealId: string
  tenantId: string
  enableMonitoring: boolean
  enableReporting: boolean
  enableVDRIntegration: boolean
  enableDueDiligenceIntegration: boolean
  notificationSettings: {
    channels: string[]
    recipients: string[]
    alertThresholds: {
      critical: number
      high: number
      medium: number
    }
  }
  reportingSettings: {
    autoGenerateReports: boolean
    reportFrequency: 'DAILY' | 'WEEKLY' | 'MONTHLY'
    reportRecipients: string[]
  }
}

export interface ComplianceSystemStatus {
  dealId: string
  isActive: boolean
  lastHealthCheck: Date
  services: {
    compliance: { status: 'HEALTHY' | 'DEGRADED' | 'DOWN'; lastCheck: Date }
    monitoring: { status: 'HEALTHY' | 'DEGRADED' | 'DOWN'; lastCheck: Date }
    reporting: { status: 'HEALTHY' | 'DEGRADED' | 'DOWN'; lastCheck: Date }
    documents: { status: 'HEALTHY' | 'DEGRADED' | 'DOWN'; lastCheck: Date }
    integration: { status: 'HEALTHY' | 'DEGRADED' | 'DOWN'; lastCheck: Date }
  }
  metrics: {
    totalRequirements: number
    activeAlerts: number
    documentsProcessed: number
    reportsGenerated: number
    systemUptime: number
  }
}

export class ComplianceIntegrationService extends EventEmitter {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  // Service dependencies
  private complianceService: ComplianceService
  private documentService: ComplianceDocumentService
  private monitoringService: ComplianceMonitoringService
  private reportingService: ComplianceReportingService
  private dealService: DealService
  private userService: UserService
  private notificationService: NotificationService
  private vdrService: VDRService
  private ddVdrIntegrationService: DDVDRIntegrationService

  constructor(
    prisma: PrismaClient,
    cache: CacheService,
    complianceService: ComplianceService,
    documentService: ComplianceDocumentService,
    monitoringService: ComplianceMonitoringService,
    reportingService: ComplianceReportingService,
    dealService: DealService,
    userService: UserService,
    notificationService: NotificationService,
    vdrService: VDRService,
    ddVdrIntegrationService: DDVDRIntegrationService
  ) {
    super()
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('ComplianceIntegrationService')

    // Initialize service dependencies
    this.complianceService = complianceService
    this.documentService = documentService
    this.monitoringService = monitoringService
    this.reportingService = reportingService
    this.dealService = dealService
    this.userService = userService
    this.notificationService = notificationService
    this.vdrService = vdrService
    this.ddVdrIntegrationService = ddVdrIntegrationService

    // Set up event listeners
    this.setupEventListeners()
  }

  /**
   * Initialize compliance system for a deal
   */
  async initializeComplianceSystem(
    config: ComplianceIntegrationConfig,
    userId: string
  ): Promise<ComplianceAssessment> {
    try {
      this.logger.info('Initializing compliance system', {
        dealId: config.dealId,
        tenantId: config.tenantId,
        userId
      })

      // Get deal information
      const deal = await this.dealService.getDeal(config.dealId, config.tenantId)
      if (!deal) {
        throw new Error('Deal not found')
      }

      // Create compliance configuration
      const complianceConfig: ComplianceConfiguration = {
        dealId: config.dealId,
        transactionType: this.mapDealTypeToTransactionType(deal.type),
        transactionValue: deal.value || 0,
        transactionCurrency: deal.currency || 'USD',
        jurisdictions: deal.jurisdictions || ['US'],
        industries: deal.industries || [],
        entityTypes: [EntityType.PUBLIC_COMPANY], // Would be determined from deal data
        targetCompanyName: deal.targetCompany || '',
        acquirerCompanyName: deal.acquirerCompany || '',
        expectedClosingDate: deal.expectedClosingDate ? new Date(deal.expectedClosingDate) : undefined,
        customRequirements: [],
        exemptionClaims: []
      }

      // Initialize compliance tracking
      const assessment = await this.complianceService.initializeCompliance(
        complianceConfig,
        userId,
        config.tenantId
      )

      // Start monitoring if enabled
      if (config.enableMonitoring) {
        await this.monitoringService.startMonitoring(config.dealId, config.tenantId)
      }

      // Set up VDR integration if enabled
      if (config.enableVDRIntegration && deal.vdrId) {
        await this.setupVDRIntegration(config.dealId, deal.vdrId, config.tenantId, userId)
      }

      // Set up due diligence integration if enabled
      if (config.enableDueDiligenceIntegration && deal.checklistId) {
        await this.setupDueDiligenceIntegration(
          config.dealId,
          deal.checklistId,
          deal.vdrId,
          config.tenantId,
          userId
        )
      }

      // Set up automated reporting if enabled
      if (config.enableReporting && config.reportingSettings.autoGenerateReports) {
        await this.setupAutomatedReporting(config, userId)
      }

      // Store integration configuration
      await this.storeIntegrationConfig(config, userId)

      // Emit system initialized event
      this.emit('system:initialized', {
        dealId: config.dealId,
        tenantId: config.tenantId,
        userId,
        assessment
      })

      this.logger.info('Compliance system initialized successfully', {
        dealId: config.dealId,
        requirementsCount: assessment.complianceStatuses.length,
        overallRisk: assessment.overallRiskLevel
      })

      return assessment
    } catch (error) {
      this.logger.error('Failed to initialize compliance system', error)
      throw error
    }
  }

  /**
   * Get system health status
   */
  async getSystemStatus(dealId: string, tenantId: string): Promise<ComplianceSystemStatus> {
    try {
      const cacheKey = `compliance:system:status:${dealId}`
      
      // Try cache first
      const cached = await this.cache.get<ComplianceSystemStatus>(cacheKey)
      if (cached) {
        return cached
      }

      // Check service health
      const serviceChecks = await Promise.allSettled([
        this.checkComplianceServiceHealth(dealId, tenantId),
        this.checkMonitoringServiceHealth(dealId, tenantId),
        this.checkReportingServiceHealth(dealId, tenantId),
        this.checkDocumentServiceHealth(dealId, tenantId),
        this.checkIntegrationHealth(dealId, tenantId)
      ])

      const services = {
        compliance: serviceChecks[0].status === 'fulfilled' ? 
          { status: 'HEALTHY' as const, lastCheck: new Date() } :
          { status: 'DOWN' as const, lastCheck: new Date() },
        monitoring: serviceChecks[1].status === 'fulfilled' ? 
          { status: 'HEALTHY' as const, lastCheck: new Date() } :
          { status: 'DOWN' as const, lastCheck: new Date() },
        reporting: serviceChecks[2].status === 'fulfilled' ? 
          { status: 'HEALTHY' as const, lastCheck: new Date() } :
          { status: 'DOWN' as const, lastCheck: new Date() },
        documents: serviceChecks[3].status === 'fulfilled' ? 
          { status: 'HEALTHY' as const, lastCheck: new Date() } :
          { status: 'DOWN' as const, lastCheck: new Date() },
        integration: serviceChecks[4].status === 'fulfilled' ? 
          { status: 'HEALTHY' as const, lastCheck: new Date() } :
          { status: 'DOWN' as const, lastCheck: new Date() }
      }

      // Get system metrics
      const metrics = await this.getSystemMetrics(dealId, tenantId)

      const systemStatus: ComplianceSystemStatus = {
        dealId,
        isActive: Object.values(services).every(s => s.status === 'HEALTHY'),
        lastHealthCheck: new Date(),
        services,
        metrics
      }

      // Cache for 5 minutes
      await this.cache.set(cacheKey, systemStatus, 300)

      return systemStatus
    } catch (error) {
      this.logger.error('Failed to get system status', error)
      throw error
    }
  }

  /**
   * Sync with external systems
   */
  async syncWithExternalSystems(dealId: string, tenantId: string): Promise<void> {
    try {
      this.logger.info('Syncing with external systems', { dealId, tenantId })

      // Get integration configuration
      const config = await this.getIntegrationConfig(dealId, tenantId)
      if (!config) {
        this.logger.warn('No integration configuration found', { dealId })
        return
      }

      // Sync with VDR if enabled
      if (config.enableVDRIntegration) {
        await this.syncWithVDR(dealId, tenantId)
      }

      // Sync with due diligence system if enabled
      if (config.enableDueDiligenceIntegration) {
        await this.syncWithDueDiligence(dealId, tenantId)
      }

      // Update compliance status based on external data
      await this.updateComplianceFromExternalSources(dealId, tenantId)

      this.logger.info('External systems sync completed', { dealId })
    } catch (error) {
      this.logger.error('Failed to sync with external systems', error)
    }
  }

  /**
   * Handle compliance events
   */
  async handleComplianceEvent(
    eventType: string,
    eventData: any,
    dealId: string,
    tenantId: string
  ): Promise<void> {
    try {
      this.logger.info('Handling compliance event', { eventType, dealId })

      switch (eventType) {
        case 'requirement:completed':
          await this.handleRequirementCompleted(eventData, dealId, tenantId)
          break
        case 'deadline:approaching':
          await this.handleDeadlineApproaching(eventData, dealId, tenantId)
          break
        case 'document:uploaded':
          await this.handleDocumentUploaded(eventData, dealId, tenantId)
          break
        case 'alert:critical':
          await this.handleCriticalAlert(eventData, dealId, tenantId)
          break
        default:
          this.logger.warn('Unknown compliance event type', { eventType })
      }
    } catch (error) {
      this.logger.error('Failed to handle compliance event', error)
    }
  }

  /**
   * Private helper methods
   */
  private setupEventListeners(): void {
    // Listen to compliance service events
    this.complianceService.on('compliance:status_updated', (data) => {
      this.handleComplianceEvent('status:updated', data, data.dealId, data.tenantId)
    })

    this.complianceService.on('compliance:requirement_completed', (data) => {
      this.handleComplianceEvent('requirement:completed', data, data.dealId, data.tenantId)
    })

    // Listen to monitoring service events
    this.monitoringService.on('alert:created', (data) => {
      this.handleComplianceEvent('alert:created', data, data.dealId, data.tenantId)
    })

    this.monitoringService.on('alert:critical', (data) => {
      this.handleComplianceEvent('alert:critical', data, data.dealId, data.tenantId)
    })

    // Listen to document service events
    this.documentService.on('document:uploaded', (data) => {
      this.handleComplianceEvent('document:uploaded', data, data.dealId, data.tenantId)
    })

    this.documentService.on('document:reviewed', (data) => {
      this.handleComplianceEvent('document:reviewed', data, data.dealId, data.tenantId)
    })
  }

  private mapDealTypeToTransactionType(dealType: string): TransactionType {
    switch (dealType?.toUpperCase()) {
      case 'MERGER':
        return TransactionType.MERGER
      case 'ACQUISITION':
        return TransactionType.ACQUISITION
      case 'ASSET_PURCHASE':
        return TransactionType.ASSET_PURCHASE
      case 'STOCK_PURCHASE':
        return TransactionType.STOCK_PURCHASE
      default:
        return TransactionType.ACQUISITION
    }
  }

  private async setupVDRIntegration(
    dealId: string,
    vdrId: string,
    tenantId: string,
    userId: string
  ): Promise<void> {
    try {
      // Create VDR integration for compliance documents
      await this.vdrService.createIntegration({
        dealId,
        vdrId,
        integrationType: 'COMPLIANCE',
        configuration: {
          autoSync: true,
          syncDirection: 'BIDIRECTIONAL',
          documentTypes: ['COMPLIANCE_DOCUMENT', 'REGULATORY_FILING']
        },
        createdBy: userId,
        tenantId
      })

      this.logger.info('VDR integration set up successfully', { dealId, vdrId })
    } catch (error) {
      this.logger.error('Failed to set up VDR integration', error)
    }
  }

  private async setupDueDiligenceIntegration(
    dealId: string,
    checklistId: string,
    vdrId: string | undefined,
    tenantId: string,
    userId: string
  ): Promise<void> {
    try {
      if (vdrId) {
        // Create DD-VDR integration
        await this.ddVdrIntegrationService.createIntegration(
          checklistId,
          vdrId,
          dealId,
          userId,
          tenantId,
          {
            autoSync: true,
            syncDirection: 'BIDIRECTIONAL'
          }
        )
      }

      this.logger.info('Due diligence integration set up successfully', { dealId, checklistId })
    } catch (error) {
      this.logger.error('Failed to set up due diligence integration', error)
    }
  }

  private async setupAutomatedReporting(
    config: ComplianceIntegrationConfig,
    userId: string
  ): Promise<void> {
    try {
      // Create scheduled compliance report
      await this.reportingService.scheduleReport({
        name: `Automated Compliance Report - ${config.dealId}`,
        type: 'COMPLIANCE_STATUS',
        dealId: config.dealId,
        frameworkIds: [],
        dateRange: undefined,
        includeCompleted: true,
        includeInProgress: true,
        includeOverdue: true,
        groupBy: ['FRAMEWORK', 'STATUS'],
        format: 'PDF',
        isScheduled: true,
        schedule: {
          frequency: config.reportingSettings.reportFrequency,
          dayOfWeek: 1, // Monday
          dayOfMonth: 1,
          time: '09:00',
          timezone: 'UTC',
          recipients: config.reportingSettings.reportRecipients,
          isActive: true
        },
        visibility: 'TEAM',
        allowedUsers: config.reportingSettings.reportRecipients,
        createdBy: userId,
        tenantId: config.tenantId
      }, userId)

      this.logger.info('Automated reporting set up successfully', { dealId: config.dealId })
    } catch (error) {
      this.logger.error('Failed to set up automated reporting', error)
    }
  }

  private async storeIntegrationConfig(
    config: ComplianceIntegrationConfig,
    userId: string
  ): Promise<void> {
    await this.cache.set(
      `compliance:integration:config:${config.dealId}`,
      { ...config, createdBy: userId, createdAt: new Date() },
      86400 // 24 hours
    )
  }

  private async getIntegrationConfig(
    dealId: string,
    tenantId: string
  ): Promise<ComplianceIntegrationConfig | null> {
    return this.cache.get(`compliance:integration:config:${dealId}`)
  }

  private async checkComplianceServiceHealth(dealId: string, tenantId: string): Promise<void> {
    await this.complianceService.getComplianceAssessment(dealId, tenantId)
  }

  private async checkMonitoringServiceHealth(dealId: string, tenantId: string): Promise<void> {
    await this.monitoringService.getMonitoringDashboard(dealId, tenantId)
  }

  private async checkReportingServiceHealth(dealId: string, tenantId: string): Promise<void> {
    await this.reportingService.getUserReports('system', tenantId, { dealId, limit: 1 })
  }

  private async checkDocumentServiceHealth(dealId: string, tenantId: string): Promise<void> {
    await this.documentService.searchDocuments({ dealId }, tenantId, { limit: 1 })
  }

  private async checkIntegrationHealth(dealId: string, tenantId: string): Promise<void> {
    // Check if integration configuration exists
    const config = await this.getIntegrationConfig(dealId, tenantId)
    if (!config) {
      throw new Error('Integration configuration not found')
    }
  }

  private async getSystemMetrics(dealId: string, tenantId: string): Promise<ComplianceSystemStatus['metrics']> {
    // Get metrics from various services
    const [assessment, dashboard] = await Promise.all([
      this.complianceService.getComplianceAssessment(dealId, tenantId),
      this.monitoringService.getMonitoringDashboard(dealId, tenantId)
    ])

    return {
      totalRequirements: assessment.complianceStatuses.length,
      activeAlerts: dashboard.alerts.filter(a => a.status === 'ACTIVE').length,
      documentsProcessed: 0, // Would be calculated from document service
      reportsGenerated: 0, // Would be calculated from reporting service
      systemUptime: 99.9 // Would be calculated from actual uptime
    }
  }

  private async syncWithVDR(dealId: string, tenantId: string): Promise<void> {
    // Sync compliance documents with VDR
    this.logger.info('Syncing with VDR', { dealId })
  }

  private async syncWithDueDiligence(dealId: string, tenantId: string): Promise<void> {
    // Sync compliance requirements with due diligence checklist
    this.logger.info('Syncing with due diligence system', { dealId })
  }

  private async updateComplianceFromExternalSources(dealId: string, tenantId: string): Promise<void> {
    // Update compliance status based on external system data
    this.logger.info('Updating compliance from external sources', { dealId })
  }

  private async handleRequirementCompleted(eventData: any, dealId: string, tenantId: string): Promise<void> {
    // Handle requirement completion event
    this.logger.info('Handling requirement completed', { dealId, requirementId: eventData.requirementId })
  }

  private async handleDeadlineApproaching(eventData: any, dealId: string, tenantId: string): Promise<void> {
    // Handle approaching deadline event
    this.logger.info('Handling deadline approaching', { dealId, deadline: eventData.deadline })
  }

  private async handleDocumentUploaded(eventData: any, dealId: string, tenantId: string): Promise<void> {
    // Handle document upload event
    this.logger.info('Handling document uploaded', { dealId, documentId: eventData.documentId })
  }

  private async handleCriticalAlert(eventData: any, dealId: string, tenantId: string): Promise<void> {
    // Handle critical alert event
    this.logger.info('Handling critical alert', { dealId, alertId: eventData.alertId })
    
    // Send immediate notifications for critical alerts
    await this.notificationService.sendNotification({
      type: 'CRITICAL_COMPLIANCE_ALERT',
      title: 'Critical Compliance Alert',
      message: eventData.message,
      recipients: eventData.recipients,
      channels: ['EMAIL', 'SMS'],
      priority: 'URGENT',
      data: eventData
    })
  }
}
