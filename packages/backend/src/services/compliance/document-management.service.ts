import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { FileStorageService } from '@/services/file-storage.service'
import { EventEmitter } from 'events'
import {
  ComplianceDocument,
  DocumentStatus,
  SubmittedDocument
} from '@/shared/types/compliance'

export interface DocumentUploadOptions {
  complianceStatusId: string
  name: string
  type: string
  description?: string
  version?: number
  metadata?: any
}

export interface DocumentReviewResult {
  approved: boolean
  reviewComments: string
  requiredChanges?: string[]
  nextSteps?: string[]
}

export interface DocumentRetentionPolicy {
  id: string
  name: string
  description: string
  retentionPeriodYears: number
  autoDeleteEnabled: boolean
  archiveBeforeDelete: boolean
  applicableDocumentTypes: string[]
  exemptions: string[]
  isActive: boolean
}

export interface DocumentSearchCriteria {
  dealId?: string
  complianceStatusId?: string
  documentType?: string
  status?: DocumentStatus
  submittedBy?: string
  dateRange?: {
    from: Date
    to: Date
  }
  searchTerm?: string
  tags?: string[]
}

export interface DocumentAccessLog {
  id: string
  documentId: string
  userId: string
  userEmail: string
  action: 'VIEW' | 'DOWNLOAD' | 'EDIT' | 'DELETE' | 'SHARE'
  timestamp: Date
  ipAddress?: string
  userAgent?: string
  metadata?: any
}

export class ComplianceDocumentService extends EventEmitter {
  private prisma: PrismaClient
  private cache: CacheService
  private fileStorage: FileStorageService
  private logger: Logger

  constructor(
    prisma: PrismaClient,
    cache: CacheService,
    fileStorage: FileStorageService
  ) {
    super()
    this.prisma = prisma
    this.cache = cache
    this.fileStorage = fileStorage
    this.logger = new Logger('ComplianceDocumentService')
  }

  /**
   * Upload compliance document
   */
  async uploadDocument(
    file: Buffer,
    filename: string,
    mimeType: string,
    options: DocumentUploadOptions,
    userId: string,
    tenantId: string
  ): Promise<ComplianceDocument> {
    try {
      this.logger.info('Uploading compliance document', {
        filename,
        complianceStatusId: options.complianceStatusId,
        userId
      })

      // Validate file
      await this.validateDocument(file, filename, mimeType, options.type)

      // Upload file to storage
      const fileUrl = await this.fileStorage.uploadFile(
        file,
        `compliance/${options.complianceStatusId}/${filename}`,
        {
          mimeType,
          metadata: {
            complianceStatusId: options.complianceStatusId,
            documentType: options.type,
            uploadedBy: userId,
            ...options.metadata
          }
        }
      )

      // Create document record
      const document = await this.prisma.complianceDocument.create({
        data: {
          name: options.name,
          type: options.type,
          description: options.description,
          fileUrl,
          fileSize: file.length,
          mimeType,
          version: options.version || 1,
          status: DocumentStatus.SUBMITTED,
          complianceStatusId: options.complianceStatusId,
          submittedBy: userId,
          submittedDate: new Date(),
          metadata: options.metadata || {},
          tenantId
        }
      })

      // Update compliance status with submitted document
      await this.updateComplianceStatusDocuments(options.complianceStatusId, document as ComplianceDocument)

      // Log access
      await this.logDocumentAccess(document.id, userId, 'UPLOAD')

      // Emit document uploaded event
      this.emit('document:uploaded', {
        documentId: document.id,
        complianceStatusId: options.complianceStatusId,
        userId
      })

      this.logger.info('Compliance document uploaded successfully', {
        documentId: document.id,
        filename
      })

      return document as ComplianceDocument
    } catch (error) {
      this.logger.error('Failed to upload compliance document', error)
      throw error
    }
  }

  /**
   * Review document
   */
  async reviewDocument(
    documentId: string,
    reviewResult: DocumentReviewResult,
    reviewerId: string
  ): Promise<ComplianceDocument> {
    try {
      this.logger.info('Reviewing compliance document', {
        documentId,
        approved: reviewResult.approved,
        reviewerId
      })

      const document = await this.getDocument(documentId)
      if (!document) {
        throw new Error('Document not found')
      }

      // Update document status
      const newStatus = reviewResult.approved ? DocumentStatus.APPROVED : DocumentStatus.REJECTED
      
      const updatedDocument = await this.prisma.complianceDocument.update({
        where: { id: documentId },
        data: {
          status: newStatus,
          reviewedBy: reviewerId,
          reviewedDate: new Date(),
          rejectionReason: reviewResult.approved ? null : reviewResult.reviewComments,
          metadata: {
            ...document.metadata,
            reviewComments: reviewResult.reviewComments,
            requiredChanges: reviewResult.requiredChanges,
            nextSteps: reviewResult.nextSteps
          },
          updatedAt: new Date()
        }
      })

      // Log access
      await this.logDocumentAccess(documentId, reviewerId, 'REVIEW')

      // Emit document reviewed event
      this.emit('document:reviewed', {
        documentId,
        approved: reviewResult.approved,
        reviewerId
      })

      this.logger.info('Document review completed', {
        documentId,
        status: newStatus
      })

      return updatedDocument as ComplianceDocument
    } catch (error) {
      this.logger.error('Failed to review document', error)
      throw error
    }
  }

  /**
   * Get document with access control
   */
  async getDocument(documentId: string, userId?: string): Promise<ComplianceDocument | null> {
    try {
      const document = await this.prisma.complianceDocument.findUnique({
        where: { id: documentId }
      })

      if (!document) {
        return null
      }

      // Log access if user provided
      if (userId) {
        await this.logDocumentAccess(documentId, userId, 'VIEW')
      }

      return document as ComplianceDocument
    } catch (error) {
      this.logger.error('Failed to get document', error)
      throw error
    }
  }

  /**
   * Search documents
   */
  async searchDocuments(
    criteria: DocumentSearchCriteria,
    tenantId: string,
    options: {
      limit?: number
      offset?: number
      sortBy?: 'name' | 'submittedDate' | 'status'
      sortOrder?: 'asc' | 'desc'
    } = {}
  ): Promise<{ documents: ComplianceDocument[]; total: number }> {
    try {
      const where: any = { tenantId }

      // Apply search criteria
      if (criteria.complianceStatusId) {
        where.complianceStatusId = criteria.complianceStatusId
      }

      if (criteria.documentType) {
        where.type = criteria.documentType
      }

      if (criteria.status) {
        where.status = criteria.status
      }

      if (criteria.submittedBy) {
        where.submittedBy = criteria.submittedBy
      }

      if (criteria.dateRange) {
        where.submittedDate = {
          gte: criteria.dateRange.from,
          lte: criteria.dateRange.to
        }
      }

      if (criteria.searchTerm) {
        where.OR = [
          { name: { contains: criteria.searchTerm, mode: 'insensitive' } },
          { description: { contains: criteria.searchTerm, mode: 'insensitive' } }
        ]
      }

      // Execute search
      const [documents, total] = await Promise.all([
        this.prisma.complianceDocument.findMany({
          where,
          orderBy: {
            [options.sortBy || 'submittedDate']: options.sortOrder || 'desc'
          },
          take: options.limit || 50,
          skip: options.offset || 0
        }),
        this.prisma.complianceDocument.count({ where })
      ])

      return {
        documents: documents as ComplianceDocument[],
        total
      }
    } catch (error) {
      this.logger.error('Failed to search documents', error)
      throw error
    }
  }

  /**
   * Download document
   */
  async downloadDocument(documentId: string, userId: string): Promise<{
    fileBuffer: Buffer
    filename: string
    mimeType: string
  }> {
    try {
      const document = await this.getDocument(documentId)
      if (!document) {
        throw new Error('Document not found')
      }

      // Download file from storage
      const fileBuffer = await this.fileStorage.downloadFile(document.fileUrl)

      // Log access
      await this.logDocumentAccess(documentId, userId, 'DOWNLOAD')

      // Emit download event
      this.emit('document:downloaded', {
        documentId,
        userId
      })

      return {
        fileBuffer,
        filename: document.name,
        mimeType: document.mimeType
      }
    } catch (error) {
      this.logger.error('Failed to download document', error)
      throw error
    }
  }

  /**
   * Delete document
   */
  async deleteDocument(documentId: string, userId: string): Promise<void> {
    try {
      this.logger.info('Deleting compliance document', { documentId, userId })

      const document = await this.getDocument(documentId)
      if (!document) {
        throw new Error('Document not found')
      }

      // Check if document can be deleted (business rules)
      await this.validateDocumentDeletion(document)

      // Delete file from storage
      await this.fileStorage.deleteFile(document.fileUrl)

      // Delete document record
      await this.prisma.complianceDocument.delete({
        where: { id: documentId }
      })

      // Log access
      await this.logDocumentAccess(documentId, userId, 'DELETE')

      // Emit document deleted event
      this.emit('document:deleted', {
        documentId,
        userId
      })

      this.logger.info('Document deleted successfully', { documentId })
    } catch (error) {
      this.logger.error('Failed to delete document', error)
      throw error
    }
  }

  /**
   * Apply retention policies
   */
  async applyRetentionPolicies(tenantId: string): Promise<void> {
    try {
      this.logger.info('Applying document retention policies', { tenantId })

      // Get active retention policies
      const policies = await this.getRetentionPolicies(tenantId)

      for (const policy of policies) {
        await this.applyRetentionPolicy(policy, tenantId)
      }

      this.logger.info('Retention policies applied successfully', { tenantId })
    } catch (error) {
      this.logger.error('Failed to apply retention policies', error)
    }
  }

  /**
   * Get document access logs
   */
  async getDocumentAccessLogs(
    documentId: string,
    options: {
      limit?: number
      offset?: number
      action?: string
      userId?: string
    } = {}
  ): Promise<{ logs: DocumentAccessLog[]; total: number }> {
    try {
      const where: any = { documentId }

      if (options.action) {
        where.action = options.action
      }

      if (options.userId) {
        where.userId = options.userId
      }

      const [logs, total] = await Promise.all([
        this.prisma.documentAccessLog.findMany({
          where,
          orderBy: { timestamp: 'desc' },
          take: options.limit || 50,
          skip: options.offset || 0
        }),
        this.prisma.documentAccessLog.count({ where })
      ])

      return {
        logs: logs as DocumentAccessLog[],
        total
      }
    } catch (error) {
      this.logger.error('Failed to get document access logs', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async validateDocument(
    file: Buffer,
    filename: string,
    mimeType: string,
    documentType: string
  ): Promise<void> {
    // Validate file size (50MB limit)
    if (file.length > 50 * 1024 * 1024) {
      throw new Error('File size exceeds 50MB limit')
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png'
    ]

    if (!allowedTypes.includes(mimeType)) {
      throw new Error(`File type ${mimeType} is not allowed`)
    }

    // Additional validation based on document type
    if (documentType === 'FINANCIAL_STATEMENT' && !mimeType.includes('pdf')) {
      throw new Error('Financial statements must be in PDF format')
    }
  }

  private async updateComplianceStatusDocuments(
    complianceStatusId: string,
    document: ComplianceDocument
  ): Promise<void> {
    const complianceStatus = await this.prisma.complianceStatus.findUnique({
      where: { id: complianceStatusId }
    })

    if (!complianceStatus) {
      return
    }

    const submittedDocuments = complianceStatus.submittedDocuments as SubmittedDocument[]
    const missingDocuments = complianceStatus.missingDocuments as string[]

    // Add to submitted documents
    const submittedDoc: SubmittedDocument = {
      id: document.id,
      name: document.name,
      type: document.type,
      fileUrl: document.fileUrl,
      fileSize: document.fileSize,
      submittedDate: document.submittedDate,
      submittedBy: document.submittedBy,
      version: document.version,
      status: document.status
    }

    const updatedSubmitted = [...submittedDocuments, submittedDoc]

    // Remove from missing documents if applicable
    const updatedMissing = missingDocuments.filter(name => name !== document.name)

    // Update compliance status
    await this.prisma.complianceStatus.update({
      where: { id: complianceStatusId },
      data: {
        submittedDocuments: updatedSubmitted,
        missingDocuments: updatedMissing,
        updatedAt: new Date()
      }
    })
  }

  private async validateDocumentDeletion(document: ComplianceDocument): Promise<void> {
    // Check if document is approved and in retention period
    if (document.status === DocumentStatus.APPROVED && document.retentionDate) {
      if (new Date() < document.retentionDate) {
        throw new Error('Document is in retention period and cannot be deleted')
      }
    }

    // Check if document is required for ongoing compliance
    // Additional business rules can be added here
  }

  private async getRetentionPolicies(tenantId: string): Promise<DocumentRetentionPolicy[]> {
    // Mock implementation - would fetch from database
    return [
      {
        id: 'policy-1',
        name: 'Financial Documents',
        description: 'Retention policy for financial documents',
        retentionPeriodYears: 7,
        autoDeleteEnabled: false,
        archiveBeforeDelete: true,
        applicableDocumentTypes: ['FINANCIAL_STATEMENT', 'TAX_RETURN'],
        exemptions: [],
        isActive: true
      }
    ]
  }

  private async applyRetentionPolicy(policy: DocumentRetentionPolicy, tenantId: string): Promise<void> {
    const retentionDate = new Date()
    retentionDate.setFullYear(retentionDate.getFullYear() - policy.retentionPeriodYears)

    // Find documents that exceed retention period
    const expiredDocuments = await this.prisma.complianceDocument.findMany({
      where: {
        tenantId,
        type: { in: policy.applicableDocumentTypes },
        submittedDate: { lt: retentionDate },
        retentionDate: null
      }
    })

    // Apply retention policy
    for (const document of expiredDocuments) {
      if (policy.autoDeleteEnabled) {
        if (policy.archiveBeforeDelete) {
          // Archive document before deletion
          await this.archiveDocument(document.id)
        }
        await this.deleteDocument(document.id, 'system')
      } else {
        // Mark for manual review
        await this.prisma.complianceDocument.update({
          where: { id: document.id },
          data: {
            retentionDate: new Date(),
            metadata: {
              ...document.metadata,
              retentionPolicyApplied: policy.id,
              requiresManualReview: true
            }
          }
        })
      }
    }
  }

  private async archiveDocument(documentId: string): Promise<void> {
    // Mock implementation - would move document to archive storage
    this.logger.info('Archiving document', { documentId })
  }

  private async logDocumentAccess(
    documentId: string,
    userId: string,
    action: DocumentAccessLog['action'],
    metadata?: any
  ): Promise<void> {
    try {
      await this.prisma.documentAccessLog.create({
        data: {
          documentId,
          userId,
          userEmail: '', // Would be fetched from user service
          action,
          timestamp: new Date(),
          metadata
        }
      })
    } catch (error) {
      this.logger.error('Failed to log document access', error)
      // Don't throw error as this is logging
    }
  }
}
