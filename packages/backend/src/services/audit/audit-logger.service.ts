import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface AuditEvent {
  id?: string
  eventType: AuditEventType
  category: AuditCategory
  action: string
  resourceType: string
  resourceId?: string
  userId: string
  tenantId: string
  sessionId?: string
  ipAddress?: string
  userAgent?: string
  timestamp: Date
  success: boolean
  details?: Record<string, any>
  changes?: AuditChanges
  metadata?: Record<string, any>
  severity: AuditSeverity
  compliance?: ComplianceInfo[]
}

export enum AuditEventType {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_ACCESS = 'data_access',
  DATA_MODIFICATION = 'data_modification',
  SYSTEM_CONFIGURATION = 'system_configuration',
  SECURITY_EVENT = 'security_event',
  COMPLIANCE_EVENT = 'compliance_event',
  POLICY_EVALUATION = 'policy_evaluation',
  ROLE_MANAGEMENT = 'role_management',
  USER_MANAGEMENT = 'user_management'
}

export enum AuditCategory {
  SECURITY = 'security',
  ACCESS_CONTROL = 'access_control',
  DATA_PROTECTION = 'data_protection',
  COMPLIANCE = 'compliance',
  SYSTEM = 'system',
  BUSINESS = 'business'
}

export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface AuditChanges {
  before?: Record<string, any>
  after?: Record<string, any>
  fields?: string[]
}

export interface ComplianceInfo {
  framework: string
  requirement: string
  status: 'compliant' | 'non_compliant' | 'unknown'
}

export interface AuditQuery {
  tenantId?: string
  userId?: string
  eventType?: AuditEventType
  category?: AuditCategory
  resourceType?: string
  resourceId?: string
  action?: string
  severity?: AuditSeverity
  success?: boolean
  startDate?: Date
  endDate?: Date
  limit?: number
  offset?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface AuditStatistics {
  totalEvents: number
  eventsByType: Record<AuditEventType, number>
  eventsByCategory: Record<AuditCategory, number>
  eventsBySeverity: Record<AuditSeverity, number>
  successRate: number
  topUsers: Array<{ userId: string; eventCount: number }>
  topResources: Array<{ resourceType: string; eventCount: number }>
  timeRange: { start: Date; end: Date }
}

export class AuditLoggerService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger
  private batchQueue: AuditEvent[] = []
  private batchSize = 100
  private batchTimeout = 5000 // 5 seconds
  private batchTimer?: NodeJS.Timeout

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('AuditLogger')
    
    // Start batch processing
    this.startBatchProcessing()
  }

  /**
   * Log an audit event
   */
  async logEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): Promise<void> {
    try {
      const auditEvent: AuditEvent = {
        ...event,
        id: this.generateEventId(),
        timestamp: new Date()
      }

      // Add to batch queue for performance
      this.batchQueue.push(auditEvent)

      // Process immediately if batch is full
      if (this.batchQueue.length >= this.batchSize) {
        await this.processBatch()
      }

      // Log critical events immediately
      if (event.severity === AuditSeverity.CRITICAL) {
        await this.logImmediately(auditEvent)
      }

      this.logger.debug('Audit event queued', {
        eventType: event.eventType,
        action: event.action,
        userId: event.userId,
        severity: event.severity
      })
    } catch (error) {
      this.logger.error('Failed to log audit event', error, event)
    }
  }

  /**
   * Log authentication event
   */
  async logAuthentication(
    userId: string,
    tenantId: string,
    action: string,
    success: boolean,
    details?: Record<string, any>,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logEvent({
      eventType: AuditEventType.AUTHENTICATION,
      category: AuditCategory.SECURITY,
      action,
      resourceType: 'user',
      resourceId: userId,
      userId,
      tenantId,
      success,
      details,
      metadata,
      severity: success ? AuditSeverity.LOW : AuditSeverity.HIGH
    })
  }

  /**
   * Log authorization event
   */
  async logAuthorization(
    userId: string,
    tenantId: string,
    action: string,
    resourceType: string,
    resourceId: string | undefined,
    success: boolean,
    details?: Record<string, any>,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logEvent({
      eventType: AuditEventType.AUTHORIZATION,
      category: AuditCategory.ACCESS_CONTROL,
      action,
      resourceType,
      resourceId,
      userId,
      tenantId,
      success,
      details,
      metadata,
      severity: success ? AuditSeverity.LOW : AuditSeverity.MEDIUM
    })
  }

  /**
   * Log data access event
   */
  async logDataAccess(
    userId: string,
    tenantId: string,
    action: string,
    resourceType: string,
    resourceId: string | undefined,
    success: boolean,
    details?: Record<string, any>,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logEvent({
      eventType: AuditEventType.DATA_ACCESS,
      category: AuditCategory.DATA_PROTECTION,
      action,
      resourceType,
      resourceId,
      userId,
      tenantId,
      success,
      details,
      metadata,
      severity: AuditSeverity.LOW
    })
  }

  /**
   * Log data modification event
   */
  async logDataModification(
    userId: string,
    tenantId: string,
    action: string,
    resourceType: string,
    resourceId: string | undefined,
    changes: AuditChanges,
    success: boolean,
    details?: Record<string, any>,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logEvent({
      eventType: AuditEventType.DATA_MODIFICATION,
      category: AuditCategory.DATA_PROTECTION,
      action,
      resourceType,
      resourceId,
      userId,
      tenantId,
      success,
      changes,
      details,
      metadata,
      severity: AuditSeverity.MEDIUM
    })
  }

  /**
   * Log role management event
   */
  async logRoleManagement(
    userId: string,
    tenantId: string,
    action: string,
    roleId: string,
    changes?: AuditChanges,
    success: boolean = true,
    details?: Record<string, any>,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logEvent({
      eventType: AuditEventType.ROLE_MANAGEMENT,
      category: AuditCategory.ACCESS_CONTROL,
      action,
      resourceType: 'role',
      resourceId: roleId,
      userId,
      tenantId,
      success,
      changes,
      details,
      metadata,
      severity: AuditSeverity.HIGH
    })
  }

  /**
   * Log policy evaluation event
   */
  async logPolicyEvaluation(
    userId: string,
    tenantId: string,
    action: string,
    resourceType: string,
    resourceId: string | undefined,
    decision: string,
    policies: string[],
    evaluationTime: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logEvent({
      eventType: AuditEventType.POLICY_EVALUATION,
      category: AuditCategory.ACCESS_CONTROL,
      action,
      resourceType,
      resourceId,
      userId,
      tenantId,
      success: decision === 'permit',
      details: {
        decision,
        policies,
        evaluationTime
      },
      metadata,
      severity: AuditSeverity.LOW
    })
  }

  /**
   * Log security event
   */
  async logSecurityEvent(
    userId: string,
    tenantId: string,
    action: string,
    severity: AuditSeverity,
    details: Record<string, any>,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logEvent({
      eventType: AuditEventType.SECURITY_EVENT,
      category: AuditCategory.SECURITY,
      action,
      resourceType: 'system',
      userId,
      tenantId,
      success: false, // Security events are typically failures
      details,
      metadata,
      severity
    })
  }

  /**
   * Query audit events
   */
  async queryEvents(query: AuditQuery): Promise<{
    events: AuditEvent[]
    total: number
    hasMore: boolean
  }> {
    try {
      const where: any = {}

      // Build where clause
      if (query.tenantId) where.tenantId = query.tenantId
      if (query.userId) where.userId = query.userId
      if (query.eventType) where.eventType = query.eventType
      if (query.category) where.category = query.category
      if (query.resourceType) where.resourceType = query.resourceType
      if (query.resourceId) where.resourceId = query.resourceId
      if (query.action) where.action = query.action
      if (query.severity) where.severity = query.severity
      if (query.success !== undefined) where.success = query.success

      if (query.startDate || query.endDate) {
        where.timestamp = {}
        if (query.startDate) where.timestamp.gte = query.startDate
        if (query.endDate) where.timestamp.lte = query.endDate
      }

      // Get total count
      const total = await this.prisma.auditLog.count({ where })

      // Get events
      const events = await this.prisma.auditLog.findMany({
        where,
        orderBy: {
          [query.sortBy || 'timestamp']: query.sortOrder || 'desc'
        },
        take: query.limit || 100,
        skip: query.offset || 0
      })

      const hasMore = (query.offset || 0) + events.length < total

      return {
        events: events as AuditEvent[],
        total,
        hasMore
      }
    } catch (error) {
      this.logger.error('Failed to query audit events', error, query)
      return { events: [], total: 0, hasMore: false }
    }
  }

  /**
   * Get audit statistics
   */
  async getStatistics(
    tenantId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<AuditStatistics> {
    try {
      const where: any = { tenantId }
      
      if (startDate || endDate) {
        where.timestamp = {}
        if (startDate) where.timestamp.gte = startDate
        if (endDate) where.timestamp.lte = endDate
      }

      // Get total events
      const totalEvents = await this.prisma.auditLog.count({ where })

      // Get events by type
      const eventsByType = await this.prisma.auditLog.groupBy({
        by: ['eventType'],
        where,
        _count: { eventType: true }
      })

      // Get events by category
      const eventsByCategory = await this.prisma.auditLog.groupBy({
        by: ['category'],
        where,
        _count: { category: true }
      })

      // Get events by severity
      const eventsBySeverity = await this.prisma.auditLog.groupBy({
        by: ['severity'],
        where,
        _count: { severity: true }
      })

      // Get success rate
      const successfulEvents = await this.prisma.auditLog.count({
        where: { ...where, success: true }
      })
      const successRate = totalEvents > 0 ? (successfulEvents / totalEvents) * 100 : 0

      // Get top users
      const topUsers = await this.prisma.auditLog.groupBy({
        by: ['userId'],
        where,
        _count: { userId: true },
        orderBy: { _count: { userId: 'desc' } },
        take: 10
      })

      // Get top resources
      const topResources = await this.prisma.auditLog.groupBy({
        by: ['resourceType'],
        where,
        _count: { resourceType: true },
        orderBy: { _count: { resourceType: 'desc' } },
        take: 10
      })

      // Get time range
      const timeRange = await this.prisma.auditLog.aggregate({
        where,
        _min: { timestamp: true },
        _max: { timestamp: true }
      })

      return {
        totalEvents,
        eventsByType: this.groupByToRecord(eventsByType, 'eventType'),
        eventsByCategory: this.groupByToRecord(eventsByCategory, 'category'),
        eventsBySeverity: this.groupByToRecord(eventsBySeverity, 'severity'),
        successRate,
        topUsers: topUsers.map(u => ({
          userId: u.userId,
          eventCount: u._count.userId
        })),
        topResources: topResources.map(r => ({
          resourceType: r.resourceType,
          eventCount: r._count.resourceType
        })),
        timeRange: {
          start: timeRange._min.timestamp || new Date(),
          end: timeRange._max.timestamp || new Date()
        }
      }
    } catch (error) {
      this.logger.error('Failed to get audit statistics', error, { tenantId })
      throw error
    }
  }

  /**
   * Process batch of audit events
   */
  private async processBatch(): Promise<void> {
    if (this.batchQueue.length === 0) return

    const batch = this.batchQueue.splice(0, this.batchSize)

    try {
      await this.prisma.auditLog.createMany({
        data: batch.map(event => ({
          eventType: event.eventType,
          category: event.category,
          action: event.action,
          resourceType: event.resourceType,
          resourceId: event.resourceId,
          userId: event.userId,
          tenantId: event.tenantId,
          sessionId: event.sessionId,
          ipAddress: event.ipAddress,
          userAgent: event.userAgent,
          timestamp: event.timestamp,
          success: event.success,
          details: event.details,
          changes: event.changes,
          metadata: event.metadata,
          severity: event.severity
        }))
      })

      this.logger.debug('Audit batch processed', { batchSize: batch.length })
    } catch (error) {
      this.logger.error('Failed to process audit batch', error, { batchSize: batch.length })
      
      // Re-queue failed events
      this.batchQueue.unshift(...batch)
    }
  }

  /**
   * Log event immediately (for critical events)
   */
  private async logImmediately(event: AuditEvent): Promise<void> {
    try {
      await this.prisma.auditLog.create({
        data: {
          eventType: event.eventType,
          category: event.category,
          action: event.action,
          resourceType: event.resourceType,
          resourceId: event.resourceId,
          userId: event.userId,
          tenantId: event.tenantId,
          sessionId: event.sessionId,
          ipAddress: event.ipAddress,
          userAgent: event.userAgent,
          timestamp: event.timestamp,
          success: event.success,
          details: event.details,
          changes: event.changes,
          metadata: event.metadata,
          severity: event.severity
        }
      })

      this.logger.info('Critical audit event logged immediately', {
        eventType: event.eventType,
        action: event.action,
        userId: event.userId
      })
    } catch (error) {
      this.logger.error('Failed to log critical audit event', error, event)
    }
  }

  /**
   * Start batch processing timer
   */
  private startBatchProcessing(): void {
    this.batchTimer = setInterval(async () => {
      if (this.batchQueue.length > 0) {
        await this.processBatch()
      }
    }, this.batchTimeout)
  }

  /**
   * Stop batch processing
   */
  async stop(): Promise<void> {
    if (this.batchTimer) {
      clearInterval(this.batchTimer)
    }

    // Process remaining events
    while (this.batchQueue.length > 0) {
      await this.processBatch()
    }
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Convert Prisma groupBy result to record
   */
  private groupByToRecord(groupBy: any[], field: string): Record<string, number> {
    const result: Record<string, number> = {}
    groupBy.forEach(item => {
      result[item[field]] = item._count[field]
    })
    return result
  }
}
