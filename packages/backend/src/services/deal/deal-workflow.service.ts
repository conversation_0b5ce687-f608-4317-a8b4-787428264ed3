import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface WorkflowRule {
  id: string
  name: string
  description?: string
  trigger: WorkflowTrigger
  conditions: WorkflowCondition[]
  actions: WorkflowAction[]
  isActive: boolean
}

export interface WorkflowTrigger {
  type: 'stage_change' | 'field_update' | 'time_based' | 'manual'
  config: Record<string, any>
}

export interface WorkflowCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in'
  value: any
}

export interface WorkflowAction {
  type: 'update_field' | 'create_task' | 'send_notification' | 'create_activity' | 'assign_user'
  config: Record<string, any>
}

export class DealWorkflowEngine {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DealWorkflowEngine')
  }

  /**
   * Initialize workflow for a new deal
   */
  async initializeDeal(dealId: string, stageId?: string): Promise<void> {
    try {
      this.logger.info('Initializing deal workflow', { dealId, stageId })

      if (stageId) {
        // Create initial stage history entry
        await this.prisma.dealStageHistory.create({
          data: {
            dealId,
            stageId,
            enteredAt: new Date(),
            changedBy: 'system', // This would be the user ID in real implementation
            reason: 'Deal created'
          }
        })

        // Execute stage entry workflows
        await this.executeStageEntryWorkflows(dealId, stageId)
      }

      this.logger.info('Deal workflow initialized', { dealId })
    } catch (error) {
      this.logger.error('Failed to initialize deal workflow', error, { dealId, stageId })
      throw error
    }
  }

  /**
   * Handle stage change workflow
   */
  async handleStageChange(
    dealId: string, 
    fromStageId: string, 
    toStageId: string, 
    userId: string,
    reason?: string
  ): Promise<void> {
    try {
      this.logger.info('Handling stage change workflow', { 
        dealId, 
        fromStageId, 
        toStageId, 
        userId 
      })

      // Update previous stage history entry
      await this.prisma.dealStageHistory.updateMany({
        where: {
          dealId,
          stageId: fromStageId,
          exitedAt: null
        },
        data: {
          exitedAt: new Date(),
          daysInStage: await this.calculateDaysInStage(dealId, fromStageId)
        }
      })

      // Create new stage history entry
      await this.prisma.dealStageHistory.create({
        data: {
          dealId,
          stageId: toStageId,
          enteredAt: new Date(),
          changedBy: userId,
          reason: reason || 'Stage changed'
        }
      })

      // Execute stage exit workflows
      await this.executeStageExitWorkflows(dealId, fromStageId)

      // Execute stage entry workflows
      await this.executeStageEntryWorkflows(dealId, toStageId)

      // Update deal pipeline metrics
      await this.updatePipelineMetrics(dealId)

      this.logger.info('Stage change workflow completed', { dealId, fromStageId, toStageId })
    } catch (error) {
      this.logger.error('Failed to handle stage change workflow', error, { 
        dealId, 
        fromStageId, 
        toStageId, 
        userId 
      })
      throw error
    }
  }

  /**
   * Execute workflows when entering a stage
   */
  private async executeStageEntryWorkflows(dealId: string, stageId: string): Promise<void> {
    try {
      const stage = await this.prisma.dealStage.findUnique({
        where: { id: stageId }
      })

      if (!stage) return

      // Get workflow rules for stage entry
      const rules = await this.getWorkflowRules('stage_entry', stage.tenantId)

      for (const rule of rules) {
        if (await this.evaluateConditions(rule.conditions, dealId)) {
          await this.executeActions(rule.actions, dealId, stageId)
        }
      }

      // Auto-create tasks based on stage requirements
      await this.createStageRequiredTasks(dealId, stageId)

      // Check for auto-advance conditions
      if (stage.autoAdvance) {
        await this.checkAutoAdvanceConditions(dealId, stageId)
      }
    } catch (error) {
      this.logger.error('Failed to execute stage entry workflows', error, { dealId, stageId })
    }
  }

  /**
   * Execute workflows when exiting a stage
   */
  private async executeStageExitWorkflows(dealId: string, stageId: string): Promise<void> {
    try {
      const stage = await this.prisma.dealStage.findUnique({
        where: { id: stageId }
      })

      if (!stage) return

      // Get workflow rules for stage exit
      const rules = await this.getWorkflowRules('stage_exit', stage.tenantId)

      for (const rule of rules) {
        if (await this.evaluateConditions(rule.conditions, dealId)) {
          await this.executeActions(rule.actions, dealId, stageId)
        }
      }

      // Mark stage-specific tasks as completed
      await this.completeStageSpecificTasks(dealId, stageId)
    } catch (error) {
      this.logger.error('Failed to execute stage exit workflows', error, { dealId, stageId })
    }
  }

  /**
   * Create required tasks for a stage
   */
  private async createStageRequiredTasks(dealId: string, stageId: string): Promise<void> {
    try {
      const stage = await this.prisma.dealStage.findUnique({
        where: { id: stageId }
      })

      if (!stage || !stage.requiredFields.length) return

      // Create tasks for required fields that are not yet completed
      const deal = await this.prisma.deal.findUnique({
        where: { id: dealId },
        include: { tasks: true }
      })

      if (!deal) return

      const existingTaskTitles = deal.tasks.map(task => task.title)

      for (const field of stage.requiredFields) {
        const taskTitle = `Complete ${field} for ${stage.name} stage`
        
        if (!existingTaskTitles.includes(taskTitle)) {
          await this.prisma.dealTask.create({
            data: {
              dealId,
              title: taskTitle,
              description: `This task is required to advance from the ${stage.name} stage`,
              status: 'PENDING',
              priority: 'HIGH',
              createdBy: deal.createdBy,
              dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
            }
          })
        }
      }
    } catch (error) {
      this.logger.error('Failed to create stage required tasks', error, { dealId, stageId })
    }
  }

  /**
   * Check auto-advance conditions
   */
  private async checkAutoAdvanceConditions(dealId: string, stageId: string): Promise<void> {
    try {
      const stage = await this.prisma.dealStage.findUnique({
        where: { id: stageId }
      })

      if (!stage || !stage.autoAdvance) return

      // Check if all required fields are completed
      const deal = await this.prisma.deal.findUnique({
        where: { id: dealId },
        include: { tasks: true }
      })

      if (!deal) return

      // Check if all required tasks are completed
      const requiredTasks = deal.tasks.filter(task => 
        task.title.includes(`for ${stage.name} stage`)
      )

      const allTasksCompleted = requiredTasks.every(task => task.status === 'COMPLETED')

      if (allTasksCompleted && stage.requiredFields.length > 0) {
        // Auto-advance to next stage
        const nextStage = await this.prisma.dealStage.findFirst({
          where: {
            tenantId: stage.tenantId,
            order: { gt: stage.order },
            isActive: true
          },
          orderBy: { order: 'asc' }
        })

        if (nextStage) {
          await this.prisma.deal.update({
            where: { id: dealId },
            data: {
              currentStageId: nextStage.id,
              stageEnteredAt: new Date(),
              daysInCurrentStage: 0
            }
          })

          // Handle the stage change workflow
          await this.handleStageChange(dealId, stageId, nextStage.id, 'system', 'Auto-advanced')
        }
      }
    } catch (error) {
      this.logger.error('Failed to check auto-advance conditions', error, { dealId, stageId })
    }
  }

  /**
   * Complete stage-specific tasks
   */
  private async completeStageSpecificTasks(dealId: string, stageId: string): Promise<void> {
    try {
      const stage = await this.prisma.dealStage.findUnique({
        where: { id: stageId }
      })

      if (!stage) return

      // Mark stage-specific tasks as completed
      await this.prisma.dealTask.updateMany({
        where: {
          dealId,
          title: { contains: `for ${stage.name} stage` },
          status: { not: 'COMPLETED' }
        },
        data: {
          status: 'COMPLETED',
          completedAt: new Date()
        }
      })
    } catch (error) {
      this.logger.error('Failed to complete stage-specific tasks', error, { dealId, stageId })
    }
  }

  /**
   * Calculate days in stage
   */
  private async calculateDaysInStage(dealId: string, stageId: string): Promise<number> {
    try {
      const stageHistory = await this.prisma.dealStageHistory.findFirst({
        where: {
          dealId,
          stageId,
          exitedAt: null
        },
        orderBy: { enteredAt: 'desc' }
      })

      if (!stageHistory) return 0

      const now = new Date()
      const enteredAt = new Date(stageHistory.enteredAt)
      const diffTime = Math.abs(now.getTime() - enteredAt.getTime())
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      return diffDays
    } catch (error) {
      this.logger.error('Failed to calculate days in stage', error, { dealId, stageId })
      return 0
    }
  }

  /**
   * Update pipeline metrics
   */
  private async updatePipelineMetrics(dealId: string): Promise<void> {
    try {
      const deal = await this.prisma.deal.findUnique({
        where: { id: dealId },
        include: { stageHistory: true }
      })

      if (!deal) return

      // Calculate total days in pipeline
      const firstEntry = deal.stageHistory.sort((a, b) => 
        new Date(a.enteredAt).getTime() - new Date(b.enteredAt).getTime()
      )[0]

      if (firstEntry) {
        const now = new Date()
        const startDate = new Date(firstEntry.enteredAt)
        const totalDays = Math.ceil((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))

        await this.prisma.deal.update({
          where: { id: dealId },
          data: { totalDaysInPipeline: totalDays }
        })
      }
    } catch (error) {
      this.logger.error('Failed to update pipeline metrics', error, { dealId })
    }
  }

  /**
   * Get workflow rules (placeholder - would be implemented with actual rule storage)
   */
  private async getWorkflowRules(trigger: string, tenantId: string): Promise<WorkflowRule[]> {
    // This would fetch actual workflow rules from database
    // For now, return empty array
    return []
  }

  /**
   * Evaluate workflow conditions
   */
  private async evaluateConditions(conditions: WorkflowCondition[], dealId: string): Promise<boolean> {
    // This would evaluate actual conditions against deal data
    // For now, return true
    return true
  }

  /**
   * Execute workflow actions
   */
  private async executeActions(actions: WorkflowAction[], dealId: string, stageId: string): Promise<void> {
    // This would execute actual workflow actions
    // For now, just log
    this.logger.info('Executing workflow actions', { dealId, stageId, actionCount: actions.length })
  }

  /**
   * Process time-based workflows (to be called by a scheduler)
   */
  async processTimeBasedWorkflows(tenantId: string): Promise<void> {
    try {
      this.logger.info('Processing time-based workflows', { tenantId })

      // Get deals that might need time-based workflow processing
      const deals = await this.prisma.deal.findMany({
        where: {
          tenantId,
          status: { not: 'CLOSED' }
        },
        include: {
          currentStage: true,
          stageHistory: {
            where: { exitedAt: null },
            orderBy: { enteredAt: 'desc' },
            take: 1
          }
        }
      })

      for (const deal of deals) {
        // Update days in current stage
        if (deal.stageHistory.length > 0) {
          const daysInStage = await this.calculateDaysInStage(deal.id, deal.currentStageId!)
          
          await this.prisma.deal.update({
            where: { id: deal.id },
            data: { daysInCurrentStage: daysInStage }
          })
        }

        // Check for overdue deals
        if (deal.expectedCloseDate && new Date() > deal.expectedCloseDate) {
          // Handle overdue deal workflow
          await this.handleOverdueDeal(deal.id)
        }
      }

      this.logger.info('Time-based workflows processed', { tenantId, dealCount: deals.length })
    } catch (error) {
      this.logger.error('Failed to process time-based workflows', error, { tenantId })
    }
  }

  /**
   * Handle overdue deal workflow
   */
  private async handleOverdueDeal(dealId: string): Promise<void> {
    try {
      // Update deal risk level
      await this.prisma.deal.update({
        where: { id: dealId },
        data: { riskLevel: 'HIGH' }
      })

      // Create overdue task if not exists
      const existingTask = await this.prisma.dealTask.findFirst({
        where: {
          dealId,
          title: 'Review overdue deal'
        }
      })

      if (!existingTask) {
        const deal = await this.prisma.deal.findUnique({
          where: { id: dealId }
        })

        if (deal) {
          await this.prisma.dealTask.create({
            data: {
              dealId,
              title: 'Review overdue deal',
              description: 'This deal has passed its expected close date and needs review',
              status: 'PENDING',
              priority: 'URGENT',
              createdBy: deal.createdBy,
              assignedTo: deal.assignedTo,
              dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000) // 1 day from now
            }
          })
        }
      }
    } catch (error) {
      this.logger.error('Failed to handle overdue deal', error, { dealId })
    }
  }
}
