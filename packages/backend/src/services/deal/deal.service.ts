import { PrismaClient, DealStatus, DealPriority, DealType, DealSource, RiskLevel, ForecastCategory } from '@prisma/client'
import { 
  Deal, 
  CreateDealRequest, 
  UpdateDealRequest, 
  DealFilters, 
  DealSortOptions,
  DealAnalytics,
  DealWithRelations
} from '@/shared/types/deal'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { DealWorkflowEngine } from './deal-workflow.service'
import { DealAnalyticsService } from './deal-analytics.service'

export class DealService {
  private prisma: PrismaClient
  private cache: CacheService
  private workflowEngine: DealWorkflowEngine
  private analyticsService: DealAnalyticsService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.workflowEngine = new DealWorkflowEngine(prisma, cache)
    this.analyticsService = new DealAnalyticsService(prisma, cache)
    this.logger = new Logger('DealService')
  }

  /**
   * Create a new deal
   */
  async createDeal(data: CreateDealRequest, userId: string, tenantId: string): Promise<Deal> {
    try {
      this.logger.info('Creating new deal', { title: data.title, userId, tenantId })

      // Get default stage for new deals
      const defaultStage = await this.getDefaultStage(tenantId)
      
      const deal = await this.prisma.deal.create({
        data: {
          title: data.title,
          description: data.description,
          dealType: data.dealType,
          dealSource: data.dealSource,
          targetCompany: data.targetCompany,
          targetIndustry: data.targetIndustry,
          dealValue: data.dealValue,
          currency: data.currency || 'USD',
          priority: data.priority || DealPriority.MEDIUM,
          expectedCloseDate: data.expectedCloseDate,
          assignedTo: data.assignedTo,
          tags: data.tags || [],
          confidentiality: data.confidentiality,
          tenantId,
          createdBy: userId,
          currentStageId: defaultStage?.id,
          stageEnteredAt: new Date(),
          healthScore: 50, // Default health score
          riskLevel: RiskLevel.MEDIUM,
          forecastCategory: ForecastCategory.PIPELINE
        },
        include: this.getIncludeOptions()
      })

      // Initialize workflow for the new deal
      await this.workflowEngine.initializeDeal(deal.id, defaultStage?.id)

      // Clear cache
      await this.clearDealCache(tenantId)

      this.logger.info('Deal created successfully', { dealId: deal.id, title: deal.title })
      
      return deal as Deal
    } catch (error) {
      this.logger.error('Failed to create deal', error, { data, userId, tenantId })
      throw error
    }
  }

  /**
   * Get deal by ID
   */
  async getDealById(dealId: string, tenantId: string): Promise<DealWithRelations | null> {
    try {
      const cacheKey = `deal:${dealId}:${tenantId}`
      
      // Try cache first
      const cached = await this.cache.get<DealWithRelations>(cacheKey)
      if (cached) {
        return cached
      }

      const deal = await this.prisma.deal.findFirst({
        where: { id: dealId, tenantId },
        include: this.getIncludeOptions()
      })

      if (deal) {
        // Cache for 5 minutes
        await this.cache.set(cacheKey, deal, 300)
      }

      return deal as DealWithRelations | null
    } catch (error) {
      this.logger.error('Failed to get deal by ID', error, { dealId, tenantId })
      throw error
    }
  }

  /**
   * Update deal
   */
  async updateDeal(dealId: string, data: UpdateDealRequest, userId: string, tenantId: string): Promise<Deal> {
    try {
      this.logger.info('Updating deal', { dealId, userId, tenantId })

      // Get current deal
      const currentDeal = await this.getDealById(dealId, tenantId)
      if (!currentDeal) {
        throw new Error('Deal not found')
      }

      // Check if stage is changing
      const isStageChange = data.stage && data.stage !== currentDeal.stage
      
      const updateData: any = {
        ...data,
        updatedAt: new Date()
      }

      // Handle stage change
      if (isStageChange) {
        const newStage = await this.getStageByName(data.stage!, tenantId)
        if (newStage) {
          updateData.currentStageId = newStage.id
          updateData.stageEnteredAt = new Date()
          updateData.daysInCurrentStage = 0
        }
      }

      // Update health score if relevant fields changed
      if (this.shouldRecalculateHealthScore(data)) {
        updateData.healthScore = await this.calculateHealthScore(dealId, data)
      }

      // Update weighted value if deal value or probability changed
      if (data.dealValue !== undefined || data.probability !== undefined) {
        const dealValue = data.dealValue ?? currentDeal.dealValue ?? 0
        const probability = data.probability ?? currentDeal.probability ?? 0
        updateData.weightedValue = (dealValue * probability) / 100
      }

      const updatedDeal = await this.prisma.deal.update({
        where: { id: dealId },
        data: updateData,
        include: this.getIncludeOptions()
      })

      // Handle workflow if stage changed
      if (isStageChange) {
        await this.workflowEngine.handleStageChange(
          dealId, 
          currentDeal.currentStageId!, 
          updateData.currentStageId,
          userId,
          data.stage
        )
      }

      // Clear cache
      await this.clearDealCache(tenantId, dealId)

      this.logger.info('Deal updated successfully', { dealId, changes: Object.keys(data) })
      
      return updatedDeal as Deal
    } catch (error) {
      this.logger.error('Failed to update deal', error, { dealId, data, userId, tenantId })
      throw error
    }
  }

  /**
   * Delete deal
   */
  async deleteDeal(dealId: string, userId: string, tenantId: string): Promise<void> {
    try {
      this.logger.info('Deleting deal', { dealId, userId, tenantId })

      await this.prisma.deal.delete({
        where: { id: dealId, tenantId }
      })

      // Clear cache
      await this.clearDealCache(tenantId, dealId)

      this.logger.info('Deal deleted successfully', { dealId })
    } catch (error) {
      this.logger.error('Failed to delete deal', error, { dealId, userId, tenantId })
      throw error
    }
  }

  /**
   * Get deals with filtering and pagination
   */
  async getDeals(
    tenantId: string,
    filters: DealFilters = {},
    sort: DealSortOptions = { field: 'createdAt', direction: 'desc' },
    page: number = 1,
    limit: number = 20
  ): Promise<{ deals: Deal[]; total: number; hasMore: boolean }> {
    try {
      const where = this.buildWhereClause(filters, tenantId)
      const orderBy = this.buildOrderByClause(sort)

      const [deals, total] = await Promise.all([
        this.prisma.deal.findMany({
          where,
          orderBy,
          skip: (page - 1) * limit,
          take: limit,
          include: this.getIncludeOptions()
        }),
        this.prisma.deal.count({ where })
      ])

      const hasMore = page * limit < total

      return {
        deals: deals as Deal[],
        total,
        hasMore
      }
    } catch (error) {
      this.logger.error('Failed to get deals', error, { tenantId, filters, sort, page, limit })
      throw error
    }
  }

  /**
   * Get deal analytics
   */
  async getDealAnalytics(tenantId: string, dateFrom?: Date, dateTo?: Date): Promise<DealAnalytics> {
    return this.analyticsService.getDealAnalytics(tenantId, dateFrom, dateTo)
  }

  /**
   * Move deal to next stage
   */
  async moveToNextStage(dealId: string, userId: string, tenantId: string, reason?: string): Promise<Deal> {
    try {
      const deal = await this.getDealById(dealId, tenantId)
      if (!deal) {
        throw new Error('Deal not found')
      }

      const nextStage = await this.getNextStage(deal.currentStageId!, tenantId)
      if (!nextStage) {
        throw new Error('No next stage available')
      }

      return this.updateDeal(dealId, { stage: nextStage.name }, userId, tenantId)
    } catch (error) {
      this.logger.error('Failed to move deal to next stage', error, { dealId, userId, tenantId })
      throw error
    }
  }

  /**
   * Move deal to previous stage
   */
  async moveToPreviousStage(dealId: string, userId: string, tenantId: string, reason?: string): Promise<Deal> {
    try {
      const deal = await this.getDealById(dealId, tenantId)
      if (!deal) {
        throw new Error('Deal not found')
      }

      const previousStage = await this.getPreviousStage(deal.currentStageId!, tenantId)
      if (!previousStage) {
        throw new Error('No previous stage available')
      }

      return this.updateDeal(dealId, { stage: previousStage.name }, userId, tenantId)
    } catch (error) {
      this.logger.error('Failed to move deal to previous stage', error, { dealId, userId, tenantId })
      throw error
    }
  }

  /**
   * Get default stage for new deals
   */
  private async getDefaultStage(tenantId: string) {
    return this.prisma.dealStage.findFirst({
      where: { tenantId, isDefault: true, isActive: true },
      orderBy: { order: 'asc' }
    })
  }

  /**
   * Get stage by name
   */
  private async getStageByName(stageName: string, tenantId: string) {
    return this.prisma.dealStage.findFirst({
      where: { tenantId, name: stageName, isActive: true }
    })
  }

  /**
   * Get next stage in pipeline
   */
  private async getNextStage(currentStageId: string, tenantId: string) {
    const currentStage = await this.prisma.dealStage.findUnique({
      where: { id: currentStageId }
    })

    if (!currentStage) return null

    return this.prisma.dealStage.findFirst({
      where: { 
        tenantId, 
        order: { gt: currentStage.order },
        isActive: true
      },
      orderBy: { order: 'asc' }
    })
  }

  /**
   * Get previous stage in pipeline
   */
  private async getPreviousStage(currentStageId: string, tenantId: string) {
    const currentStage = await this.prisma.dealStage.findUnique({
      where: { id: currentStageId }
    })

    if (!currentStage) return null

    return this.prisma.dealStage.findFirst({
      where: { 
        tenantId, 
        order: { lt: currentStage.order },
        isActive: true
      },
      orderBy: { order: 'desc' }
    })
  }

  /**
   * Build where clause for filtering
   */
  private buildWhereClause(filters: DealFilters, tenantId: string) {
    const where: any = { tenantId }

    if (filters.status?.length) {
      where.status = { in: filters.status }
    }

    if (filters.stage?.length) {
      where.stage = { in: filters.stage }
    }

    if (filters.assignedTo?.length) {
      where.assignedTo = { in: filters.assignedTo }
    }

    if (filters.priority?.length) {
      where.priority = { in: filters.priority }
    }

    if (filters.dealType?.length) {
      where.dealType = { in: filters.dealType }
    }

    if (filters.dealSource?.length) {
      where.dealSource = { in: filters.dealSource }
    }

    if (filters.riskLevel?.length) {
      where.riskLevel = { in: filters.riskLevel }
    }

    if (filters.minValue !== undefined || filters.maxValue !== undefined) {
      where.dealValue = {}
      if (filters.minValue !== undefined) where.dealValue.gte = filters.minValue
      if (filters.maxValue !== undefined) where.dealValue.lte = filters.maxValue
    }

    if (filters.expectedCloseDateFrom || filters.expectedCloseDateTo) {
      where.expectedCloseDate = {}
      if (filters.expectedCloseDateFrom) where.expectedCloseDate.gte = filters.expectedCloseDateFrom
      if (filters.expectedCloseDateTo) where.expectedCloseDate.lte = filters.expectedCloseDateTo
    }

    if (filters.tags?.length) {
      where.tags = { hasSome: filters.tags }
    }

    if (filters.search) {
      where.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
        { targetCompany: { contains: filters.search, mode: 'insensitive' } }
      ]
    }

    return where
  }

  /**
   * Build order by clause for sorting
   */
  private buildOrderByClause(sort: DealSortOptions) {
    return { [sort.field]: sort.direction }
  }

  /**
   * Get include options for deal queries
   */
  private getIncludeOptions() {
    return {
      currentStage: true,
      stageHistory: {
        include: { stage: true },
        orderBy: { enteredAt: 'desc' as const }
      },
      activities: {
        orderBy: { startTime: 'desc' as const },
        take: 10
      },
      contacts: true,
      team: { include: { user: true } },
      tasks: {
        where: { status: { not: 'COMPLETED' } },
        orderBy: { dueDate: 'asc' as const }
      },
      notes: {
        orderBy: { createdAt: 'desc' as const },
        take: 5
      },
      milestones: {
        orderBy: { targetDate: 'asc' as const }
      }
    }
  }

  /**
   * Check if health score should be recalculated
   */
  private shouldRecalculateHealthScore(data: UpdateDealRequest): boolean {
    const healthRelevantFields = ['status', 'stage', 'probability', 'riskLevel', 'lastActivityDate']
    return healthRelevantFields.some(field => data[field as keyof UpdateDealRequest] !== undefined)
  }

  /**
   * Calculate deal health score
   */
  private async calculateHealthScore(dealId: string, data: UpdateDealRequest): Promise<number> {
    // Simplified health score calculation
    // In a real implementation, this would be more sophisticated
    let score = 50 // Base score

    // Adjust based on probability
    if (data.probability !== undefined) {
      score += (data.probability - 50) * 0.5
    }

    // Adjust based on risk level
    if (data.riskLevel) {
      switch (data.riskLevel) {
        case RiskLevel.LOW: score += 20; break
        case RiskLevel.MEDIUM: score += 0; break
        case RiskLevel.HIGH: score -= 20; break
        case RiskLevel.CRITICAL: score -= 40; break
      }
    }

    // Ensure score is between 0 and 100
    return Math.max(0, Math.min(100, Math.round(score)))
  }

  /**
   * Clear deal cache
   */
  private async clearDealCache(tenantId: string, dealId?: string): Promise<void> {
    try {
      if (dealId) {
        await this.cache.delete(`deal:${dealId}:${tenantId}`)
      }
      
      // Clear list caches
      await this.cache.deletePattern(`deals:${tenantId}:*`)
      await this.cache.deletePattern(`analytics:${tenantId}:*`)
    } catch (error) {
      this.logger.error('Failed to clear deal cache', error, { tenantId, dealId })
    }
  }
}
