import { PrismaClient, DealStatus } from '@prisma/client'
import { DealAnalytics, PipelineMetrics, DealForecast } from '@/shared/types/deal'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export class DealAnalyticsService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DealAnalyticsService')
  }

  /**
   * Get comprehensive deal analytics
   */
  async getDealAnalytics(tenantId: string, dateFrom?: Date, dateTo?: Date): Promise<DealAnalytics> {
    try {
      const cacheKey = `analytics:${tenantId}:${dateFrom?.toISOString()}:${dateTo?.toISOString()}`
      
      // Try cache first
      const cached = await this.cache.get<DealAnalytics>(cacheKey)
      if (cached) {
        return cached
      }

      const whereClause = this.buildDateWhereClause(tenantId, dateFrom, dateTo)

      // Get basic metrics
      const [
        totalDeals,
        totalValue,
        dealsByStatus,
        valueByStatus,
        dealsByStage,
        valueByStage,
        monthlyMetrics,
        healthMetrics
      ] = await Promise.all([
        this.getTotalDeals(whereClause),
        this.getTotalValue(whereClause),
        this.getDealsByStatus(whereClause),
        this.getValueByStatus(whereClause),
        this.getDealsByStage(whereClause),
        this.getValueByStage(whereClause),
        this.getMonthlyMetrics(tenantId),
        this.getHealthMetrics(whereClause)
      ])

      // Calculate derived metrics
      const averageDealSize = totalDeals > 0 ? totalValue / totalDeals : 0
      const averageDaysInPipeline = await this.getAverageDaysInPipeline(whereClause)
      const conversionRate = await this.getConversionRate(whereClause)
      const forecastedValue = await this.getForecastedValue(tenantId)
      const weightedPipelineValue = await this.getWeightedPipelineValue(tenantId)

      const analytics: DealAnalytics = {
        totalDeals,
        totalValue,
        averageDealSize,
        averageDaysInPipeline,
        conversionRate,
        dealsByStatus,
        valueByStatus,
        dealsByStage,
        valueByStage,
        dealsCreatedThisMonth: monthlyMetrics.created,
        dealsClosedThisMonth: monthlyMetrics.closed,
        valueClosedThisMonth: monthlyMetrics.valueClosedThisMonth,
        forecastedValue,
        weightedPipelineValue,
        averageHealthScore: healthMetrics.averageHealthScore,
        dealsAtRisk: healthMetrics.dealsAtRisk,
        overdueDeals: healthMetrics.overdueDeals
      }

      // Cache for 10 minutes
      await this.cache.set(cacheKey, analytics, 600)

      return analytics
    } catch (error) {
      this.logger.error('Failed to get deal analytics', error, { tenantId, dateFrom, dateTo })
      throw error
    }
  }

  /**
   * Get pipeline metrics by stage
   */
  async getPipelineMetrics(tenantId: string): Promise<PipelineMetrics[]> {
    try {
      const cacheKey = `pipeline-metrics:${tenantId}`
      
      const cached = await this.cache.get<PipelineMetrics[]>(cacheKey)
      if (cached) {
        return cached
      }

      const stages = await this.prisma.dealStage.findMany({
        where: { tenantId, isActive: true },
        orderBy: { order: 'asc' }
      })

      const metrics: PipelineMetrics[] = []

      for (const stage of stages) {
        const [dealCount, totalValue, avgDaysInStage, conversionData] = await Promise.all([
          this.prisma.deal.count({
            where: { tenantId, currentStageId: stage.id }
          }),
          this.prisma.deal.aggregate({
            where: { tenantId, currentStageId: stage.id },
            _sum: { dealValue: true }
          }),
          this.getAverageDaysInStage(stage.id),
          this.getStageConversionData(stage.id, tenantId)
        ])

        metrics.push({
          stageId: stage.id,
          stageName: stage.name,
          dealCount,
          totalValue: totalValue._sum.dealValue?.toNumber() || 0,
          averageDaysInStage: avgDaysInStage,
          conversionRate: conversionData.conversionRate,
          dropOffRate: conversionData.dropOffRate
        })
      }

      // Cache for 15 minutes
      await this.cache.set(cacheKey, metrics, 900)

      return metrics
    } catch (error) {
      this.logger.error('Failed to get pipeline metrics', error, { tenantId })
      throw error
    }
  }

  /**
   * Get deal forecast
   */
  async getDealForecast(tenantId: string, quarters: number = 4): Promise<DealForecast[]> {
    try {
      const forecasts: DealForecast[] = []
      const currentDate = new Date()

      for (let i = 0; i < quarters; i++) {
        const quarterStart = new Date(currentDate.getFullYear(), Math.floor(currentDate.getMonth() / 3) * 3 + i * 3, 1)
        const quarterEnd = new Date(quarterStart.getFullYear(), quarterStart.getMonth() + 3, 0)
        
        const period = `${quarterStart.getFullYear()}-Q${Math.floor(quarterStart.getMonth() / 3) + 1}`

        const [bestCase, commit, pipeline, closed] = await Promise.all([
          this.getForecastValue(tenantId, 'BEST_CASE', quarterStart, quarterEnd),
          this.getForecastValue(tenantId, 'COMMIT', quarterStart, quarterEnd),
          this.getForecastValue(tenantId, 'PIPELINE', quarterStart, quarterEnd),
          this.getForecastValue(tenantId, 'CLOSED', quarterStart, quarterEnd)
        ])

        // Calculate confidence based on historical data
        const confidence = await this.calculateForecastConfidence(tenantId, quarterStart, quarterEnd)

        forecasts.push({
          period,
          bestCase,
          commit,
          pipeline,
          closed,
          confidence
        })
      }

      return forecasts
    } catch (error) {
      this.logger.error('Failed to get deal forecast', error, { tenantId, quarters })
      throw error
    }
  }

  /**
   * Get total deals count
   */
  private async getTotalDeals(whereClause: any): Promise<number> {
    return this.prisma.deal.count({ where: whereClause })
  }

  /**
   * Get total deal value
   */
  private async getTotalValue(whereClause: any): Promise<number> {
    const result = await this.prisma.deal.aggregate({
      where: whereClause,
      _sum: { dealValue: true }
    })
    return result._sum.dealValue?.toNumber() || 0
  }

  /**
   * Get deals grouped by status
   */
  private async getDealsByStatus(whereClause: any): Promise<Record<DealStatus, number>> {
    const results = await this.prisma.deal.groupBy({
      by: ['status'],
      where: whereClause,
      _count: { status: true }
    })

    const dealsByStatus: Record<DealStatus, number> = {
      PIPELINE: 0,
      DUE_DILIGENCE: 0,
      NEGOTIATION: 0,
      CLOSING: 0,
      CLOSED: 0,
      CANCELLED: 0
    }

    results.forEach(result => {
      dealsByStatus[result.status] = result._count.status
    })

    return dealsByStatus
  }

  /**
   * Get deal value grouped by status
   */
  private async getValueByStatus(whereClause: any): Promise<Record<DealStatus, number>> {
    const results = await this.prisma.deal.groupBy({
      by: ['status'],
      where: whereClause,
      _sum: { dealValue: true }
    })

    const valueByStatus: Record<DealStatus, number> = {
      PIPELINE: 0,
      DUE_DILIGENCE: 0,
      NEGOTIATION: 0,
      CLOSING: 0,
      CLOSED: 0,
      CANCELLED: 0
    }

    results.forEach(result => {
      valueByStatus[result.status] = result._sum.dealValue?.toNumber() || 0
    })

    return valueByStatus
  }

  /**
   * Get deals grouped by stage
   */
  private async getDealsByStage(whereClause: any): Promise<Record<string, number>> {
    const results = await this.prisma.deal.groupBy({
      by: ['stage'],
      where: whereClause,
      _count: { stage: true }
    })

    const dealsByStage: Record<string, number> = {}
    results.forEach(result => {
      dealsByStage[result.stage] = result._count.stage
    })

    return dealsByStage
  }

  /**
   * Get deal value grouped by stage
   */
  private async getValueByStage(whereClause: any): Promise<Record<string, number>> {
    const results = await this.prisma.deal.groupBy({
      by: ['stage'],
      where: whereClause,
      _sum: { dealValue: true }
    })

    const valueByStage: Record<string, number> = {}
    results.forEach(result => {
      valueByStage[result.stage] = result._sum.dealValue?.toNumber() || 0
    })

    return valueByStage
  }

  /**
   * Get monthly metrics
   */
  private async getMonthlyMetrics(tenantId: string) {
    const currentMonth = new Date()
    currentMonth.setDate(1)
    currentMonth.setHours(0, 0, 0, 0)

    const nextMonth = new Date(currentMonth)
    nextMonth.setMonth(nextMonth.getMonth() + 1)

    const [created, closed, valueClosedThisMonth] = await Promise.all([
      this.prisma.deal.count({
        where: {
          tenantId,
          createdAt: { gte: currentMonth, lt: nextMonth }
        }
      }),
      this.prisma.deal.count({
        where: {
          tenantId,
          status: 'CLOSED',
          actualCloseDate: { gte: currentMonth, lt: nextMonth }
        }
      }),
      this.prisma.deal.aggregate({
        where: {
          tenantId,
          status: 'CLOSED',
          actualCloseDate: { gte: currentMonth, lt: nextMonth }
        },
        _sum: { dealValue: true }
      })
    ])

    return {
      created,
      closed,
      valueClosedThisMonth: valueClosedThisMonth._sum.dealValue?.toNumber() || 0
    }
  }

  /**
   * Get health metrics
   */
  private async getHealthMetrics(whereClause: any) {
    const [avgHealthScore, dealsAtRisk, overdueDeals] = await Promise.all([
      this.prisma.deal.aggregate({
        where: whereClause,
        _avg: { healthScore: true }
      }),
      this.prisma.deal.count({
        where: { ...whereClause, riskLevel: { in: ['HIGH', 'CRITICAL'] } }
      }),
      this.prisma.deal.count({
        where: { 
          ...whereClause, 
          expectedCloseDate: { lt: new Date() },
          status: { not: 'CLOSED' }
        }
      })
    ])

    return {
      averageHealthScore: avgHealthScore._avg.healthScore || 0,
      dealsAtRisk,
      overdueDeals
    }
  }

  /**
   * Get average days in pipeline
   */
  private async getAverageDaysInPipeline(whereClause: any): Promise<number> {
    const result = await this.prisma.deal.aggregate({
      where: whereClause,
      _avg: { totalDaysInPipeline: true }
    })
    return result._avg.totalDaysInPipeline || 0
  }

  /**
   * Get conversion rate
   */
  private async getConversionRate(whereClause: any): Promise<number> {
    const [totalDeals, closedDeals] = await Promise.all([
      this.prisma.deal.count({ where: whereClause }),
      this.prisma.deal.count({ 
        where: { ...whereClause, status: 'CLOSED' }
      })
    ])

    return totalDeals > 0 ? (closedDeals / totalDeals) * 100 : 0
  }

  /**
   * Get forecasted value
   */
  private async getForecastedValue(tenantId: string): Promise<number> {
    const result = await this.prisma.deal.aggregate({
      where: {
        tenantId,
        status: { in: ['PIPELINE', 'DUE_DILIGENCE', 'NEGOTIATION', 'CLOSING'] }
      },
      _sum: { weightedValue: true }
    })
    return result._sum.weightedValue?.toNumber() || 0
  }

  /**
   * Get weighted pipeline value
   */
  private async getWeightedPipelineValue(tenantId: string): Promise<number> {
    const result = await this.prisma.deal.aggregate({
      where: {
        tenantId,
        status: { not: 'CLOSED' }
      },
      _sum: { weightedValue: true }
    })
    return result._sum.weightedValue?.toNumber() || 0
  }

  /**
   * Get average days in specific stage
   */
  private async getAverageDaysInStage(stageId: string): Promise<number> {
    const result = await this.prisma.dealStageHistory.aggregate({
      where: { stageId, exitedAt: { not: null } },
      _avg: { daysInStage: true }
    })
    return result._avg.daysInStage || 0
  }

  /**
   * Get stage conversion data
   */
  private async getStageConversionData(stageId: string, tenantId: string) {
    // This would calculate conversion and drop-off rates for the stage
    // Simplified implementation
    return {
      conversionRate: 75, // Placeholder
      dropOffRate: 25     // Placeholder
    }
  }

  /**
   * Get forecast value for specific category and period
   */
  private async getForecastValue(
    tenantId: string, 
    category: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<number> {
    const result = await this.prisma.deal.aggregate({
      where: {
        tenantId,
        forecastCategory: category as any,
        expectedCloseDate: { gte: startDate, lte: endDate }
      },
      _sum: { dealValue: true }
    })
    return result._sum.dealValue?.toNumber() || 0
  }

  /**
   * Calculate forecast confidence
   */
  private async calculateForecastConfidence(
    tenantId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<number> {
    // This would calculate confidence based on historical accuracy
    // Simplified implementation
    return 85 // Placeholder percentage
  }

  /**
   * Build date-based where clause
   */
  private buildDateWhereClause(tenantId: string, dateFrom?: Date, dateTo?: Date) {
    const where: any = { tenantId }

    if (dateFrom || dateTo) {
      where.createdAt = {}
      if (dateFrom) where.createdAt.gte = dateFrom
      if (dateTo) where.createdAt.lte = dateTo
    }

    return where
  }
}
