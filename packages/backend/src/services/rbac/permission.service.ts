import { PrismaClient } from '@prisma/client'
import { 
  Permission, 
  PermissionCheck, 
  AccessContext, 
  PermissionResult, 
  ParsedPermission,
  PERMISSION_SEPARATOR,
  WILDCARD,
  PermissionCondition
} from '@/shared/types/rbac'
import { CacheService } from '@/services/cache.service'
import { Logger } from '@/shared/utils/logger'

export class PermissionService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('PermissionService')
  }

  /**
   * Check if a user has permission to perform an action on a resource
   */
  async checkPermission(
    context: AccessContext,
    check: PermissionCheck
  ): Promise<PermissionResult> {
    try {
      // Create cache key for permission check
      const cacheKey = this.createPermissionCacheKey(context, check)
      
      // Try to get from cache first
      const cached = await this.cache.get<PermissionResult>(cacheKey)
      if (cached) {
        this.logger.debug('Permission check cache hit', { cacheKey })
        return cached
      }

      // Perform permission evaluation
      const result = await this.evaluatePermission(context, check)

      // Cache the result
      await this.cache.set(cacheKey, result, 300) // 5 minutes

      this.logger.debug('Permission check completed', {
        userId: context.userId,
        tenantId: context.tenantId,
        resource: check.resource,
        action: check.action,
        granted: result.granted
      })

      return result
    } catch (error) {
      this.logger.error('Permission check failed', error, {
        userId: context.userId,
        tenantId: context.tenantId,
        check
      })

      return {
        granted: false,
        reason: 'Permission check failed due to system error'
      }
    }
  }

  /**
   * Evaluate permission based on user roles and context
   */
  private async evaluatePermission(
    context: AccessContext,
    check: PermissionCheck
  ): Promise<PermissionResult> {
    // Get all permissions for the user's roles
    const userPermissions = await this.getUserPermissions(context)

    // Check if user has the required permission
    const permissionString = this.buildPermissionString(check)
    
    for (const permission of userPermissions) {
      const match = this.matchPermission(permission, permissionString)
      if (match.matches) {
        // Check additional conditions if any
        const conditionResult = await this.evaluateConditions(
          context,
          check,
          match.conditions
        )

        if (conditionResult.granted) {
          return {
            granted: true,
            reason: `Permission granted via role permission: ${permission}`,
            conditions: match.conditions
          }
        }
      }
    }

    return {
      granted: false,
      reason: `No matching permission found for ${permissionString}`
    }
  }

  /**
   * Get all permissions for a user based on their roles
   */
  private async getUserPermissions(context: AccessContext): Promise<string[]> {
    const cacheKey = `user_permissions:${context.userId}:${context.tenantId}`
    
    // Try cache first
    const cached = await this.cache.get<string[]>(cacheKey)
    if (cached) {
      return cached
    }

    // Get user roles with permissions
    const userRoles = await this.prisma.userRole.findMany({
      where: {
        userId: context.userId,
        isActive: true,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      },
      include: {
        role: {
          where: {
            tenantId: context.tenantId
          }
        }
      }
    })

    // Collect all permissions from roles
    const permissions = new Set<string>()
    
    for (const userRole of userRoles) {
      if (userRole.role) {
        const rolePermissions = userRole.role.permissions as string[]
        rolePermissions.forEach(permission => permissions.add(permission))
        
        // Handle role hierarchy - get inherited permissions
        const inheritedPermissions = await this.getInheritedPermissions(userRole.role.id)
        inheritedPermissions.forEach(permission => permissions.add(permission))
      }
    }

    const permissionArray = Array.from(permissions)
    
    // Cache the result
    await this.cache.set(cacheKey, permissionArray, 300)
    
    return permissionArray
  }

  /**
   * Get inherited permissions from parent roles
   */
  private async getInheritedPermissions(roleId: string): Promise<string[]> {
    const role = await this.prisma.role.findUnique({
      where: { id: roleId },
      include: {
        parentRole: true
      }
    })

    if (!role?.parentRole) {
      return []
    }

    const parentPermissions = role.parentRole.permissions as string[]
    const grandParentPermissions = await this.getInheritedPermissions(role.parentRole.id)

    return [...parentPermissions, ...grandParentPermissions]
  }

  /**
   * Match a permission string against a required permission
   */
  private matchPermission(
    userPermission: string,
    requiredPermission: string
  ): { matches: boolean; conditions?: PermissionCondition[] } {
    const userParsed = this.parsePermission(userPermission)
    const requiredParsed = this.parsePermission(requiredPermission)

    // Check for wildcard permissions
    if (userParsed.resource === WILDCARD) {
      return { matches: true }
    }

    // Check resource match
    if (userParsed.resource !== requiredParsed.resource && userParsed.resource !== WILDCARD) {
      return { matches: false }
    }

    // Check action match
    if (userParsed.action !== requiredParsed.action && userParsed.action !== WILDCARD) {
      return { matches: false }
    }

    // Check scope match
    if (userParsed.scope !== requiredParsed.scope && userParsed.scope !== WILDCARD) {
      // Special scope hierarchy: all > tenant > team > own
      const scopeHierarchy = ['all', 'tenant', 'team', 'own']
      const userScopeIndex = scopeHierarchy.indexOf(userParsed.scope)
      const requiredScopeIndex = scopeHierarchy.indexOf(requiredParsed.scope)
      
      if (userScopeIndex === -1 || requiredScopeIndex === -1 || userScopeIndex > requiredScopeIndex) {
        return { matches: false }
      }
    }

    return { matches: true }
  }

  /**
   * Parse a permission string into components
   */
  private parsePermission(permission: string): ParsedPermission {
    const parts = permission.split(PERMISSION_SEPARATOR)
    
    if (parts.length !== 3) {
      throw new Error(`Invalid permission format: ${permission}`)
    }

    const [resource, action, scope] = parts
    
    return {
      resource,
      action,
      scope,
      isWildcard: permission === WILDCARD || parts.some(part => part === WILDCARD),
      specificity: this.calculateSpecificity(resource, action, scope)
    }
  }

  /**
   * Calculate permission specificity for conflict resolution
   */
  private calculateSpecificity(resource: string, action: string, scope: string): number {
    let specificity = 0
    
    if (resource !== WILDCARD) specificity += 100
    if (action !== WILDCARD) specificity += 10
    if (scope !== WILDCARD) specificity += 1
    
    return specificity
  }

  /**
   * Build permission string from check parameters
   */
  private buildPermissionString(check: PermissionCheck): string {
    const scope = check.scope || 'tenant'
    return `${check.resource}${PERMISSION_SEPARATOR}${check.action}${PERMISSION_SEPARATOR}${scope}`
  }

  /**
   * Evaluate additional conditions for permission
   */
  private async evaluateConditions(
    context: AccessContext,
    check: PermissionCheck,
    conditions?: PermissionCondition[]
  ): Promise<PermissionResult> {
    if (!conditions || conditions.length === 0) {
      return { granted: true }
    }

    for (const condition of conditions) {
      const result = await this.evaluateCondition(context, check, condition)
      if (!result.granted) {
        return result
      }
    }

    return { granted: true }
  }

  /**
   * Evaluate a single condition
   */
  private async evaluateCondition(
    context: AccessContext,
    check: PermissionCheck,
    condition: PermissionCondition
  ): Promise<PermissionResult> {
    // Get the field value from context or resource
    let fieldValue: any

    if (condition.field.startsWith('user.')) {
      // User context field
      const field = condition.field.replace('user.', '')
      fieldValue = (context as any)[field] || context.metadata?.[field]
    } else if (condition.field.startsWith('resource.')) {
      // Resource field - would need to fetch from database
      const field = condition.field.replace('resource.', '')
      if (check.resourceId) {
        fieldValue = await this.getResourceField(check.resource, check.resourceId, field)
      }
    } else {
      // Direct context field
      fieldValue = (context as any)[condition.field] || context.metadata?.[condition.field]
    }

    // Evaluate condition based on operator
    const matches = this.evaluateConditionOperator(
      fieldValue,
      condition.operator,
      condition.value
    )

    return {
      granted: matches,
      reason: matches ? undefined : `Condition failed: ${condition.field} ${condition.operator} ${condition.value}`
    }
  }

  /**
   * Evaluate condition operator
   */
  private evaluateConditionOperator(
    fieldValue: any,
    operator: PermissionCondition['operator'],
    expectedValue: any
  ): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === expectedValue
      case 'not_equals':
        return fieldValue !== expectedValue
      case 'in':
        return Array.isArray(expectedValue) && expectedValue.includes(fieldValue)
      case 'not_in':
        return Array.isArray(expectedValue) && !expectedValue.includes(fieldValue)
      case 'contains':
        return typeof fieldValue === 'string' && fieldValue.includes(expectedValue)
      case 'starts_with':
        return typeof fieldValue === 'string' && fieldValue.startsWith(expectedValue)
      case 'ends_with':
        return typeof fieldValue === 'string' && fieldValue.endsWith(expectedValue)
      case 'greater_than':
        return fieldValue > expectedValue
      case 'less_than':
        return fieldValue < expectedValue
      default:
        return false
    }
  }

  /**
   * Get a field value from a resource
   */
  private async getResourceField(
    resource: string,
    resourceId: string,
    field: string
  ): Promise<any> {
    // This would be implemented based on the resource type
    // For now, return null as a placeholder
    return null
  }

  /**
   * Create cache key for permission check
   */
  private createPermissionCacheKey(context: AccessContext, check: PermissionCheck): string {
    const roleIds = context.roles.map(r => r.id).sort().join(',')
    return `permission:${context.userId}:${context.tenantId}:${roleIds}:${check.resource}:${check.action}:${check.scope || 'tenant'}`
  }

  /**
   * Invalidate permission cache for a user
   */
  async invalidateUserPermissions(userId: string, tenantId: string): Promise<void> {
    const pattern = `*permission*${userId}*${tenantId}*`
    await this.cache.deletePattern(pattern)
    
    const userPermissionsKey = `user_permissions:${userId}:${tenantId}`
    await this.cache.delete(userPermissionsKey)
  }

  /**
   * Bulk check multiple permissions
   */
  async checkMultiplePermissions(
    context: AccessContext,
    checks: PermissionCheck[]
  ): Promise<Record<string, PermissionResult>> {
    const results: Record<string, PermissionResult> = {}
    
    // Execute all checks in parallel
    const promises = checks.map(async (check) => {
      const key = this.buildPermissionString(check)
      const result = await this.checkPermission(context, check)
      return { key, result }
    })

    const resolvedResults = await Promise.all(promises)
    
    resolvedResults.forEach(({ key, result }) => {
      results[key] = result
    })

    return results
  }
}
