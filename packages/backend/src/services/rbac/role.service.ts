import { PrismaClient, Role, UserRole } from '@prisma/client'
import { 
  SystemRole, 
  PERMISSION_SETS, 
  RoleHierarchy,
  PermissionAudit
} from '@/shared/types/rbac'
import { CacheService } from '@/services/cache.service'
import { Logger } from '@/shared/utils/logger'
import { ValidationError, NotFoundError, ConflictError } from '@/shared/errors'

export interface CreateRoleData {
  name: string
  description?: string
  permissions: string[]
  isSystem?: boolean
  isDefault?: boolean
  priority?: number
  color?: string
  icon?: string
  category?: string
  tags?: string[]
  parentRoleId?: string
}

export interface UpdateRoleData {
  name?: string
  description?: string
  permissions?: string[]
  priority?: number
  color?: string
  icon?: string
  category?: string
  tags?: string[]
  parentRoleId?: string
}

export interface RoleAssignmentData {
  userId: string
  roleId: string
  assignedBy: string
  expiresAt?: Date
  conditions?: any
}

export class RoleService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('RoleService')
  }

  /**
   * Create a new role
   */
  async createRole(
    tenantId: string,
    data: CreateRoleData,
    createdBy: string
  ): Promise<Role> {
    try {
      // Validate role name uniqueness within tenant
      const existingRole = await this.prisma.role.findFirst({
        where: {
          tenantId,
          name: data.name
        }
      })

      if (existingRole) {
        throw new ConflictError(`Role with name "${data.name}" already exists`)
      }

      // Validate parent role if specified
      if (data.parentRoleId) {
        const parentRole = await this.prisma.role.findFirst({
          where: {
            id: data.parentRoleId,
            tenantId
          }
        })

        if (!parentRole) {
          throw new NotFoundError('Parent role not found')
        }

        // Check for circular hierarchy
        if (await this.wouldCreateCircularHierarchy(data.parentRoleId, tenantId)) {
          throw new ValidationError('Cannot create circular role hierarchy')
        }
      }

      // Validate permissions
      this.validatePermissions(data.permissions)

      // Create the role
      const role = await this.prisma.role.create({
        data: {
          name: data.name,
          description: data.description,
          permissions: data.permissions,
          isSystem: data.isSystem || false,
          isDefault: data.isDefault || false,
          priority: data.priority || 0,
          color: data.color,
          icon: data.icon,
          category: data.category,
          tags: data.tags || [],
          parentRoleId: data.parentRoleId,
          tenantId
        }
      })

      // Create audit log
      await this.createPermissionAudit({
        userId: createdBy,
        tenantId,
        action: 'grant',
        resourceType: 'role',
        resourceId: role.id,
        changes: { created: data },
        performedBy: createdBy,
        reason: 'Role created'
      })

      // Invalidate cache
      await this.invalidateRoleCache(tenantId)

      this.logger.info('Role created', {
        roleId: role.id,
        roleName: role.name,
        tenantId,
        createdBy
      })

      return role
    } catch (error) {
      this.logger.error('Failed to create role', error, {
        tenantId,
        roleName: data.name,
        createdBy
      })
      throw error
    }
  }

  /**
   * Update an existing role
   */
  async updateRole(
    roleId: string,
    tenantId: string,
    data: UpdateRoleData,
    updatedBy: string
  ): Promise<Role> {
    try {
      // Get existing role
      const existingRole = await this.prisma.role.findFirst({
        where: {
          id: roleId,
          tenantId
        }
      })

      if (!existingRole) {
        throw new NotFoundError('Role not found')
      }

      // Validate name uniqueness if changing name
      if (data.name && data.name !== existingRole.name) {
        const nameConflict = await this.prisma.role.findFirst({
          where: {
            tenantId,
            name: data.name,
            id: { not: roleId }
          }
        })

        if (nameConflict) {
          throw new ConflictError(`Role with name "${data.name}" already exists`)
        }
      }

      // Validate parent role if changing
      if (data.parentRoleId !== undefined) {
        if (data.parentRoleId) {
          const parentRole = await this.prisma.role.findFirst({
            where: {
              id: data.parentRoleId,
              tenantId
            }
          })

          if (!parentRole) {
            throw new NotFoundError('Parent role not found')
          }

          // Check for circular hierarchy
          if (await this.wouldCreateCircularHierarchy(data.parentRoleId, tenantId, roleId)) {
            throw new ValidationError('Cannot create circular role hierarchy')
          }
        }
      }

      // Validate permissions if changing
      if (data.permissions) {
        this.validatePermissions(data.permissions)
      }

      // Update the role
      const updatedRole = await this.prisma.role.update({
        where: { id: roleId },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.permissions && { permissions: data.permissions }),
          ...(data.priority !== undefined && { priority: data.priority }),
          ...(data.color !== undefined && { color: data.color }),
          ...(data.icon !== undefined && { icon: data.icon }),
          ...(data.category !== undefined && { category: data.category }),
          ...(data.tags && { tags: data.tags }),
          ...(data.parentRoleId !== undefined && { parentRoleId: data.parentRoleId })
        }
      })

      // Create audit log
      await this.createPermissionAudit({
        userId: updatedBy,
        tenantId,
        action: 'modify',
        resourceType: 'role',
        resourceId: roleId,
        changes: { 
          before: existingRole,
          after: data
        },
        performedBy: updatedBy,
        reason: 'Role updated'
      })

      // Invalidate cache
      await this.invalidateRoleCache(tenantId)

      this.logger.info('Role updated', {
        roleId,
        tenantId,
        updatedBy,
        changes: data
      })

      return updatedRole
    } catch (error) {
      this.logger.error('Failed to update role', error, {
        roleId,
        tenantId,
        updatedBy
      })
      throw error
    }
  }

  /**
   * Delete a role
   */
  async deleteRole(
    roleId: string,
    tenantId: string,
    deletedBy: string
  ): Promise<void> {
    try {
      // Get existing role
      const existingRole = await this.prisma.role.findFirst({
        where: {
          id: roleId,
          tenantId
        },
        include: {
          userRoles: true,
          childRoles: true
        }
      })

      if (!existingRole) {
        throw new NotFoundError('Role not found')
      }

      // Check if role is system role
      if (existingRole.isSystem) {
        throw new ValidationError('Cannot delete system role')
      }

      // Check if role has users assigned
      if (existingRole.userRoles.length > 0) {
        throw new ValidationError('Cannot delete role with assigned users')
      }

      // Check if role has child roles
      if (existingRole.childRoles.length > 0) {
        throw new ValidationError('Cannot delete role with child roles')
      }

      // Delete the role
      await this.prisma.role.delete({
        where: { id: roleId }
      })

      // Create audit log
      await this.createPermissionAudit({
        userId: deletedBy,
        tenantId,
        action: 'revoke',
        resourceType: 'role',
        resourceId: roleId,
        changes: { deleted: existingRole },
        performedBy: deletedBy,
        reason: 'Role deleted'
      })

      // Invalidate cache
      await this.invalidateRoleCache(tenantId)

      this.logger.info('Role deleted', {
        roleId,
        roleName: existingRole.name,
        tenantId,
        deletedBy
      })
    } catch (error) {
      this.logger.error('Failed to delete role', error, {
        roleId,
        tenantId,
        deletedBy
      })
      throw error
    }
  }

  /**
   * Assign role to user
   */
  async assignRole(
    data: RoleAssignmentData,
    assignedBy: string,
    tenantId: string
  ): Promise<UserRole> {
    try {
      // Validate user exists in tenant
      const user = await this.prisma.user.findFirst({
        where: {
          id: data.userId,
          tenantId
        }
      })

      if (!user) {
        throw new NotFoundError('User not found in tenant')
      }

      // Validate role exists in tenant
      const role = await this.prisma.role.findFirst({
        where: {
          id: data.roleId,
          tenantId
        }
      })

      if (!role) {
        throw new NotFoundError('Role not found in tenant')
      }

      // Check if assignment already exists
      const existingAssignment = await this.prisma.userRole.findFirst({
        where: {
          userId: data.userId,
          roleId: data.roleId
        }
      })

      if (existingAssignment) {
        throw new ConflictError('User already has this role assigned')
      }

      // Create role assignment
      const userRole = await this.prisma.userRole.create({
        data: {
          userId: data.userId,
          roleId: data.roleId,
          assignedBy: data.assignedBy,
          expiresAt: data.expiresAt,
          conditions: data.conditions,
          isActive: true
        }
      })

      // Create audit log
      await this.createPermissionAudit({
        userId: data.userId,
        tenantId,
        action: 'grant',
        resourceType: 'user_role',
        resourceId: userRole.id,
        changes: { assigned: data },
        performedBy: assignedBy,
        reason: 'Role assigned to user'
      })

      // Invalidate user permission cache
      await this.invalidateUserPermissionCache(data.userId, tenantId)

      this.logger.info('Role assigned to user', {
        userId: data.userId,
        roleId: data.roleId,
        assignedBy,
        tenantId
      })

      return userRole
    } catch (error) {
      this.logger.error('Failed to assign role', error, {
        userId: data.userId,
        roleId: data.roleId,
        assignedBy,
        tenantId
      })
      throw error
    }
  }

  /**
   * Revoke role from user
   */
  async revokeRole(
    userId: string,
    roleId: string,
    revokedBy: string,
    tenantId: string
  ): Promise<void> {
    try {
      // Find existing assignment
      const userRole = await this.prisma.userRole.findFirst({
        where: {
          userId,
          roleId,
          user: { tenantId }
        }
      })

      if (!userRole) {
        throw new NotFoundError('Role assignment not found')
      }

      // Delete role assignment
      await this.prisma.userRole.delete({
        where: { id: userRole.id }
      })

      // Create audit log
      await this.createPermissionAudit({
        userId,
        tenantId,
        action: 'revoke',
        resourceType: 'user_role',
        resourceId: userRole.id,
        changes: { revoked: userRole },
        performedBy: revokedBy,
        reason: 'Role revoked from user'
      })

      // Invalidate user permission cache
      await this.invalidateUserPermissionCache(userId, tenantId)

      this.logger.info('Role revoked from user', {
        userId,
        roleId,
        revokedBy,
        tenantId
      })
    } catch (error) {
      this.logger.error('Failed to revoke role', error, {
        userId,
        roleId,
        revokedBy,
        tenantId
      })
      throw error
    }
  }

  /**
   * Get role hierarchy for a tenant
   */
  async getRoleHierarchy(tenantId: string): Promise<RoleHierarchy[]> {
    const cacheKey = `role_hierarchy:${tenantId}`
    
    // Try cache first
    const cached = await this.cache.get<RoleHierarchy[]>(cacheKey)
    if (cached) {
      return cached
    }

    // Get all roles for tenant
    const roles = await this.prisma.role.findMany({
      where: { tenantId },
      orderBy: { priority: 'desc' }
    })

    // Build hierarchy
    const hierarchy = this.buildRoleHierarchy(roles)

    // Cache result
    await this.cache.set(cacheKey, hierarchy, 600) // 10 minutes

    return hierarchy
  }

  /**
   * Initialize system roles for a tenant
   */
  async initializeSystemRoles(tenantId: string): Promise<void> {
    try {
      const systemRoles = Object.entries(PERMISSION_SETS)

      for (const [roleName, permissions] of systemRoles) {
        const existingRole = await this.prisma.role.findFirst({
          where: {
            tenantId,
            name: roleName
          }
        })

        if (!existingRole) {
          await this.prisma.role.create({
            data: {
              name: roleName,
              description: `System role: ${roleName}`,
              permissions,
              isSystem: true,
              isDefault: roleName === SystemRole.VIEWER,
              tenantId
            }
          })
        }
      }

      this.logger.info('System roles initialized', { tenantId })
    } catch (error) {
      this.logger.error('Failed to initialize system roles', error, { tenantId })
      throw error
    }
  }

  /**
   * Validate permissions array
   */
  private validatePermissions(permissions: string[]): void {
    for (const permission of permissions) {
      if (typeof permission !== 'string') {
        throw new ValidationError('All permissions must be strings')
      }

      if (permission !== '*' && !permission.includes(':')) {
        throw new ValidationError(`Invalid permission format: ${permission}`)
      }
    }
  }

  /**
   * Check if adding parent role would create circular hierarchy
   */
  private async wouldCreateCircularHierarchy(
    parentRoleId: string,
    tenantId: string,
    currentRoleId?: string
  ): Promise<boolean> {
    if (currentRoleId === parentRoleId) {
      return true
    }

    const parentRole = await this.prisma.role.findFirst({
      where: {
        id: parentRoleId,
        tenantId
      }
    })

    if (!parentRole?.parentRoleId) {
      return false
    }

    return this.wouldCreateCircularHierarchy(parentRole.parentRoleId, tenantId, currentRoleId)
  }

  /**
   * Build role hierarchy tree
   */
  private buildRoleHierarchy(roles: Role[]): RoleHierarchy[] {
    const roleMap = new Map<string, Role>()
    roles.forEach(role => roleMap.set(role.id, role))

    const hierarchy: RoleHierarchy[] = []
    const processed = new Set<string>()

    const buildNode = (role: Role): RoleHierarchy => {
      const children: RoleHierarchy[] = []
      const inheritedPermissions = new Set<string>(role.permissions as string[])

      // Find child roles
      const childRoles = roles.filter(r => r.parentRoleId === role.id)
      for (const childRole of childRoles) {
        if (!processed.has(childRole.id)) {
          processed.add(childRole.id)
          children.push(buildNode(childRole))
        }
      }

      // Add inherited permissions from parent
      if (role.parentRoleId) {
        const parentRole = roleMap.get(role.parentRoleId)
        if (parentRole) {
          const parentPermissions = parentRole.permissions as string[]
          parentPermissions.forEach(p => inheritedPermissions.add(p))
        }
      }

      return {
        roleId: role.id,
        parentRoleId: role.parentRoleId,
        children,
        inheritedPermissions: Array.from(inheritedPermissions)
      }
    }

    // Build hierarchy starting from root roles (no parent)
    const rootRoles = roles.filter(role => !role.parentRoleId)
    for (const rootRole of rootRoles) {
      if (!processed.has(rootRole.id)) {
        processed.add(rootRole.id)
        hierarchy.push(buildNode(rootRole))
      }
    }

    return hierarchy
  }

  /**
   * Create permission audit record
   */
  private async createPermissionAudit(data: Omit<PermissionAudit, 'id' | 'createdAt'>): Promise<void> {
    await this.prisma.permissionAudit.create({
      data: {
        userId: data.userId,
        tenantId: data.tenantId,
        action: data.action,
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        changes: data.changes,
        performedBy: data.performedBy,
        reason: data.reason,
        metadata: data.metadata
      }
    })
  }

  /**
   * Invalidate role cache
   */
  private async invalidateRoleCache(tenantId: string): Promise<void> {
    const patterns = [
      `role_hierarchy:${tenantId}`,
      `*permission*${tenantId}*`,
      `user_permissions:*:${tenantId}`
    ]

    for (const pattern of patterns) {
      await this.cache.deletePattern(pattern)
    }
  }

  /**
   * Invalidate user permission cache
   */
  private async invalidateUserPermissionCache(userId: string, tenantId: string): Promise<void> {
    const patterns = [
      `user_permissions:${userId}:${tenantId}`,
      `*permission*${userId}*${tenantId}*`
    ]

    for (const pattern of patterns) {
      await this.cache.deletePattern(pattern)
    }
  }
}
