import { Policy } from '@/shared/types/policy'
import { CacheService } from '@/services/cache.service'
import { Logger } from '@/shared/utils/logger'

export class PolicyCache {
  private cache: CacheService
  private logger: Logger
  private hitCounts: Map<string, number> = new Map()
  private missCounts: Map<string, number> = new Map()

  constructor(cache: CacheService) {
    this.cache = cache
    this.logger = new Logger('PolicyCache')
  }

  /**
   * Get policies from cache
   */
  async get(key: string): Promise<Policy[] | null> {
    try {
      const cached = await this.cache.get<Policy[]>(key)
      
      if (cached) {
        this.incrementHitCount(key)
        this.logger.debug('Policy cache hit', { key })
        return cached
      } else {
        this.incrementMissCount(key)
        this.logger.debug('Policy cache miss', { key })
        return null
      }
    } catch (error) {
      this.logger.error('Policy cache get failed', error, { key })
      this.incrementMissCount(key)
      return null
    }
  }

  /**
   * Set policies in cache
   */
  async set(key: string, policies: Policy[], ttl: number): Promise<void> {
    try {
      await this.cache.set(key, policies, ttl)
      this.logger.debug('Policy cache set', { key, count: policies.length, ttl })
    } catch (error) {
      this.logger.error('Policy cache set failed', error, { key })
    }
  }

  /**
   * Delete specific cache entry
   */
  async delete(key: string): Promise<void> {
    try {
      await this.cache.delete(key)
      this.logger.debug('Policy cache delete', { key })
    } catch (error) {
      this.logger.error('Policy cache delete failed', error, { key })
    }
  }

  /**
   * Invalidate all policies for a tenant
   */
  async invalidateTenant(tenantId: string): Promise<void> {
    try {
      const pattern = `policy:${tenantId}:*`
      await this.cache.deletePattern(pattern)
      this.logger.info('Policy cache invalidated for tenant', { tenantId })
    } catch (error) {
      this.logger.error('Policy cache invalidation failed', error, { tenantId })
    }
  }

  /**
   * Invalidate specific policy
   */
  async invalidatePolicy(tenantId: string, policyId: string): Promise<void> {
    try {
      const pattern = `policy:${tenantId}:*:${policyId}:*`
      await this.cache.deletePattern(pattern)
      this.logger.debug('Policy cache invalidated for policy', { tenantId, policyId })
    } catch (error) {
      this.logger.error('Policy cache invalidation failed', error, { tenantId, policyId })
    }
  }

  /**
   * Create cache key for policy lookup
   */
  createCacheKey(tenantId: string, action: string, resource: string): string {
    return `policy:${tenantId}:${action}:${resource}`
  }

  /**
   * Create cache key for specific policy
   */
  createPolicyCacheKey(tenantId: string, policyId: string): string {
    return `policy:${tenantId}:single:${policyId}`
  }

  /**
   * Get cache hit rate for a tenant
   */
  async getHitRate(tenantId: string): Promise<number> {
    const hitKey = `hits:${tenantId}`
    const missKey = `misses:${tenantId}`
    
    const hits = this.hitCounts.get(hitKey) || 0
    const misses = this.missCounts.get(missKey) || 0
    const total = hits + misses
    
    return total > 0 ? (hits / total) * 100 : 0
  }

  /**
   * Get cache statistics
   */
  async getStatistics(): Promise<{
    totalHits: number
    totalMisses: number
    hitRate: number
    tenantStats: Record<string, { hits: number; misses: number; hitRate: number }>
  }> {
    let totalHits = 0
    let totalMisses = 0
    const tenantStats: Record<string, { hits: number; misses: number; hitRate: number }> = {}

    // Aggregate hit counts
    for (const [key, hits] of this.hitCounts.entries()) {
      totalHits += hits
      
      if (key.startsWith('hits:')) {
        const tenantId = key.replace('hits:', '')
        if (!tenantStats[tenantId]) {
          tenantStats[tenantId] = { hits: 0, misses: 0, hitRate: 0 }
        }
        tenantStats[tenantId].hits = hits
      }
    }

    // Aggregate miss counts
    for (const [key, misses] of this.missCounts.entries()) {
      totalMisses += misses
      
      if (key.startsWith('misses:')) {
        const tenantId = key.replace('misses:', '')
        if (!tenantStats[tenantId]) {
          tenantStats[tenantId] = { hits: 0, misses: 0, hitRate: 0 }
        }
        tenantStats[tenantId].misses = misses
      }
    }

    // Calculate hit rates
    const totalRequests = totalHits + totalMisses
    const overallHitRate = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0

    for (const tenantId in tenantStats) {
      const stats = tenantStats[tenantId]
      const tenantTotal = stats.hits + stats.misses
      stats.hitRate = tenantTotal > 0 ? (stats.hits / tenantTotal) * 100 : 0
    }

    return {
      totalHits,
      totalMisses,
      hitRate: overallHitRate,
      tenantStats
    }
  }

  /**
   * Clear all cache statistics
   */
  clearStatistics(): void {
    this.hitCounts.clear()
    this.missCounts.clear()
    this.logger.info('Policy cache statistics cleared')
  }

  /**
   * Warm up cache with frequently accessed policies
   */
  async warmUp(tenantId: string, policies: Policy[]): Promise<void> {
    try {
      const warmUpPromises = policies.map(async (policy) => {
        const key = this.createPolicyCacheKey(tenantId, policy.id)
        await this.set(key, [policy], 3600) // 1 hour TTL for warm-up
      })

      await Promise.all(warmUpPromises)
      this.logger.info('Policy cache warmed up', { tenantId, count: policies.length })
    } catch (error) {
      this.logger.error('Policy cache warm-up failed', error, { tenantId })
    }
  }

  /**
   * Preload policies for common access patterns
   */
  async preloadCommonPolicies(tenantId: string): Promise<void> {
    try {
      // Common access patterns that should be cached
      const commonPatterns = [
        { action: 'read', resource: 'deals' },
        { action: 'create', resource: 'deals' },
        { action: 'update', resource: 'deals' },
        { action: 'read', resource: 'documents' },
        { action: 'upload', resource: 'documents' },
        { action: 'read', resource: 'users' },
        { action: 'manage', resource: 'roles' }
      ]

      for (const pattern of commonPatterns) {
        const key = this.createCacheKey(tenantId, pattern.action, pattern.resource)
        
        // Check if already cached
        const cached = await this.get(key)
        if (!cached) {
          // This would trigger a database lookup and cache the result
          // The actual policy loading would be done by the PolicyEngine
          this.logger.debug('Marking for preload', { tenantId, pattern })
        }
      }

      this.logger.info('Common policies preload completed', { tenantId })
    } catch (error) {
      this.logger.error('Policy preload failed', error, { tenantId })
    }
  }

  /**
   * Get cache memory usage (if supported by cache implementation)
   */
  async getMemoryUsage(): Promise<{
    used: number
    total: number
    percentage: number
  } | null> {
    try {
      // This would depend on the cache implementation
      // For now, return null as a placeholder
      return null
    } catch (error) {
      this.logger.error('Failed to get cache memory usage', error)
      return null
    }
  }

  /**
   * Increment hit count for statistics
   */
  private incrementHitCount(key: string): void {
    const tenantId = this.extractTenantId(key)
    if (tenantId) {
      const hitKey = `hits:${tenantId}`
      const current = this.hitCounts.get(hitKey) || 0
      this.hitCounts.set(hitKey, current + 1)
    }
  }

  /**
   * Increment miss count for statistics
   */
  private incrementMissCount(key: string): void {
    const tenantId = this.extractTenantId(key)
    if (tenantId) {
      const missKey = `misses:${tenantId}`
      const current = this.missCounts.get(missKey) || 0
      this.missCounts.set(missKey, current + 1)
    }
  }

  /**
   * Extract tenant ID from cache key
   */
  private extractTenantId(key: string): string | null {
    const parts = key.split(':')
    return parts.length > 1 ? parts[1] : null
  }
}
