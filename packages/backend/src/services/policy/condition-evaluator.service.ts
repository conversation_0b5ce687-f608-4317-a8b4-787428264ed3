import {
  PolicyCondition,
  PolicyContext,
  ConditionType,
  ConditionOperator,
  ConditionEvaluationResult
} from '@/shared/types/policy'
import { Logger } from '@/shared/utils/logger'
import { isIP, isIPv4, isIPv6 } from 'net'
import { parse as parseIP } from 'ipaddr.js'

export interface ConditionEvaluationResponse {
  result: boolean
  actualValue: any
  reason?: string
}

export class ConditionEvaluator {
  private logger: Logger

  constructor() {
    this.logger = new Logger('ConditionEvaluator')
  }

  /**
   * Evaluate a policy condition against the context
   */
  async evaluate(
    condition: PolicyCondition,
    context: PolicyContext
  ): Promise<ConditionEvaluationResponse> {
    try {
      // Get the actual value from context
      const actualValue = this.getFieldValue(condition.field, context)

      // Evaluate based on condition type and operator
      const result = this.evaluateCondition(
        actualValue,
        condition.operator,
        condition.value,
        condition.type
      )

      return {
        result,
        actualValue,
        reason: result ? undefined : this.getFailureReason(condition, actualValue)
      }
    } catch (error) {
      this.logger.error('Condition evaluation failed', error, {
        field: condition.field,
        operator: condition.operator,
        type: condition.type
      })

      return {
        result: false,
        actualValue: null,
        reason: `Evaluation error: ${error.message}`
      }
    }
  }

  /**
   * Get field value from context using dot notation
   */
  private getFieldValue(field: string, context: PolicyContext): any {
    const parts = field.split('.')
    let value: any = context

    for (const part of parts) {
      if (value === null || value === undefined) {
        return undefined
      }

      // Handle array access like "roles[0]" or "attributes['key']"
      if (part.includes('[') && part.includes(']')) {
        const [arrayField, indexPart] = part.split('[')
        const index = indexPart.replace(']', '').replace(/['"]/g, '')
        
        value = value[arrayField]
        if (Array.isArray(value)) {
          value = value[parseInt(index)] || value[index]
        } else if (typeof value === 'object') {
          value = value[index]
        } else {
          return undefined
        }
      } else {
        value = value[part]
      }
    }

    return value
  }

  /**
   * Evaluate condition based on operator and type
   */
  private evaluateCondition(
    actualValue: any,
    operator: ConditionOperator,
    expectedValue: any,
    type: ConditionType
  ): boolean {
    // Handle null/undefined values
    if (operator === ConditionOperator.EXISTS) {
      return actualValue !== null && actualValue !== undefined
    }
    
    if (operator === ConditionOperator.NOT_EXISTS) {
      return actualValue === null || actualValue === undefined
    }

    if (actualValue === null || actualValue === undefined) {
      return false
    }

    // Type-specific evaluations
    switch (type) {
      case ConditionType.STRING:
        return this.evaluateStringCondition(actualValue, operator, expectedValue)
      
      case ConditionType.NUMBER:
        return this.evaluateNumberCondition(actualValue, operator, expectedValue)
      
      case ConditionType.BOOLEAN:
        return this.evaluateBooleanCondition(actualValue, operator, expectedValue)
      
      case ConditionType.DATE:
        return this.evaluateDateCondition(actualValue, operator, expectedValue)
      
      case ConditionType.TIME:
        return this.evaluateTimeCondition(actualValue, operator, expectedValue)
      
      case ConditionType.IP_ADDRESS:
        return this.evaluateIPCondition(actualValue, operator, expectedValue)
      
      case ConditionType.USER_ATTRIBUTE:
      case ConditionType.RESOURCE_ATTRIBUTE:
      case ConditionType.CONTEXT_ATTRIBUTE:
        return this.evaluateGenericCondition(actualValue, operator, expectedValue)
      
      case ConditionType.CUSTOM:
        return this.evaluateCustomCondition(actualValue, operator, expectedValue)
      
      default:
        return this.evaluateGenericCondition(actualValue, operator, expectedValue)
    }
  }

  /**
   * Evaluate string conditions
   */
  private evaluateStringCondition(
    actualValue: any,
    operator: ConditionOperator,
    expectedValue: any
  ): boolean {
    const actual = String(actualValue)
    const expected = String(expectedValue)

    switch (operator) {
      case ConditionOperator.EQUALS:
        return actual === expected
      
      case ConditionOperator.NOT_EQUALS:
        return actual !== expected
      
      case ConditionOperator.CONTAINS:
        return actual.includes(expected)
      
      case ConditionOperator.NOT_CONTAINS:
        return !actual.includes(expected)
      
      case ConditionOperator.STARTS_WITH:
        return actual.startsWith(expected)
      
      case ConditionOperator.ENDS_WITH:
        return actual.endsWith(expected)
      
      case ConditionOperator.MATCHES_REGEX:
        try {
          const regex = new RegExp(expected)
          return regex.test(actual)
        } catch {
          return false
        }
      
      case ConditionOperator.IN:
        return Array.isArray(expectedValue) && expectedValue.includes(actual)
      
      case ConditionOperator.NOT_IN:
        return Array.isArray(expectedValue) && !expectedValue.includes(actual)
      
      default:
        return false
    }
  }

  /**
   * Evaluate number conditions
   */
  private evaluateNumberCondition(
    actualValue: any,
    operator: ConditionOperator,
    expectedValue: any
  ): boolean {
    const actual = Number(actualValue)
    const expected = Number(expectedValue)

    if (isNaN(actual) || isNaN(expected)) {
      return false
    }

    switch (operator) {
      case ConditionOperator.EQUALS:
        return actual === expected
      
      case ConditionOperator.NOT_EQUALS:
        return actual !== expected
      
      case ConditionOperator.GREATER_THAN:
        return actual > expected
      
      case ConditionOperator.GREATER_THAN_OR_EQUAL:
        return actual >= expected
      
      case ConditionOperator.LESS_THAN:
        return actual < expected
      
      case ConditionOperator.LESS_THAN_OR_EQUAL:
        return actual <= expected
      
      case ConditionOperator.IN:
        return Array.isArray(expectedValue) && expectedValue.includes(actual)
      
      case ConditionOperator.NOT_IN:
        return Array.isArray(expectedValue) && !expectedValue.includes(actual)
      
      case ConditionOperator.BETWEEN:
        if (Array.isArray(expectedValue) && expectedValue.length === 2) {
          const [min, max] = expectedValue.map(Number)
          return actual >= min && actual <= max
        }
        return false
      
      default:
        return false
    }
  }

  /**
   * Evaluate boolean conditions
   */
  private evaluateBooleanCondition(
    actualValue: any,
    operator: ConditionOperator,
    expectedValue: any
  ): boolean {
    const actual = Boolean(actualValue)
    const expected = Boolean(expectedValue)

    switch (operator) {
      case ConditionOperator.EQUALS:
        return actual === expected
      
      case ConditionOperator.NOT_EQUALS:
        return actual !== expected
      
      case ConditionOperator.IS_TRUE:
        return actual === true
      
      case ConditionOperator.IS_FALSE:
        return actual === false
      
      default:
        return false
    }
  }

  /**
   * Evaluate date conditions
   */
  private evaluateDateCondition(
    actualValue: any,
    operator: ConditionOperator,
    expectedValue: any
  ): boolean {
    const actual = new Date(actualValue)
    const expected = new Date(expectedValue)

    if (isNaN(actual.getTime()) || isNaN(expected.getTime())) {
      return false
    }

    switch (operator) {
      case ConditionOperator.EQUALS:
        return actual.getTime() === expected.getTime()
      
      case ConditionOperator.NOT_EQUALS:
        return actual.getTime() !== expected.getTime()
      
      case ConditionOperator.BEFORE:
        return actual < expected
      
      case ConditionOperator.AFTER:
        return actual > expected
      
      case ConditionOperator.BETWEEN:
        if (Array.isArray(expectedValue) && expectedValue.length === 2) {
          const [start, end] = expectedValue.map(d => new Date(d))
          return actual >= start && actual <= end
        }
        return false
      
      default:
        return false
    }
  }

  /**
   * Evaluate time conditions (time of day)
   */
  private evaluateTimeCondition(
    actualValue: any,
    operator: ConditionOperator,
    expectedValue: any
  ): boolean {
    // Extract time from date or parse time string
    let actualTime: string
    
    if (actualValue instanceof Date) {
      actualTime = actualValue.toTimeString().split(' ')[0] // HH:MM:SS
    } else {
      actualTime = String(actualValue)
    }

    const expectedTime = String(expectedValue)

    switch (operator) {
      case ConditionOperator.EQUALS:
        return actualTime === expectedTime
      
      case ConditionOperator.NOT_EQUALS:
        return actualTime !== expectedTime
      
      case ConditionOperator.BEFORE:
        return actualTime < expectedTime
      
      case ConditionOperator.AFTER:
        return actualTime > expectedTime
      
      case ConditionOperator.BETWEEN:
        if (Array.isArray(expectedValue) && expectedValue.length === 2) {
          const [start, end] = expectedValue
          return actualTime >= start && actualTime <= end
        }
        return false
      
      default:
        return false
    }
  }

  /**
   * Evaluate IP address conditions
   */
  private evaluateIPCondition(
    actualValue: any,
    operator: ConditionOperator,
    expectedValue: any
  ): boolean {
    const actualIP = String(actualValue)

    if (!isIP(actualIP)) {
      return false
    }

    switch (operator) {
      case ConditionOperator.EQUALS:
        return actualIP === expectedValue
      
      case ConditionOperator.NOT_EQUALS:
        return actualIP !== expectedValue
      
      case ConditionOperator.IP_IN_RANGE:
        return this.isIPInRange(actualIP, expectedValue)
      
      case ConditionOperator.IP_NOT_IN_RANGE:
        return !this.isIPInRange(actualIP, expectedValue)
      
      case ConditionOperator.IN:
        return Array.isArray(expectedValue) && expectedValue.includes(actualIP)
      
      case ConditionOperator.NOT_IN:
        return Array.isArray(expectedValue) && !expectedValue.includes(actualIP)
      
      default:
        return false
    }
  }

  /**
   * Evaluate generic conditions (fallback)
   */
  private evaluateGenericCondition(
    actualValue: any,
    operator: ConditionOperator,
    expectedValue: any
  ): boolean {
    switch (operator) {
      case ConditionOperator.EQUALS:
        return actualValue === expectedValue
      
      case ConditionOperator.NOT_EQUALS:
        return actualValue !== expectedValue
      
      case ConditionOperator.IN:
        return Array.isArray(expectedValue) && expectedValue.includes(actualValue)
      
      case ConditionOperator.NOT_IN:
        return Array.isArray(expectedValue) && !expectedValue.includes(actualValue)
      
      case ConditionOperator.CONTAINS:
        if (Array.isArray(actualValue)) {
          return actualValue.includes(expectedValue)
        }
        return String(actualValue).includes(String(expectedValue))
      
      case ConditionOperator.NOT_CONTAINS:
        if (Array.isArray(actualValue)) {
          return !actualValue.includes(expectedValue)
        }
        return !String(actualValue).includes(String(expectedValue))
      
      default:
        return false
    }
  }

  /**
   * Evaluate custom conditions (extensible)
   */
  private evaluateCustomCondition(
    actualValue: any,
    operator: ConditionOperator,
    expectedValue: any
  ): boolean {
    // This can be extended to support custom condition types
    // For now, fall back to generic evaluation
    return this.evaluateGenericCondition(actualValue, operator, expectedValue)
  }

  /**
   * Check if IP is in CIDR range
   */
  private isIPInRange(ip: string, range: string): boolean {
    try {
      if (range.includes('/')) {
        // CIDR notation
        const [rangeIP, prefixLength] = range.split('/')
        const actualAddr = parseIP(ip)
        const rangeAddr = parseIP(rangeIP)
        
        return actualAddr.match(rangeAddr, parseInt(prefixLength))
      } else {
        // Single IP
        return ip === range
      }
    } catch {
      return false
    }
  }

  /**
   * Generate failure reason for debugging
   */
  private getFailureReason(condition: PolicyCondition, actualValue: any): string {
    return `Condition failed: ${condition.field} (${actualValue}) ${condition.operator} ${condition.value}`
  }
}
