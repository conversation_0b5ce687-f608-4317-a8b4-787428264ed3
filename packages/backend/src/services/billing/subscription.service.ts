import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { PrismaClient } from '@prisma/client'
import Stripe from 'stripe'

export interface SubscriptionPlan {
  id: string
  name: string
  description: string
  
  // Pricing
  price: number
  currency: string
  billingInterval: 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
  
  // Features and limits
  features: PlanFeature[]
  limits: PlanLimits
  
  // Plan configuration
  isActive: boolean
  isPopular: boolean
  trialDays: number
  
  // Stripe integration
  stripeProductId?: string
  stripePriceId?: string
  
  // Metadata
  createdAt: Date
  updatedAt?: Date
}

export interface PlanFeature {
  id: string
  name: string
  description: string
  included: boolean
  limit?: number
  category: 'CORE' | 'ADVANCED' | 'PREMIUM' | 'ENTERPRISE'
}

export interface PlanLimits {
  maxDeals: number
  maxUsers: number
  maxStorage: number // GB
  maxAPIRequests: number // per month
  maxIntegrations: number
  maxCustomFields: number
  maxReports: number
  supportLevel: 'BASIC' | 'STANDARD' | 'PREMIUM' | 'ENTERPRISE'
}

export interface Subscription {
  id: string
  tenantId: string
  planId: string
  
  // Billing details
  status: 'ACTIVE' | 'PAST_DUE' | 'CANCELLED' | 'UNPAID' | 'TRIALING'
  currentPeriodStart: Date
  currentPeriodEnd: Date
  trialEnd?: Date
  
  // Payment information
  stripeSubscriptionId?: string
  stripeCustomerId?: string
  
  // Usage tracking
  usage: SubscriptionUsage
  
  // Billing history
  invoices: Invoice[]
  
  // Metadata
  createdAt: Date
  updatedAt?: Date
  cancelledAt?: Date
  cancelReason?: string
}

export interface SubscriptionUsage {
  period: string // YYYY-MM format
  deals: number
  users: number
  storage: number // GB
  apiRequests: number
  integrations: number
  reports: number
  lastUpdated: Date
}

export interface Invoice {
  id: string
  subscriptionId: string
  tenantId: string
  
  // Invoice details
  invoiceNumber: string
  amount: number
  currency: string
  tax: number
  total: number
  
  // Billing period
  periodStart: Date
  periodEnd: Date
  
  // Status
  status: 'DRAFT' | 'OPEN' | 'PAID' | 'VOID' | 'UNCOLLECTIBLE'
  paidAt?: Date
  dueDate: Date
  
  // Line items
  lineItems: InvoiceLineItem[]
  
  // Payment details
  stripeInvoiceId?: string
  paymentMethod?: string
  
  // Files
  pdfUrl?: string
  
  // Metadata
  createdAt: Date
  updatedAt?: Date
}

export interface InvoiceLineItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  amount: number
  type: 'SUBSCRIPTION' | 'USAGE' | 'ONE_TIME' | 'DISCOUNT'
}

export interface PaymentMethod {
  id: string
  tenantId: string
  
  // Payment method details
  type: 'CARD' | 'BANK_ACCOUNT' | 'PAYPAL'
  last4?: string
  brand?: string
  expiryMonth?: number
  expiryYear?: number
  
  // Stripe details
  stripePaymentMethodId: string
  
  // Status
  isDefault: boolean
  isActive: boolean
  
  // Metadata
  createdAt: Date
  updatedAt?: Date
}

export class SubscriptionService {
  private logger: Logger
  private cache: CacheService
  private prisma: PrismaClient
  private stripe: Stripe

  constructor(cache: CacheService, prisma: PrismaClient) {
    this.logger = new Logger('SubscriptionService')
    this.cache = cache
    this.prisma = prisma
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2023-10-16'
    })
  }

  /**
   * Get all subscription plans
   */
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    try {
      const cacheKey = 'subscription:plans'
      const cached = await this.cache.get<SubscriptionPlan[]>(cacheKey)
      
      if (cached) {
        return cached
      }

      // Get default plans
      const plans = this.getDefaultPlans()
      
      await this.cache.set(cacheKey, plans, 3600) // Cache for 1 hour
      
      return plans
    } catch (error) {
      this.logger.error('Failed to get subscription plans', error)
      throw error
    }
  }

  /**
   * Create subscription
   */
  async createSubscription(
    tenantId: string,
    planId: string,
    paymentMethodId?: string,
    trialDays?: number
  ): Promise<Subscription> {
    try {
      const plan = await this.getSubscriptionPlan(planId)
      if (!plan) {
        throw new Error('Subscription plan not found')
      }

      // Create Stripe customer if not exists
      let stripeCustomerId = await this.getStripeCustomerId(tenantId)
      if (!stripeCustomerId) {
        stripeCustomerId = await this.createStripeCustomer(tenantId)
      }

      // Attach payment method if provided
      if (paymentMethodId) {
        await this.stripe.paymentMethods.attach(paymentMethodId, {
          customer: stripeCustomerId
        })
      }

      // Create Stripe subscription
      const stripeSubscription = await this.stripe.subscriptions.create({
        customer: stripeCustomerId,
        items: [{ price: plan.stripePriceId }],
        trial_period_days: trialDays || plan.trialDays || undefined,
        default_payment_method: paymentMethodId,
        expand: ['latest_invoice.payment_intent']
      })

      // Create subscription record
      const subscription: Subscription = {
        id: this.generateId(),
        tenantId,
        planId,
        status: stripeSubscription.status === 'trialing' ? 'TRIALING' : 'ACTIVE',
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : undefined,
        stripeSubscriptionId: stripeSubscription.id,
        stripeCustomerId,
        usage: {
          period: this.getCurrentPeriod(),
          deals: 0,
          users: 0,
          storage: 0,
          apiRequests: 0,
          integrations: 0,
          reports: 0,
          lastUpdated: new Date()
        },
        invoices: [],
        createdAt: new Date()
      }

      // Save subscription
      await this.saveSubscription(subscription)

      this.logger.info('Created subscription', {
        subscriptionId: subscription.id,
        tenantId,
        planId,
        stripeSubscriptionId: stripeSubscription.id
      })

      return subscription
    } catch (error) {
      this.logger.error('Failed to create subscription', error)
      throw error
    }
  }

  /**
   * Get tenant subscription
   */
  async getTenantSubscription(tenantId: string): Promise<Subscription | null> {
    try {
      const cacheKey = `subscription:tenant:${tenantId}`
      const cached = await this.cache.get<Subscription>(cacheKey)
      
      if (cached) {
        return cached
      }

      // In a real implementation, this would query the database
      return null
    } catch (error) {
      this.logger.error('Failed to get tenant subscription', error)
      return null
    }
  }

  /**
   * Update subscription plan
   */
  async updateSubscriptionPlan(
    subscriptionId: string,
    newPlanId: string,
    prorationBehavior: 'create_prorations' | 'none' = 'create_prorations'
  ): Promise<Subscription> {
    try {
      const subscription = await this.getSubscription(subscriptionId)
      if (!subscription) {
        throw new Error('Subscription not found')
      }

      const newPlan = await this.getSubscriptionPlan(newPlanId)
      if (!newPlan) {
        throw new Error('New subscription plan not found')
      }

      // Update Stripe subscription
      if (subscription.stripeSubscriptionId) {
        const stripeSubscription = await this.stripe.subscriptions.retrieve(
          subscription.stripeSubscriptionId
        )

        await this.stripe.subscriptions.update(subscription.stripeSubscriptionId, {
          items: [{
            id: stripeSubscription.items.data[0].id,
            price: newPlan.stripePriceId
          }],
          proration_behavior: prorationBehavior
        })
      }

      // Update subscription record
      subscription.planId = newPlanId
      subscription.updatedAt = new Date()

      await this.saveSubscription(subscription)

      this.logger.info('Updated subscription plan', {
        subscriptionId,
        oldPlanId: subscription.planId,
        newPlanId
      })

      return subscription
    } catch (error) {
      this.logger.error('Failed to update subscription plan', error)
      throw error
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(
    subscriptionId: string,
    cancelReason?: string,
    cancelAtPeriodEnd: boolean = true
  ): Promise<Subscription> {
    try {
      const subscription = await this.getSubscription(subscriptionId)
      if (!subscription) {
        throw new Error('Subscription not found')
      }

      // Cancel Stripe subscription
      if (subscription.stripeSubscriptionId) {
        if (cancelAtPeriodEnd) {
          await this.stripe.subscriptions.update(subscription.stripeSubscriptionId, {
            cancel_at_period_end: true
          })
        } else {
          await this.stripe.subscriptions.cancel(subscription.stripeSubscriptionId)
        }
      }

      // Update subscription record
      subscription.status = 'CANCELLED'
      subscription.cancelledAt = new Date()
      subscription.cancelReason = cancelReason
      subscription.updatedAt = new Date()

      await this.saveSubscription(subscription)

      this.logger.info('Cancelled subscription', {
        subscriptionId,
        cancelReason,
        cancelAtPeriodEnd
      })

      return subscription
    } catch (error) {
      this.logger.error('Failed to cancel subscription', error)
      throw error
    }
  }

  /**
   * Track usage
   */
  async trackUsage(
    tenantId: string,
    usageType: keyof Omit<SubscriptionUsage, 'period' | 'lastUpdated'>,
    amount: number
  ): Promise<void> {
    try {
      const subscription = await this.getTenantSubscription(tenantId)
      if (!subscription) {
        return // No active subscription
      }

      const currentPeriod = this.getCurrentPeriod()
      
      // Reset usage if new period
      if (subscription.usage.period !== currentPeriod) {
        subscription.usage = {
          period: currentPeriod,
          deals: 0,
          users: 0,
          storage: 0,
          apiRequests: 0,
          integrations: 0,
          reports: 0,
          lastUpdated: new Date()
        }
      }

      // Update usage
      subscription.usage[usageType] = Math.max(subscription.usage[usageType], amount)
      subscription.usage.lastUpdated = new Date()

      await this.saveSubscription(subscription)

      // Check limits
      await this.checkUsageLimits(subscription)
    } catch (error) {
      this.logger.error('Failed to track usage', error)
    }
  }

  /**
   * Generate invoice
   */
  async generateInvoice(subscriptionId: string): Promise<Invoice> {
    try {
      const subscription = await this.getSubscription(subscriptionId)
      if (!subscription) {
        throw new Error('Subscription not found')
      }

      const plan = await this.getSubscriptionPlan(subscription.planId)
      if (!plan) {
        throw new Error('Subscription plan not found')
      }

      const invoice: Invoice = {
        id: this.generateId(),
        subscriptionId,
        tenantId: subscription.tenantId,
        invoiceNumber: this.generateInvoiceNumber(),
        amount: plan.price,
        currency: plan.currency,
        tax: this.calculateTax(plan.price),
        total: plan.price + this.calculateTax(plan.price),
        periodStart: subscription.currentPeriodStart,
        periodEnd: subscription.currentPeriodEnd,
        status: 'OPEN',
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        lineItems: [
          {
            id: this.generateId(),
            description: `${plan.name} - ${plan.billingInterval}`,
            quantity: 1,
            unitPrice: plan.price,
            amount: plan.price,
            type: 'SUBSCRIPTION'
          }
        ],
        createdAt: new Date()
      }

      // Save invoice
      await this.saveInvoice(invoice)

      this.logger.info('Generated invoice', {
        invoiceId: invoice.id,
        subscriptionId,
        amount: invoice.total
      })

      return invoice
    } catch (error) {
      this.logger.error('Failed to generate invoice', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private getDefaultPlans(): SubscriptionPlan[] {
    return [
      {
        id: 'starter',
        name: 'Starter',
        description: 'Perfect for small teams getting started with M&A',
        price: 99,
        currency: 'USD',
        billingInterval: 'MONTHLY',
        features: [
          { id: 'deals', name: 'Deal Management', description: 'Basic deal tracking', included: true, category: 'CORE' },
          { id: 'users', name: 'Team Members', description: 'Up to 5 users', included: true, limit: 5, category: 'CORE' },
          { id: 'storage', name: 'Storage', description: '10GB storage', included: true, limit: 10, category: 'CORE' }
        ],
        limits: {
          maxDeals: 10,
          maxUsers: 5,
          maxStorage: 10,
          maxAPIRequests: 1000,
          maxIntegrations: 2,
          maxCustomFields: 10,
          maxReports: 5,
          supportLevel: 'BASIC'
        },
        isActive: true,
        isPopular: false,
        trialDays: 14,
        createdAt: new Date()
      },
      {
        id: 'professional',
        name: 'Professional',
        description: 'Advanced features for growing M&A teams',
        price: 299,
        currency: 'USD',
        billingInterval: 'MONTHLY',
        features: [
          { id: 'deals', name: 'Deal Management', description: 'Advanced deal tracking', included: true, category: 'CORE' },
          { id: 'users', name: 'Team Members', description: 'Up to 25 users', included: true, limit: 25, category: 'CORE' },
          { id: 'storage', name: 'Storage', description: '100GB storage', included: true, limit: 100, category: 'CORE' },
          { id: 'analytics', name: 'Analytics', description: 'Advanced reporting', included: true, category: 'ADVANCED' }
        ],
        limits: {
          maxDeals: 50,
          maxUsers: 25,
          maxStorage: 100,
          maxAPIRequests: 10000,
          maxIntegrations: 10,
          maxCustomFields: 50,
          maxReports: 25,
          supportLevel: 'STANDARD'
        },
        isActive: true,
        isPopular: true,
        trialDays: 14,
        createdAt: new Date()
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        description: 'Complete solution for large organizations',
        price: 999,
        currency: 'USD',
        billingInterval: 'MONTHLY',
        features: [
          { id: 'deals', name: 'Deal Management', description: 'Unlimited deals', included: true, category: 'CORE' },
          { id: 'users', name: 'Team Members', description: 'Unlimited users', included: true, category: 'CORE' },
          { id: 'storage', name: 'Storage', description: '1TB storage', included: true, limit: 1000, category: 'CORE' },
          { id: 'analytics', name: 'Analytics', description: 'Advanced reporting', included: true, category: 'ADVANCED' },
          { id: 'api', name: 'API Access', description: 'Full API access', included: true, category: 'ENTERPRISE' },
          { id: 'sso', name: 'SSO', description: 'Single sign-on', included: true, category: 'ENTERPRISE' }
        ],
        limits: {
          maxDeals: -1, // Unlimited
          maxUsers: -1, // Unlimited
          maxStorage: 1000,
          maxAPIRequests: 100000,
          maxIntegrations: -1, // Unlimited
          maxCustomFields: -1, // Unlimited
          maxReports: -1, // Unlimited
          supportLevel: 'ENTERPRISE'
        },
        isActive: true,
        isPopular: false,
        trialDays: 30,
        createdAt: new Date()
      }
    ]
  }

  private async getSubscriptionPlan(planId: string): Promise<SubscriptionPlan | null> {
    const plans = await this.getSubscriptionPlans()
    return plans.find(p => p.id === planId) || null
  }

  private async getSubscription(subscriptionId: string): Promise<Subscription | null> {
    return await this.cache.get<Subscription>(`subscription:${subscriptionId}`)
  }

  private async saveSubscription(subscription: Subscription): Promise<void> {
    await this.cache.set(`subscription:${subscription.id}`, subscription, 86400)
    await this.cache.set(`subscription:tenant:${subscription.tenantId}`, subscription, 86400)
  }

  private async saveInvoice(invoice: Invoice): Promise<void> {
    await this.cache.set(`invoice:${invoice.id}`, invoice, 86400)
  }

  private async getStripeCustomerId(tenantId: string): Promise<string | null> {
    // In a real implementation, this would query the database
    return null
  }

  private async createStripeCustomer(tenantId: string): Promise<string> {
    const customer = await this.stripe.customers.create({
      metadata: { tenantId }
    })
    return customer.id
  }

  private async checkUsageLimits(subscription: Subscription): Promise<void> {
    const plan = await this.getSubscriptionPlan(subscription.planId)
    if (!plan) return

    const usage = subscription.usage
    const limits = plan.limits

    // Check each limit
    if (limits.maxDeals > 0 && usage.deals >= limits.maxDeals) {
      this.logger.warn('Deal limit exceeded', { tenantId: subscription.tenantId, usage: usage.deals, limit: limits.maxDeals })
    }

    if (limits.maxUsers > 0 && usage.users >= limits.maxUsers) {
      this.logger.warn('User limit exceeded', { tenantId: subscription.tenantId, usage: usage.users, limit: limits.maxUsers })
    }

    if (limits.maxStorage > 0 && usage.storage >= limits.maxStorage) {
      this.logger.warn('Storage limit exceeded', { tenantId: subscription.tenantId, usage: usage.storage, limit: limits.maxStorage })
    }
  }

  private getCurrentPeriod(): string {
    const now = new Date()
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
  }

  private calculateTax(amount: number): number {
    // Simplified tax calculation - 8.5%
    return Math.round(amount * 0.085 * 100) / 100
  }

  private generateInvoiceNumber(): string {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const random = Math.random().toString(36).substr(2, 6).toUpperCase()
    return `INV-${year}${month}-${random}`
  }

  private generateId(): string {
    return `sub-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
