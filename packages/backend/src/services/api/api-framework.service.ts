import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { PrismaClient } from '@prisma/client'
import { Request, Response, NextFunction } from 'express'
import * as jwt from 'jsonwebtoken'
import * as crypto from 'crypto'

export interface APIKey {
  id: string
  name: string
  keyHash: string
  keyPrefix: string
  tenantId: string
  userId: string
  
  // Permissions and scopes
  scopes: string[]
  permissions: APIPermission[]
  
  // Rate limiting
  rateLimit: {
    requestsPerMinute: number
    requestsPerHour: number
    requestsPerDay: number
  }
  
  // Usage tracking
  usage: {
    totalRequests: number
    lastUsed?: Date
    monthlyRequests: number
    dailyRequests: number
  }
  
  // Status and lifecycle
  status: 'ACTIVE' | 'SUSPENDED' | 'REVOKED'
  expiresAt?: Date
  
  // IP restrictions
  allowedIPs?: string[]
  
  // Webhook settings
  webhookUrl?: string
  webhookSecret?: string
  
  // Metadata
  createdAt: Date
  updatedAt?: Date
  lastRotatedAt?: Date
}

export interface APIPermission {
  resource: string
  actions: ('CREATE' | 'READ' | 'UPDATE' | 'DELETE')[]
  conditions?: Record<string, any>
}

export interface WebhookEvent {
  id: string
  type: string
  data: any
  timestamp: Date
  tenantId: string
  
  // Delivery tracking
  deliveryAttempts: WebhookDeliveryAttempt[]
  status: 'PENDING' | 'DELIVERED' | 'FAILED' | 'CANCELLED'
  
  // Retry configuration
  maxRetries: number
  retryDelay: number // seconds
  
  // Metadata
  createdAt: Date
}

export interface WebhookDeliveryAttempt {
  id: string
  attemptNumber: number
  timestamp: Date
  httpStatus?: number
  responseTime?: number
  errorMessage?: string
  success: boolean
}

export interface RateLimitInfo {
  limit: number
  remaining: number
  resetTime: Date
  retryAfter?: number
}

export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  meta?: {
    pagination?: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    rateLimit?: RateLimitInfo
    requestId: string
    timestamp: Date
    version: string
  }
}

export interface IntegrationConfig {
  id: string
  name: string
  type: 'WEBHOOK' | 'API' | 'OAUTH' | 'CUSTOM'
  tenantId: string
  
  // Configuration
  config: {
    baseUrl?: string
    authType: 'API_KEY' | 'OAUTH2' | 'BASIC' | 'BEARER'
    credentials: Record<string, string>
    headers?: Record<string, string>
    timeout: number
    retryConfig: {
      maxRetries: number
      retryDelay: number
      backoffMultiplier: number
    }
  }
  
  // Event mapping
  eventMappings: Array<{
    internalEvent: string
    externalEvent: string
    transformation?: string
    enabled: boolean
  }>
  
  // Status
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR'
  lastSync?: Date
  lastError?: string
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt?: Date
}

export class APIFrameworkService {
  private logger: Logger
  private cache: CacheService
  private prisma: PrismaClient
  private webhookQueue: WebhookEvent[]

  constructor(cache: CacheService, prisma: PrismaClient) {
    this.logger = new Logger('APIFrameworkService')
    this.cache = cache
    this.prisma = prisma
    this.webhookQueue = []

    // Start webhook processing
    this.startWebhookProcessor()
  }

  /**
   * Generate API key
   */
  async generateAPIKey(
    name: string,
    tenantId: string,
    userId: string,
    options: {
      scopes?: string[]
      permissions?: APIPermission[]
      rateLimit?: Partial<APIKey['rateLimit']>
      expiresAt?: Date
      allowedIPs?: string[]
    } = {}
  ): Promise<{ apiKey: APIKey; plainKey: string }> {
    try {
      // Generate key
      const plainKey = this.generateSecureKey()
      const keyHash = this.hashKey(plainKey)
      const keyPrefix = plainKey.substring(0, 8)

      const apiKey: APIKey = {
        id: this.generateId(),
        name,
        keyHash,
        keyPrefix,
        tenantId,
        userId,
        scopes: options.scopes || ['read'],
        permissions: options.permissions || [],
        rateLimit: {
          requestsPerMinute: 60,
          requestsPerHour: 1000,
          requestsPerDay: 10000,
          ...options.rateLimit
        },
        usage: {
          totalRequests: 0,
          monthlyRequests: 0,
          dailyRequests: 0
        },
        status: 'ACTIVE',
        expiresAt: options.expiresAt,
        allowedIPs: options.allowedIPs,
        createdAt: new Date()
      }

      // Save API key
      await this.saveAPIKey(apiKey)

      this.logger.info('Generated API key', {
        keyId: apiKey.id,
        name,
        tenantId,
        userId
      })

      return { apiKey, plainKey }
    } catch (error) {
      this.logger.error('Failed to generate API key', error)
      throw error
    }
  }

  /**
   * Validate API key
   */
  async validateAPIKey(key: string, requiredScopes?: string[]): Promise<APIKey | null> {
    try {
      const keyHash = this.hashKey(key)
      const keyPrefix = key.substring(0, 8)

      // Find API key
      const apiKey = await this.findAPIKeyByHash(keyHash, keyPrefix)
      if (!apiKey) {
        return null
      }

      // Check status
      if (apiKey.status !== 'ACTIVE') {
        return null
      }

      // Check expiration
      if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
        return null
      }

      // Check scopes
      if (requiredScopes && !this.hasRequiredScopes(apiKey.scopes, requiredScopes)) {
        return null
      }

      // Update usage
      await this.updateAPIKeyUsage(apiKey.id)

      return apiKey
    } catch (error) {
      this.logger.error('Failed to validate API key', error)
      return null
    }
  }

  /**
   * Rate limiting middleware
   */
  rateLimitMiddleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const apiKey = (req as any).apiKey as APIKey
        if (!apiKey) {
          return next()
        }

        const rateLimitInfo = await this.checkRateLimit(apiKey)
        
        // Add rate limit headers
        res.set({
          'X-RateLimit-Limit': rateLimitInfo.limit.toString(),
          'X-RateLimit-Remaining': rateLimitInfo.remaining.toString(),
          'X-RateLimit-Reset': Math.ceil(rateLimitInfo.resetTime.getTime() / 1000).toString()
        })

        if (rateLimitInfo.remaining <= 0) {
          if (rateLimitInfo.retryAfter) {
            res.set('Retry-After', rateLimitInfo.retryAfter.toString())
          }
          
          return res.status(429).json(this.createErrorResponse(
            'RATE_LIMIT_EXCEEDED',
            'Rate limit exceeded',
            { rateLimitInfo }
          ))
        }

        next()
      } catch (error) {
        this.logger.error('Rate limit middleware error', error)
        next(error)
      }
    }
  }

  /**
   * API authentication middleware
   */
  authenticationMiddleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const authHeader = req.headers.authorization
        if (!authHeader) {
          return res.status(401).json(this.createErrorResponse(
            'MISSING_AUTHORIZATION',
            'Authorization header is required'
          ))
        }

        let apiKey: APIKey | null = null

        if (authHeader.startsWith('Bearer ')) {
          const token = authHeader.substring(7)
          
          // Try JWT token first
          try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
            // Handle JWT authentication
            (req as any).user = decoded
            return next()
          } catch (jwtError) {
            // Try as API key
            apiKey = await this.validateAPIKey(token)
          }
        } else if (authHeader.startsWith('ApiKey ')) {
          const key = authHeader.substring(7)
          apiKey = await this.validateAPIKey(key)
        }

        if (!apiKey) {
          return res.status(401).json(this.createErrorResponse(
            'INVALID_API_KEY',
            'Invalid or expired API key'
          ))
        }

        // Check IP restrictions
        if (apiKey.allowedIPs && apiKey.allowedIPs.length > 0) {
          const clientIP = req.ip
          if (!apiKey.allowedIPs.includes(clientIP)) {
            return res.status(403).json(this.createErrorResponse(
              'IP_NOT_ALLOWED',
              'Request from unauthorized IP address'
            ))
          }
        }

        // Add API key to request
        (req as any).apiKey = apiKey
        (req as any).tenantId = apiKey.tenantId

        next()
      } catch (error) {
        this.logger.error('Authentication middleware error', error)
        res.status(500).json(this.createErrorResponse(
          'AUTHENTICATION_ERROR',
          'Authentication failed'
        ))
      }
    }
  }

  /**
   * Permission checking middleware
   */
  permissionMiddleware(resource: string, action: string) {
    return (req: Request, res: Response, next: NextFunction) => {
      try {
        const apiKey = (req as any).apiKey as APIKey
        if (!apiKey) {
          return next()
        }

        const hasPermission = this.checkPermission(apiKey, resource, action)
        if (!hasPermission) {
          return res.status(403).json(this.createErrorResponse(
            'INSUFFICIENT_PERMISSIONS',
            `Insufficient permissions for ${action} on ${resource}`
          ))
        }

        next()
      } catch (error) {
        this.logger.error('Permission middleware error', error)
        next(error)
      }
    }
  }

  /**
   * Send webhook event
   */
  async sendWebhookEvent(
    type: string,
    data: any,
    tenantId: string,
    webhookUrl?: string
  ): Promise<void> {
    try {
      const event: WebhookEvent = {
        id: this.generateId(),
        type,
        data,
        timestamp: new Date(),
        tenantId,
        deliveryAttempts: [],
        status: 'PENDING',
        maxRetries: 3,
        retryDelay: 60,
        createdAt: new Date()
      }

      // Add to queue
      this.webhookQueue.push(event)

      this.logger.info('Webhook event queued', {
        eventId: event.id,
        type,
        tenantId
      })
    } catch (error) {
      this.logger.error('Failed to queue webhook event', error)
    }
  }

  /**
   * Create standardized API response
   */
  createSuccessResponse<T>(data: T, meta?: any): APIResponse<T> {
    return {
      success: true,
      data,
      meta: {
        ...meta,
        requestId: this.generateId(),
        timestamp: new Date(),
        version: '1.0'
      }
    }
  }

  /**
   * Create standardized error response
   */
  createErrorResponse(code: string, message: string, details?: any): APIResponse {
    return {
      success: false,
      error: {
        code,
        message,
        details
      },
      meta: {
        requestId: this.generateId(),
        timestamp: new Date(),
        version: '1.0'
      }
    }
  }

  /**
   * Private helper methods
   */
  private generateSecureKey(): string {
    return `gj_${crypto.randomBytes(32).toString('hex')}`
  }

  private hashKey(key: string): string {
    return crypto.createHash('sha256').update(key).digest('hex')
  }

  private async saveAPIKey(apiKey: APIKey): Promise<void> {
    // In a real implementation, this would save to database
    await this.cache.set(`api-key:${apiKey.id}`, apiKey, 86400)
    await this.cache.set(`api-key-hash:${apiKey.keyHash}`, apiKey.id, 86400)
  }

  private async findAPIKeyByHash(keyHash: string, keyPrefix: string): Promise<APIKey | null> {
    try {
      const keyId = await this.cache.get<string>(`api-key-hash:${keyHash}`)
      if (!keyId) return null

      const apiKey = await this.cache.get<APIKey>(`api-key:${keyId}`)
      if (!apiKey || apiKey.keyPrefix !== keyPrefix) return null

      return apiKey
    } catch (error) {
      return null
    }
  }

  private hasRequiredScopes(userScopes: string[], requiredScopes: string[]): boolean {
    return requiredScopes.every(scope => userScopes.includes(scope) || userScopes.includes('*'))
  }

  private checkPermission(apiKey: APIKey, resource: string, action: string): boolean {
    return apiKey.permissions.some(permission => 
      permission.resource === resource && 
      permission.actions.includes(action as any)
    )
  }

  private async checkRateLimit(apiKey: APIKey): Promise<RateLimitInfo> {
    const now = new Date()
    const minuteKey = `rate-limit:${apiKey.id}:${Math.floor(now.getTime() / 60000)}`
    
    const currentMinuteRequests = await this.cache.get<number>(minuteKey) || 0
    const remaining = Math.max(0, apiKey.rateLimit.requestsPerMinute - currentMinuteRequests)
    
    return {
      limit: apiKey.rateLimit.requestsPerMinute,
      remaining,
      resetTime: new Date(Math.ceil(now.getTime() / 60000) * 60000),
      retryAfter: remaining <= 0 ? 60 : undefined
    }
  }

  private async updateAPIKeyUsage(keyId: string): Promise<void> {
    try {
      const apiKey = await this.cache.get<APIKey>(`api-key:${keyId}`)
      if (!apiKey) return

      apiKey.usage.totalRequests++
      apiKey.usage.dailyRequests++
      apiKey.usage.monthlyRequests++
      apiKey.usage.lastUsed = new Date()

      await this.cache.set(`api-key:${keyId}`, apiKey, 86400)

      // Update rate limit counter
      const now = new Date()
      const minuteKey = `rate-limit:${keyId}:${Math.floor(now.getTime() / 60000)}`
      const currentCount = await this.cache.get<number>(minuteKey) || 0
      await this.cache.set(minuteKey, currentCount + 1, 60)
    } catch (error) {
      this.logger.error('Failed to update API key usage', error)
    }
  }

  private startWebhookProcessor(): void {
    setInterval(async () => {
      await this.processWebhookQueue()
    }, 5000) // Process every 5 seconds
  }

  private async processWebhookQueue(): Promise<void> {
    if (this.webhookQueue.length === 0) return

    const event = this.webhookQueue.shift()!
    
    try {
      await this.deliverWebhookEvent(event)
    } catch (error) {
      this.logger.error('Failed to deliver webhook event', error)
      
      // Retry logic
      if (event.deliveryAttempts.length < event.maxRetries) {
        setTimeout(() => {
          this.webhookQueue.push(event)
        }, event.retryDelay * 1000)
      } else {
        event.status = 'FAILED'
      }
    }
  }

  private async deliverWebhookEvent(event: WebhookEvent): Promise<void> {
    // Get webhook URL for tenant
    const webhookUrl = await this.getWebhookUrlForTenant(event.tenantId)
    if (!webhookUrl) {
      event.status = 'CANCELLED'
      return
    }

    const attempt: WebhookDeliveryAttempt = {
      id: this.generateId(),
      attemptNumber: event.deliveryAttempts.length + 1,
      timestamp: new Date(),
      success: false
    }

    try {
      const startTime = Date.now()
      
      // In a real implementation, this would make HTTP request
      // const response = await fetch(webhookUrl, { ... })
      
      attempt.httpStatus = 200
      attempt.responseTime = Date.now() - startTime
      attempt.success = true
      
      event.status = 'DELIVERED'
    } catch (error) {
      attempt.errorMessage = error instanceof Error ? error.message : 'Unknown error'
      attempt.success = false
    }

    event.deliveryAttempts.push(attempt)
  }

  private async getWebhookUrlForTenant(tenantId: string): Promise<string | null> {
    // In a real implementation, this would query tenant webhook configuration
    return null
  }

  private generateId(): string {
    return `api-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
