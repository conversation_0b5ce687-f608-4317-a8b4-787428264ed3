import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { PrismaClient } from '@prisma/client'
import { EventEmitter } from 'events'

export interface Workflow {
  id: string
  name: string
  description?: string
  version: string
  tenantId: string
  
  // Workflow definition
  definition: WorkflowDefinition
  
  // Configuration
  isActive: boolean
  isTemplate: boolean
  category: string
  tags: string[]
  
  // Permissions
  createdBy: string
  allowedUsers: string[]
  allowedRoles: string[]
  
  // Execution settings
  maxConcurrentExecutions: number
  timeoutMinutes: number
  retryPolicy: RetryPolicy
  
  // Metadata
  createdAt: Date
  updatedAt?: Date
  lastExecuted?: Date
  executionCount: number
}

export interface WorkflowDefinition {
  triggers: WorkflowTrigger[]
  nodes: WorkflowNode[]
  connections: WorkflowConnection[]
  variables: WorkflowVariable[]
  settings: WorkflowSettings
}

export interface WorkflowTrigger {
  id: string
  type: 'MANUAL' | 'SCHEDULED' | 'EVENT' | 'WEBHOOK' | 'API'
  name: string
  
  // Trigger configuration
  config: {
    // Scheduled trigger
    schedule?: {
      cron: string
      timezone: string
      startDate?: Date
      endDate?: Date
    }
    
    // Event trigger
    event?: {
      source: string
      eventType: string
      filters?: Record<string, any>
    }
    
    // Webhook trigger
    webhook?: {
      url: string
      method: 'GET' | 'POST' | 'PUT' | 'DELETE'
      headers?: Record<string, string>
      authentication?: {
        type: 'NONE' | 'API_KEY' | 'BEARER' | 'BASIC'
        config: Record<string, string>
      }
    }
  }
  
  // Conditions
  conditions?: WorkflowCondition[]
  
  // Status
  isActive: boolean
}

export interface WorkflowNode {
  id: string
  type: 'ACTION' | 'CONDITION' | 'APPROVAL' | 'DELAY' | 'PARALLEL' | 'MERGE'
  name: string
  description?: string
  
  // Position for UI
  position: { x: number; y: number }
  
  // Node configuration
  config: {
    // Action node
    action?: {
      type: string
      parameters: Record<string, any>
      timeout?: number
      retries?: number
    }
    
    // Condition node
    condition?: WorkflowCondition
    
    // Approval node
    approval?: {
      approvers: string[]
      approvalType: 'ANY' | 'ALL' | 'MAJORITY'
      timeoutHours: number
      escalation?: {
        timeoutHours: number
        escalateTo: string[]
      }
    }
    
    // Delay node
    delay?: {
      duration: number
      unit: 'SECONDS' | 'MINUTES' | 'HOURS' | 'DAYS'
    }
  }
  
  // Error handling
  onError: 'STOP' | 'CONTINUE' | 'RETRY' | 'SKIP'
  errorHandlers?: WorkflowNode[]
}

export interface WorkflowConnection {
  id: string
  sourceNodeId: string
  targetNodeId: string
  
  // Connection conditions
  condition?: WorkflowCondition
  label?: string
  
  // Connection type
  type: 'SUCCESS' | 'ERROR' | 'CONDITIONAL' | 'DEFAULT'
}

export interface WorkflowCondition {
  field: string
  operator: 'EQUALS' | 'NOT_EQUALS' | 'GREATER_THAN' | 'LESS_THAN' | 'CONTAINS' | 'EXISTS'
  value: any
  logicalOperator?: 'AND' | 'OR'
  conditions?: WorkflowCondition[]
}

export interface WorkflowVariable {
  name: string
  type: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'OBJECT' | 'ARRAY'
  defaultValue?: any
  required: boolean
  description?: string
}

export interface WorkflowSettings {
  notifications: {
    onStart: boolean
    onComplete: boolean
    onError: boolean
    recipients: string[]
  }
  logging: {
    level: 'NONE' | 'ERROR' | 'INFO' | 'DEBUG'
    retentionDays: number
  }
  security: {
    requireApproval: boolean
    allowedExecutors: string[]
  }
}

export interface RetryPolicy {
  maxRetries: number
  retryDelay: number // seconds
  backoffMultiplier: number
  maxRetryDelay: number // seconds
}

export interface WorkflowExecution {
  id: string
  workflowId: string
  workflowVersion: string
  tenantId: string
  
  // Execution context
  triggeredBy: string
  triggerType: string
  triggerData?: any
  
  // Status
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'TIMEOUT'
  
  // Execution data
  variables: Record<string, any>
  currentNodeId?: string
  completedNodes: string[]
  failedNodes: string[]
  
  // Node executions
  nodeExecutions: NodeExecution[]
  
  // Timing
  startedAt: Date
  completedAt?: Date
  duration?: number // milliseconds
  
  // Results
  result?: any
  error?: {
    message: string
    code: string
    nodeId?: string
    stack?: string
  }
  
  // Metadata
  createdAt: Date
  updatedAt?: Date
}

export interface NodeExecution {
  nodeId: string
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'SKIPPED'
  
  // Timing
  startedAt?: Date
  completedAt?: Date
  duration?: number // milliseconds
  
  // Input/Output
  input?: any
  output?: any
  
  // Error details
  error?: {
    message: string
    code: string
    retryCount: number
  }
  
  // Approval details (for approval nodes)
  approval?: {
    status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'TIMEOUT'
    approvers: Array<{
      userId: string
      decision: 'APPROVED' | 'REJECTED'
      comment?: string
      timestamp: Date
    }>
    timeoutAt: Date
  }
}

export interface WorkflowTemplate {
  id: string
  name: string
  description: string
  category: string
  
  // Template definition
  definition: WorkflowDefinition
  
  // Configuration options
  configOptions: Array<{
    name: string
    type: string
    required: boolean
    defaultValue?: any
    description?: string
  }>
  
  // Usage
  usageCount: number
  rating: number
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt?: Date
}

export class WorkflowService extends EventEmitter {
  private logger: Logger
  private cache: CacheService
  private prisma: PrismaClient
  private executionQueue: WorkflowExecution[]
  private isProcessing: boolean

  constructor(cache: CacheService, prisma: PrismaClient) {
    super()
    this.logger = new Logger('WorkflowService')
    this.cache = cache
    this.prisma = prisma
    this.executionQueue = []
    this.isProcessing = false

    // Start execution processor
    this.startExecutionProcessor()
  }

  /**
   * Create workflow
   */
  async createWorkflow(
    workflow: Omit<Workflow, 'id' | 'createdAt' | 'executionCount'>,
    userId: string
  ): Promise<Workflow> {
    try {
      const newWorkflow: Workflow = {
        ...workflow,
        id: this.generateId(),
        createdBy: userId,
        createdAt: new Date(),
        executionCount: 0
      }

      // Validate workflow definition
      await this.validateWorkflowDefinition(newWorkflow.definition)

      // Save workflow
      await this.saveWorkflow(newWorkflow)

      // Register triggers
      await this.registerTriggers(newWorkflow)

      this.logger.info('Created workflow', {
        workflowId: newWorkflow.id,
        name: workflow.name,
        tenantId: workflow.tenantId,
        userId
      })

      return newWorkflow
    } catch (error) {
      this.logger.error('Failed to create workflow', error)
      throw error
    }
  }

  /**
   * Execute workflow
   */
  async executeWorkflow(
    workflowId: string,
    triggerData: any = {},
    triggeredBy: string,
    triggerType: string = 'MANUAL'
  ): Promise<WorkflowExecution> {
    try {
      const workflow = await this.getWorkflow(workflowId)
      if (!workflow) {
        throw new Error('Workflow not found')
      }

      if (!workflow.isActive) {
        throw new Error('Workflow is not active')
      }

      // Create execution
      const execution: WorkflowExecution = {
        id: this.generateId(),
        workflowId,
        workflowVersion: workflow.version,
        tenantId: workflow.tenantId,
        triggeredBy,
        triggerType,
        triggerData,
        status: 'PENDING',
        variables: { ...triggerData },
        completedNodes: [],
        failedNodes: [],
        nodeExecutions: [],
        startedAt: new Date(),
        createdAt: new Date()
      }

      // Save execution
      await this.saveExecution(execution)

      // Add to execution queue
      this.executionQueue.push(execution)

      // Start processing if not already running
      if (!this.isProcessing) {
        this.processExecutionQueue()
      }

      this.logger.info('Workflow execution queued', {
        executionId: execution.id,
        workflowId,
        triggeredBy,
        triggerType
      })

      return execution
    } catch (error) {
      this.logger.error('Failed to execute workflow', error)
      throw error
    }
  }

  /**
   * Get workflow execution status
   */
  async getExecutionStatus(executionId: string): Promise<WorkflowExecution | null> {
    try {
      return await this.getExecution(executionId)
    } catch (error) {
      this.logger.error('Failed to get execution status', error)
      return null
    }
  }

  /**
   * Cancel workflow execution
   */
  async cancelExecution(executionId: string, userId: string): Promise<void> {
    try {
      const execution = await this.getExecution(executionId)
      if (!execution) {
        throw new Error('Execution not found')
      }

      if (execution.status === 'COMPLETED' || execution.status === 'CANCELLED') {
        throw new Error('Execution cannot be cancelled')
      }

      execution.status = 'CANCELLED'
      execution.completedAt = new Date()
      execution.duration = Date.now() - execution.startedAt.getTime()
      execution.updatedAt = new Date()

      await this.saveExecution(execution)

      this.logger.info('Workflow execution cancelled', {
        executionId,
        userId
      })
    } catch (error) {
      this.logger.error('Failed to cancel execution', error)
      throw error
    }
  }

  /**
   * Get workflow templates
   */
  async getWorkflowTemplates(category?: string): Promise<WorkflowTemplate[]> {
    try {
      // In a real implementation, this would query the database
      return this.getDefaultTemplates().filter(t => 
        !category || t.category === category
      )
    } catch (error) {
      this.logger.error('Failed to get workflow templates', error)
      return []
    }
  }

  /**
   * Private helper methods
   */
  private async processExecutionQueue(): Promise<void> {
    if (this.isProcessing || this.executionQueue.length === 0) {
      return
    }

    this.isProcessing = true

    try {
      while (this.executionQueue.length > 0) {
        const execution = this.executionQueue.shift()!
        await this.processExecution(execution)
      }
    } catch (error) {
      this.logger.error('Error processing execution queue', error)
    } finally {
      this.isProcessing = false
    }
  }

  private async processExecution(execution: WorkflowExecution): Promise<void> {
    try {
      const workflow = await this.getWorkflow(execution.workflowId)
      if (!workflow) {
        throw new Error('Workflow not found')
      }

      execution.status = 'RUNNING'
      execution.updatedAt = new Date()
      await this.saveExecution(execution)

      // Find start node (first node without incoming connections)
      const startNode = this.findStartNode(workflow.definition)
      if (!startNode) {
        throw new Error('No start node found')
      }

      // Execute workflow
      await this.executeNode(execution, workflow, startNode)

      // Update execution status
      if (execution.status === 'RUNNING') {
        execution.status = 'COMPLETED'
        execution.completedAt = new Date()
        execution.duration = Date.now() - execution.startedAt.getTime()
      }

      await this.saveExecution(execution)

      this.emit('execution_completed', execution)

      this.logger.info('Workflow execution completed', {
        executionId: execution.id,
        status: execution.status,
        duration: execution.duration
      })
    } catch (error) {
      execution.status = 'FAILED'
      execution.error = {
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'EXECUTION_ERROR'
      }
      execution.completedAt = new Date()
      execution.duration = Date.now() - execution.startedAt.getTime()

      await this.saveExecution(execution)

      this.emit('execution_failed', execution)

      this.logger.error('Workflow execution failed', error)
    }
  }

  private async executeNode(
    execution: WorkflowExecution,
    workflow: Workflow,
    node: WorkflowNode
  ): Promise<void> {
    const nodeExecution: NodeExecution = {
      nodeId: node.id,
      status: 'RUNNING',
      startedAt: new Date()
    }

    execution.nodeExecutions.push(nodeExecution)
    execution.currentNodeId = node.id

    try {
      // Execute node based on type
      switch (node.type) {
        case 'ACTION':
          await this.executeActionNode(execution, node, nodeExecution)
          break
        case 'CONDITION':
          await this.executeConditionNode(execution, workflow, node, nodeExecution)
          break
        case 'APPROVAL':
          await this.executeApprovalNode(execution, node, nodeExecution)
          break
        case 'DELAY':
          await this.executeDelayNode(execution, node, nodeExecution)
          break
        default:
          throw new Error(`Unsupported node type: ${node.type}`)
      }

      nodeExecution.status = 'COMPLETED'
      nodeExecution.completedAt = new Date()
      nodeExecution.duration = Date.now() - nodeExecution.startedAt!.getTime()

      execution.completedNodes.push(node.id)

      // Find and execute next nodes
      const nextNodes = this.findNextNodes(workflow.definition, node.id, execution)
      for (const nextNode of nextNodes) {
        await this.executeNode(execution, workflow, nextNode)
      }
    } catch (error) {
      nodeExecution.status = 'FAILED'
      nodeExecution.error = {
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'NODE_EXECUTION_ERROR',
        retryCount: 0
      }
      nodeExecution.completedAt = new Date()
      nodeExecution.duration = Date.now() - nodeExecution.startedAt!.getTime()

      execution.failedNodes.push(node.id)

      if (node.onError === 'STOP') {
        throw error
      }
    }
  }

  private async executeActionNode(
    execution: WorkflowExecution,
    node: WorkflowNode,
    nodeExecution: NodeExecution
  ): Promise<void> {
    const action = node.config.action!
    
    // Execute action based on type
    switch (action.type) {
      case 'SEND_EMAIL':
        await this.sendEmail(action.parameters, execution.variables)
        break
      case 'UPDATE_DEAL':
        await this.updateDeal(action.parameters, execution.variables)
        break
      case 'CREATE_TASK':
        await this.createTask(action.parameters, execution.variables)
        break
      default:
        throw new Error(`Unsupported action type: ${action.type}`)
    }
  }

  private async executeConditionNode(
    execution: WorkflowExecution,
    workflow: Workflow,
    node: WorkflowNode,
    nodeExecution: NodeExecution
  ): Promise<void> {
    const condition = node.config.condition!
    const result = this.evaluateCondition(condition, execution.variables)
    
    nodeExecution.output = { result }
  }

  private async executeApprovalNode(
    execution: WorkflowExecution,
    node: WorkflowNode,
    nodeExecution: NodeExecution
  ): Promise<void> {
    const approval = node.config.approval!
    
    // Create approval request
    nodeExecution.approval = {
      status: 'PENDING',
      approvers: [],
      timeoutAt: new Date(Date.now() + approval.timeoutHours * 60 * 60 * 1000)
    }

    // In a real implementation, this would send approval requests
    // For now, we'll simulate immediate approval
    nodeExecution.approval.status = 'APPROVED'
    nodeExecution.approval.approvers.push({
      userId: 'system',
      decision: 'APPROVED',
      timestamp: new Date()
    })
  }

  private async executeDelayNode(
    execution: WorkflowExecution,
    node: WorkflowNode,
    nodeExecution: NodeExecution
  ): Promise<void> {
    const delay = node.config.delay!
    
    let delayMs = delay.duration
    switch (delay.unit) {
      case 'SECONDS':
        delayMs *= 1000
        break
      case 'MINUTES':
        delayMs *= 60 * 1000
        break
      case 'HOURS':
        delayMs *= 60 * 60 * 1000
        break
      case 'DAYS':
        delayMs *= 24 * 60 * 60 * 1000
        break
    }

    // In a real implementation, this would schedule the delay
    // For now, we'll simulate immediate completion
    await new Promise(resolve => setTimeout(resolve, Math.min(delayMs, 1000)))
  }

  private findStartNode(definition: WorkflowDefinition): WorkflowNode | null {
    // Find node with no incoming connections
    const incomingNodes = new Set(definition.connections.map(c => c.targetNodeId))
    return definition.nodes.find(node => !incomingNodes.has(node.id)) || null
  }

  private findNextNodes(
    definition: WorkflowDefinition,
    currentNodeId: string,
    execution: WorkflowExecution
  ): WorkflowNode[] {
    const connections = definition.connections.filter(c => c.sourceNodeId === currentNodeId)
    const nextNodes: WorkflowNode[] = []

    for (const connection of connections) {
      if (!connection.condition || this.evaluateCondition(connection.condition, execution.variables)) {
        const nextNode = definition.nodes.find(n => n.id === connection.targetNodeId)
        if (nextNode) {
          nextNodes.push(nextNode)
        }
      }
    }

    return nextNodes
  }

  private evaluateCondition(condition: WorkflowCondition, variables: Record<string, any>): boolean {
    const value = variables[condition.field]
    
    switch (condition.operator) {
      case 'EQUALS':
        return value === condition.value
      case 'NOT_EQUALS':
        return value !== condition.value
      case 'GREATER_THAN':
        return value > condition.value
      case 'LESS_THAN':
        return value < condition.value
      case 'CONTAINS':
        return String(value).includes(String(condition.value))
      case 'EXISTS':
        return value !== undefined && value !== null
      default:
        return false
    }
  }

  private async sendEmail(parameters: any, variables: Record<string, any>): Promise<void> {
    // Implement email sending logic
    this.logger.info('Sending email', { parameters, variables })
  }

  private async updateDeal(parameters: any, variables: Record<string, any>): Promise<void> {
    // Implement deal update logic
    this.logger.info('Updating deal', { parameters, variables })
  }

  private async createTask(parameters: any, variables: Record<string, any>): Promise<void> {
    // Implement task creation logic
    this.logger.info('Creating task', { parameters, variables })
  }

  private async validateWorkflowDefinition(definition: WorkflowDefinition): Promise<void> {
    // Validate workflow definition
    if (!definition.nodes || definition.nodes.length === 0) {
      throw new Error('Workflow must have at least one node')
    }

    // Check for cycles, validate connections, etc.
  }

  private async registerTriggers(workflow: Workflow): Promise<void> {
    // Register workflow triggers
    for (const trigger of workflow.definition.triggers) {
      if (trigger.isActive) {
        await this.registerTrigger(workflow, trigger)
      }
    }
  }

  private async registerTrigger(workflow: Workflow, trigger: WorkflowTrigger): Promise<void> {
    // Register trigger based on type
    switch (trigger.type) {
      case 'SCHEDULED':
        // Register cron job
        break
      case 'EVENT':
        // Register event listener
        break
      case 'WEBHOOK':
        // Register webhook endpoint
        break
    }
  }

  private getDefaultTemplates(): WorkflowTemplate[] {
    return [
      {
        id: 'deal-approval',
        name: 'Deal Approval Workflow',
        description: 'Standard deal approval process',
        category: 'APPROVAL',
        definition: {
          triggers: [],
          nodes: [],
          connections: [],
          variables: [],
          settings: {
            notifications: { onStart: true, onComplete: true, onError: true, recipients: [] },
            logging: { level: 'INFO', retentionDays: 30 },
            security: { requireApproval: false, allowedExecutors: [] }
          }
        },
        configOptions: [],
        usageCount: 0,
        rating: 4.5,
        createdBy: 'system',
        createdAt: new Date()
      }
    ]
  }

  private async saveWorkflow(workflow: Workflow): Promise<void> {
    await this.cache.set(`workflow:${workflow.id}`, workflow, 86400)
  }

  private async getWorkflow(workflowId: string): Promise<Workflow | null> {
    return await this.cache.get<Workflow>(`workflow:${workflowId}`)
  }

  private async saveExecution(execution: WorkflowExecution): Promise<void> {
    await this.cache.set(`execution:${execution.id}`, execution, 86400)
  }

  private async getExecution(executionId: string): Promise<WorkflowExecution | null> {
    return await this.cache.get<WorkflowExecution>(`execution:${executionId}`)
  }

  private startExecutionProcessor(): void {
    setInterval(() => {
      this.processExecutionQueue()
    }, 1000) // Process every second
  }

  private generateId(): string {
    return `wf-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
