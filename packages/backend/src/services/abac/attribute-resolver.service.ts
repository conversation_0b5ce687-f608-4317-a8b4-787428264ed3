import { PrismaClient } from '@prisma/client'
import {
  AttributeSet,
  AttributeValue,
  AttributeProvider,
  AttributeProviderType,
  ComputedAttribute
} from '@/shared/types/abac'
import { AttributeCache } from './attribute-cache.service'
import { Logger } from '@/shared/utils/logger'

export class AttributeResolver {
  private prisma: PrismaClient
  private cache: AttributeCache
  private logger: Logger
  private providers: Map<string, AttributeProvider> = new Map()

  constructor(prisma: PrismaClient, cache: AttributeCache) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('AttributeResolver')
  }

  /**
   * Resolve subject (user) attributes
   */
  async resolveSubjectAttributes(
    subjectId: string,
    requiredAttributes: string[]
  ): Promise<AttributeSet> {
    try {
      const cacheKey = `subject:${subjectId}:${requiredAttributes.join(',')}`
      
      // Try cache first
      const cached = await this.cache.get(cacheKey)
      if (cached) {
        return this.parseAttributeSet(cached)
      }

      // Get user from database
      const user = await this.prisma.user.findUnique({
        where: { id: subjectId },
        include: {
          userRoles: {
            include: {
              role: true
            },
            where: {
              isActive: true
            }
          },
          tenant: true
        }
      })

      if (!user) {
        throw new Error(`User not found: ${subjectId}`)
      }

      // Build attribute set
      const attributes: Record<string, AttributeValue> = {}

      // Basic user attributes
      this.addAttribute(attributes, 'id', user.id, 'database')
      this.addAttribute(attributes, 'email', user.email, 'database')
      this.addAttribute(attributes, 'firstName', user.firstName, 'database')
      this.addAttribute(attributes, 'lastName', user.lastName, 'database')
      this.addAttribute(attributes, 'status', user.status, 'database')
      this.addAttribute(attributes, 'tenantId', user.tenantId, 'database')
      this.addAttribute(attributes, 'lastLoginAt', user.lastLoginAt, 'database')

      // Role attributes
      const roles = user.userRoles.map(ur => ur.role.name)
      this.addAttribute(attributes, 'roles', roles, 'database')

      // Tenant attributes
      if (user.tenant) {
        this.addAttribute(attributes, 'tenantPlan', user.tenant.plan, 'database')
        this.addAttribute(attributes, 'tenantStatus', user.tenant.status, 'database')
      }

      // Computed attributes
      await this.addComputedAttributes(attributes, 'subject', user)

      // Resolve additional attributes from providers
      await this.resolveFromProviders(attributes, 'subject', subjectId, requiredAttributes)

      const attributeSet: AttributeSet = {
        entityId: subjectId,
        entityType: 'subject',
        attributes
      }

      // Cache the result
      await this.cache.set(cacheKey, attributeSet, 300) // 5 minutes

      return attributeSet
    } catch (error) {
      this.logger.error('Failed to resolve subject attributes', error, {
        subjectId,
        requiredAttributes
      })

      // Return minimal attribute set on error
      return {
        entityId: subjectId,
        entityType: 'subject',
        attributes: {
          id: this.createAttributeValue('id', subjectId, 'error')
        }
      }
    }
  }

  /**
   * Resolve resource attributes
   */
  async resolveResourceAttributes(
    resourceId: string,
    resourceType: string,
    requiredAttributes: string[]
  ): Promise<AttributeSet> {
    try {
      const cacheKey = `resource:${resourceType}:${resourceId}:${requiredAttributes.join(',')}`
      
      // Try cache first
      const cached = await this.cache.get(cacheKey)
      if (cached) {
        return this.parseAttributeSet(cached)
      }

      const attributes: Record<string, AttributeValue> = {}

      // Resolve based on resource type
      switch (resourceType) {
        case 'deals':
          await this.resolveDealAttributes(resourceId, attributes)
          break
        case 'documents':
          await this.resolveDocumentAttributes(resourceId, attributes)
          break
        case 'users':
          await this.resolveUserResourceAttributes(resourceId, attributes)
          break
        default:
          // Generic resource attributes
          this.addAttribute(attributes, 'id', resourceId, 'database')
          this.addAttribute(attributes, 'type', resourceType, 'static')
      }

      // Computed attributes
      await this.addComputedAttributes(attributes, 'resource', { id: resourceId, type: resourceType })

      // Resolve additional attributes from providers
      await this.resolveFromProviders(attributes, 'resource', resourceId, requiredAttributes)

      const attributeSet: AttributeSet = {
        entityId: resourceId,
        entityType: 'resource',
        attributes
      }

      // Cache the result
      await this.cache.set(cacheKey, attributeSet, 300)

      return attributeSet
    } catch (error) {
      this.logger.error('Failed to resolve resource attributes', error, {
        resourceId,
        resourceType,
        requiredAttributes
      })

      return {
        entityId: resourceId,
        entityType: 'resource',
        attributes: {
          id: this.createAttributeValue('id', resourceId, 'error'),
          type: this.createAttributeValue('type', resourceType, 'static')
        }
      }
    }
  }

  /**
   * Resolve action attributes
   */
  async resolveActionAttributes(action: string): Promise<AttributeSet> {
    const attributes: Record<string, AttributeValue> = {}

    // Basic action attributes
    this.addAttribute(attributes, 'name', action, 'static')
    this.addAttribute(attributes, 'type', this.getActionType(action), 'computed')
    this.addAttribute(attributes, 'risk', this.getActionRisk(action), 'computed')

    return {
      entityId: action,
      entityType: 'action',
      attributes
    }
  }

  /**
   * Resolve environment attributes
   */
  async resolveEnvironmentAttributes(
    context: Record<string, any>
  ): Promise<AttributeSet> {
    const attributes: Record<string, AttributeValue> = {}

    // Time-based attributes
    const now = new Date()
    this.addAttribute(attributes, 'currentTime', now.toISOString(), 'computed')
    this.addAttribute(attributes, 'dayOfWeek', now.getDay(), 'computed')
    this.addAttribute(attributes, 'hour', now.getHours(), 'computed')
    this.addAttribute(attributes, 'isBusinessHours', this.isBusinessHours(now), 'computed')

    // Network attributes
    if (context.ipAddress) {
      this.addAttribute(attributes, 'ipAddress', context.ipAddress, 'context')
      this.addAttribute(attributes, 'isInternalNetwork', this.isInternalIP(context.ipAddress), 'computed')
    }

    // Device attributes
    if (context.userAgent) {
      this.addAttribute(attributes, 'userAgent', context.userAgent, 'context')
      this.addAttribute(attributes, 'deviceType', this.getDeviceType(context.userAgent), 'computed')
    }

    // Location attributes
    if (context.location) {
      this.addAttribute(attributes, 'country', context.location.country, 'context')
      this.addAttribute(attributes, 'region', context.location.region, 'context')
    }

    return {
      entityId: 'environment',
      entityType: 'environment',
      attributes
    }
  }

  /**
   * Resolve deal-specific attributes
   */
  private async resolveDealAttributes(
    dealId: string,
    attributes: Record<string, AttributeValue>
  ): Promise<void> {
    const deal = await this.prisma.deal.findUnique({
      where: { id: dealId },
      include: {
        creator: true,
        assignee: true,
        tenant: true
      }
    })

    if (deal) {
      this.addAttribute(attributes, 'id', deal.id, 'database')
      this.addAttribute(attributes, 'title', deal.title, 'database')
      this.addAttribute(attributes, 'type', deal.type, 'database')
      this.addAttribute(attributes, 'status', deal.status, 'database')
      this.addAttribute(attributes, 'dealValue', deal.dealValue, 'database')
      this.addAttribute(attributes, 'currency', deal.currency, 'database')
      this.addAttribute(attributes, 'createdBy', deal.createdBy, 'database')
      this.addAttribute(attributes, 'assignedTo', deal.assignedTo, 'database')
      this.addAttribute(attributes, 'tenantId', deal.tenantId, 'database')
      this.addAttribute(attributes, 'isHighValue', deal.dealValue > 10000000, 'computed')
    }
  }

  /**
   * Resolve document-specific attributes
   */
  private async resolveDocumentAttributes(
    documentId: string,
    attributes: Record<string, AttributeValue>
  ): Promise<void> {
    const document = await this.prisma.document.findUnique({
      where: { id: documentId },
      include: {
        uploader: true,
        deal: true
      }
    })

    if (document) {
      this.addAttribute(attributes, 'id', document.id, 'database')
      this.addAttribute(attributes, 'name', document.name, 'database')
      this.addAttribute(attributes, 'type', document.type, 'database')
      this.addAttribute(attributes, 'size', document.size, 'database')
      this.addAttribute(attributes, 'uploadedBy', document.uploadedBy, 'database')
      this.addAttribute(attributes, 'dealId', document.dealId, 'database')
      this.addAttribute(attributes, 'tenantId', document.tenantId, 'database')
      this.addAttribute(attributes, 'isLarge', document.size > 10485760, 'computed') // > 10MB
    }
  }

  /**
   * Resolve user resource attributes (when user is the resource)
   */
  private async resolveUserResourceAttributes(
    userId: string,
    attributes: Record<string, AttributeValue>
  ): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    })

    if (user) {
      this.addAttribute(attributes, 'id', user.id, 'database')
      this.addAttribute(attributes, 'email', user.email, 'database')
      this.addAttribute(attributes, 'status', user.status, 'database')
      this.addAttribute(attributes, 'tenantId', user.tenantId, 'database')
    }
  }

  /**
   * Add computed attributes
   */
  private async addComputedAttributes(
    attributes: Record<string, AttributeValue>,
    entityType: string,
    entity: any
  ): Promise<void> {
    // This would implement computed attribute logic
    // For now, just add a placeholder
    this.addAttribute(attributes, 'computed_timestamp', new Date().toISOString(), 'computed')
  }

  /**
   * Resolve attributes from external providers
   */
  private async resolveFromProviders(
    attributes: Record<string, AttributeValue>,
    entityType: string,
    entityId: string,
    requiredAttributes: string[]
  ): Promise<void> {
    // This would implement external attribute provider logic
    // For now, just log the attempt
    this.logger.debug('Resolving from providers', {
      entityType,
      entityId,
      requiredAttributes
    })
  }

  /**
   * Helper method to add attribute
   */
  private addAttribute(
    attributes: Record<string, AttributeValue>,
    name: string,
    value: any,
    source: string
  ): void {
    attributes[name] = this.createAttributeValue(name, value, source)
  }

  /**
   * Create attribute value object
   */
  private createAttributeValue(
    attributeId: string,
    value: any,
    source: string
  ): AttributeValue {
    return {
      attributeId,
      value,
      source,
      timestamp: new Date()
    }
  }

  /**
   * Parse cached attribute set
   */
  private parseAttributeSet(cached: any): AttributeSet {
    // This would properly parse cached data
    return cached as AttributeSet
  }

  /**
   * Get action type based on action name
   */
  private getActionType(action: string): string {
    if (['create', 'update', 'delete'].includes(action)) {
      return 'write'
    }
    return 'read'
  }

  /**
   * Get action risk level
   */
  private getActionRisk(action: string): string {
    if (['delete', 'admin'].includes(action)) {
      return 'high'
    }
    if (['create', 'update'].includes(action)) {
      return 'medium'
    }
    return 'low'
  }

  /**
   * Check if current time is business hours
   */
  private isBusinessHours(date: Date): boolean {
    const hour = date.getHours()
    const day = date.getDay()
    return day >= 1 && day <= 5 && hour >= 9 && hour <= 17
  }

  /**
   * Check if IP is internal network
   */
  private isInternalIP(ip: string): boolean {
    // Simplified internal IP check
    return ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')
  }

  /**
   * Get device type from user agent
   */
  private getDeviceType(userAgent: string): string {
    if (userAgent.includes('Mobile')) {
      return 'mobile'
    }
    if (userAgent.includes('Tablet')) {
      return 'tablet'
    }
    return 'desktop'
  }
}
