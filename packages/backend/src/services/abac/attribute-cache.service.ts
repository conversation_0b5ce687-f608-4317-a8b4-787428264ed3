import { AttributeSet, AttributeValue } from '@/shared/types/abac'
import { CacheService } from '@/services/cache.service'
import { Logger } from '@/shared/utils/logger'

export class AttributeCache {
  private cache: CacheService
  private logger: Logger
  private hitCounts: Map<string, number> = new Map()
  private missCounts: Map<string, number> = new Map()

  constructor(cache: CacheService) {
    this.cache = cache
    this.logger = new Logger('AttributeCache')
  }

  /**
   * Get attribute set from cache
   */
  async get(key: string): Promise<AttributeSet | null> {
    try {
      const cached = await this.cache.get<AttributeSet>(key)
      
      if (cached) {
        this.incrementHitCount(key)
        this.logger.debug('Attribute cache hit', { key })
        return cached
      } else {
        this.incrementMissCount(key)
        this.logger.debug('Attribute cache miss', { key })
        return null
      }
    } catch (error) {
      this.logger.error('Attribute cache get failed', error, { key })
      this.incrementMissCount(key)
      return null
    }
  }

  /**
   * Set attribute set in cache
   */
  async set(key: string, attributeSet: AttributeSet, ttl: number): Promise<void> {
    try {
      await this.cache.set(key, attributeSet, ttl)
      this.logger.debug('Attribute cache set', { 
        key, 
        entityType: attributeSet.entityType,
        attributeCount: Object.keys(attributeSet.attributes).length,
        ttl 
      })
    } catch (error) {
      this.logger.error('Attribute cache set failed', error, { key })
    }
  }

  /**
   * Get single attribute value from cache
   */
  async getAttribute(entityId: string, attributeId: string): Promise<AttributeValue | null> {
    const key = `attr:${entityId}:${attributeId}`
    
    try {
      const cached = await this.cache.get<AttributeValue>(key)
      
      if (cached) {
        this.incrementHitCount(key)
        return cached
      } else {
        this.incrementMissCount(key)
        return null
      }
    } catch (error) {
      this.logger.error('Single attribute cache get failed', error, { key })
      this.incrementMissCount(key)
      return null
    }
  }

  /**
   * Set single attribute value in cache
   */
  async setAttribute(
    entityId: string, 
    attributeId: string, 
    value: AttributeValue, 
    ttl: number
  ): Promise<void> {
    const key = `attr:${entityId}:${attributeId}`
    
    try {
      await this.cache.set(key, value, ttl)
      this.logger.debug('Single attribute cache set', { key, ttl })
    } catch (error) {
      this.logger.error('Single attribute cache set failed', error, { key })
    }
  }

  /**
   * Delete specific cache entry
   */
  async delete(key: string): Promise<void> {
    try {
      await this.cache.delete(key)
      this.logger.debug('Attribute cache delete', { key })
    } catch (error) {
      this.logger.error('Attribute cache delete failed', error, { key })
    }
  }

  /**
   * Invalidate all attributes for an entity
   */
  async invalidateEntity(entityId: string): Promise<void> {
    try {
      const patterns = [
        `subject:${entityId}:*`,
        `resource:*:${entityId}:*`,
        `attr:${entityId}:*`
      ]

      for (const pattern of patterns) {
        await this.cache.deletePattern(pattern)
      }

      this.logger.info('Attribute cache invalidated for entity', { entityId })
    } catch (error) {
      this.logger.error('Attribute cache invalidation failed', error, { entityId })
    }
  }

  /**
   * Invalidate specific attribute across all entities
   */
  async invalidateAttribute(attributeId: string): Promise<void> {
    try {
      const pattern = `attr:*:${attributeId}`
      await this.cache.deletePattern(pattern)
      
      this.logger.debug('Attribute cache invalidated for attribute', { attributeId })
    } catch (error) {
      this.logger.error('Attribute invalidation failed', error, { attributeId })
    }
  }

  /**
   * Invalidate all subject attributes
   */
  async invalidateAllSubjects(): Promise<void> {
    try {
      await this.cache.deletePattern('subject:*')
      this.logger.info('All subject attributes invalidated')
    } catch (error) {
      this.logger.error('Subject attributes invalidation failed', error)
    }
  }

  /**
   * Invalidate all resource attributes of a specific type
   */
  async invalidateResourceType(resourceType: string): Promise<void> {
    try {
      await this.cache.deletePattern(`resource:${resourceType}:*`)
      this.logger.info('Resource type attributes invalidated', { resourceType })
    } catch (error) {
      this.logger.error('Resource type invalidation failed', error, { resourceType })
    }
  }

  /**
   * Get cache hit rate
   */
  getHitRate(): number {
    const totalHits = Array.from(this.hitCounts.values()).reduce((sum, hits) => sum + hits, 0)
    const totalMisses = Array.from(this.missCounts.values()).reduce((sum, misses) => sum + misses, 0)
    const total = totalHits + totalMisses
    
    return total > 0 ? (totalHits / total) * 100 : 0
  }

  /**
   * Get cache statistics
   */
  getStatistics(): {
    totalHits: number
    totalMisses: number
    hitRate: number
    uniqueKeys: number
  } {
    const totalHits = Array.from(this.hitCounts.values()).reduce((sum, hits) => sum + hits, 0)
    const totalMisses = Array.from(this.missCounts.values()).reduce((sum, misses) => sum + misses, 0)
    const uniqueKeys = new Set([
      ...this.hitCounts.keys(),
      ...this.missCounts.keys()
    ]).size

    return {
      totalHits,
      totalMisses,
      hitRate: this.getHitRate(),
      uniqueKeys
    }
  }

  /**
   * Clear all cache statistics
   */
  clearStatistics(): void {
    this.hitCounts.clear()
    this.missCounts.clear()
    this.logger.info('Attribute cache statistics cleared')
  }

  /**
   * Warm up cache with frequently accessed attributes
   */
  async warmUp(entities: Array<{ id: string; type: string }>): Promise<void> {
    try {
      this.logger.info('Starting attribute cache warm-up', { entityCount: entities.length })

      // This would pre-load commonly accessed attributes
      // For now, just log the attempt
      for (const entity of entities) {
        this.logger.debug('Warming up entity', { entityId: entity.id, entityType: entity.type })
      }

      this.logger.info('Attribute cache warm-up completed')
    } catch (error) {
      this.logger.error('Attribute cache warm-up failed', error)
    }
  }

  /**
   * Get cache memory usage estimation
   */
  async getMemoryUsage(): Promise<{
    estimatedSize: number
    keyCount: number
  } | null> {
    try {
      // This would depend on the cache implementation
      // For now, return estimated values based on statistics
      const stats = this.getStatistics()
      const estimatedSize = stats.uniqueKeys * 1024 // Rough estimate: 1KB per key
      
      return {
        estimatedSize,
        keyCount: stats.uniqueKeys
      }
    } catch (error) {
      this.logger.error('Failed to get cache memory usage', error)
      return null
    }
  }

  /**
   * Batch get multiple attribute sets
   */
  async batchGet(keys: string[]): Promise<Map<string, AttributeSet | null>> {
    const results = new Map<string, AttributeSet | null>()
    
    try {
      // Get all keys in parallel
      const promises = keys.map(async (key) => {
        const value = await this.get(key)
        return { key, value }
      })

      const resolved = await Promise.all(promises)
      
      resolved.forEach(({ key, value }) => {
        results.set(key, value)
      })

      this.logger.debug('Batch attribute cache get completed', { 
        requestedKeys: keys.length,
        foundKeys: resolved.filter(r => r.value !== null).length
      })
    } catch (error) {
      this.logger.error('Batch attribute cache get failed', error, { keys })
    }

    return results
  }

  /**
   * Batch set multiple attribute sets
   */
  async batchSet(entries: Array<{ key: string; value: AttributeSet; ttl: number }>): Promise<void> {
    try {
      // Set all entries in parallel
      const promises = entries.map(({ key, value, ttl }) => this.set(key, value, ttl))
      await Promise.all(promises)

      this.logger.debug('Batch attribute cache set completed', { entryCount: entries.length })
    } catch (error) {
      this.logger.error('Batch attribute cache set failed', error)
    }
  }

  /**
   * Check if attribute is cached and fresh
   */
  async isFresh(key: string, maxAge: number): Promise<boolean> {
    try {
      const cached = await this.cache.get<AttributeSet>(key)
      
      if (!cached) {
        return false
      }

      // Check if any attribute is older than maxAge
      const now = Date.now()
      const attributes = Object.values(cached.attributes)
      
      return attributes.every(attr => {
        const age = now - attr.timestamp.getTime()
        return age <= maxAge
      })
    } catch (error) {
      this.logger.error('Freshness check failed', error, { key })
      return false
    }
  }

  /**
   * Increment hit count for statistics
   */
  private incrementHitCount(key: string): void {
    const current = this.hitCounts.get(key) || 0
    this.hitCounts.set(key, current + 1)
  }

  /**
   * Increment miss count for statistics
   */
  private incrementMissCount(key: string): void {
    const current = this.missCounts.get(key) || 0
    this.missCounts.set(key, current + 1)
  }
}
