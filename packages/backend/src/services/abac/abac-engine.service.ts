import { CacheService } from '@/services/cache.service'
import {
    ABACCondition,
    ABACConfig,
    ABACContext,
    ABACPolicy,
    ABACRequest,
    ABACResult,
    ABACRule,
    AttributeExpression,
    AttributeOperator,
    AttributeValue,
    LogicalOperator
} from '@/shared/types/abac'
import { Logger } from '@/shared/utils/logger'
import { PrismaClient } from '@prisma/client'
import { performance } from 'perf_hooks'
import { AttributeCache } from './attribute-cache.service'
import { AttributeResolver } from './attribute-resolver.service'

export class ABACEngineService {
  private prisma: PrismaClient
  private attributeResolver: AttributeResolver
  private attributeCache: AttributeCache
  private logger: Logger
  private config: ABACConfig

  constructor(
    prisma: PrismaClient,
    cache: CacheService,
    config: Partial<ABACConfig> = {}
  ) {
    this.prisma = prisma
    this.attributeCache = new AttributeCache(cache)
    this.attributeResolver = new AttributeResolver(prisma, this.attributeCache)
    this.logger = new Logger('ABACEngine')
    
    // Default configuration
    this.config = {
      enableAttributeCaching: true,
      defaultCacheTimeout: 300, // 5 minutes
      maxEvaluationTime: 5000, // 5 seconds
      enableObligations: true,
      enableAdvice: true,
      attributeProviders: [],
      computedAttributes: [],
      ...config
    }
  }

  /**
   * Evaluate ABAC request and return access decision
   */
  async evaluate(request: ABACRequest, tenantId: string): Promise<ABACResult> {
    const startTime = performance.now()
    const requestId = this.generateRequestId()

    try {
      this.logger.debug('Starting ABAC evaluation', {
        requestId,
        subjectId: request.subjectId,
        resourceType: request.resourceType,
        action: request.action,
        tenantId
      })

      // Build evaluation context
      const context = await this.buildEvaluationContext(request, requestId, tenantId)

      // Get applicable policies
      const policies = await this.getApplicablePolicies(request, tenantId)

      if (policies.length === 0) {
        return {
          decision: 'not_applicable',
          matchedPolicies: [],
          obligations: [],
          advice: [],
          evaluationTime: performance.now() - startTime,
          attributesUsed: []
        }
      }

      // Evaluate policies
      const results = await Promise.all(
        policies.map(policy => this.evaluatePolicy(policy, context))
      )

      // Combine results
      const finalResult = this.combineResults(results, context)
      finalResult.evaluationTime = performance.now() - startTime

      // Check for timeout
      if (finalResult.evaluationTime > this.config.maxEvaluationTime) {
        this.logger.warn('ABAC evaluation timeout', {
          requestId,
          evaluationTime: finalResult.evaluationTime,
          maxTime: this.config.maxEvaluationTime
        })
      }

      // Log evaluation for audit
      await this.logEvaluation(request, finalResult, requestId, tenantId)

      this.logger.debug('ABAC evaluation completed', {
        requestId,
        decision: finalResult.decision,
        evaluationTime: finalResult.evaluationTime,
        matchedPolicies: finalResult.matchedPolicies.length
      })

      return finalResult
    } catch (error) {
      const evaluationTime = performance.now() - startTime
      
      this.logger.error('ABAC evaluation failed', error, {
        requestId,
        subjectId: request.subjectId,
        resourceType: request.resourceType,
        action: request.action
      })

      return {
        decision: 'indeterminate',
        matchedPolicies: [],
        obligations: [],
        advice: [],
        evaluationTime,
        errors: [{
          code: 'EVALUATION_FAILED',
          message: `ABAC evaluation failed: ${error.message}`
        }],
        attributesUsed: []
      }
    }
  }

  /**
   * Build evaluation context by resolving all required attributes
   */
  private async buildEvaluationContext(
    request: ABACRequest,
    requestId: string,
    tenantId: string
  ): Promise<ABACContext> {
    const timestamp = new Date()

    // Determine required attributes by analyzing policies
    const requiredAttributes = await this.getRequiredAttributes(request, tenantId)

    // Resolve subject attributes
    const subjectAttributes = await this.attributeResolver.resolveSubjectAttributes(
      request.subjectId,
      requiredAttributes.subject
    )

    // Resolve resource attributes
    const resourceAttributes = await this.attributeResolver.resolveResourceAttributes(
      request.resourceId || '',
      request.resourceType,
      requiredAttributes.resource
    )

    // Resolve action attributes
    const actionAttributes = await this.attributeResolver.resolveActionAttributes(
      request.action
    )

    // Resolve environment attributes
    const environmentAttributes = await this.attributeResolver.resolveEnvironmentAttributes(
      request.environment || {}
    )

    return {
      requestId,
      timestamp,
      subject: subjectAttributes,
      resource: resourceAttributes,
      action: actionAttributes,
      environment: environmentAttributes,
      metadata: request.context
    }
  }

  /**
   * Evaluate a single policy against the context
   */
  private async evaluatePolicy(
    policy: ABACPolicy,
    context: ABACContext
  ): Promise<ABACResult> {
    try {
      // Check if policy target matches
      if (!this.matchesTarget(policy.target, context)) {
        return {
          decision: 'not_applicable',
          matchedPolicies: [],
          obligations: [],
          advice: [],
          evaluationTime: 0,
          attributesUsed: []
        }
      }

      // Evaluate policy rules
      const ruleResults = await Promise.all(
        policy.rules.map(rule => this.evaluateRule(rule, context))
      )

      // Determine policy decision based on rule results
      const decision = this.combineRuleResults(ruleResults)
      const attributesUsed = this.extractAttributesUsed(context)

      const result: ABACResult = {
        decision,
        matchedPolicies: decision !== 'not_applicable' ? [{
          policyId: policy.id,
          policyName: policy.name,
          ruleId: ruleResults.find(r => r.matches)?.ruleId || '',
          effect: decision === 'permit' ? 'permit' : 'deny',
          confidence: this.calculateConfidence(ruleResults)
        }] : [],
        obligations: this.config.enableObligations ? (policy.obligations || []) : [],
        advice: this.config.enableAdvice ? (policy.advice || []) : [],
        evaluationTime: 0,
        attributesUsed
      }

      return result
    } catch (error) {
      this.logger.error('Policy evaluation failed', error, {
        policyId: policy.id,
        policyName: policy.name
      })

      return {
        decision: 'indeterminate',
        matchedPolicies: [],
        obligations: [],
        advice: [],
        evaluationTime: 0,
        errors: [{
          code: 'POLICY_EVALUATION_ERROR',
          message: `Policy evaluation failed: ${error.message}`,
          policyId: policy.id
        }],
        attributesUsed: []
      }
    }
  }

  /**
   * Evaluate a single rule against the context
   */
  private async evaluateRule(
    rule: ABACRule,
    context: ABACContext
  ): Promise<{ matches: boolean; ruleId: string; effect: string }> {
    const matches = await this.evaluateCondition(rule.condition, context)
    
    return {
      matches,
      ruleId: rule.id,
      effect: rule.effect
    }
  }

  /**
   * Evaluate a condition (simple or composite)
   */
  private async evaluateCondition(
    condition: ABACCondition,
    context: ABACContext
  ): Promise<boolean> {
    if (condition.type === 'simple') {
      // Evaluate simple expressions
      if (condition.expressions) {
        const results = await Promise.all(
          condition.expressions.map(expr => this.evaluateExpression(expr, context))
        )
        
        // Apply logical operator (default AND)
        const operator = condition.operator || LogicalOperator.AND
        return this.applyLogicalOperator(results, operator)
      }
      return false
    } else {
      // Evaluate composite conditions
      if (condition.conditions) {
        const results = await Promise.all(
          condition.conditions.map(cond => this.evaluateCondition(cond, context))
        )
        
        const operator = condition.operator || LogicalOperator.AND
        return this.applyLogicalOperator(results, operator)
      }
      return false
    }
  }

  /**
   * Evaluate an attribute expression
   */
  private async evaluateExpression(
    expression: AttributeExpression,
    context: ABACContext
  ): Promise<boolean> {
    // Get attribute value from context
    const attributeValue = this.getAttributeFromContext(expression.attributeId, context)
    
    if (!attributeValue) {
      return false
    }

    // Apply function if specified
    let actualValue = attributeValue.value
    if (expression.function) {
      actualValue = await this.applyAttributeFunction(
        expression.function,
        actualValue,
        context
      )
    }

    // Evaluate based on operator
    return this.evaluateAttributeOperator(
      actualValue,
      expression.operator,
      expression.value
    )
  }

  /**
   * Get attribute value from context
   */
  private getAttributeFromContext(
    attributeId: string,
    context: ABACContext
  ): AttributeValue | null {
    // Check all attribute sets in context
    const attributeSets = [
      context.subject,
      context.resource,
      context.action,
      context.environment
    ]

    for (const attributeSet of attributeSets) {
      if (attributeSet.attributes[attributeId]) {
        return attributeSet.attributes[attributeId]
      }
    }

    return null
  }

  /**
   * Evaluate attribute operator
   */
  private evaluateAttributeOperator(
    actualValue: any,
    operator: AttributeOperator,
    expectedValue: any
  ): boolean {
    switch (operator) {
      case AttributeOperator.EQUALS:
        return actualValue === expectedValue
      
      case AttributeOperator.NOT_EQUALS:
        return actualValue !== expectedValue
      
      case AttributeOperator.GREATER_THAN:
        return actualValue > expectedValue
      
      case AttributeOperator.LESS_THAN:
        return actualValue < expectedValue
      
      case AttributeOperator.CONTAINS:
        if (Array.isArray(actualValue)) {
          return actualValue.includes(expectedValue)
        }
        return String(actualValue).includes(String(expectedValue))
      
      case AttributeOperator.IN:
        return Array.isArray(expectedValue) && expectedValue.includes(actualValue)
      
      case AttributeOperator.MATCHES:
        try {
          const regex = new RegExp(expectedValue)
          return regex.test(String(actualValue))
        } catch {
          return false
        }
      
      case AttributeOperator.EXISTS:
        return actualValue !== null && actualValue !== undefined
      
      case AttributeOperator.IS_SUBSET_OF:
        if (Array.isArray(actualValue) && Array.isArray(expectedValue)) {
          return actualValue.every(item => expectedValue.includes(item))
        }
        return false
      
      case AttributeOperator.INTERSECTS:
        if (Array.isArray(actualValue) && Array.isArray(expectedValue)) {
          return actualValue.some(item => expectedValue.includes(item))
        }
        return false
      
      default:
        return false
    }
  }

  /**
   * Apply logical operator to boolean results
   */
  private applyLogicalOperator(results: boolean[], operator: LogicalOperator): boolean {
    switch (operator) {
      case LogicalOperator.AND:
        return results.every(result => result)
      
      case LogicalOperator.OR:
        return results.some(result => result)
      
      case LogicalOperator.NOT:
        return !results[0] // NOT applies to first result only
      
      default:
        return false
    }
  }

  /**
   * Check if policy target matches the context
   */
  private matchesTarget(target: any, context: ABACContext): boolean {
    // Simplified target matching - would be more sophisticated in practice
    return true
  }

  /**
   * Combine multiple policy results into final decision
   */
  private combineResults(results: ABACResult[], context: ABACContext): ABACResult {
    // Simplified combining algorithm - permit overrides
    const permitResults = results.filter(r => r.decision === 'permit')
    const denyResults = results.filter(r => r.decision === 'deny')

    let decision: 'permit' | 'deny' | 'not_applicable' | 'indeterminate'
    
    if (permitResults.length > 0) {
      decision = 'permit'
    } else if (denyResults.length > 0) {
      decision = 'deny'
    } else {
      decision = 'not_applicable'
    }

    // Combine matched policies
    const matchedPolicies = results.flatMap(r => r.matchedPolicies)
    
    // Combine obligations and advice
    const obligations = results.flatMap(r => r.obligations)
    const advice = results.flatMap(r => r.advice)
    
    // Combine errors
    const errors = results.flatMap(r => r.errors || [])
    
    // Combine attributes used
    const attributesUsed = [...new Set(results.flatMap(r => r.attributesUsed))]

    return {
      decision,
      matchedPolicies,
      obligations,
      advice,
      evaluationTime: 0, // Will be set by caller
      errors: errors.length > 0 ? errors : undefined,
      attributesUsed
    }
  }

  /**
   * Combine rule results into policy decision
   */
  private combineRuleResults(results: any[]): 'permit' | 'deny' | 'not_applicable' {
    const matchingRules = results.filter(r => r.matches)
    
    if (matchingRules.length === 0) {
      return 'not_applicable'
    }

    // If any rule denies, deny (deny overrides)
    if (matchingRules.some(r => r.effect === 'deny')) {
      return 'deny'
    }

    // If any rule permits, permit
    if (matchingRules.some(r => r.effect === 'permit')) {
      return 'permit'
    }

    return 'not_applicable'
  }

  /**
   * Calculate confidence score for policy match
   */
  private calculateConfidence(ruleResults: any[]): number {
    // Simplified confidence calculation
    const matchingRules = ruleResults.filter(r => r.matches)
    return matchingRules.length > 0 ? 0.9 : 0.0
  }

  /**
   * Extract list of attributes used in evaluation
   */
  private extractAttributesUsed(context: ABACContext): string[] {
    const attributes = new Set<string>()
    
    // Add all attributes from context
    Object.keys(context.subject.attributes).forEach(attr => attributes.add(attr))
    Object.keys(context.resource.attributes).forEach(attr => attributes.add(attr))
    Object.keys(context.action.attributes).forEach(attr => attributes.add(attr))
    Object.keys(context.environment.attributes).forEach(attr => attributes.add(attr))
    
    return Array.from(attributes)
  }

  /**
   * Get applicable policies for the request
   */
  private async getApplicablePolicies(
    request: ABACRequest,
    tenantId: string
  ): Promise<ABACPolicy[]> {
    // This would query the database for applicable ABAC policies
    // For now, return empty array as placeholder
    return []
  }

  /**
   * Get required attributes for evaluation
   */
  private async getRequiredAttributes(
    request: ABACRequest,
    tenantId: string
  ): Promise<{
    subject: string[]
    resource: string[]
    action: string[]
    environment: string[]
  }> {
    // This would analyze policies to determine required attributes
    // For now, return basic set
    return {
      subject: ['id', 'roles', 'department', 'clearanceLevel'],
      resource: ['id', 'type', 'owner', 'classification', 'tags'],
      action: ['name', 'type', 'risk'],
      environment: ['time', 'location', 'network', 'device']
    }
  }

  /**
   * Apply attribute function
   */
  private async applyAttributeFunction(
    func: any,
    value: any,
    context: ABACContext
  ): Promise<any> {
    // This would implement various attribute functions
    // For now, return value unchanged
    return value
  }

  /**
   * Log evaluation for audit trail
   */
  private async logEvaluation(
    request: ABACRequest,
    result: ABACResult,
    requestId: string,
    tenantId: string
  ): Promise<void> {
    try {
      // This would log to audit system
      this.logger.info('ABAC evaluation logged', {
        requestId,
        subjectId: request.subjectId,
        resourceType: request.resourceType,
        action: request.action,
        decision: result.decision,
        evaluationTime: result.evaluationTime,
        tenantId
      })
    } catch (error) {
      this.logger.error('Failed to log ABAC evaluation', error)
    }
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `abac_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}
