import { Request, Response, NextFunction } from 'express'
import helmet from 'helmet'
import cors from 'cors'
import rateLimit from 'express-rate-limit'
import slowDown from 'express-slow-down'
import { Logger } from '@/shared/utils/logger'

const logger = new Logger('SecurityMiddleware')

/**
 * CORS Configuration
 */
export const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    const allowedOrigins = [
      process.env.FRONTEND_URL || 'http://localhost:3000',
      process.env.ADMIN_URL || 'http://localhost:3001',
      'https://app.mnaplatform.com',
      'https://admin.mnaplatform.com',
      'https://staging.mnaplatform.com'
    ]

    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true)

    if (allowedOrigins.includes(origin)) {
      callback(null, true)
    } else {
      logger.warn('CORS blocked request from origin', { origin })
      callback(new Error('Not allowed by CORS'), false)
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'X-Tenant-ID',
    'X-Request-ID'
  ],
  exposedHeaders: [
    'X-Total-Count',
    'X-Page-Count',
    'X-Rate-Limit-Remaining',
    'X-Rate-Limit-Reset'
  ],
  maxAge: 86400 // 24 hours
}

/**
 * Helmet Security Configuration
 */
export const helmetOptions = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", 'wss:', 'https:'],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      manifestSrc: ["'self'"],
      workerSrc: ["'self'", 'blob:']
    }
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
}

/**
 * Rate Limiting Configuration
 */
export const createRateLimit = (windowMs: number, max: number, message?: string) => {
  return rateLimit({
    windowMs,
    max,
    message: message || 'Too many requests from this IP, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
        method: req.method
      })
      
      res.status(429).json({
        error: 'Too Many Requests',
        message: 'Rate limit exceeded',
        retryAfter: Math.round(windowMs / 1000)
      })
    },
    skip: (req: Request) => {
      // Skip rate limiting for health checks
      return req.path === '/health' || req.path === '/metrics'
    }
  })
}

/**
 * Speed Limiting Configuration
 */
export const createSpeedLimit = (windowMs: number, delayAfter: number, delayMs: number) => {
  return slowDown({
    windowMs,
    delayAfter,
    delayMs,
    maxDelayMs: 20000, // Maximum delay of 20 seconds
    skipFailedRequests: false,
    skipSuccessfulRequests: false,
    onLimitReached: (req: Request) => {
      logger.warn('Speed limit reached', {
        ip: req.ip,
        path: req.path,
        method: req.method
      })
    }
  })
}

/**
 * API Rate Limits
 */
export const apiRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  100, // 100 requests per window
  'Too many API requests'
)

export const authRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  5, // 5 login attempts per window
  'Too many authentication attempts'
)

export const uploadRateLimit = createRateLimit(
  60 * 60 * 1000, // 1 hour
  10, // 10 uploads per hour
  'Too many file uploads'
)

export const strictRateLimit = createRateLimit(
  60 * 1000, // 1 minute
  10, // 10 requests per minute
  'Rate limit exceeded for sensitive operations'
)

/**
 * Speed Limits
 */
export const apiSpeedLimit = createSpeedLimit(
  15 * 60 * 1000, // 15 minutes
  50, // Start slowing down after 50 requests
  500 // Add 500ms delay per request
)

/**
 * Security Headers Middleware
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Remove server information
  res.removeHeader('X-Powered-By')
  
  // Add custom security headers
  res.setHeader('X-Content-Type-Options', 'nosniff')
  res.setHeader('X-Frame-Options', 'DENY')
  res.setHeader('X-XSS-Protection', '1; mode=block')
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()')
  
  // Add request ID for tracking
  const requestId = req.headers['x-request-id'] || generateRequestId()
  res.setHeader('X-Request-ID', requestId)
  
  next()
}

/**
 * API Key Validation Middleware
 */
export const validateApiKey = (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.headers['x-api-key'] as string
  
  if (!apiKey) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'API key is required'
    })
  }
  
  // Validate API key format
  if (!isValidApiKeyFormat(apiKey)) {
    logger.warn('Invalid API key format', {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    })
    
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid API key format'
    })
  }
  
  // Add API key to request for further validation
  req.apiKey = apiKey
  next()
}

/**
 * IP Whitelist Middleware
 */
export const createIpWhitelist = (allowedIps: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIp = getClientIp(req)
    
    if (!allowedIps.includes(clientIp)) {
      logger.warn('IP not whitelisted', {
        ip: clientIp,
        path: req.path,
        method: req.method
      })
      
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Access denied from this IP address'
      })
    }
    
    next()
  }
}

/**
 * Request Size Limit Middleware
 */
export const requestSizeLimit = (maxSize: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = req.headers['content-length']
    
    if (contentLength) {
      const sizeInBytes = parseInt(contentLength, 10)
      const maxSizeInBytes = parseSize(maxSize)
      
      if (sizeInBytes > maxSizeInBytes) {
        logger.warn('Request size limit exceeded', {
          ip: req.ip,
          size: sizeInBytes,
          maxSize: maxSizeInBytes,
          path: req.path
        })
        
        return res.status(413).json({
          error: 'Payload Too Large',
          message: `Request size exceeds limit of ${maxSize}`
        })
      }
    }
    
    next()
  }
}

/**
 * SQL Injection Protection Middleware
 */
export const sqlInjectionProtection = (req: Request, res: Response, next: NextFunction) => {
  const suspiciousPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
    /(--|\/\*|\*\/|;|'|"|`)/g,
    /(\bOR\b|\bAND\b).*?[=<>]/gi
  ]
  
  const checkForSqlInjection = (obj: any, path = ''): boolean => {
    if (typeof obj === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(obj))
    }
    
    if (typeof obj === 'object' && obj !== null) {
      for (const [key, value] of Object.entries(obj)) {
        if (checkForSqlInjection(value, `${path}.${key}`)) {
          return true
        }
      }
    }
    
    return false
  }
  
  // Check query parameters, body, and headers
  const sources = [req.query, req.body, req.params]
  
  for (const source of sources) {
    if (checkForSqlInjection(source)) {
      logger.warn('Potential SQL injection attempt detected', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
        method: req.method,
        source
      })
      
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid input detected'
      })
    }
  }
  
  next()
}

/**
 * XSS Protection Middleware
 */
export const xssProtection = (req: Request, res: Response, next: NextFunction) => {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<img[^>]+src[^>]*>/gi
  ]
  
  const sanitizeValue = (value: any): any => {
    if (typeof value === 'string') {
      return xssPatterns.reduce((acc, pattern) => {
        return acc.replace(pattern, '')
      }, value)
    }
    
    if (typeof value === 'object' && value !== null) {
      const sanitized: any = Array.isArray(value) ? [] : {}
      for (const [key, val] of Object.entries(value)) {
        sanitized[key] = sanitizeValue(val)
      }
      return sanitized
    }
    
    return value
  }
  
  // Sanitize request data
  req.body = sanitizeValue(req.body)
  req.query = sanitizeValue(req.query)
  req.params = sanitizeValue(req.params)
  
  next()
}

/**
 * Helper Functions
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

function isValidApiKeyFormat(apiKey: string): boolean {
  // API key should be at least 32 characters and contain only alphanumeric characters
  return /^[a-zA-Z0-9]{32,}$/.test(apiKey)
}

function getClientIp(req: Request): string {
  return (
    req.headers['x-forwarded-for'] as string ||
    req.headers['x-real-ip'] as string ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    req.ip ||
    'unknown'
  ).split(',')[0].trim()
}

function parseSize(size: string): number {
  const units: Record<string, number> = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  }
  
  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/)
  if (!match) return 0
  
  const value = parseFloat(match[1])
  const unit = match[2] || 'b'
  
  return value * units[unit]
}

/**
 * Security Middleware Stack
 */
export const securityMiddlewareStack = [
  helmet(helmetOptions),
  cors(corsOptions),
  securityHeaders,
  apiSpeedLimit,
  apiRateLimit,
  requestSizeLimit('10mb'),
  sqlInjectionProtection,
  xssProtection
]

/**
 * Authentication Security Stack
 */
export const authSecurityStack = [
  helmet(helmetOptions),
  cors(corsOptions),
  securityHeaders,
  authRateLimit,
  requestSizeLimit('1mb'),
  sqlInjectionProtection,
  xssProtection
]

/**
 * Upload Security Stack
 */
export const uploadSecurityStack = [
  helmet(helmetOptions),
  cors(corsOptions),
  securityHeaders,
  uploadRateLimit,
  requestSizeLimit('100mb'),
  sqlInjectionProtection
]

/**
 * Admin Security Stack
 */
export const adminSecurityStack = [
  helmet(helmetOptions),
  cors(corsOptions),
  securityHeaders,
  strictRateLimit,
  requestSizeLimit('5mb'),
  sqlInjectionProtection,
  xssProtection,
  validateApiKey
]

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      apiKey?: string
    }
  }
}
