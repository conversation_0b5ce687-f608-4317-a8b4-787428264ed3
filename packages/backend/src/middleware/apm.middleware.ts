import { Request, Response, NextFunction } from 'express'
import { APMService } from '@/services/monitoring/apm.service'
import { Logger } from '@/shared/utils/logger'

// Extend Express Request to include APM context
declare global {
  namespace Express {
    interface Request {
      apm?: {
        transactionId: string
        startTime: number
        spanId?: string
      }
    }
  }
}

export class APMMiddleware {
  private apmService: APMService
  private logger: Logger

  constructor(apmService: APMService) {
    this.apmService = apmService
    this.logger = new Logger('APMMiddleware')
  }

  /**
   * Main APM middleware for request tracking
   */
  trackRequest() {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now()
      
      // Generate request ID if not present
      const requestId = req.headers['x-request-id'] as string || this.generateRequestId()
      
      // Start transaction
      const transactionId = this.apmService.startTransaction(
        `${req.method} ${req.route?.path || req.path}`,
        'web',
        {
          method: req.method,
          url: req.url,
          userAgent: req.headers['user-agent'],
          ip: req.ip,
          requestId
        }
      )

      // Add APM context to request
      req.apm = {
        transactionId,
        startTime
      }

      // Track response
      const originalSend = res.send
      res.send = function(body) {
        const responseTime = Date.now() - startTime
        
        // Record request metrics
        apmService.recordRequest({
          requestId,
          method: req.method,
          url: req.url,
          statusCode: res.statusCode,
          responseTime,
          userAgent: req.headers['user-agent'] as string,
          userId: (req as any).user?.id,
          tenantId: (req as any).user?.tenantId
        })

        // End transaction
        const status = res.statusCode >= 400 ? 'failed' : 'completed'
        apmService.endTransaction(transactionId, status)

        return originalSend.call(this, body)
      }

      // Handle errors
      const originalNext = next
      next = (error?: any) => {
        if (error) {
          // Record error metrics
          apmService.recordRequest({
            requestId,
            method: req.method,
            url: req.url,
            statusCode: 500,
            responseTime: Date.now() - startTime,
            userAgent: req.headers['user-agent'] as string,
            userId: (req as any).user?.id,
            tenantId: (req as any).user?.tenantId,
            errorMessage: error.message,
            stackTrace: error.stack
          })

          // End transaction with error
          apmService.endTransaction(transactionId, 'failed')
        }
        
        return originalNext(error)
      }

      next()
    }
  }

  /**
   * Database operation tracking middleware
   */
  trackDatabaseOperation(operationName: string) {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.apm) return next()

      const spanId = this.apmService.addSpan(
        req.apm.transactionId,
        `db.${operationName}`,
        {
          'db.type': 'postgresql',
          'db.operation': operationName
        }
      )

      // Store span ID for ending later
      req.apm.spanId = spanId

      // Override next to end span
      const originalNext = next
      next = (error?: any) => {
        this.apmService.endSpan(req.apm!.transactionId, spanId)
        
        if (error) {
          this.apmService.addSpanLog(
            req.apm!.transactionId,
            spanId,
            'error',
            'Database operation failed',
            { error: error.message }
          )
        }
        
        return originalNext(error)
      }

      next()
    }
  }

  /**
   * External API call tracking
   */
  trackExternalCall(serviceName: string, operation: string) {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.apm) return next()

      const spanId = this.apmService.addSpan(
        req.apm.transactionId,
        `external.${serviceName}.${operation}`,
        {
          'external.service': serviceName,
          'external.operation': operation
        }
      )

      // Add to request context
      req.apm.spanId = spanId

      next()
    }
  }

  /**
   * Cache operation tracking
   */
  trackCacheOperation(operation: 'get' | 'set' | 'delete') {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.apm) return next()

      const spanId = this.apmService.addSpan(
        req.apm.transactionId,
        `cache.${operation}`,
        {
          'cache.operation': operation
        }
      )

      req.apm.spanId = spanId

      next()
    }
  }

  /**
   * Custom operation tracking
   */
  trackCustomOperation(operationName: string, tags: Record<string, any> = {}) {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.apm) return next()

      const spanId = this.apmService.addSpan(
        req.apm.transactionId,
        operationName,
        tags
      )

      req.apm.spanId = spanId

      next()
    }
  }

  /**
   * Error tracking middleware
   */
  trackErrors() {
    return (error: Error, req: Request, res: Response, next: NextFunction) => {
      if (req.apm) {
        // Add error log to current span
        if (req.apm.spanId) {
          this.apmService.addSpanLog(
            req.apm.transactionId,
            req.apm.spanId,
            'error',
            error.message,
            {
              stack: error.stack,
              name: error.name
            }
          )
        }

        // End transaction with error
        this.apmService.endTransaction(req.apm.transactionId, 'failed')
      }

      next(error)
    }
  }

  /**
   * Performance monitoring for specific routes
   */
  monitorPerformance(thresholds: { warning: number; critical: number }) {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.apm) return next()

      const originalSend = res.send
      res.send = function(body) {
        const responseTime = Date.now() - req.apm!.startTime

        // Check performance thresholds
        if (responseTime > thresholds.critical) {
          apmService.addSpanLog(
            req.apm!.transactionId,
            req.apm!.spanId || 'root',
            'error',
            'Critical performance threshold exceeded',
            {
              responseTime,
              threshold: thresholds.critical,
              url: req.url
            }
          )
        } else if (responseTime > thresholds.warning) {
          apmService.addSpanLog(
            req.apm!.transactionId,
            req.apm!.spanId || 'root',
            'warn',
            'Performance warning threshold exceeded',
            {
              responseTime,
              threshold: thresholds.warning,
              url: req.url
            }
          )
        }

        return originalSend.call(this, body)
      }

      next()
    }
  }

  private generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}

// Helper functions for manual instrumentation
export class APMInstrumentation {
  private apmService: APMService

  constructor(apmService: APMService) {
    this.apmService = apmService
  }

  /**
   * Instrument a function with APM tracking
   */
  instrument<T extends (...args: any[]) => any>(
    fn: T,
    operationName: string,
    tags: Record<string, any> = {}
  ): T {
    return ((...args: any[]) => {
      const transactionId = this.apmService.startTransaction(operationName, 'background', tags)
      
      try {
        const result = fn(...args)
        
        // Handle promises
        if (result && typeof result.then === 'function') {
          return result
            .then((value: any) => {
              this.apmService.endTransaction(transactionId, 'completed')
              return value
            })
            .catch((error: any) => {
              this.apmService.endTransaction(transactionId, 'failed')
              throw error
            })
        }
        
        // Handle synchronous functions
        this.apmService.endTransaction(transactionId, 'completed')
        return result
      } catch (error) {
        this.apmService.endTransaction(transactionId, 'failed')
        throw error
      }
    }) as T
  }

  /**
   * Instrument a class method
   */
  instrumentMethod(
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor,
    operationName?: string
  ) {
    const originalMethod = descriptor.value
    const className = target.constructor.name
    const methodName = operationName || `${className}.${propertyName}`

    descriptor.value = function(...args: any[]) {
      const transactionId = apmService.startTransaction(methodName, 'background', {
        class: className,
        method: propertyName
      })

      try {
        const result = originalMethod.apply(this, args)
        
        if (result && typeof result.then === 'function') {
          return result
            .then((value: any) => {
              apmService.endTransaction(transactionId, 'completed')
              return value
            })
            .catch((error: any) => {
              apmService.endTransaction(transactionId, 'failed')
              throw error
            })
        }
        
        apmService.endTransaction(transactionId, 'completed')
        return result
      } catch (error) {
        apmService.endTransaction(transactionId, 'failed')
        throw error
      }
    }

    return descriptor
  }

  /**
   * Create a span within current transaction
   */
  createSpan(req: Request, operationName: string, tags: Record<string, any> = {}): string | null {
    if (!req.apm) return null
    
    return this.apmService.addSpan(req.apm.transactionId, operationName, tags)
  }

  /**
   * End a span
   */
  endSpan(req: Request, spanId: string): void {
    if (!req.apm || !spanId) return
    
    this.apmService.endSpan(req.apm.transactionId, spanId)
  }

  /**
   * Add log to current span
   */
  addLog(req: Request, level: 'info' | 'warn' | 'error' | 'debug', message: string, fields?: Record<string, any>): void {
    if (!req.apm || !req.apm.spanId) return
    
    this.apmService.addSpanLog(req.apm.transactionId, req.apm.spanId, level, message, fields)
  }
}

// Decorator for automatic method instrumentation
export function APMTrace(operationName?: string) {
  return function(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const instrumentation = new APMInstrumentation(apmService)
    return instrumentation.instrumentMethod(target, propertyName, descriptor, operationName)
  }
}

// Global APM service instance (would be properly initialized in app setup)
let apmService: APMService

export function initializeAPM(service: APMService) {
  apmService = service
}

export { apmService }
