import { Request, Response, NextFunction } from 'express'
import { PrismaClient } from '@prisma/client'
import { ABACEngineService } from '@/services/abac/abac-engine.service'
import { CacheService } from '@/services/cache.service'
import { 
  ABACRequest,
  ABACResult
} from '@/shared/types/abac'
import { ForbiddenError, UnauthorizedError } from '@/shared/errors'
import { Logger } from '@/shared/utils/logger'

// Extend Express Request to include ABAC context
declare global {
  namespace Express {
    interface Request {
      abac?: {
        evaluate: (request: ABACRequest) => Promise<ABACResult>
        checkAccess: (resourceType: string, action: string, resourceId?: string) => Promise<boolean>
        requireAccess: (resourceType: string, action: string, resourceId?: string) => Promise<void>
      }
    }
  }
}

export interface ABACMiddlewareOptions {
  resourceType: string
  action: string
  resourceIdParam?: string // Parameter name for resource ID (e.g., 'dealId')
  optional?: boolean // If true, don't throw error on access denial
  fallbackToRBAC?: boolean // If true, fall back to RBAC if ABAC is not applicable
}

export class ABACMiddleware {
  private abacEngine: ABACEngineService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.abacEngine = new ABACEngineService(prisma, cache)
    this.logger = new Logger('ABACMiddleware')
  }

  /**
   * Initialize ABAC context for authenticated requests
   */
  initializeContext() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        // Ensure user is authenticated
        if (!req.user || !req.tenant) {
          throw new UnauthorizedError('Authentication required for ABAC')
        }

        // Add ABAC helper methods to request
        req.abac = {
          evaluate: async (request: ABACRequest) => {
            return this.abacEngine.evaluate(request, req.tenant!.id)
          },
          
          checkAccess: async (resourceType: string, action: string, resourceId?: string) => {
            const request: ABACRequest = {
              subjectId: req.user!.id,
              resourceType,
              action,
              resourceId,
              environment: {
                ipAddress: req.ip,
                userAgent: req.headers['user-agent'],
                timestamp: new Date()
              },
              context: {
                requestId: req.headers['x-request-id'] as string,
                sessionId: req.headers['x-session-id'] as string
              }
            }

            const result = await this.abacEngine.evaluate(request, req.tenant!.id)
            return result.decision === 'permit'
          },

          requireAccess: async (resourceType: string, action: string, resourceId?: string) => {
            const hasAccess = await req.abac!.checkAccess(resourceType, action, resourceId)
            if (!hasAccess) {
              throw new ForbiddenError(`Access denied to ${action} ${resourceType}`)
            }
          }
        }

        next()
      } catch (error) {
        this.logger.error('Failed to initialize ABAC context', error, {
          userId: req.user?.id,
          tenantId: req.tenant?.id
        })
        next(error)
      }
    }
  }

  /**
   * Require specific access for route
   */
  requireAccess(options: ABACMiddlewareOptions) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.abac) {
          throw new UnauthorizedError('ABAC context not initialized')
        }

        // Build ABAC request
        const resourceId = options.resourceIdParam ? req.params[options.resourceIdParam] : undefined
        
        const abacRequest: ABACRequest = {
          subjectId: req.user!.id,
          resourceType: options.resourceType,
          action: options.action,
          resourceId,
          environment: {
            ipAddress: req.ip,
            userAgent: req.headers['user-agent'],
            timestamp: new Date(),
            method: req.method,
            path: req.path,
            query: req.query
          },
          context: {
            requestId: req.headers['x-request-id'] as string,
            sessionId: req.headers['x-session-id'] as string,
            tenantId: req.tenant!.id
          }
        }

        // Evaluate ABAC request
        const result = await this.abacEngine.evaluate(abacRequest, req.tenant!.id)

        // Handle result
        if (result.decision === 'permit') {
          // Access granted
          req.abacResult = result
          return next()
        }

        if (result.decision === 'not_applicable' && options.fallbackToRBAC) {
          // Fall back to RBAC if ABAC is not applicable
          this.logger.debug('ABAC not applicable, falling back to RBAC', {
            userId: req.user!.id,
            resourceType: options.resourceType,
            action: options.action
          })
          return next() // Let RBAC middleware handle it
        }

        if (options.optional) {
          // Add result to request for later use
          req.abacResult = result
          return next()
        }

        // Access denied
        const reason = result.errors?.[0]?.message || 'Access denied by ABAC policy'
        
        this.logger.warn('ABAC access denied', {
          userId: req.user!.id,
          tenantId: req.tenant!.id,
          resourceType: options.resourceType,
          action: options.action,
          resourceId,
          decision: result.decision,
          reason
        })

        throw new ForbiddenError(reason)
      } catch (error) {
        this.logger.error('ABAC access check failed', error, {
          userId: req.abac?.checkAccess ? req.user?.id : 'unknown',
          tenantId: req.tenant?.id,
          resourceType: options.resourceType,
          action: options.action
        })
        next(error)
      }
    }
  }

  /**
   * Check multiple access requirements (all must pass)
   */
  requireAllAccess(checks: ABACMiddlewareOptions[]) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.abac) {
          throw new UnauthorizedError('ABAC context not initialized')
        }

        for (const options of checks) {
          const resourceId = options.resourceIdParam ? req.params[options.resourceIdParam] : undefined
          
          const hasAccess = await req.abac.checkAccess(
            options.resourceType,
            options.action,
            resourceId
          )

          if (!hasAccess) {
            throw new ForbiddenError(
              `Access denied for ${options.action} on ${options.resourceType}`
            )
          }
        }

        next()
      } catch (error) {
        next(error)
      }
    }
  }

  /**
   * Check multiple access requirements (any can pass)
   */
  requireAnyAccess(checks: ABACMiddlewareOptions[]) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.abac) {
          throw new UnauthorizedError('ABAC context not initialized')
        }

        let hasAccess = false
        const errors: string[] = []

        for (const options of checks) {
          try {
            const resourceId = options.resourceIdParam ? req.params[options.resourceIdParam] : undefined
            
            const access = await req.abac.checkAccess(
              options.resourceType,
              options.action,
              resourceId
            )

            if (access) {
              hasAccess = true
              break
            } else {
              errors.push(`${options.action} on ${options.resourceType}`)
            }
          } catch (error) {
            errors.push(`${options.action} on ${options.resourceType}: ${error.message}`)
          }
        }

        if (!hasAccess) {
          throw new ForbiddenError(`Access denied. Tried: ${errors.join(', ')}`)
        }

        next()
      } catch (error) {
        next(error)
      }
    }
  }

  /**
   * Conditional access based on resource ownership or higher permissions
   */
  requireOwnershipOrAccess(options: ABACMiddlewareOptions) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.abac) {
          throw new UnauthorizedError('ABAC context not initialized')
        }

        const resourceId = options.resourceIdParam ? req.params[options.resourceIdParam] : undefined

        // First check if user has general access
        const hasGeneralAccess = await req.abac.checkAccess(
          options.resourceType,
          options.action,
          resourceId
        )

        if (hasGeneralAccess) {
          return next()
        }

        // If no general access, check ownership-based access
        const hasOwnershipAccess = await req.abac.checkAccess(
          options.resourceType,
          `${options.action}_own`,
          resourceId
        )

        if (!hasOwnershipAccess) {
          throw new ForbiddenError('Access denied: insufficient privileges or not owner')
        }

        next()
      } catch (error) {
        next(error)
      }
    }
  }

  /**
   * Log access attempts for audit
   */
  logAccess(options: { includeSuccess?: boolean; includeFailure?: boolean } = {}) {
    const { includeSuccess = true, includeFailure = true } = options

    return async (req: Request, res: Response, next: NextFunction) => {
      const originalNext = next

      // Override next to capture the result
      const wrappedNext = (error?: any) => {
        if (error && includeFailure) {
          this.logger.warn('ABAC access attempt failed', {
            userId: req.user?.id,
            tenantId: req.tenant?.id,
            method: req.method,
            path: req.path,
            error: error.message,
            timestamp: new Date()
          })
        } else if (!error && includeSuccess) {
          this.logger.info('ABAC access granted', {
            userId: req.user?.id,
            tenantId: req.tenant?.id,
            method: req.method,
            path: req.path,
            timestamp: new Date()
          })
        }

        originalNext(error)
      }

      // Continue with wrapped next
      next = wrappedNext
      next()
    }
  }
}

// Convenience functions for common ABAC patterns
export const abacMiddleware = (prisma: PrismaClient, cache: CacheService) => {
  const abac = new ABACMiddleware(prisma, cache)

  return {
    // Initialize ABAC context
    init: abac.initializeContext(),

    // Resource-specific access checks
    deals: {
      read: abac.requireAccess({ resourceType: 'deals', action: 'read' }),
      create: abac.requireAccess({ resourceType: 'deals', action: 'create' }),
      update: abac.requireOwnershipOrAccess({ 
        resourceType: 'deals', 
        action: 'update',
        resourceIdParam: 'dealId'
      }),
      delete: abac.requireAccess({ resourceType: 'deals', action: 'delete' }),
      manage: abac.requireAccess({ resourceType: 'deals', action: 'manage' })
    },

    documents: {
      read: abac.requireAccess({ resourceType: 'documents', action: 'read' }),
      create: abac.requireAccess({ resourceType: 'documents', action: 'create' }),
      update: abac.requireOwnershipOrAccess({ 
        resourceType: 'documents', 
        action: 'update',
        resourceIdParam: 'documentId'
      }),
      delete: abac.requireAccess({ resourceType: 'documents', action: 'delete' }),
      upload: abac.requireAccess({ resourceType: 'documents', action: 'upload' }),
      download: abac.requireAccess({ resourceType: 'documents', action: 'download' })
    },

    users: {
      read: abac.requireAccess({ resourceType: 'users', action: 'read' }),
      create: abac.requireAccess({ resourceType: 'users', action: 'create' }),
      update: abac.requireAccess({ resourceType: 'users', action: 'update' }),
      delete: abac.requireAccess({ resourceType: 'users', action: 'delete' })
    },

    // Custom access checker
    require: (options: ABACMiddlewareOptions) => abac.requireAccess(options),
    requireAll: (checks: ABACMiddlewareOptions[]) => abac.requireAllAccess(checks),
    requireAny: (checks: ABACMiddlewareOptions[]) => abac.requireAnyAccess(checks),
    
    // Audit logging
    logAccess: abac.logAccess()
  }
}
