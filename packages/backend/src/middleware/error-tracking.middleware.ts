import { Request, Response, NextFunction } from 'express'
import { ErrorTrackingService } from '@/services/monitoring/error-tracking.service'
import { Logger } from '@/shared/utils/logger'

export class ErrorTrackingMiddleware {
  private errorTrackingService: ErrorTrackingService
  private logger: Logger

  constructor(errorTrackingService: ErrorTrackingService) {
    this.errorTrackingService = errorTrackingService
    this.logger = new Logger('ErrorTrackingMiddleware')
  }

  /**
   * Middleware to capture request context for error tracking
   */
  captureRequestContext() {
    return (req: Request, res: Response, next: NextFunction) => {
      // Add error tracking context to request
      req.errorContext = {
        url: req.url,
        method: req.method,
        userAgent: req.headers['user-agent'] as string,
        ip: req.ip,
        headers: this.sanitizeHeaders(req.headers),
        userId: (req as any).user?.id,
        tenantId: (req as any).user?.tenantId,
        sessionId: req.sessionID,
        requestId: req.headers['x-request-id'] as string || this.generateRequestId()
      }

      next()
    }
  }

  /**
   * Global error handler middleware
   */
  handleErrors() {
    return (error: Error, req: Request, res: Response, next: NextFunction) => {
      try {
        // Capture the error
        const errorId = this.errorTrackingService.captureException(error, {
          ...req.errorContext,
          tags: {
            route: req.route?.path || req.path,
            handler: 'express_error_handler'
          },
          extra: {
            body: this.sanitizeRequestBody(req.body),
            query: req.query,
            params: req.params
          }
        })

        this.logger.error('Unhandled error captured', {
          errorId,
          message: error.message,
          stack: error.stack,
          url: req.url,
          method: req.method,
          userId: req.errorContext?.userId,
          tenantId: req.errorContext?.tenantId
        })

        // Send error response
        if (!res.headersSent) {
          const statusCode = this.getErrorStatusCode(error)
          res.status(statusCode).json({
            success: false,
            error: process.env.NODE_ENV === 'production' 
              ? 'Internal server error' 
              : error.message,
            errorId: process.env.NODE_ENV !== 'production' ? errorId : undefined
          })
        }
      } catch (trackingError) {
        this.logger.error('Failed to track error', trackingError)
        
        // Fallback error response
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            error: 'Internal server error'
          })
        }
      }
    }
  }

  /**
   * Middleware to capture 404 errors
   */
  handle404() {
    return (req: Request, res: Response, next: NextFunction) => {
      const error = new Error(`Route not found: ${req.method} ${req.url}`)
      
      this.errorTrackingService.captureError(error, {
        ...req.errorContext,
        level: 'warning',
        tags: {
          type: '404',
          route: req.url
        }
      })

      res.status(404).json({
        success: false,
        error: 'Route not found'
      })
    }
  }

  /**
   * Middleware to capture validation errors
   */
  captureValidationErrors() {
    return (req: Request, res: Response, next: NextFunction) => {
      const originalSend = res.send

      res.send = function(body) {
        // Check if this is a validation error response
        if (res.statusCode === 400 && body) {
          try {
            const parsedBody = typeof body === 'string' ? JSON.parse(body) : body
            
            if (parsedBody.errors || parsedBody.validationErrors) {
              errorTrackingService.captureError('Validation Error', {
                ...req.errorContext,
                level: 'warning',
                tags: {
                  type: 'validation',
                  route: req.route?.path || req.path
                },
                extra: {
                  validationErrors: parsedBody.errors || parsedBody.validationErrors,
                  requestBody: sanitizeRequestBody(req.body)
                }
              })
            }
          } catch (parseError) {
            // Ignore parsing errors
          }
        }

        return originalSend.call(this, body)
      }

      next()
    }
  }

  /**
   * Middleware to capture slow requests
   */
  captureSlowRequests(threshold: number = 5000) {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now()

      const originalSend = res.send
      res.send = function(body) {
        const duration = Date.now() - startTime

        if (duration > threshold) {
          errorTrackingService.captureMessage(
            `Slow request detected: ${req.method} ${req.url}`,
            'warning',
            {
              ...req.errorContext,
              tags: {
                type: 'performance',
                route: req.route?.path || req.path
              },
              extra: {
                duration,
                threshold,
                statusCode: res.statusCode
              }
            }
          )
        }

        return originalSend.call(this, body)
      }

      next()
    }
  }

  /**
   * Middleware to capture database errors
   */
  captureDatabaseErrors() {
    return (req: Request, res: Response, next: NextFunction) => {
      // Store original next function
      const originalNext = next

      // Override next to capture database errors
      next = (error?: any) => {
        if (error && this.isDatabaseError(error)) {
          this.errorTrackingService.captureException(error, {
            ...req.errorContext,
            tags: {
              type: 'database',
              operation: this.extractDatabaseOperation(error),
              route: req.route?.path || req.path
            },
            extra: {
              query: this.extractDatabaseQuery(error),
              constraint: this.extractDatabaseConstraint(error)
            }
          })
        }

        return originalNext(error)
      }

      next()
    }
  }

  /**
   * Middleware to capture authentication errors
   */
  captureAuthErrors() {
    return (req: Request, res: Response, next: NextFunction) => {
      const originalSend = res.send

      res.send = function(body) {
        if (res.statusCode === 401 || res.statusCode === 403) {
          errorTrackingService.captureMessage(
            `Authentication/Authorization error: ${res.statusCode}`,
            'warning',
            {
              ...req.errorContext,
              tags: {
                type: 'auth',
                statusCode: res.statusCode.toString(),
                route: req.route?.path || req.path
              },
              extra: {
                authHeader: req.headers.authorization ? 'present' : 'missing',
                userAgent: req.headers['user-agent']
              }
            }
          )
        }

        return originalSend.call(this, body)
      }

      next()
    }
  }

  /**
   * Helper methods
   */
  private sanitizeHeaders(headers: any): Record<string, string> {
    const sanitized: Record<string, string> = {}
    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token']

    for (const [key, value] of Object.entries(headers)) {
      if (sensitiveHeaders.includes(key.toLowerCase())) {
        sanitized[key] = '[REDACTED]'
      } else {
        sanitized[key] = String(value)
      }
    }

    return sanitized
  }

  private sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body
    }

    const sanitized = { ...body }
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'apiKey', 'accessToken']

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]'
      }
    }

    return sanitized
  }

  private getErrorStatusCode(error: any): number {
    if (error.statusCode) return error.statusCode
    if (error.status) return error.status
    if (error.name === 'ValidationError') return 400
    if (error.name === 'UnauthorizedError') return 401
    if (error.name === 'ForbiddenError') return 403
    if (error.name === 'NotFoundError') return 404
    return 500
  }

  private isDatabaseError(error: any): boolean {
    if (!error) return false
    
    // Check for common database error patterns
    const dbErrorPatterns = [
      'PrismaClientKnownRequestError',
      'PrismaClientUnknownRequestError',
      'PrismaClientValidationError',
      'SequelizeError',
      'MongoError',
      'PostgresError'
    ]

    return dbErrorPatterns.some(pattern => 
      error.constructor.name.includes(pattern) || 
      error.name?.includes(pattern) ||
      error.code?.startsWith('P') // Prisma error codes
    )
  }

  private extractDatabaseOperation(error: any): string {
    if (error.meta?.target) {
      return `constraint_violation:${error.meta.target}`
    }
    if (error.code) {
      return `error_code:${error.code}`
    }
    return 'unknown'
  }

  private extractDatabaseQuery(error: any): string | undefined {
    // Extract query information if available
    if (error.meta?.query) {
      return error.meta.query
    }
    if (error.sql) {
      return error.sql
    }
    return undefined
  }

  private extractDatabaseConstraint(error: any): string | undefined {
    if (error.meta?.constraint) {
      return error.meta.constraint
    }
    if (error.constraint) {
      return error.constraint
    }
    return undefined
  }

  private generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      errorContext?: {
        url: string
        method: string
        userAgent: string
        ip: string
        headers: Record<string, string>
        userId?: string
        tenantId?: string
        sessionId?: string
        requestId: string
      }
    }
  }
}

// Global error tracking service instance
let errorTrackingService: ErrorTrackingService

export function initializeErrorTracking(service: ErrorTrackingService) {
  errorTrackingService = service
}

// Helper functions for manual error tracking
export function captureError(error: Error | string, context?: any): string {
  if (!errorTrackingService) {
    console.error('Error tracking service not initialized')
    return 'not-initialized'
  }
  
  return errorTrackingService.captureError(error, context)
}

export function captureException(error: Error, context?: any): string {
  if (!errorTrackingService) {
    console.error('Error tracking service not initialized')
    return 'not-initialized'
  }
  
  return errorTrackingService.captureException(error, context)
}

export function captureMessage(message: string, level: 'error' | 'warning' | 'info' | 'debug' = 'info', context?: any): string {
  if (!errorTrackingService) {
    console.error('Error tracking service not initialized')
    return 'not-initialized'
  }
  
  return errorTrackingService.captureMessage(message, level, context)
}

export { errorTrackingService }
