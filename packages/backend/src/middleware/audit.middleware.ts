import { Request, Response, NextFunction } from 'express'
import { PrismaClient } from '@prisma/client'
import { 
  AuditLoggerService, 
  AuditEventType, 
  AuditCategory, 
  AuditSeverity 
} from '@/services/audit/audit-logger.service'
import { CacheService } from '@/services/cache.service'
import { Logger } from '@/shared/utils/logger'

export interface AuditMiddlewareOptions {
  eventType?: AuditEventType
  category?: AuditCategory
  action?: string
  resourceType?: string
  resourceIdParam?: string
  severity?: AuditSeverity
  logSuccess?: boolean
  logFailure?: boolean
  includeRequestBody?: boolean
  includeResponseBody?: boolean
  sensitiveFields?: string[]
}

export class AuditMiddleware {
  private auditLogger: AuditLoggerService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.auditLogger = new AuditLoggerService(prisma, cache)
    this.logger = new Logger('AuditMiddleware')
  }

  /**
   * General audit logging middleware
   */
  logRequest(options: AuditMiddlewareOptions = {}) {
    return async (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now()
      
      // Store original response methods
      const originalSend = res.send
      const originalJson = res.json
      
      let responseBody: any
      let statusCode: number

      // Override response methods to capture response
      res.send = function(body: any) {
        responseBody = body
        statusCode = res.statusCode
        return originalSend.call(this, body)
      }

      res.json = function(body: any) {
        responseBody = body
        statusCode = res.statusCode
        return originalJson.call(this, body)
      }

      // Continue with request processing
      next()

      // Log after response is sent
      res.on('finish', async () => {
        try {
          await this.logRequestEvent(req, res, options, {
            responseBody,
            statusCode,
            duration: Date.now() - startTime
          })
        } catch (error) {
          this.logger.error('Failed to log audit event', error)
        }
      })
    }
  }

  /**
   * Log authentication events
   */
  logAuthentication(action: string = 'login') {
    return this.logRequest({
      eventType: AuditEventType.AUTHENTICATION,
      category: AuditCategory.SECURITY,
      action,
      resourceType: 'user',
      severity: AuditSeverity.MEDIUM,
      logSuccess: true,
      logFailure: true
    })
  }

  /**
   * Log authorization events
   */
  logAuthorization(resourceType: string, action: string = 'access') {
    return this.logRequest({
      eventType: AuditEventType.AUTHORIZATION,
      category: AuditCategory.ACCESS_CONTROL,
      action,
      resourceType,
      severity: AuditSeverity.LOW,
      logSuccess: false, // Only log failures for authorization
      logFailure: true
    })
  }

  /**
   * Log data access events
   */
  logDataAccess(resourceType: string, action: string = 'read', resourceIdParam?: string) {
    return this.logRequest({
      eventType: AuditEventType.DATA_ACCESS,
      category: AuditCategory.DATA_PROTECTION,
      action,
      resourceType,
      resourceIdParam,
      severity: AuditSeverity.LOW,
      logSuccess: true,
      logFailure: true
    })
  }

  /**
   * Log data modification events
   */
  logDataModification(resourceType: string, action: string, resourceIdParam?: string) {
    return this.logRequest({
      eventType: AuditEventType.DATA_MODIFICATION,
      category: AuditCategory.DATA_PROTECTION,
      action,
      resourceType,
      resourceIdParam,
      severity: AuditSeverity.MEDIUM,
      logSuccess: true,
      logFailure: true,
      includeRequestBody: true,
      sensitiveFields: ['password', 'token', 'secret', 'key']
    })
  }

  /**
   * Log role management events
   */
  logRoleManagement(action: string, resourceIdParam: string = 'roleId') {
    return this.logRequest({
      eventType: AuditEventType.ROLE_MANAGEMENT,
      category: AuditCategory.ACCESS_CONTROL,
      action,
      resourceType: 'role',
      resourceIdParam,
      severity: AuditSeverity.HIGH,
      logSuccess: true,
      logFailure: true,
      includeRequestBody: true
    })
  }

  /**
   * Log user management events
   */
  logUserManagement(action: string, resourceIdParam: string = 'userId') {
    return this.logRequest({
      eventType: AuditEventType.USER_MANAGEMENT,
      category: AuditCategory.ACCESS_CONTROL,
      action,
      resourceType: 'user',
      resourceIdParam,
      severity: AuditSeverity.HIGH,
      logSuccess: true,
      logFailure: true,
      includeRequestBody: true,
      sensitiveFields: ['password', 'currentPassword', 'newPassword']
    })
  }

  /**
   * Log system configuration events
   */
  logSystemConfiguration(action: string, resourceType: string = 'system') {
    return this.logRequest({
      eventType: AuditEventType.SYSTEM_CONFIGURATION,
      category: AuditCategory.SYSTEM,
      action,
      resourceType,
      severity: AuditSeverity.HIGH,
      logSuccess: true,
      logFailure: true,
      includeRequestBody: true
    })
  }

  /**
   * Log security events
   */
  logSecurityEvent(action: string, severity: AuditSeverity = AuditSeverity.HIGH) {
    return this.logRequest({
      eventType: AuditEventType.SECURITY_EVENT,
      category: AuditCategory.SECURITY,
      action,
      resourceType: 'system',
      severity,
      logSuccess: false, // Security events are typically failures
      logFailure: true,
      includeRequestBody: true
    })
  }

  /**
   * Log compliance events
   */
  logComplianceEvent(action: string, resourceType: string) {
    return this.logRequest({
      eventType: AuditEventType.COMPLIANCE_EVENT,
      category: AuditCategory.COMPLIANCE,
      action,
      resourceType,
      severity: AuditSeverity.MEDIUM,
      logSuccess: true,
      logFailure: true
    })
  }

  /**
   * Log the actual request event
   */
  private async logRequestEvent(
    req: Request,
    res: Response,
    options: AuditMiddlewareOptions,
    responseInfo: {
      responseBody: any
      statusCode: number
      duration: number
    }
  ): Promise<void> {
    // Determine if we should log this event
    const success = responseInfo.statusCode < 400
    const shouldLog = (success && options.logSuccess !== false) || 
                     (!success && options.logFailure !== false)

    if (!shouldLog) {
      return
    }

    // Extract user and tenant info
    const userId = req.user?.id || 'anonymous'
    const tenantId = req.tenant?.id || req.user?.tenantId || 'unknown'

    // Extract resource ID if specified
    const resourceId = options.resourceIdParam ? req.params[options.resourceIdParam] : undefined

    // Determine action
    const action = options.action || this.getActionFromMethod(req.method)

    // Determine resource type
    const resourceType = options.resourceType || this.getResourceTypeFromPath(req.path)

    // Build details object
    const details: Record<string, any> = {
      method: req.method,
      path: req.path,
      query: req.query,
      statusCode: responseInfo.statusCode,
      duration: responseInfo.duration,
      userAgent: req.headers['user-agent'],
      referer: req.headers.referer
    }

    // Include request body if specified
    if (options.includeRequestBody && req.body) {
      details.requestBody = this.sanitizeData(req.body, options.sensitiveFields)
    }

    // Include response body if specified
    if (options.includeResponseBody && responseInfo.responseBody) {
      details.responseBody = this.sanitizeData(responseInfo.responseBody, options.sensitiveFields)
    }

    // Build metadata
    const metadata: Record<string, any> = {
      sessionId: req.headers['x-session-id'] as string,
      requestId: req.headers['x-request-id'] as string,
      correlationId: req.headers['x-correlation-id'] as string,
      contentType: req.headers['content-type'],
      contentLength: req.headers['content-length']
    }

    // Determine changes for data modification events
    let changes
    if (options.eventType === AuditEventType.DATA_MODIFICATION && req.body) {
      changes = {
        after: this.sanitizeData(req.body, options.sensitiveFields)
      }
    }

    // Log the event
    await this.auditLogger.logEvent({
      eventType: options.eventType || AuditEventType.DATA_ACCESS,
      category: options.category || AuditCategory.SYSTEM,
      action,
      resourceType,
      resourceId,
      userId,
      tenantId,
      sessionId: metadata.sessionId,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'] as string,
      success,
      details,
      changes,
      metadata,
      severity: options.severity || (success ? AuditSeverity.LOW : AuditSeverity.MEDIUM)
    })
  }

  /**
   * Get action from HTTP method
   */
  private getActionFromMethod(method: string): string {
    switch (method.toUpperCase()) {
      case 'GET':
        return 'read'
      case 'POST':
        return 'create'
      case 'PUT':
      case 'PATCH':
        return 'update'
      case 'DELETE':
        return 'delete'
      default:
        return method.toLowerCase()
    }
  }

  /**
   * Get resource type from request path
   */
  private getResourceTypeFromPath(path: string): string {
    // Extract resource type from path like /api/deals/123 -> deals
    const pathParts = path.split('/').filter(part => part.length > 0)
    
    if (pathParts.length >= 2 && pathParts[0] === 'api') {
      return pathParts[1]
    }
    
    return 'unknown'
  }

  /**
   * Sanitize sensitive data from objects
   */
  private sanitizeData(data: any, sensitiveFields: string[] = []): any {
    if (!data || typeof data !== 'object') {
      return data
    }

    const defaultSensitiveFields = [
      'password', 'token', 'secret', 'key', 'authorization',
      'currentPassword', 'newPassword', 'confirmPassword',
      'apiKey', 'accessToken', 'refreshToken', 'sessionToken'
    ]

    const allSensitiveFields = [...defaultSensitiveFields, ...sensitiveFields]
    
    const sanitized = Array.isArray(data) ? [] : {}

    for (const [key, value] of Object.entries(data)) {
      if (allSensitiveFields.some(field => 
        key.toLowerCase().includes(field.toLowerCase())
      )) {
        (sanitized as any)[key] = '[REDACTED]'
      } else if (value && typeof value === 'object') {
        (sanitized as any)[key] = this.sanitizeData(value, sensitiveFields)
      } else {
        (sanitized as any)[key] = value
      }
    }

    return sanitized
  }
}

// Convenience functions for common audit patterns
export const auditMiddleware = (prisma: PrismaClient, cache: CacheService) => {
  const audit = new AuditMiddleware(prisma, cache)

  return {
    // Authentication and authorization
    logLogin: audit.logAuthentication('login'),
    logLogout: audit.logAuthentication('logout'),
    logPasswordChange: audit.logAuthentication('password_change'),
    logPasswordReset: audit.logAuthentication('password_reset'),
    
    // Data access
    logDataAccess: (resourceType: string, resourceIdParam?: string) =>
      audit.logDataAccess(resourceType, 'read', resourceIdParam),
    
    // Data modification
    logCreate: (resourceType: string) =>
      audit.logDataModification(resourceType, 'create'),
    logUpdate: (resourceType: string, resourceIdParam?: string) =>
      audit.logDataModification(resourceType, 'update', resourceIdParam),
    logDelete: (resourceType: string, resourceIdParam?: string) =>
      audit.logDataModification(resourceType, 'delete', resourceIdParam),
    
    // Role and user management
    logRoleCreate: audit.logRoleManagement('create'),
    logRoleUpdate: audit.logRoleManagement('update'),
    logRoleDelete: audit.logRoleManagement('delete'),
    logRoleAssign: audit.logRoleManagement('assign'),
    logRoleRevoke: audit.logRoleManagement('revoke'),
    
    logUserCreate: audit.logUserManagement('create'),
    logUserUpdate: audit.logUserManagement('update'),
    logUserDelete: audit.logUserManagement('delete'),
    logUserActivate: audit.logUserManagement('activate'),
    logUserDeactivate: audit.logUserManagement('deactivate'),
    
    // System configuration
    logSystemConfig: (action: string) => audit.logSystemConfiguration(action),
    
    // Security events
    logSecurityEvent: (action: string, severity?: AuditSeverity) =>
      audit.logSecurityEvent(action, severity),
    
    // Custom logging
    log: (options: AuditMiddlewareOptions) => audit.logRequest(options)
  }
}
