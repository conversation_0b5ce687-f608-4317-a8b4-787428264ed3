import { CacheService } from '@/services/cache.service'
import { PermissionService } from '@/services/rbac/permission.service'
import { ForbiddenError, UnauthorizedError } from '@/shared/errors'
import {
    AccessContext,
    Action,
    PermissionCheck,
    Resource,
    Scope
} from '@/shared/types/rbac'
import { Logger } from '@/shared/utils/logger'
import { PrismaClient } from '@prisma/client'
import { NextFunction, Request, Response } from 'express'

// Extend Express Request to include RBAC context
declare global {
  namespace Express {
    interface Request {
      rbac?: {
        context: AccessContext
        checkPermission: (check: PermissionCheck) => Promise<boolean>
        requirePermission: (check: PermissionCheck) => Promise<void>
        hasPermission: (resource: string, action: string, scope?: string) => Promise<boolean>
      }
    }
  }
}

export interface RBACMiddlewareOptions {
  resource: Resource | string
  action: Action | string
  scope?: Scope | string
  resourceIdParam?: string // Parameter name for resource ID (e.g., 'dealId')
  optional?: boolean // If true, don't throw error on permission failure
  conditions?: Record<string, any>
}

export class RBACMiddleware {
  private permissionService: PermissionService
  private auditLogger: AuditLoggerService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.permissionService = new PermissionService(prisma, cache)
    this.auditLogger = new AuditLoggerService(prisma, cache)
    this.logger = new Logger('RBACMiddleware')
  }

  /**
   * Initialize RBAC context for authenticated requests
   */
  initializeContext() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        // Ensure user is authenticated
        if (!req.user || !req.tenant) {
          throw new UnauthorizedError('Authentication required for RBAC')
        }

        // Get user roles
        const userRoles = await this.getUserRoles(req.user.id, req.tenant.id)

        // Create access context
        const context: AccessContext = {
          userId: req.user.id,
          tenantId: req.tenant.id,
          roles: userRoles,
          teamIds: req.user.teamIds || [],
          metadata: {
            userStatus: req.user.status,
            tenantPlan: req.tenant.plan,
            userAgent: req.headers['user-agent'],
            ip: req.ip
          }
        }

        // Add RBAC helper methods to request
        req.rbac = {
          context,
          checkPermission: async (check: PermissionCheck) => {
            const result = await this.permissionService.checkPermission(context, check)
            return result.granted
          },
          requirePermission: async (check: PermissionCheck) => {
            const result = await this.permissionService.checkPermission(context, check)
            if (!result.granted) {
              throw new ForbiddenError(result.reason || 'Permission denied')
            }
          },
          hasPermission: async (resource: string, action: string, scope?: string) => {
            const check: PermissionCheck = { resource, action, scope }
            const result = await this.permissionService.checkPermission(context, check)
            return result.granted
          }
        }

        next()
      } catch (error) {
        this.logger.error('Failed to initialize RBAC context', error, {
          userId: req.user?.id,
          tenantId: req.tenant?.id
        })
        next(error)
      }
    }
  }

  /**
   * Require specific permission for route access
   */
  requirePermission(options: RBACMiddlewareOptions) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.rbac) {
          throw new UnauthorizedError('RBAC context not initialized')
        }

        // Build permission check
        const check: PermissionCheck = {
          resource: options.resource,
          action: options.action,
          scope: options.scope,
          conditions: options.conditions
        }

        // Add resource ID if specified
        if (options.resourceIdParam && req.params[options.resourceIdParam]) {
          check.resourceId = req.params[options.resourceIdParam]
        }

        // Check permission
        const result = await this.permissionService.checkPermission(req.rbac.context, check)

        // Log authorization attempt
        await this.auditLogger.logAuthorization(
          req.rbac.context.userId,
          req.rbac.context.tenantId,
          `${options.action}:${options.resource}`,
          options.resource,
          check.resourceId,
          result.granted,
          {
            scope: options.scope,
            conditions: options.conditions,
            reason: result.reason
          },
          {
            userAgent: req.headers['user-agent'],
            ipAddress: req.ip,
            sessionId: req.headers['x-session-id'] as string
          }
        )

        if (!result.granted) {
          if (options.optional) {
            // Add permission result to request for later use
            req.permissionResult = result
            return next()
          }

          this.logger.warn('Permission denied', {
            userId: req.rbac.context.userId,
            tenantId: req.rbac.context.tenantId,
            resource: options.resource,
            action: options.action,
            scope: options.scope,
            reason: result.reason
          })

          throw new ForbiddenError(result.reason || 'Permission denied')
        }

        // Add permission result to request
        req.permissionResult = result

        next()
      } catch (error) {
        this.logger.error('Permission check failed', error, {
          userId: req.rbac?.context.userId,
          tenantId: req.rbac?.context.tenantId,
          resource: options.resource,
          action: options.action
        })
        next(error)
      }
    }
  }

  /**
   * Check multiple permissions (all must pass)
   */
  requireAllPermissions(checks: RBACMiddlewareOptions[]) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.rbac) {
          throw new UnauthorizedError('RBAC context not initialized')
        }

        for (const options of checks) {
          const check: PermissionCheck = {
            resource: options.resource,
            action: options.action,
            scope: options.scope,
            conditions: options.conditions
          }

          if (options.resourceIdParam && req.params[options.resourceIdParam]) {
            check.resourceId = req.params[options.resourceIdParam]
          }

          const result = await this.permissionService.checkPermission(req.rbac.context, check)

          if (!result.granted) {
            throw new ForbiddenError(
              result.reason || `Permission denied for ${options.resource}:${options.action}`
            )
          }
        }

        next()
      } catch (error) {
        next(error)
      }
    }
  }

  /**
   * Check multiple permissions (any can pass)
   */
  requireAnyPermission(checks: RBACMiddlewareOptions[]) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.rbac) {
          throw new UnauthorizedError('RBAC context not initialized')
        }

        let hasPermission = false
        const errors: string[] = []

        for (const options of checks) {
          const check: PermissionCheck = {
            resource: options.resource,
            action: options.action,
            scope: options.scope,
            conditions: options.conditions
          }

          if (options.resourceIdParam && req.params[options.resourceIdParam]) {
            check.resourceId = req.params[options.resourceIdParam]
          }

          const result = await this.permissionService.checkPermission(req.rbac.context, check)

          if (result.granted) {
            hasPermission = true
            break
          } else {
            errors.push(result.reason || `${options.resource}:${options.action}`)
          }
        }

        if (!hasPermission) {
          throw new ForbiddenError(`Permission denied. Tried: ${errors.join(', ')}`)
        }

        next()
      } catch (error) {
        next(error)
      }
    }
  }

  /**
   * Check if user has admin role
   */
  requireAdmin() {
    return this.requirePermission({
      resource: '*',
      action: '*',
      scope: 'tenant'
    })
  }

  /**
   * Check if user can manage users
   */
  requireUserManagement() {
    return this.requirePermission({
      resource: Resource.USERS,
      action: Action.MANAGE,
      scope: Scope.TENANT
    })
  }

  /**
   * Check if user can manage roles
   */
  requireRoleManagement() {
    return this.requirePermission({
      resource: Resource.ROLES,
      action: Action.MANAGE,
      scope: Scope.TENANT
    })
  }

  /**
   * Check resource ownership or higher permissions
   */
  requireOwnershipOrPermission(options: RBACMiddlewareOptions) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.rbac) {
          throw new UnauthorizedError('RBAC context not initialized')
        }

        // First check if user has the required permission with higher scope
        const higherScopeCheck: PermissionCheck = {
          resource: options.resource,
          action: options.action,
          scope: options.scope || Scope.TENANT
        }

        const higherScopeResult = await this.permissionService.checkPermission(
          req.rbac.context,
          higherScopeCheck
        )

        if (higherScopeResult.granted) {
          return next()
        }

        // If no higher scope permission, check ownership
        const ownershipCheck: PermissionCheck = {
          resource: options.resource,
          action: options.action,
          scope: Scope.OWN,
          resourceId: options.resourceIdParam ? req.params[options.resourceIdParam] : undefined
        }

        const ownershipResult = await this.permissionService.checkPermission(
          req.rbac.context,
          ownershipCheck
        )

        if (!ownershipResult.granted) {
          throw new ForbiddenError('Permission denied: insufficient privileges or not owner')
        }

        next()
      } catch (error) {
        next(error)
      }
    }
  }

  /**
   * Get user roles with caching
   */
  private async getUserRoles(userId: string, tenantId: string) {
    // This would typically be cached, but for now we'll query directly
    const userRoles = await this.permissionService['prisma'].userRole.findMany({
      where: {
        userId,
        isActive: true,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      },
      include: {
        role: {
          where: { tenantId }
        }
      }
    })

    return userRoles
      .filter(ur => ur.role)
      .map(ur => ur.role!)
  }
}

// Convenience functions for common permission patterns
export const rbacMiddleware = (prisma: PrismaClient, cache: CacheService) => {
  const rbac = new RBACMiddleware(prisma, cache)

  return {
    // Initialize RBAC context
    init: rbac.initializeContext(),

    // Common permission checks
    requireAdmin: rbac.requireAdmin(),
    requireUserManagement: rbac.requireUserManagement(),
    requireRoleManagement: rbac.requireRoleManagement(),

    // Resource-specific permissions
    deals: {
      read: rbac.requirePermission({ resource: Resource.DEALS, action: Action.READ }),
      create: rbac.requirePermission({ resource: Resource.DEALS, action: Action.CREATE }),
      update: rbac.requireOwnershipOrPermission({ 
        resource: Resource.DEALS, 
        action: Action.UPDATE,
        resourceIdParam: 'dealId'
      }),
      delete: rbac.requirePermission({ resource: Resource.DEALS, action: Action.DELETE }),
      manage: rbac.requirePermission({ resource: Resource.DEALS, action: Action.MANAGE })
    },

    documents: {
      read: rbac.requirePermission({ resource: Resource.DOCUMENTS, action: Action.READ }),
      create: rbac.requirePermission({ resource: Resource.DOCUMENTS, action: Action.CREATE }),
      update: rbac.requireOwnershipOrPermission({ 
        resource: Resource.DOCUMENTS, 
        action: Action.UPDATE,
        resourceIdParam: 'documentId'
      }),
      delete: rbac.requirePermission({ resource: Resource.DOCUMENTS, action: Action.DELETE }),
      upload: rbac.requirePermission({ resource: Resource.DOCUMENTS, action: Action.UPLOAD }),
      download: rbac.requirePermission({ resource: Resource.DOCUMENTS, action: Action.DOWNLOAD })
    },

    users: {
      read: rbac.requirePermission({ resource: Resource.USERS, action: Action.READ }),
      create: rbac.requirePermission({ resource: Resource.USERS, action: Action.CREATE }),
      update: rbac.requirePermission({ resource: Resource.USERS, action: Action.UPDATE }),
      delete: rbac.requirePermission({ resource: Resource.USERS, action: Action.DELETE })
    },

    // Custom permission checker
    require: (options: RBACMiddlewareOptions) => rbac.requirePermission(options),
    requireAll: (checks: RBACMiddlewareOptions[]) => rbac.requireAllPermissions(checks),
    requireAny: (checks: RBACMiddlewareOptions[]) => rbac.requireAnyPermission(checks)
  }
}
