import { Request, Response } from 'express'
import { PrismaClient } from '@prisma/client'
import { DealService } from '@/services/deal/deal.service'
import { DealReportingService } from '@/services/reporting/deal-reporting.service'
import { CacheService } from '@/services/cache.service'
import { Logger } from '@/shared/utils/logger'
import { 
  CreateDealRequest, 
  UpdateDealRequest, 
  DealFilters, 
  DealSortOptions 
} from '@/shared/types/deal'
import { ReportParameters, ReportFormat } from '@/services/reporting/deal-reporting.service'

export class DealController {
  private dealService: DealService
  private reportingService: DealReportingService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.dealService = new DealService(prisma, cache)
    this.reportingService = new DealReportingService(prisma, cache)
    this.logger = new Logger('DealController')
  }

  /**
   * Create a new deal
   */
  createDeal = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const dealData: CreateDealRequest = req.body

      // Validate required fields
      if (!dealData.title || !dealData.targetCompany || !dealData.dealType) {
        res.status(400).json({
          error: 'Missing required fields: title, targetCompany, dealType'
        })
        return
      }

      const deal = await this.dealService.createDeal(dealData, userId, tenantId)

      res.status(201).json({
        success: true,
        data: deal,
        message: 'Deal created successfully'
      })
    } catch (error) {
      this.logger.error('Failed to create deal', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to create deal',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Get deal by ID
   */
  getDeal = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { dealId } = req.params

      const deal = await this.dealService.getDealById(dealId, tenantId)

      if (!deal) {
        res.status(404).json({
          error: 'Deal not found'
        })
        return
      }

      res.json({
        success: true,
        data: deal
      })
    } catch (error) {
      this.logger.error('Failed to get deal', error, { dealId: req.params.dealId })
      res.status(500).json({
        error: 'Failed to get deal',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Update deal
   */
  updateDeal = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const { dealId } = req.params
      const updateData: UpdateDealRequest = req.body

      const deal = await this.dealService.updateDeal(dealId, updateData, userId, tenantId)

      res.json({
        success: true,
        data: deal,
        message: 'Deal updated successfully'
      })
    } catch (error) {
      this.logger.error('Failed to update deal', error, { 
        dealId: req.params.dealId, 
        body: req.body 
      })
      res.status(500).json({
        error: 'Failed to update deal',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Delete deal
   */
  deleteDeal = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const { dealId } = req.params

      await this.dealService.deleteDeal(dealId, userId, tenantId)

      res.json({
        success: true,
        message: 'Deal deleted successfully'
      })
    } catch (error) {
      this.logger.error('Failed to delete deal', error, { dealId: req.params.dealId })
      res.status(500).json({
        error: 'Failed to delete deal',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Get deals with filtering and pagination
   */
  getDeals = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const {
        page = 1,
        limit = 20,
        sortField = 'createdAt',
        sortDirection = 'desc',
        ...filterParams
      } = req.query

      // Build filters
      const filters: DealFilters = {}
      
      if (filterParams.status) {
        filters.status = Array.isArray(filterParams.status) 
          ? filterParams.status as any[] 
          : [filterParams.status as any]
      }
      
      if (filterParams.stage) {
        filters.stage = Array.isArray(filterParams.stage) 
          ? filterParams.stage as string[] 
          : [filterParams.stage as string]
      }
      
      if (filterParams.assignedTo) {
        filters.assignedTo = Array.isArray(filterParams.assignedTo) 
          ? filterParams.assignedTo as string[] 
          : [filterParams.assignedTo as string]
      }
      
      if (filterParams.priority) {
        filters.priority = Array.isArray(filterParams.priority) 
          ? filterParams.priority as any[] 
          : [filterParams.priority as any]
      }
      
      if (filterParams.dealType) {
        filters.dealType = Array.isArray(filterParams.dealType) 
          ? filterParams.dealType as any[] 
          : [filterParams.dealType as any]
      }
      
      if (filterParams.minValue) {
        filters.minValue = Number(filterParams.minValue)
      }
      
      if (filterParams.maxValue) {
        filters.maxValue = Number(filterParams.maxValue)
      }
      
      if (filterParams.search) {
        filters.search = filterParams.search as string
      }

      // Build sort options
      const sort: DealSortOptions = {
        field: sortField as any,
        direction: sortDirection as 'asc' | 'desc'
      }

      const result = await this.dealService.getDeals(
        tenantId,
        filters,
        sort,
        Number(page),
        Number(limit)
      )

      res.json({
        success: true,
        data: result.deals,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: result.total,
          hasMore: result.hasMore
        }
      })
    } catch (error) {
      this.logger.error('Failed to get deals', error, { query: req.query })
      res.status(500).json({
        error: 'Failed to get deals',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Get deal analytics
   */
  getDealAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { dateFrom, dateTo } = req.query

      const analytics = await this.dealService.getDealAnalytics(
        tenantId,
        dateFrom ? new Date(dateFrom as string) : undefined,
        dateTo ? new Date(dateTo as string) : undefined
      )

      res.json({
        success: true,
        data: analytics
      })
    } catch (error) {
      this.logger.error('Failed to get deal analytics', error, { query: req.query })
      res.status(500).json({
        error: 'Failed to get deal analytics',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Move deal to next stage
   */
  moveToNextStage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const { dealId } = req.params
      const { reason } = req.body

      const deal = await this.dealService.moveToNextStage(dealId, userId, tenantId, reason)

      res.json({
        success: true,
        data: deal,
        message: 'Deal moved to next stage successfully'
      })
    } catch (error) {
      this.logger.error('Failed to move deal to next stage', error, { 
        dealId: req.params.dealId 
      })
      res.status(500).json({
        error: 'Failed to move deal to next stage',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Move deal to previous stage
   */
  moveToPreviousStage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const { dealId } = req.params
      const { reason } = req.body

      const deal = await this.dealService.moveToPreviousStage(dealId, userId, tenantId, reason)

      res.json({
        success: true,
        data: deal,
        message: 'Deal moved to previous stage successfully'
      })
    } catch (error) {
      this.logger.error('Failed to move deal to previous stage', error, { 
        dealId: req.params.dealId 
      })
      res.status(500).json({
        error: 'Failed to move deal to previous stage',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Generate deal pipeline report
   */
  generatePipelineReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const parameters: ReportParameters = req.body

      // Validate parameters
      if (!parameters.dateRange || !parameters.dateRange.from || !parameters.dateRange.to) {
        res.status(400).json({
          error: 'Date range is required'
        })
        return
      }

      const report = await this.reportingService.generateDealPipelineReport(tenantId, parameters)

      res.json({
        success: true,
        data: report
      })
    } catch (error) {
      this.logger.error('Failed to generate pipeline report', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to generate pipeline report',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Generate deal performance report
   */
  generatePerformanceReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const parameters: ReportParameters = req.body

      const report = await this.reportingService.generateDealPerformanceReport(tenantId, parameters)

      res.json({
        success: true,
        data: report
      })
    } catch (error) {
      this.logger.error('Failed to generate performance report', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to generate performance report',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Generate forecast report
   */
  generateForecastReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const parameters: ReportParameters = req.body

      const report = await this.reportingService.generateForecastReport(tenantId, parameters)

      res.json({
        success: true,
        data: report
      })
    } catch (error) {
      this.logger.error('Failed to generate forecast report', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to generate forecast report',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Export report
   */
  exportReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { reportData, format } = req.body

      if (!reportData || !format) {
        res.status(400).json({
          error: 'Report data and format are required'
        })
        return
      }

      const exportBuffer = await this.reportingService.exportReport(reportData, format as ReportFormat)

      // Set appropriate headers based on format
      const contentTypes = {
        pdf: 'application/pdf',
        excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        csv: 'text/csv',
        json: 'application/json',
        html: 'text/html'
      }

      const fileExtensions = {
        pdf: 'pdf',
        excel: 'xlsx',
        csv: 'csv',
        json: 'json',
        html: 'html'
      }

      res.setHeader('Content-Type', contentTypes[format as keyof typeof contentTypes])
      res.setHeader(
        'Content-Disposition', 
        `attachment; filename="report.${fileExtensions[format as keyof typeof fileExtensions]}"`
      )

      res.send(exportBuffer)
    } catch (error) {
      this.logger.error('Failed to export report', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to export report',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
}
