import { Request, Response } from 'express'
import { PrismaClient } from '@prisma/client'
import { 
  AuditLoggerService, 
  AuditQuery, 
  AuditEventType, 
  AuditCategory, 
  AuditSeverity 
} from '@/services/audit/audit-logger.service'
import { CacheService } from '@/services/cache.service'
import { Logger } from '@/shared/utils/logger'
import { validateRequest } from '@/middleware/validation.middleware'
import { z } from 'zod'

// Validation schemas
const auditQuerySchema = z.object({
  userId: z.string().optional(),
  eventType: z.nativeEnum(AuditEventType).optional(),
  category: z.nativeEnum(AuditCategory).optional(),
  resourceType: z.string().optional(),
  resourceId: z.string().optional(),
  action: z.string().optional(),
  severity: z.nativeEnum(AuditSeverity).optional(),
  success: z.boolean().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
})

const exportAuditSchema = z.object({
  format: z.enum(['json', 'csv', 'xlsx']),
  filters: auditQuerySchema.optional(),
  includeDetails: z.boolean().optional(),
  includeMetadata: z.boolean().optional()
})

export class AuditController {
  private auditLogger: AuditLoggerService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.auditLogger = new AuditLoggerService(prisma, cache)
    this.logger = new Logger('AuditController')
  }

  /**
   * Get audit events with filtering and pagination
   */
  getAuditEvents = [
    validateRequest(auditQuerySchema),
    async (req: Request, res: Response) => {
      try {
        const { tenantId } = req.tenant!
        const query: AuditQuery = {
          ...req.query,
          tenantId,
          startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
          endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined
        }

        const result = await this.auditLogger.queryEvents(query)

        res.json({
          success: true,
          data: {
            events: result.events,
            pagination: {
              total: result.total,
              limit: query.limit || 100,
              offset: query.offset || 0,
              hasMore: result.hasMore
            }
          }
        })
      } catch (error) {
        this.logger.error('Failed to get audit events', error)
        res.status(500).json({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Failed to retrieve audit events'
          }
        })
      }
    }
  ]

  /**
   * Get audit statistics
   */
  getAuditStatistics = async (req: Request, res: Response) => {
    try {
      const { tenantId } = req.tenant!
      const { startDate, endDate } = req.query

      const start = startDate ? new Date(startDate as string) : undefined
      const end = endDate ? new Date(endDate as string) : undefined

      const statistics = await this.auditLogger.getStatistics(tenantId, start, end)

      res.json({
        success: true,
        data: statistics
      })
    } catch (error) {
      this.logger.error('Failed to get audit statistics', error)
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve audit statistics'
        }
      })
    }
  }

  /**
   * Get audit event by ID
   */
  getAuditEvent = async (req: Request, res: Response) => {
    try {
      const { eventId } = req.params
      const { tenantId } = req.tenant!

      const result = await this.auditLogger.queryEvents({
        tenantId,
        limit: 1,
        offset: 0
      })

      const event = result.events.find(e => e.id === eventId)

      if (!event) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Audit event not found'
          }
        })
      }

      res.json({
        success: true,
        data: event
      })
    } catch (error) {
      this.logger.error('Failed to get audit event', error)
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve audit event'
        }
      })
    }
  }

  /**
   * Export audit events
   */
  exportAuditEvents = [
    validateRequest(exportAuditSchema),
    async (req: Request, res: Response) => {
      try {
        const { tenantId } = req.tenant!
        const { format, filters, includeDetails, includeMetadata } = req.body

        const query: AuditQuery = {
          ...filters,
          tenantId,
          startDate: filters?.startDate ? new Date(filters.startDate) : undefined,
          endDate: filters?.endDate ? new Date(filters.endDate) : undefined,
          limit: 10000 // Large limit for export
        }

        const result = await this.auditLogger.queryEvents(query)

        // Process events for export
        const exportData = result.events.map(event => {
          const baseData = {
            id: event.id,
            timestamp: event.timestamp,
            eventType: event.eventType,
            category: event.category,
            action: event.action,
            resourceType: event.resourceType,
            resourceId: event.resourceId,
            userId: event.userId,
            success: event.success,
            severity: event.severity,
            ipAddress: event.ipAddress,
            userAgent: event.userAgent
          }

          if (includeDetails && event.details) {
            Object.assign(baseData, { details: event.details })
          }

          if (includeMetadata && event.metadata) {
            Object.assign(baseData, { metadata: event.metadata })
          }

          return baseData
        })

        // Set appropriate headers based on format
        const timestamp = new Date().toISOString().split('T')[0]
        const filename = `audit-export-${timestamp}`

        switch (format) {
          case 'json':
            res.setHeader('Content-Type', 'application/json')
            res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`)
            res.json(exportData)
            break

          case 'csv':
            const csv = this.convertToCSV(exportData)
            res.setHeader('Content-Type', 'text/csv')
            res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`)
            res.send(csv)
            break

          case 'xlsx':
            // For XLSX, you would use a library like 'xlsx' or 'exceljs'
            // For now, return JSON with appropriate headers
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            res.setHeader('Content-Disposition', `attachment; filename="${filename}.xlsx"`)
            res.json({
              success: false,
              error: {
                code: 'NOT_IMPLEMENTED',
                message: 'XLSX export not yet implemented'
              }
            })
            break

          default:
            res.status(400).json({
              success: false,
              error: {
                code: 'INVALID_FORMAT',
                message: 'Unsupported export format'
              }
            })
        }
      } catch (error) {
        this.logger.error('Failed to export audit events', error)
        res.status(500).json({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Failed to export audit events'
          }
        })
      }
    }
  ]

  /**
   * Get audit event types and categories
   */
  getAuditMetadata = async (req: Request, res: Response) => {
    try {
      const metadata = {
        eventTypes: Object.values(AuditEventType),
        categories: Object.values(AuditCategory),
        severities: Object.values(AuditSeverity),
        commonActions: [
          'login', 'logout', 'create', 'read', 'update', 'delete',
          'assign', 'revoke', 'activate', 'deactivate', 'approve', 'reject'
        ],
        commonResourceTypes: [
          'user', 'role', 'deal', 'document', 'tenant', 'system',
          'policy', 'permission', 'session', 'api_key'
        ]
      }

      res.json({
        success: true,
        data: metadata
      })
    } catch (error) {
      this.logger.error('Failed to get audit metadata', error)
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve audit metadata'
        }
      })
    }
  }

  /**
   * Get user activity summary
   */
  getUserActivity = async (req: Request, res: Response) => {
    try {
      const { userId } = req.params
      const { tenantId } = req.tenant!
      const { startDate, endDate, limit = 100 } = req.query

      const query: AuditQuery = {
        tenantId,
        userId,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        limit: Number(limit),
        sortBy: 'timestamp',
        sortOrder: 'desc'
      }

      const result = await this.auditLogger.queryEvents(query)

      // Get activity summary
      const activitySummary = {
        totalEvents: result.total,
        recentEvents: result.events,
        eventsByType: this.groupEventsByField(result.events, 'eventType'),
        eventsByAction: this.groupEventsByField(result.events, 'action'),
        successRate: this.calculateSuccessRate(result.events),
        mostActiveHours: this.getMostActiveHours(result.events)
      }

      res.json({
        success: true,
        data: activitySummary
      })
    } catch (error) {
      this.logger.error('Failed to get user activity', error)
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve user activity'
        }
      })
    }
  }

  /**
   * Get security events
   */
  getSecurityEvents = async (req: Request, res: Response) => {
    try {
      const { tenantId } = req.tenant!
      const { startDate, endDate, severity, limit = 100 } = req.query

      const query: AuditQuery = {
        tenantId,
        eventType: AuditEventType.SECURITY_EVENT,
        severity: severity as AuditSeverity,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        limit: Number(limit),
        sortBy: 'timestamp',
        sortOrder: 'desc'
      }

      const result = await this.auditLogger.queryEvents(query)

      res.json({
        success: true,
        data: {
          events: result.events,
          total: result.total,
          criticalCount: result.events.filter(e => e.severity === AuditSeverity.CRITICAL).length,
          highCount: result.events.filter(e => e.severity === AuditSeverity.HIGH).length
        }
      })
    } catch (error) {
      this.logger.error('Failed to get security events', error)
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve security events'
        }
      })
    }
  }

  /**
   * Convert data to CSV format
   */
  private convertToCSV(data: any[]): string {
    if (data.length === 0) return ''

    const headers = Object.keys(data[0])
    const csvHeaders = headers.join(',')
    
    const csvRows = data.map(row => {
      return headers.map(header => {
        const value = row[header]
        if (value === null || value === undefined) return ''
        if (typeof value === 'object') return JSON.stringify(value)
        return `"${String(value).replace(/"/g, '""')}"`
      }).join(',')
    })

    return [csvHeaders, ...csvRows].join('\n')
  }

  /**
   * Group events by a specific field
   */
  private groupEventsByField(events: any[], field: string): Record<string, number> {
    const groups: Record<string, number> = {}
    
    events.forEach(event => {
      const value = event[field] || 'unknown'
      groups[value] = (groups[value] || 0) + 1
    })

    return groups
  }

  /**
   * Calculate success rate from events
   */
  private calculateSuccessRate(events: any[]): number {
    if (events.length === 0) return 0
    
    const successfulEvents = events.filter(event => event.success).length
    return (successfulEvents / events.length) * 100
  }

  /**
   * Get most active hours from events
   */
  private getMostActiveHours(events: any[]): Record<string, number> {
    const hours: Record<string, number> = {}
    
    events.forEach(event => {
      const hour = new Date(event.timestamp).getHours()
      const hourKey = `${hour}:00`
      hours[hourKey] = (hours[hourKey] || 0) + 1
    })

    return hours
  }
}
