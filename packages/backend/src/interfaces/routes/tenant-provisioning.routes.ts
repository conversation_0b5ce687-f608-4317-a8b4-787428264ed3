import { Router, Request, Response, NextFunction } from 'express'
import { z } from 'zod'
import { TenantProvisioningService } from '@/application/services/tenant-provisioning.service'
import { authenticateToken, AuthenticatedRequest } from '@/shared/middleware/auth'
import { requirePermission } from '@/shared/middleware/permissions'

const router = Router()

// Validation schemas
const provisionTenantSchema = z.object({
  tenantData: z.object({
    name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
    slug: z.string().optional(),
    subdomain: z.string().min(1, 'Subdomain is required').max(50, 'Subdomain too long')
      .regex(/^[a-z0-9-]+$/, 'Subdomain must contain only lowercase letters, numbers, and hyphens'),
    domain: z.string().optional(),
    description: z.string().optional(),
    industry: z.string().optional(),
    size: z.enum(['SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE']).optional(),
    plan: z.enum(['STARTER', 'PROFESSIONAL', 'ENTERPRISE', 'CUSTOM']).optional(),
    billingEmail: z.string().email().optional(),
    settings: z.any().optional(),
    features: z.any().optional(),
    limits: z.any().optional(),
    branding: z.any().optional()
  }),
  adminUser: z.object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    email: z.string().email('Invalid email format'),
    password: z.string().min(8, 'Password must be at least 8 characters')
  }),
  options: z.object({
    skipMigrations: z.boolean().optional(),
    skipDefaultRoles: z.boolean().optional(),
    skipWelcomeEmail: z.boolean().optional(),
    customSettings: z.any().optional()
  }).optional()
})

const deprovisionTenantSchema = z.object({
  tenantId: z.string().min(1, 'Tenant ID is required'),
  options: z.object({
    hardDelete: z.boolean().optional(),
    preserveData: z.boolean().optional(),
    notifyUsers: z.boolean().optional()
  }).optional()
})

// Validation middleware
function validateRequest(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
        
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: validationErrors
          }
        })
      }
      next(error)
    }
  }
}

// POST /provision - Provision a new tenant
router.post('/provision',
  authenticateToken,
  requirePermission('system:tenants:provision'),
  validateRequest(provisionTenantSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const result = await TenantProvisioningService.provisionTenant(
        req.body,
        req.user?.id
      )
      
      res.status(201).json({
        success: true,
        data: {
          message: 'Tenant provisioned successfully',
          provisioningId: result.provisioningId,
          status: result.status,
          tenant: {
            id: result.tenant.id,
            name: result.tenant.name,
            slug: result.tenant.slug,
            subdomain: result.tenant.subdomain,
            status: result.tenant.status,
            plan: result.tenant.plan
          },
          adminUser: {
            id: result.adminUser.id,
            email: result.adminUser.email,
            firstName: result.adminUser.firstName,
            lastName: result.adminUser.lastName
          },
          migrationResults: result.migrationResults?.length || 0,
          errors: result.errors
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /deprovision - Deprovision a tenant
router.post('/deprovision',
  authenticateToken,
  requirePermission('system:tenants:deprovision'),
  validateRequest(deprovisionTenantSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId, options } = req.body
      
      await TenantProvisioningService.deprovisionTenant(
        tenantId,
        req.user?.id!,
        options
      )
      
      res.status(200).json({
        success: true,
        data: {
          message: 'Tenant deprovisioned successfully',
          tenantId
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /provision/validate - Validate provisioning request without creating
router.post('/provision/validate',
  authenticateToken,
  requirePermission('system:tenants:provision'),
  validateRequest(provisionTenantSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      // This would call a validation method that doesn't actually create anything
      // For now, we'll just validate the schema (which already happened)
      
      res.status(200).json({
        success: true,
        data: {
          message: 'Provisioning request is valid',
          valid: true
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /provision/status/:provisioningId - Get provisioning status
router.get('/provision/status/:provisioningId',
  authenticateToken,
  requirePermission('system:tenants:read'),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { provisioningId } = req.params
      
      // In a real implementation, this would check the status of an ongoing provisioning
      // For now, we'll return a placeholder response
      
      res.status(200).json({
        success: true,
        data: {
          provisioningId,
          status: 'completed',
          message: 'Provisioning status check not yet implemented'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /provision/bulk - Bulk provision multiple tenants
router.post('/provision/bulk',
  authenticateToken,
  requirePermission('system:tenants:provision'),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenants } = req.body
      
      if (!Array.isArray(tenants) || tenants.length === 0) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_REQUEST',
            message: 'Tenants array is required and must not be empty'
          }
        })
      }
      
      const results = []
      const errors = []
      
      for (const tenantRequest of tenants) {
        try {
          const result = await TenantProvisioningService.provisionTenant(
            tenantRequest,
            req.user?.id
          )
          results.push({
            success: true,
            tenant: result.tenant,
            provisioningId: result.provisioningId
          })
        } catch (error) {
          errors.push({
            success: false,
            tenantData: tenantRequest.tenantData,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }
      
      res.status(200).json({
        success: true,
        data: {
          message: 'Bulk provisioning completed',
          total: tenants.length,
          successful: results.length,
          failed: errors.length,
          results,
          errors: errors.length > 0 ? errors : undefined
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /provision/templates - Get provisioning templates
router.get('/provision/templates',
  authenticateToken,
  requirePermission('system:tenants:read'),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const templates = {
        starter: {
          name: 'Starter Template',
          description: 'Basic tenant setup for small teams',
          tenantData: {
            plan: 'STARTER',
            size: 'SMALL',
            features: {
              apiAccess: false,
              customBranding: false,
              advancedReporting: false
            },
            limits: {
              maxUsers: 5,
              maxDeals: 10,
              maxStorage: 1024 * 1024 * 1024 // 1GB
            }
          },
          options: {
            skipMigrations: false,
            skipDefaultRoles: false,
            skipWelcomeEmail: false
          }
        },
        professional: {
          name: 'Professional Template',
          description: 'Advanced features for growing businesses',
          tenantData: {
            plan: 'PROFESSIONAL',
            size: 'MEDIUM',
            features: {
              apiAccess: true,
              customBranding: true,
              advancedReporting: true
            },
            limits: {
              maxUsers: 25,
              maxDeals: 100,
              maxStorage: 10 * 1024 * 1024 * 1024 // 10GB
            }
          },
          options: {
            skipMigrations: false,
            skipDefaultRoles: false,
            skipWelcomeEmail: false
          }
        },
        enterprise: {
          name: 'Enterprise Template',
          description: 'Full-featured setup for large organizations',
          tenantData: {
            plan: 'ENTERPRISE',
            size: 'LARGE',
            features: {
              apiAccess: true,
              customBranding: true,
              advancedReporting: true,
              sso: true,
              customIntegrations: true
            },
            limits: {
              maxUsers: -1, // Unlimited
              maxDeals: -1, // Unlimited
              maxStorage: -1 // Unlimited
            }
          },
          options: {
            skipMigrations: false,
            skipDefaultRoles: false,
            skipWelcomeEmail: false
          }
        }
      }
      
      res.status(200).json({
        success: true,
        data: templates
      })
    } catch (error) {
      next(error)
    }
  }
)

export { router as tenantProvisioningRoutes }
