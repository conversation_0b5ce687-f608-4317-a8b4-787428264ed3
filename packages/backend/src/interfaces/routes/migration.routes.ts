import { Router, Request, Response, NextFunction } from 'express'
import { MigrationService } from '@/application/services/migration.service'
import { authenticateToken, AuthenticatedRequest } from '@/shared/middleware/auth'
import { requirePermission } from '@/shared/middleware/permissions'
import { resolveTenant, TenantRequest } from '@/shared/middleware/tenant'

const router = Router()

// Apply tenant resolution to tenant-specific routes
router.use('/tenant', resolveTenant)

// GET /migrations/global/status - Get global migration status
router.get('/global/status',
  authenticateToken,
  requirePermission('system:migrations:read'),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const status = await MigrationService.getGlobalMigrationStatus()
      
      res.status(200).json({
        success: true,
        data: status
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /migrations/global/execute - Execute pending global migrations
router.post('/global/execute',
  authenticateToken,
  requirePermission('system:migrations:execute'),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const results = await MigrationService.executeGlobalMigrations(req.user?.id!)
      
      res.status(200).json({
        success: true,
        data: {
          message: 'Global migrations executed',
          results
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /migrations/tenant/status - Get tenant migration status
router.get('/tenant/status',
  authenticateToken,
  requirePermission('tenant:migrations:read'),
  async (req: AuthenticatedRequest & TenantRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.tenantContext) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'TENANT_REQUIRED',
            message: 'Tenant context is required'
          }
        })
      }

      const status = await MigrationService.getTenantMigrationStatus(req.tenantContext.id)
      
      res.status(200).json({
        success: true,
        data: status
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /migrations/tenant/execute - Execute pending tenant migrations
router.post('/tenant/execute',
  authenticateToken,
  requirePermission('tenant:migrations:execute'),
  async (req: AuthenticatedRequest & TenantRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.tenantContext) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'TENANT_REQUIRED',
            message: 'Tenant context is required'
          }
        })
      }

      const results = await MigrationService.executePendingMigrations(
        req.tenantContext.id,
        req.user?.id!
      )
      
      res.status(200).json({
        success: true,
        data: {
          message: 'Tenant migrations executed',
          results
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /migrations/tenant/initialize - Initialize migrations for a new tenant
router.post('/tenant/initialize',
  authenticateToken,
  requirePermission('tenant:migrations:execute'),
  async (req: AuthenticatedRequest & TenantRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.tenantContext) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'TENANT_REQUIRED',
            message: 'Tenant context is required'
          }
        })
      }

      await MigrationService.initializeTenantMigrations(
        req.tenantContext.id,
        req.user?.id!
      )
      
      res.status(200).json({
        success: true,
        data: {
          message: 'Tenant migrations initialized successfully'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /migrations/tenant/rollback/:migrationId - Rollback a specific migration
router.post('/tenant/rollback/:migrationId',
  authenticateToken,
  requirePermission('tenant:migrations:rollback'),
  async (req: AuthenticatedRequest & TenantRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.tenantContext) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'TENANT_REQUIRED',
            message: 'Tenant context is required'
          }
        })
      }

      const { migrationId } = req.params

      await MigrationService.rollbackMigration(
        migrationId,
        req.tenantContext.id,
        req.user?.id!
      )
      
      res.status(200).json({
        success: true,
        data: {
          message: `Migration ${migrationId} rolled back successfully`
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /migrations/registered - Get all registered migrations
router.get('/registered',
  authenticateToken,
  requirePermission('system:migrations:read'),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const migrations = MigrationService.getRegisteredMigrations()
      
      res.status(200).json({
        success: true,
        data: migrations.map(m => ({
          id: m.id,
          name: m.name,
          description: m.description,
          version: m.version,
          tenantSpecific: m.tenantSpecific
        }))
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /migrations/tenant/:tenantId/status - Get migration status for specific tenant (admin only)
router.get('/tenant/:tenantId/status',
  authenticateToken,
  requirePermission('system:migrations:read'),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId } = req.params
      const status = await MigrationService.getTenantMigrationStatus(tenantId)
      
      res.status(200).json({
        success: true,
        data: status
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /migrations/tenant/:tenantId/execute - Execute migrations for specific tenant (admin only)
router.post('/tenant/:tenantId/execute',
  authenticateToken,
  requirePermission('system:migrations:execute'),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId } = req.params
      
      const results = await MigrationService.executePendingMigrations(
        tenantId,
        req.user?.id!
      )
      
      res.status(200).json({
        success: true,
        data: {
          message: `Migrations executed for tenant ${tenantId}`,
          results
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /migrations/tenant/:tenantId/initialize - Initialize migrations for specific tenant (admin only)
router.post('/tenant/:tenantId/initialize',
  authenticateToken,
  requirePermission('system:migrations:execute'),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId } = req.params
      
      await MigrationService.initializeTenantMigrations(tenantId, req.user?.id!)
      
      res.status(200).json({
        success: true,
        data: {
          message: `Migrations initialized for tenant ${tenantId}`
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

export { router as migrationRoutes }
