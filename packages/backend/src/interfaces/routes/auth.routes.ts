import { Router, Request, Response, NextFunction } from 'express'
import { z } from 'zod'
import { AuthService } from '@/application/services/auth.service'
import { authRateLimit } from '@/shared/middleware/security'
import { authenticateToken, AuthenticatedRequest } from '@/shared/middleware/auth'
import { ValidationError } from '@/shared/middleware/error-handler'

const router = Router()

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
  tenantId: z.string().optional(),
  mfaCode: z.string().optional()
})

const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  tenantId: z.string().min(1, 'Tenant ID is required')
})

const passwordResetRequestSchema = z.object({
  email: z.string().email('Invalid email format')
})

const passwordResetSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
})

// Validation middleware
function validateRequest(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
        
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: validationErrors
          }
        })
      }
      next(error)
    }
  }
}

// POST /auth/register
router.post('/register', 
  authRateLimit,
  validateRequest(registerSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = await AuthService.register(req.body, req.ip)
      
      res.status(201).json({
        success: true,
        data: {
          user: result.user,
          token: result.token,
          requiresMfa: result.requiresMfa
        },
        meta: {
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /auth/login
router.post('/login',
  authRateLimit,
  validateRequest(loginSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = await AuthService.login(
        req.body,
        req.ip,
        req.get('user-agent')
      )
      
      if (result.requiresMfa) {
        return res.status(200).json({
          success: true,
          data: {
            requiresMfa: true,
            message: 'MFA code required'
          }
        })
      }
      
      res.status(200).json({
        success: true,
        data: {
          user: result.user,
          token: result.token,
          requiresMfa: false
        },
        meta: {
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /auth/logout
router.post('/logout',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const sessionId = req.headers['x-session-id'] as string
      
      if (sessionId && req.user) {
        await AuthService.logout(sessionId, req.user.id, req.ip)
      }
      
      res.status(200).json({
        success: true,
        data: {
          message: 'Logged out successfully'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /auth/refresh
router.post('/refresh',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const sessionId = req.headers['x-session-id'] as string
      
      if (!sessionId) {
        throw new ValidationError('Session ID required')
      }
      
      const result = await AuthService.refreshToken(sessionId)
      
      res.status(200).json({
        success: true,
        data: {
          token: result.token
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /auth/forgot-password
router.post('/forgot-password',
  authRateLimit,
  validateRequest(passwordResetRequestSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      await AuthService.requestPasswordReset(req.body.email, req.ip)
      
      // Always return success to prevent email enumeration
      res.status(200).json({
        success: true,
        data: {
          message: 'If an account with that email exists, a password reset link has been sent.'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /auth/reset-password
router.post('/reset-password',
  authRateLimit,
  validateRequest(passwordResetSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      await AuthService.resetPassword(
        req.body.token,
        req.body.password,
        req.ip
      )
      
      res.status(200).json({
        success: true,
        data: {
          message: 'Password reset successfully'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /auth/me
router.get('/me',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }
      
      res.status(200).json({
        success: true,
        data: {
          user: req.user
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /auth/session
router.get('/session',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const sessionId = req.headers['x-session-id'] as string
      
      if (!sessionId) {
        throw new ValidationError('Session ID required')
      }
      
      // TODO: Get session details
      
      res.status(200).json({
        success: true,
        data: {
          sessionId,
          user: req.user,
          tenant: req.tenant
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

export { router as authRoutes }
