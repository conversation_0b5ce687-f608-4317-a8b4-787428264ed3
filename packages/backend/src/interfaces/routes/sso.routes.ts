import { Router, Request, Response, NextFunction } from 'express'
import { z } from 'zod'
import { SsoService } from '@/application/services/sso.service'
import { authRateLimit } from '@/shared/middleware/security'
import { ValidationError } from '@/shared/middleware/error-handler'

const router = Router()

// Validation schemas
const authUrlSchema = z.object({
  provider: z.string().min(1, 'Provider is required'),
  tenantId: z.string().min(1, 'Tenant ID is required'),
  redirectUri: z.string().url('Invalid redirect URI'),
  state: z.string().optional()
})

const callbackSchema = z.object({
  code: z.string().min(1, 'Authorization code is required'),
  state: z.string().min(1, 'State parameter is required')
})

// Validation middleware
function validateRequest(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.method === 'GET' ? req.query : req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
        
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: validationErrors
          }
        })
      }
      next(error)
    }
  }
}

// GET /sso/providers/:tenantId
router.get('/providers/:tenantId',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId } = req.params
      
      if (!tenantId) {
        throw new ValidationError('Tenant ID is required')
      }

      const providers = await SsoService.getProviders(tenantId)
      
      // Remove sensitive information before sending to client
      const publicProviders = providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        type: provider.type,
        enabled: provider.enabled
      }))

      res.status(200).json({
        success: true,
        data: {
          providers: publicProviders
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /sso/auth
router.get('/auth',
  authRateLimit,
  validateRequest(authUrlSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { provider, tenantId, redirectUri, state } = req.query as any

      const authUrl = await SsoService.generateAuthUrl(
        provider,
        tenantId,
        redirectUri,
        state
      )

      res.status(200).json({
        success: true,
        data: {
          authUrl
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /sso/callback
router.get('/callback',
  authRateLimit,
  validateRequest(callbackSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { code, state } = req.query as any

      const result = await SsoService.handleCallback(
        code,
        state,
        req.ip,
        req.get('user-agent')
      )

      res.status(200).json({
        success: true,
        data: {
          user: result.user,
          token: result.token,
          isNewUser: result.isNewUser
        },
        meta: {
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /sso/callback (for POST-based callbacks)
router.post('/callback',
  authRateLimit,
  validateRequest(callbackSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { code, state } = req.body

      const result = await SsoService.handleCallback(
        code,
        state,
        req.ip,
        req.get('user-agent')
      )

      res.status(200).json({
        success: true,
        data: {
          user: result.user,
          token: result.token,
          isNewUser: result.isNewUser
        },
        meta: {
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /sso/redirect/:provider/:tenantId
// Convenience endpoint that redirects directly to provider
router.get('/redirect/:provider/:tenantId',
  authRateLimit,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { provider, tenantId } = req.params
      const { redirectUri, state } = req.query as any

      if (!redirectUri) {
        throw new ValidationError('Redirect URI is required')
      }

      const authUrl = await SsoService.generateAuthUrl(
        provider,
        tenantId,
        redirectUri,
        state
      )

      // Redirect user to provider
      res.redirect(authUrl)
    } catch (error) {
      next(error)
    }
  }
)

export { router as ssoRoutes }
