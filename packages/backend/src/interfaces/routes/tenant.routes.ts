import { Router, Request, Response, NextFunction } from 'express'
import { z } from 'zod'
import { TenantService } from '@/application/services/tenant.service'
import { authenticateToken, AuthenticatedRequest } from '@/shared/middleware/auth'
import { requirePermission } from '@/shared/middleware/permissions'
import { ValidationError } from '@/shared/middleware/error-handler'

const router = Router()

// Validation schemas
const createTenantSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  slug: z.string().optional(),
  subdomain: z.string().min(1, 'Subdomain is required').max(50, 'Subdomain too long')
    .regex(/^[a-z0-9-]+$/, 'Subdomain must contain only lowercase letters, numbers, and hyphens'),
  domain: z.string().optional(),
  description: z.string().optional(),
  industry: z.string().optional(),
  size: z.enum(['SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE']).optional(),
  plan: z.enum(['STARTER', 'PROFESSIONAL', 'ENTERPRISE', 'CUSTOM']).optional(),
  billingEmail: z.string().email().optional(),
  settings: z.any().optional(),
  features: z.any().optional(),
  limits: z.any().optional(),
  branding: z.any().optional()
})

const updateTenantSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  logo: z.string().optional(),
  website: z.string().url().optional(),
  industry: z.string().optional(),
  size: z.enum(['SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE']).optional(),
  plan: z.enum(['STARTER', 'PROFESSIONAL', 'ENTERPRISE', 'CUSTOM']).optional(),
  billingEmail: z.string().email().optional(),
  settings: z.any().optional(),
  features: z.any().optional(),
  limits: z.any().optional(),
  branding: z.any().optional()
})

const inviteUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  role: z.string().min(1, 'Role is required')
})

const acceptInvitationSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  password: z.string().min(8, 'Password must be at least 8 characters')
})

const generateApiKeySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  permissions: z.any().optional()
})

// Validation middleware
function validateRequest(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
        
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: validationErrors
          }
        })
      }
      next(error)
    }
  }
}

// POST /tenants - Create new tenant (admin only)
router.post('/',
  authenticateToken,
  requirePermission('tenants:create'),
  validateRequest(createTenantSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const tenant = await TenantService.createTenant(req.body, req.user?.id)
      
      res.status(201).json({
        success: true,
        data: tenant
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /tenants/:id - Get tenant by ID
router.get('/:id',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params
      
      // Users can only access their own tenant unless they have admin permissions
      if (req.user?.tenantId !== id && !req.user?.permissions?.includes('tenants:read')) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Access denied'
          }
        })
      }
      
      const tenant = await TenantService.getTenantById(id)
      
      res.status(200).json({
        success: true,
        data: tenant
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /tenants/slug/:slug - Get tenant by slug
router.get('/slug/:slug',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { slug } = req.params
      const tenant = await TenantService.getTenantBySlug(slug)
      
      // Return only public information for unauthenticated requests
      const publicTenant = {
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        description: tenant.description,
        logo: tenant.logo,
        website: tenant.website,
        industry: tenant.industry,
        branding: tenant.branding
      }
      
      res.status(200).json({
        success: true,
        data: publicTenant
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /tenants/subdomain/:subdomain - Get tenant by subdomain
router.get('/subdomain/:subdomain',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { subdomain } = req.params
      const tenant = await TenantService.getTenantBySubdomain(subdomain)
      
      // Return only public information
      const publicTenant = {
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        subdomain: tenant.subdomain,
        description: tenant.description,
        logo: tenant.logo,
        branding: tenant.branding
      }
      
      res.status(200).json({
        success: true,
        data: publicTenant
      })
    } catch (error) {
      next(error)
    }
  }
)

// PUT /tenants/:id - Update tenant
router.put('/:id',
  authenticateToken,
  requirePermission('tenants:update'),
  validateRequest(updateTenantSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params
      
      // Users can only update their own tenant unless they have admin permissions
      if (req.user?.tenantId !== id && !req.user?.permissions?.includes('tenants:update')) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Access denied'
          }
        })
      }
      
      const tenant = await TenantService.updateTenant(id, req.body, req.user?.id!)
      
      res.status(200).json({
        success: true,
        data: tenant
      })
    } catch (error) {
      next(error)
    }
  }
)

// DELETE /tenants/:id - Delete tenant
router.delete('/:id',
  authenticateToken,
  requirePermission('tenants:delete'),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params
      
      await TenantService.deleteTenant(id, req.user?.id!)
      
      res.status(200).json({
        success: true,
        data: {
          message: 'Tenant deleted successfully'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /tenants/:id/invite - Invite user to tenant
router.post('/:id/invite',
  authenticateToken,
  requirePermission('users:invite'),
  validateRequest(inviteUserSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params
      
      // Users can only invite to their own tenant
      if (req.user?.tenantId !== id) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Access denied'
          }
        })
      }
      
      const invitation = await TenantService.inviteUser(id, {
        ...req.body,
        invitedBy: req.user?.id!
      })
      
      res.status(201).json({
        success: true,
        data: invitation
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /tenants/accept-invitation - Accept tenant invitation
router.post('/accept-invitation',
  validateRequest(acceptInvitationSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = await TenantService.acceptInvitation(req.body.token, req.body)
      
      res.status(200).json({
        success: true,
        data: {
          user,
          message: 'Invitation accepted successfully'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /tenants/:id/api-keys - Generate API key
router.post('/:id/api-keys',
  authenticateToken,
  requirePermission('api_keys:create'),
  validateRequest(generateApiKeySchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params
      
      // Users can only generate API keys for their own tenant
      if (req.user?.tenantId !== id) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Access denied'
          }
        })
      }
      
      const apiKey = await TenantService.generateApiKey(id, req.body.name, req.body.permissions)
      
      res.status(201).json({
        success: true,
        data: apiKey
      })
    } catch (error) {
      next(error)
    }
  }
)

export { router as tenantRoutes }
