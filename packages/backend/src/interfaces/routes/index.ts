import { Router } from 'express'

const router = Router()

// API version
router.get('/', (req, res) => {
  res.json({
    message: 'M&A Enterprise Platform API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  })
})

// Health check
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
  })
})

// TODO: Add route modules
// router.use('/auth', authRoutes)
// router.use('/users', userRoutes)
// router.use('/tenants', tenantRoutes)
// router.use('/deals', dealRoutes)
// router.use('/documents', documentRoutes)

export { router as apiRoutes }
