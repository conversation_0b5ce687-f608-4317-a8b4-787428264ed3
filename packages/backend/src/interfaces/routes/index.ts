import { Router } from 'express'
import { authRoutes } from './auth.routes'
import { mfaRoutes } from './mfa.routes'
import { sessionRoutes } from './session.routes'
import { ssoRoutes } from './sso.routes'

const router = Router()

// API version
router.get('/', (req, res) => {
  res.json({
    message: 'M&A Enterprise Platform API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  })
})

// Health check
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
  })
})

// Authentication routes
router.use('/auth', authRoutes)

// SSO routes
router.use('/sso', ssoRoutes)

// MFA routes
router.use('/mfa', mfaRoutes)

// Session routes
router.use('/sessions', sessionRoutes)

// TODO: Add other route modules
// router.use('/users', userRoutes)
// router.use('/tenants', tenantRoutes)
// router.use('/deals', dealRoutes)
// router.use('/documents', documentRoutes)

export { router as apiRoutes }
