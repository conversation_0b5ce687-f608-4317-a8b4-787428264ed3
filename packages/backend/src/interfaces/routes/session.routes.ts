import { Router, Request, Response, NextFunction } from 'express'
import { z } from 'zod'
import { SessionService } from '@/application/services/session.service'
import { authenticateToken, AuthenticatedRequest } from '@/shared/middleware/auth'
import { ValidationError } from '@/shared/middleware/error-handler'

const router = Router()

// Validation schemas
const destroySessionSchema = z.object({
  sessionId: z.string().min(1, 'Session ID is required')
})

const extendSessionSchema = z.object({
  sessionId: z.string().min(1, 'Session ID is required'),
  additionalTime: z.number().optional()
})

// Validation middleware
function validateRequest(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
        
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: validationErrors
          }
        })
      }
      next(error)
    }
  }
}

// GET /sessions/current
router.get('/current',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const sessionId = req.headers['x-session-id'] as string
      if (!sessionId) {
        throw new ValidationError('Session ID required')
      }

      const sessionData = await SessionService.getSession(sessionId)
      
      if (!sessionData) {
        throw new ValidationError('Session not found')
      }

      res.status(200).json({
        success: true,
        data: {
          session: {
            id: sessionData.id,
            createdAt: sessionData.createdAt,
            lastAccessedAt: sessionData.lastAccessedAt,
            expiresAt: sessionData.expiresAt,
            ipAddress: sessionData.ipAddress,
            deviceInfo: sessionData.deviceInfo,
            location: sessionData.location
          }
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /sessions/all
router.get('/all',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const sessions = await SessionService.getUserSessions(req.user.id)
      const currentSessionId = req.headers['x-session-id'] as string

      const sessionList = sessions.map(session => ({
        id: session.id,
        createdAt: session.createdAt,
        lastAccessedAt: session.lastAccessedAt,
        expiresAt: session.expiresAt,
        ipAddress: session.ipAddress,
        deviceInfo: session.deviceInfo,
        location: session.location,
        isCurrent: session.id === currentSessionId
      }))

      res.status(200).json({
        success: true,
        data: {
          sessions: sessionList,
          total: sessionList.length
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /sessions/extend
router.post('/extend',
  authenticateToken,
  validateRequest(extendSessionSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const { sessionId, additionalTime } = req.body

      // Verify user owns this session
      const sessionData = await SessionService.getSession(sessionId)
      if (!sessionData || sessionData.userId !== req.user.id) {
        throw new ValidationError('Session not found or access denied')
      }

      await SessionService.extendSession(sessionId, additionalTime)

      res.status(200).json({
        success: true,
        data: {
          message: 'Session extended successfully'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// DELETE /sessions/:sessionId
router.delete('/:sessionId',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const { sessionId } = req.params

      // Verify user owns this session
      const sessionData = await SessionService.getSession(sessionId)
      if (!sessionData || sessionData.userId !== req.user.id) {
        throw new ValidationError('Session not found or access denied')
      }

      await SessionService.destroySession(sessionId)

      res.status(200).json({
        success: true,
        data: {
          message: 'Session destroyed successfully'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// DELETE /sessions/all
router.delete('/all',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const currentSessionId = req.headers['x-session-id'] as string
      const destroyedCount = await SessionService.destroyUserSessions(req.user.id, currentSessionId)

      res.status(200).json({
        success: true,
        data: {
          message: `${destroyedCount} sessions destroyed successfully`,
          destroyedCount
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /sessions/validate
router.post('/validate',
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const sessionId = req.headers['x-session-id'] as string
      
      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Session ID required'
          }
        })
      }

      const validation = await SessionService.validateSession(sessionId)

      res.status(200).json({
        success: true,
        data: {
          isValid: validation.isValid,
          user: validation.user ? {
            id: validation.user.id,
            email: validation.user.email,
            firstName: validation.user.firstName,
            lastName: validation.user.lastName,
            tenantId: validation.user.tenantId
          } : null
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /sessions/stats
router.get('/stats',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const stats = await SessionService.getSessionStats(req.user.id)

      res.status(200).json({
        success: true,
        data: stats
      })
    } catch (error) {
      next(error)
    }
  }
)

export { router as sessionRoutes }
