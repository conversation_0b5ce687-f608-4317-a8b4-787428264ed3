import { Router, Request, Response, NextFunction } from 'express'
import { z } from 'zod'
import { MfaService } from '@/application/services/mfa.service'
import { authenticateToken, AuthenticatedRequest } from '@/shared/middleware/auth'
import { authRateLimit } from '@/shared/middleware/security'
import { ValidationError } from '@/shared/middleware/error-handler'

const router = Router()

// Validation schemas
const verifySetupSchema = z.object({
  verificationCode: z.string()
    .length(6, 'Verification code must be 6 digits')
    .regex(/^\d{6}$/, 'Verification code must contain only digits')
})

const verifyCodeSchema = z.object({
  code: z.string().min(1, 'Code is required')
})

const disableMfaSchema = z.object({
  verificationCode: z.string().min(1, 'Verification code is required')
})

const generateBackupCodesSchema = z.object({
  verificationCode: z.string().min(1, 'Verification code is required')
})

// Validation middleware
function validateRequest(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
        
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: validationErrors
          }
        })
      }
      next(error)
    }
  }
}

// GET /mfa/status
router.get('/status',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const status = await MfaService.getMfaStatus(req.user.id)

      res.status(200).json({
        success: true,
        data: status
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /mfa/setup
router.post('/setup',
  authenticateToken,
  authRateLimit,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const setupResult = await MfaService.setupMfa(req.user.id, req.ip)

      res.status(200).json({
        success: true,
        data: {
          qrCodeUrl: setupResult.qrCodeUrl,
          manualEntryKey: setupResult.manualEntryKey,
          backupCodes: setupResult.backupCodes
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /mfa/verify-setup
router.post('/verify-setup',
  authenticateToken,
  authRateLimit,
  validateRequest(verifySetupSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const { verificationCode } = req.body

      const isValid = await MfaService.verifyMfaSetup(
        req.user.id,
        verificationCode,
        req.ip
      )

      res.status(200).json({
        success: true,
        data: {
          verified: isValid,
          message: 'MFA has been successfully enabled'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /mfa/verify
router.post('/verify',
  authenticateToken,
  authRateLimit,
  validateRequest(verifyCodeSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const { code } = req.body

      const result = await MfaService.verifyMfaCode(req.user.id, code)

      res.status(200).json({
        success: true,
        data: {
          valid: result.isValid,
          backupCodeUsed: result.backupCodeUsed
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /mfa/disable
router.post('/disable',
  authenticateToken,
  authRateLimit,
  validateRequest(disableMfaSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const { verificationCode } = req.body

      await MfaService.disableMfa(req.user.id, verificationCode, req.ip)

      res.status(200).json({
        success: true,
        data: {
          message: 'MFA has been disabled'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// POST /mfa/backup-codes/regenerate
router.post('/backup-codes/regenerate',
  authenticateToken,
  authRateLimit,
  validateRequest(generateBackupCodesSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const { verificationCode } = req.body

      const newBackupCodes = await MfaService.generateNewBackupCodes(
        req.user.id,
        verificationCode,
        req.ip
      )

      res.status(200).json({
        success: true,
        data: {
          backupCodes: newBackupCodes,
          message: 'New backup codes have been generated'
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

// GET /mfa/enabled
router.get('/enabled',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ValidationError('User not found')
      }

      const enabled = await MfaService.isMfaEnabled(req.user.id)

      res.status(200).json({
        success: true,
        data: {
          enabled
        }
      })
    } catch (error) {
      next(error)
    }
  }
)

export { router as mfaRoutes }
