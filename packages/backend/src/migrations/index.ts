import { MigrationService } from '@/application/services/migration.service'
import { createDefaultRolesMigration } from './001-create-default-roles'
import { setupTenantConfigurationMigration } from './002-setup-tenant-configuration'
import { globalSystemSettingsMigration } from './003-global-system-settings'

// Register all migrations
export function registerMigrations(): void {
  // Global migrations (run once for the entire system)
  MigrationService.registerMigration(globalSystemSettingsMigration)
  
  // Tenant-specific migrations (run for each tenant)
  MigrationService.registerMigration(createDefaultRolesMigration)
  MigrationService.registerMigration(setupTenantConfigurationMigration)
  
  console.log('All migrations registered successfully')
}

// Export individual migrations for testing
export {
  createDefaultRolesMigration,
  setupTenantConfigurationMigration,
  globalSystemSettingsMigration
}
