import { Router } from 'express'
import { PrismaClient } from '@prisma/client'
import { authenticate, authorize } from '@/middleware/auth'
import { validateRequest } from '@/middleware/validation'
import { CacheService } from '@/services/cache.service'
import { FileStorageService } from '@/services/file-storage.service'
import { NotificationService } from '@/services/notification.service'

// Import compliance services
import { ComplianceService } from '@/services/compliance/compliance.service'
import { ComplianceDocumentService } from '@/services/compliance/document-management.service'
import { ComplianceMonitoringService } from '@/services/compliance/monitoring.service'
import { ComplianceReportingService } from '@/services/compliance/reporting.service'
import { ComplianceIntegrationService } from '@/services/compliance/integration.service'

// Import other services
import { DealService } from '@/services/deal/deal.service'
import { UserService } from '@/services/user/user.service'
import { VDRService } from '@/services/vdr/vdr.service'
import { DDVDRIntegrationService } from '@/services/due-diligence/vdr-integration.service'

const router = Router()

// Initialize services
const prisma = new PrismaClient()
const cache = new CacheService()
const fileStorage = new FileStorageService()
const notificationService = new NotificationService()

const complianceService = new ComplianceService(prisma, cache)
const documentService = new ComplianceDocumentService(prisma, cache, fileStorage)
const monitoringService = new ComplianceMonitoringService(prisma, cache, notificationService)
const reportingService = new ComplianceReportingService(prisma, cache, fileStorage)

const dealService = new DealService(prisma, cache)
const userService = new UserService(prisma, cache)
const vdrService = new VDRService(prisma, cache, fileStorage)
const ddVdrIntegrationService = new DDVDRIntegrationService(prisma, cache, vdrService, fileStorage)

const integrationService = new ComplianceIntegrationService(
  prisma,
  cache,
  complianceService,
  documentService,
  monitoringService,
  reportingService,
  dealService,
  userService,
  notificationService,
  vdrService,
  ddVdrIntegrationService
)

// Compliance Management Routes

/**
 * Initialize compliance system for a deal
 */
router.post('/initialize', authenticate, authorize(['admin', 'compliance_manager']), async (req, res) => {
  try {
    const { dealId, tenantId, enableMonitoring, enableReporting, enableVDRIntegration, enableDueDiligenceIntegration, notificationSettings, reportingSettings } = req.body
    const userId = req.user.id

    const config = {
      dealId,
      tenantId,
      enableMonitoring: enableMonitoring ?? true,
      enableReporting: enableReporting ?? true,
      enableVDRIntegration: enableVDRIntegration ?? false,
      enableDueDiligenceIntegration: enableDueDiligenceIntegration ?? false,
      notificationSettings: notificationSettings || {
        channels: ['EMAIL'],
        recipients: [req.user.email],
        alertThresholds: { critical: 1, high: 3, medium: 5 }
      },
      reportingSettings: reportingSettings || {
        autoGenerateReports: true,
        reportFrequency: 'WEEKLY',
        reportRecipients: [req.user.email]
      }
    }

    const assessment = await integrationService.initializeComplianceSystem(config, userId)

    res.json({
      success: true,
      data: assessment
    })
  } catch (error) {
    console.error('Failed to initialize compliance system:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to initialize compliance system'
    })
  }
})

/**
 * Get compliance assessment for a deal
 */
router.get('/assessment/:dealId', authenticate, async (req, res) => {
  try {
    const { dealId } = req.params
    const { tenantId } = req.user

    const assessment = await complianceService.getComplianceAssessment(dealId, tenantId)

    res.json({
      success: true,
      data: assessment
    })
  } catch (error) {
    console.error('Failed to get compliance assessment:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get compliance assessment'
    })
  }
})

/**
 * Update compliance status
 */
router.put('/status/:statusId', authenticate, async (req, res) => {
  try {
    const { statusId } = req.params
    const updates = req.body
    const userId = req.user.id

    const updatedStatus = await complianceService.updateComplianceStatus(statusId, updates, userId)

    res.json({
      success: true,
      data: updatedStatus
    })
  } catch (error) {
    console.error('Failed to update compliance status:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to update compliance status'
    })
  }
})

/**
 * Get system status
 */
router.get('/system/status/:dealId', authenticate, async (req, res) => {
  try {
    const { dealId } = req.params
    const { tenantId } = req.user

    const systemStatus = await integrationService.getSystemStatus(dealId, tenantId)

    res.json({
      success: true,
      data: systemStatus
    })
  } catch (error) {
    console.error('Failed to get system status:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get system status'
    })
  }
})

// Document Management Routes

/**
 * Upload compliance document
 */
router.post('/documents/upload', authenticate, async (req, res) => {
  try {
    const { complianceStatusId, name, type, description, version, metadata } = req.body
    const file = req.file // Assuming multer middleware
    const userId = req.user.id
    const { tenantId } = req.user

    if (!file) {
      return res.status(400).json({
        success: false,
        error: 'No file provided'
      })
    }

    const document = await documentService.uploadDocument(
      file.buffer,
      file.originalname,
      file.mimetype,
      { complianceStatusId, name, type, description, version, metadata },
      userId,
      tenantId
    )

    res.json({
      success: true,
      data: document
    })
  } catch (error) {
    console.error('Failed to upload document:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to upload document'
    })
  }
})

/**
 * Review document
 */
router.post('/documents/:documentId/review', authenticate, authorize(['admin', 'compliance_manager', 'reviewer']), async (req, res) => {
  try {
    const { documentId } = req.params
    const { approved, reviewComments, requiredChanges, nextSteps } = req.body
    const userId = req.user.id

    const reviewResult = { approved, reviewComments, requiredChanges, nextSteps }
    const document = await documentService.reviewDocument(documentId, reviewResult, userId)

    res.json({
      success: true,
      data: document
    })
  } catch (error) {
    console.error('Failed to review document:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to review document'
    })
  }
})

/**
 * Search documents
 */
router.get('/documents/search', authenticate, async (req, res) => {
  try {
    const { dealId, complianceStatusId, documentType, status, submittedBy, searchTerm, limit, offset } = req.query
    const { tenantId } = req.user

    const criteria = {
      dealId: dealId as string,
      complianceStatusId: complianceStatusId as string,
      documentType: documentType as string,
      status: status as any,
      submittedBy: submittedBy as string,
      searchTerm: searchTerm as string
    }

    const options = {
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined
    }

    const result = await documentService.searchDocuments(criteria, tenantId, options)

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Failed to search documents:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to search documents'
    })
  }
})

// Monitoring Routes

/**
 * Get monitoring dashboard
 */
router.get('/monitoring/dashboard/:dealId', authenticate, async (req, res) => {
  try {
    const { dealId } = req.params
    const { tenantId } = req.user

    const dashboard = await monitoringService.getMonitoringDashboard(dealId, tenantId)

    res.json({
      success: true,
      data: dashboard
    })
  } catch (error) {
    console.error('Failed to get monitoring dashboard:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get monitoring dashboard'
    })
  }
})

/**
 * Acknowledge alert
 */
router.post('/monitoring/alerts/:alertId/acknowledge', authenticate, async (req, res) => {
  try {
    const { alertId } = req.params
    const { notes } = req.body
    const userId = req.user.id

    const alert = await monitoringService.acknowledgeAlert(alertId, userId, notes)

    res.json({
      success: true,
      data: alert
    })
  } catch (error) {
    console.error('Failed to acknowledge alert:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to acknowledge alert'
    })
  }
})

/**
 * Resolve alert
 */
router.post('/monitoring/alerts/:alertId/resolve', authenticate, async (req, res) => {
  try {
    const { alertId } = req.params
    const { resolution } = req.body
    const userId = req.user.id

    const alert = await monitoringService.resolveAlert(alertId, userId, resolution)

    res.json({
      success: true,
      data: alert
    })
  } catch (error) {
    console.error('Failed to resolve alert:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to resolve alert'
    })
  }
})

// Reporting Routes

/**
 * Generate report
 */
router.post('/reports/generate', authenticate, async (req, res) => {
  try {
    const reportConfig = req.body
    const userId = req.user.id

    const report = await reportingService.generateReport(reportConfig, userId)

    res.json({
      success: true,
      data: report
    })
  } catch (error) {
    console.error('Failed to generate report:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to generate report'
    })
  }
})

/**
 * Generate executive summary
 */
router.post('/reports/executive-summary/:dealId', authenticate, async (req, res) => {
  try {
    const { dealId } = req.params
    const { tenantId } = req.user
    const userId = req.user.id

    const report = await reportingService.generateExecutiveSummary(dealId, tenantId, userId)

    res.json({
      success: true,
      data: report
    })
  } catch (error) {
    console.error('Failed to generate executive summary:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to generate executive summary'
    })
  }
})

/**
 * Get user reports
 */
router.get('/reports', authenticate, async (req, res) => {
  try {
    const { dealId, type, limit, offset } = req.query
    const userId = req.user.id
    const { tenantId } = req.user

    const options = {
      dealId: dealId as string,
      type: type as any,
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined
    }

    const result = await reportingService.getUserReports(userId, tenantId, options)

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Failed to get user reports:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get user reports'
    })
  }
})

/**
 * Get report templates
 */
router.get('/reports/templates', authenticate, async (req, res) => {
  try {
    const { tenantId } = req.user

    const templates = await reportingService.getReportTemplates(tenantId)

    res.json({
      success: true,
      data: templates
    })
  } catch (error) {
    console.error('Failed to get report templates:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get report templates'
    })
  }
})

// Integration Routes

/**
 * Sync with external systems
 */
router.post('/integration/sync/:dealId', authenticate, authorize(['admin', 'compliance_manager']), async (req, res) => {
  try {
    const { dealId } = req.params
    const { tenantId } = req.user

    await integrationService.syncWithExternalSystems(dealId, tenantId)

    res.json({
      success: true,
      message: 'Sync completed successfully'
    })
  } catch (error) {
    console.error('Failed to sync with external systems:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to sync with external systems'
    })
  }
})

/**
 * Handle compliance event
 */
router.post('/integration/events', authenticate, async (req, res) => {
  try {
    const { eventType, eventData, dealId, tenantId } = req.body

    await integrationService.handleComplianceEvent(eventType, eventData, dealId, tenantId)

    res.json({
      success: true,
      message: 'Event handled successfully'
    })
  } catch (error) {
    console.error('Failed to handle compliance event:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to handle compliance event'
    })
  }
})

export default router
