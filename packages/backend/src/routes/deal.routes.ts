import { Router } from 'express'
import { PrismaClient } from '@prisma/client'
import { DealController } from '@/controllers/deal.controller'
import { CacheService } from '@/services/cache.service'
import { authMiddleware } from '@/middleware/auth.middleware'
import { rbacMiddleware } from '@/middleware/rbac.middleware'
import { validateRequest } from '@/middleware/validation.middleware'
import { rateLimit } from 'express-rate-limit'
import { body, param, query } from 'express-validator'

export function createDealRoutes(prisma: PrismaClient, cache: CacheService): Router {
  const router = Router()
  const dealController = new DealController(prisma, cache)

  // Rate limiting
  const dealRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many deal requests from this IP, please try again later.'
  })

  // Apply middleware to all routes
  router.use(authMiddleware)
  router.use(dealRateLimit)

  // Validation schemas
  const createDealValidation = [
    body('title')
      .isString()
      .isLength({ min: 1, max: 255 })
      .withMessage('Title must be between 1 and 255 characters'),
    body('description')
      .optional()
      .isString()
      .isLength({ max: 2000 })
      .withMessage('Description must be less than 2000 characters'),
    body('dealType')
      .isIn(['ACQUISITION', 'MERGER', 'ASSET_PURCHASE', 'STOCK_PURCHASE', 'JOINT_VENTURE', 'STRATEGIC_PARTNERSHIP', 'DIVESTITURE', 'SPIN_OFF', 'CARVE_OUT', 'RECAPITALIZATION'])
      .withMessage('Invalid deal type'),
    body('dealSource')
      .isIn(['DIRECT', 'INVESTMENT_BANK', 'BROKER', 'REFERRAL', 'COLD_OUTREACH', 'INBOUND', 'CONFERENCE', 'NETWORK', 'EXISTING_RELATIONSHIP', 'AUCTION'])
      .withMessage('Invalid deal source'),
    body('targetCompany')
      .isString()
      .isLength({ min: 1, max: 255 })
      .withMessage('Target company must be between 1 and 255 characters'),
    body('targetIndustry')
      .optional()
      .isString()
      .isLength({ max: 100 })
      .withMessage('Target industry must be less than 100 characters'),
    body('dealValue')
      .optional()
      .isNumeric()
      .isFloat({ min: 0 })
      .withMessage('Deal value must be a positive number'),
    body('currency')
      .optional()
      .isString()
      .isLength({ min: 3, max: 3 })
      .withMessage('Currency must be a 3-character code'),
    body('priority')
      .optional()
      .isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
      .withMessage('Invalid priority'),
    body('expectedCloseDate')
      .optional()
      .isISO8601()
      .withMessage('Expected close date must be a valid date'),
    body('assignedTo')
      .optional()
      .isString()
      .withMessage('Assigned to must be a valid user ID'),
    body('tags')
      .optional()
      .isArray()
      .withMessage('Tags must be an array'),
    body('confidentiality')
      .optional()
      .isString()
      .isLength({ max: 50 })
      .withMessage('Confidentiality must be less than 50 characters')
  ]

  const updateDealValidation = [
    body('title')
      .optional()
      .isString()
      .isLength({ min: 1, max: 255 })
      .withMessage('Title must be between 1 and 255 characters'),
    body('description')
      .optional()
      .isString()
      .isLength({ max: 2000 })
      .withMessage('Description must be less than 2000 characters'),
    body('status')
      .optional()
      .isIn(['PIPELINE', 'DUE_DILIGENCE', 'NEGOTIATION', 'CLOSING', 'CLOSED', 'CANCELLED'])
      .withMessage('Invalid status'),
    body('stage')
      .optional()
      .isString()
      .withMessage('Stage must be a string'),
    body('dealValue')
      .optional()
      .isNumeric()
      .isFloat({ min: 0 })
      .withMessage('Deal value must be a positive number'),
    body('priority')
      .optional()
      .isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
      .withMessage('Invalid priority'),
    body('expectedCloseDate')
      .optional()
      .isISO8601()
      .withMessage('Expected close date must be a valid date'),
    body('assignedTo')
      .optional()
      .isString()
      .withMessage('Assigned to must be a valid user ID'),
    body('probability')
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage('Probability must be between 0 and 100'),
    body('healthScore')
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage('Health score must be between 0 and 100'),
    body('riskLevel')
      .optional()
      .isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
      .withMessage('Invalid risk level')
  ]

  const dealIdValidation = [
    param('dealId')
      .isString()
      .isLength({ min: 1 })
      .withMessage('Deal ID is required')
  ]

  const reportParametersValidation = [
    body('dateRange.from')
      .isISO8601()
      .withMessage('From date must be a valid date'),
    body('dateRange.to')
      .isISO8601()
      .withMessage('To date must be a valid date'),
    body('filters')
      .optional()
      .isObject()
      .withMessage('Filters must be an object'),
    body('includeCharts')
      .optional()
      .isBoolean()
      .withMessage('Include charts must be a boolean'),
    body('includeDetails')
      .optional()
      .isBoolean()
      .withMessage('Include details must be a boolean')
  ]

  // Deal CRUD routes
  router.post(
    '/',
    rbacMiddleware('deal:create'),
    createDealValidation,
    validateRequest,
    dealController.createDeal
  )

  router.get(
    '/',
    rbacMiddleware('deal:read'),
    [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
      query('sortField')
        .optional()
        .isIn(['title', 'dealValue', 'expectedCloseDate', 'createdAt', 'updatedAt', 'priority', 'healthScore'])
        .withMessage('Invalid sort field'),
      query('sortDirection')
        .optional()
        .isIn(['asc', 'desc'])
        .withMessage('Sort direction must be asc or desc')
    ],
    validateRequest,
    dealController.getDeals
  )

  router.get(
    '/:dealId',
    rbacMiddleware('deal:read'),
    dealIdValidation,
    validateRequest,
    dealController.getDeal
  )

  router.put(
    '/:dealId',
    rbacMiddleware('deal:update'),
    dealIdValidation.concat(updateDealValidation),
    validateRequest,
    dealController.updateDeal
  )

  router.delete(
    '/:dealId',
    rbacMiddleware('deal:delete'),
    dealIdValidation,
    validateRequest,
    dealController.deleteDeal
  )

  // Deal workflow routes
  router.post(
    '/:dealId/move-to-next-stage',
    rbacMiddleware('deal:update'),
    dealIdValidation.concat([
      body('reason')
        .optional()
        .isString()
        .isLength({ max: 500 })
        .withMessage('Reason must be less than 500 characters')
    ]),
    validateRequest,
    dealController.moveToNextStage
  )

  router.post(
    '/:dealId/move-to-previous-stage',
    rbacMiddleware('deal:update'),
    dealIdValidation.concat([
      body('reason')
        .optional()
        .isString()
        .isLength({ max: 500 })
        .withMessage('Reason must be less than 500 characters')
    ]),
    validateRequest,
    dealController.moveToPreviousStage
  )

  // Analytics routes
  router.get(
    '/analytics/overview',
    rbacMiddleware('deal:analytics'),
    [
      query('dateFrom')
        .optional()
        .isISO8601()
        .withMessage('From date must be a valid date'),
      query('dateTo')
        .optional()
        .isISO8601()
        .withMessage('To date must be a valid date')
    ],
    validateRequest,
    dealController.getDealAnalytics
  )

  // Reporting routes
  router.post(
    '/reports/pipeline',
    rbacMiddleware('deal:reports'),
    reportParametersValidation,
    validateRequest,
    dealController.generatePipelineReport
  )

  router.post(
    '/reports/performance',
    rbacMiddleware('deal:reports'),
    reportParametersValidation,
    validateRequest,
    dealController.generatePerformanceReport
  )

  router.post(
    '/reports/forecast',
    rbacMiddleware('deal:reports'),
    reportParametersValidation,
    validateRequest,
    dealController.generateForecastReport
  )

  router.post(
    '/reports/export',
    rbacMiddleware('deal:reports'),
    [
      body('reportData')
        .isObject()
        .withMessage('Report data is required'),
      body('format')
        .isIn(['pdf', 'excel', 'csv', 'json', 'html'])
        .withMessage('Invalid export format')
    ],
    validateRequest,
    dealController.exportReport
  )

  return router
}
