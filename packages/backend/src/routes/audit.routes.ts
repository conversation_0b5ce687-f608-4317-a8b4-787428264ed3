import { Router } from 'express'
import { PrismaClient } from '@prisma/client'
import { AuditController } from '@/controllers/audit.controller'
import { rbacMiddleware } from '@/middleware/rbac.middleware'
import { authMiddleware } from '@/middleware/auth.middleware'
import { tenantMiddleware } from '@/middleware/tenant.middleware'
import { auditMiddleware } from '@/middleware/audit.middleware'
import { CacheService } from '@/services/cache.service'
import { Resource, Action } from '@/shared/types/rbac'

export function createAuditRoutes(prisma: PrismaClient, cache: CacheService): Router {
  const router = Router()
  const auditController = new AuditController(prisma, cache)
  const rbac = rbacMiddleware(prisma, cache)
  const audit = auditMiddleware(prisma, cache)

  // Apply authentication and tenant middleware to all routes
  router.use(authMiddleware(prisma))
  router.use(tenantMiddleware(prisma))
  router.use(rbac.init)

  // Audit events routes
  router.get(
    '/events',
    rbac.require({ resource: Resource.AUDIT_LOGS, action: Action.READ }),
    audit.logDataAccess('audit_logs'),
    auditController.getAuditEvents
  )

  router.get(
    '/events/:eventId',
    rbac.require({ resource: Resource.AUDIT_LOGS, action: Action.READ }),
    audit.logDataAccess('audit_logs', 'eventId'),
    auditController.getAuditEvent
  )

  // Audit statistics
  router.get(
    '/statistics',
    rbac.require({ resource: Resource.AUDIT_LOGS, action: Action.READ }),
    audit.logDataAccess('audit_statistics'),
    auditController.getAuditStatistics
  )

  // Export audit events
  router.post(
    '/export',
    rbac.require({ resource: Resource.AUDIT_LOGS, action: Action.EXPORT }),
    audit.logDataAccess('audit_export'),
    auditController.exportAuditEvents
  )

  // Audit metadata
  router.get(
    '/metadata',
    rbac.require({ resource: Resource.AUDIT_LOGS, action: Action.READ }),
    auditController.getAuditMetadata
  )

  // User activity
  router.get(
    '/users/:userId/activity',
    rbac.require({ resource: Resource.USERS, action: Action.READ }),
    audit.logDataAccess('user_activity', 'userId'),
    auditController.getUserActivity
  )

  // Security events
  router.get(
    '/security',
    rbac.require({ resource: Resource.AUDIT_LOGS, action: Action.READ }),
    audit.logDataAccess('security_events'),
    auditController.getSecurityEvents
  )

  return router
}
