import { prisma } from '@/infrastructure/database/prisma'
import { logger } from '@/shared/logger'
import { maskSensitiveData } from '@/shared/utils/encryption'

export interface AuditLogEntry {
  action: string
  entity: string
  entityId: string
  userId: string
  tenantId?: string
  oldValues?: any
  newValues?: any
  ipAddress?: string
  userAgent?: string
  metadata?: any
}

export class AuditService {
  // Log user actions
  static async logAction(entry: AuditLogEntry): Promise<void> {
    try {
      // Mask sensitive data before logging
      const maskedOldValues = entry.oldValues ? maskSensitiveData(entry.oldValues) : null
      const maskedNewValues = entry.newValues ? maskSensitiveData(entry.newValues) : null

      // Store in database
      await prisma.auditLog.create({
        data: {
          action: entry.action,
          entity: entry.entity,
          entityId: entry.entityId,
          userId: entry.userId,
          oldValues: maskedOldValues,
          newValues: maskedNewValues,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
          createdAt: new Date()
        }
      })

      // Also log to application logs for real-time monitoring
      logger.info('Audit log entry created', {
        action: entry.action,
        entity: entry.entity,
        entityId: entry.entityId,
        userId: entry.userId,
        tenantId: entry.tenantId,
        ipAddress: entry.ipAddress
      })
    } catch (error) {
      logger.error('Failed to create audit log entry', error)
      // Don't throw error to avoid breaking the main operation
    }
  }

  // Log authentication events
  static async logAuth(
    action: 'login' | 'logout' | 'login_failed' | 'password_reset' | 'mfa_enabled' | 'mfa_disabled',
    userId: string,
    ipAddress?: string,
    userAgent?: string,
    metadata?: any
  ): Promise<void> {
    await this.logAction({
      action: `auth.${action}`,
      entity: 'user',
      entityId: userId,
      userId,
      ipAddress,
      userAgent,
      metadata
    })
  }

  // Log data access events
  static async logDataAccess(
    action: 'read' | 'export' | 'download',
    entity: string,
    entityId: string,
    userId: string,
    ipAddress?: string,
    userAgent?: string,
    metadata?: any
  ): Promise<void> {
    await this.logAction({
      action: `data.${action}`,
      entity,
      entityId,
      userId,
      ipAddress,
      userAgent,
      metadata
    })
  }

  // Log CRUD operations
  static async logCreate(
    entity: string,
    entityId: string,
    userId: string,
    newValues: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logAction({
      action: 'create',
      entity,
      entityId,
      userId,
      newValues,
      ipAddress,
      userAgent
    })
  }

  static async logUpdate(
    entity: string,
    entityId: string,
    userId: string,
    oldValues: any,
    newValues: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logAction({
      action: 'update',
      entity,
      entityId,
      userId,
      oldValues,
      newValues,
      ipAddress,
      userAgent
    })
  }

  static async logDelete(
    entity: string,
    entityId: string,
    userId: string,
    oldValues: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logAction({
      action: 'delete',
      entity,
      entityId,
      userId,
      oldValues,
      ipAddress,
      userAgent
    })
  }

  // Log security events
  static async logSecurityEvent(
    event: 'unauthorized_access' | 'permission_denied' | 'suspicious_activity' | 'rate_limit_exceeded',
    userId?: string,
    ipAddress?: string,
    userAgent?: string,
    metadata?: any
  ): Promise<void> {
    await this.logAction({
      action: `security.${event}`,
      entity: 'system',
      entityId: 'security',
      userId: userId || 'anonymous',
      ipAddress,
      userAgent,
      metadata
    })
  }

  // Log admin actions
  static async logAdminAction(
    action: string,
    targetEntity: string,
    targetEntityId: string,
    adminUserId: string,
    ipAddress?: string,
    userAgent?: string,
    metadata?: any
  ): Promise<void> {
    await this.logAction({
      action: `admin.${action}`,
      entity: targetEntity,
      entityId: targetEntityId,
      userId: adminUserId,
      ipAddress,
      userAgent,
      metadata
    })
  }

  // Get audit logs for an entity
  static async getEntityAuditLogs(
    entity: string,
    entityId: string,
    options: {
      limit?: number
      offset?: number
      startDate?: Date
      endDate?: Date
      actions?: string[]
    } = {}
  ) {
    const where: any = {
      entity,
      entityId
    }

    if (options.startDate || options.endDate) {
      where.createdAt = {}
      if (options.startDate) {
        where.createdAt.gte = options.startDate
      }
      if (options.endDate) {
        where.createdAt.lte = options.endDate
      }
    }

    if (options.actions && options.actions.length > 0) {
      where.action = {
        in: options.actions
      }
    }

    return prisma.auditLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: options.limit || 50,
      skip: options.offset || 0
    })
  }

  // Get audit logs for a user
  static async getUserAuditLogs(
    userId: string,
    options: {
      limit?: number
      offset?: number
      startDate?: Date
      endDate?: Date
      actions?: string[]
    } = {}
  ) {
    const where: any = {
      userId
    }

    if (options.startDate || options.endDate) {
      where.createdAt = {}
      if (options.startDate) {
        where.createdAt.gte = options.startDate
      }
      if (options.endDate) {
        where.createdAt.lte = options.endDate
      }
    }

    if (options.actions && options.actions.length > 0) {
      where.action = {
        in: options.actions
      }
    }

    return prisma.auditLog.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      take: options.limit || 50,
      skip: options.offset || 0
    })
  }

  // Generate audit report
  static async generateAuditReport(
    tenantId: string,
    startDate: Date,
    endDate: Date,
    options: {
      entities?: string[]
      actions?: string[]
      userIds?: string[]
    } = {}
  ) {
    const where: any = {
      createdAt: {
        gte: startDate,
        lte: endDate
      },
      user: {
        tenantId
      }
    }

    if (options.entities && options.entities.length > 0) {
      where.entity = {
        in: options.entities
      }
    }

    if (options.actions && options.actions.length > 0) {
      where.action = {
        in: options.actions
      }
    }

    if (options.userIds && options.userIds.length > 0) {
      where.userId = {
        in: options.userIds
      }
    }

    const logs = await prisma.auditLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Generate summary statistics
    const summary = {
      totalEvents: logs.length,
      uniqueUsers: new Set(logs.map(log => log.userId)).size,
      eventsByAction: logs.reduce((acc, log) => {
        acc[log.action] = (acc[log.action] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      eventsByEntity: logs.reduce((acc, log) => {
        acc[log.entity] = (acc[log.entity] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      dateRange: {
        start: startDate,
        end: endDate
      }
    }

    return {
      summary,
      logs
    }
  }

  // Clean up old audit logs (for compliance)
  static async cleanupOldLogs(retentionDays: number = 2555): Promise<number> { // 7 years default
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

    const result = await prisma.auditLog.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate
        }
      }
    })

    logger.info(`Cleaned up ${result.count} old audit log entries`)
    return result.count
  }
}
