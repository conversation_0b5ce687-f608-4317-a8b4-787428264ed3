import { prisma } from '@/infrastructure/database/prisma'
import { NotFoundError, ValidationError } from '@/shared/middleware/error-handler'
import { decrypt, encrypt, generateSecureString } from '@/shared/utils/encryption'
import { authenticator } from 'otplib'
import QRCode from 'qrcode'
import { AuditService } from './audit.service'
import { CacheService } from './cache.service'

export interface MfaSetupResult {
  secret: string
  qrCodeUrl: string
  backupCodes: string[]
  manualEntryKey: string
}

export interface MfaVerificationResult {
  isValid: boolean
  backupCodeUsed?: boolean
}

export class MfaService {
  // Generate MFA secret and QR code for user
  static async setupMfa(userId: string, ipAddress?: string): Promise<MfaSetupResult> {
    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { tenant: true }
    })

    if (!user) {
      throw new NotFoundError('User not found')
    }

    // Generate secret
    const secret = authenticator.generateSecret()
    
    // Create service name for the app
    const serviceName = `M&A Platform (${user.tenant.name})`
    const accountName = user.email
    
    // Generate OTP Auth URL
    const otpAuthUrl = authenticator.keyuri(accountName, serviceName, secret)
    
    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(otpAuthUrl)
    
    // Generate backup codes
    const backupCodes = this.generateBackupCodes()
    
    // Encrypt and store MFA data temporarily (user hasn't confirmed yet)
    const encryptedSecret = encrypt(secret)
    const encryptedBackupCodes = backupCodes.map(code => encrypt(code))
    
    await CacheService.set(`mfa_setup:${userId}`, {
      secret: encryptedSecret,
      backupCodes: encryptedBackupCodes,
      createdAt: new Date()
    }, 1800) // 30 minutes
    
    // Log MFA setup initiation
    await AuditService.logAuth('mfa_setup_initiated', userId, ipAddress)
    
    return {
      secret,
      qrCodeUrl,
      backupCodes,
      manualEntryKey: secret
    }
  }

  // Verify MFA setup with initial code
  static async verifyMfaSetup(
    userId: string, 
    verificationCode: string, 
    ipAddress?: string
  ): Promise<boolean> {
    // Get temporary MFA data
    const mfaSetup = await CacheService.get(`mfa_setup:${userId}`)
    if (!mfaSetup) {
      throw new ValidationError('MFA setup session expired. Please start setup again.')
    }

    // Decrypt secret
    const secret = decrypt(mfaSetup.secret)
    
    // Verify the code
    const isValid = authenticator.verify({
      token: verificationCode,
      secret
    })

    if (!isValid) {
      throw new ValidationError('Invalid verification code')
    }

    // Save MFA configuration to database
    const encryptedSecret = encrypt(secret)
    const encryptedBackupCodes = mfaSetup.backupCodes

    // Store MFA configuration in database
    await prisma.userMfa.upsert({
      where: { userId },
      update: {
        secret: encryptedSecret,
        backupCodes: encryptedBackupCodes,
        enabled: true
      },
      create: {
        userId,
        secret: encryptedSecret,
        backupCodes: encryptedBackupCodes,
        enabled: true
      }
    })

    // Clear temporary setup data
    await CacheService.del(`mfa_setup:${userId}`)
    
    // Log MFA enablement
    await AuditService.logAuth('mfa_enabled', userId, ipAddress)
    
    return true
  }

  // Verify MFA code during login
  static async verifyMfaCode(userId: string, code: string): Promise<MfaVerificationResult> {
    // Get user's MFA configuration
    const mfaConfig = await this.getUserMfaConfig(userId)
    if (!mfaConfig || !mfaConfig.enabled) {
      throw new ValidationError('MFA is not enabled for this user')
    }

    // First try to verify as TOTP code
    const secret = decrypt(mfaConfig.secret)
    const isValidTotp = authenticator.verify({
      token: code,
      secret,
      window: 2 // Allow some time drift
    })

    if (isValidTotp) {
      return { isValid: true }
    }

    // If TOTP fails, try backup codes
    const backupCodes = mfaConfig.backupCodes.map((encryptedCode: string) => decrypt(encryptedCode))
    const backupCodeIndex = backupCodes.findIndex(backupCode => backupCode === code)

    if (backupCodeIndex !== -1) {
      // Remove used backup code
      backupCodes.splice(backupCodeIndex, 1)
      const updatedEncryptedCodes = backupCodes.map(code => encrypt(code))

      // Update database
      await prisma.userMfa.update({
        where: { userId },
        data: {
          backupCodes: updatedEncryptedCodes
        }
      })

      return { isValid: true, backupCodeUsed: true }
    }

    return { isValid: false }
  }

  // Disable MFA for user
  static async disableMfa(userId: string, verificationCode: string, ipAddress?: string): Promise<void> {
    // Verify current MFA code before disabling
    const verification = await this.verifyMfaCode(userId, verificationCode)
    if (!verification.isValid) {
      throw new ValidationError('Invalid verification code')
    }

    // Disable MFA
    await prisma.userMfa.update({
      where: { userId },
      data: { enabled: false }
    })

    // Log MFA disablement
    await AuditService.logAuth('mfa_disabled', userId, ipAddress)
  }

  // Generate new backup codes
  static async generateNewBackupCodes(
    userId: string, 
    verificationCode: string, 
    ipAddress?: string
  ): Promise<string[]> {
    // Verify current MFA code
    const verification = await this.verifyMfaCode(userId, verificationCode)
    if (!verification.isValid) {
      throw new ValidationError('Invalid verification code')
    }

    // Generate new backup codes
    const newBackupCodes = this.generateBackupCodes()
    const encryptedBackupCodes = newBackupCodes.map(code => encrypt(code))

    // Update database
    await prisma.userMfa.update({
      where: { userId },
      data: {
        backupCodes: encryptedBackupCodes
      }
    })

    // Log backup code regeneration
    await AuditService.logAuth('mfa_backup_codes_regenerated', userId, ipAddress)

    return newBackupCodes
  }

  // Check if user has MFA enabled
  static async isMfaEnabled(userId: string): Promise<boolean> {
    const mfaConfig = await this.getUserMfaConfig(userId)
    return mfaConfig?.enabled || false
  }

  // Get user's MFA status and backup code count
  static async getMfaStatus(userId: string): Promise<{
    enabled: boolean
    backupCodesRemaining: number
    setupDate?: Date
  }> {
    const mfaConfig = await this.getUserMfaConfig(userId)
    
    if (!mfaConfig || !mfaConfig.enabled) {
      return {
        enabled: false,
        backupCodesRemaining: 0
      }
    }

    const backupCodes = mfaConfig.backupCodes || []
    
    return {
      enabled: true,
      backupCodesRemaining: backupCodes.length,
      setupDate: mfaConfig.createdAt
    }
  }

  // Private helper methods
  private static generateBackupCodes(): string[] {
    const codes: string[] = []
    for (let i = 0; i < 10; i++) {
      // Generate 8-character alphanumeric codes
      codes.push(generateSecureString(8).toUpperCase())
    }
    return codes
  }

  private static async getUserMfaConfig(userId: string): Promise<any> {
    return prisma.userMfa.findUnique({
      where: { userId }
    })
  }

  // Validate TOTP code format
  static validateTotpCode(code: string): boolean {
    return /^\d{6}$/.test(code)
  }

  // Validate backup code format
  static validateBackupCode(code: string): boolean {
    return /^[A-Z0-9]{8}$/.test(code)
  }
}
