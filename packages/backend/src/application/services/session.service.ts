import { prisma } from '@/infrastructure/database/prisma'
import { CacheService } from './cache.service'
import { AuditService } from './audit.service'
import { generateSecureToken } from '@/shared/utils/encryption'
import { NotFoundError, UnauthorizedError } from '@/shared/middleware/error-handler'
import { v4 as uuidv4 } from 'uuid'

export interface SessionData {
  id: string
  userId: string
  tenantId: string
  ipAddress?: string
  userAgent?: string
  createdAt: Date
  lastAccessedAt: Date
  expiresAt: Date
  isActive: boolean
  deviceInfo?: {
    browser?: string
    os?: string
    device?: string
  }
  location?: {
    country?: string
    city?: string
    region?: string
  }
}

export interface CreateSessionOptions {
  userId: string
  tenantId: string
  ipAddress?: string
  userAgent?: string
  rememberMe?: boolean
  deviceInfo?: any
  location?: any
}

export class SessionService {
  // Create a new session
  static async createSession(options: CreateSessionOptions): Promise<SessionData> {
    const sessionId = uuidv4()
    const now = new Date()
    const expiresAt = new Date(now.getTime() + (options.rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000)) // 30 days or 1 day

    const sessionData: SessionData = {
      id: sessionId,
      userId: options.userId,
      tenantId: options.tenantId,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent,
      createdAt: now,
      lastAccessedAt: now,
      expiresAt,
      isActive: true,
      deviceInfo: options.deviceInfo,
      location: options.location
    }

    // Store in database
    await prisma.session.create({
      data: {
        id: sessionId,
        userId: options.userId,
        token: generateSecureToken(32), // Store a secure token reference
        expiresAt,
        createdAt: now
      }
    })

    // Store in cache for fast access
    await CacheService.createSession(sessionId, sessionData, Math.floor((expiresAt.getTime() - now.getTime()) / 1000))

    // Log session creation
    await AuditService.logAuth('session_created', options.userId, options.ipAddress, options.userAgent, {
      sessionId,
      rememberMe: options.rememberMe
    })

    return sessionData
  }

  // Get session by ID
  static async getSession(sessionId: string): Promise<SessionData | null> {
    // Try cache first
    let sessionData = await CacheService.getSession<SessionData>(sessionId)
    
    if (!sessionData) {
      // Fallback to database
      const dbSession = await prisma.session.findUnique({
        where: { id: sessionId },
        include: { user: true }
      })

      if (!dbSession || dbSession.expiresAt < new Date()) {
        return null
      }

      // Reconstruct session data
      sessionData = {
        id: dbSession.id,
        userId: dbSession.userId,
        tenantId: dbSession.user.tenantId,
        createdAt: dbSession.createdAt,
        lastAccessedAt: new Date(),
        expiresAt: dbSession.expiresAt,
        isActive: true
      }

      // Cache it
      const ttl = Math.floor((dbSession.expiresAt.getTime() - Date.now()) / 1000)
      if (ttl > 0) {
        await CacheService.createSession(sessionId, sessionData, ttl)
      }
    }

    // Check if session is expired
    if (sessionData.expiresAt < new Date()) {
      await this.destroySession(sessionId)
      return null
    }

    return sessionData
  }

  // Update session last accessed time
  static async touchSession(sessionId: string): Promise<void> {
    const sessionData = await this.getSession(sessionId)
    if (!sessionData) {
      return
    }

    sessionData.lastAccessedAt = new Date()
    
    // Update cache
    const ttl = Math.floor((sessionData.expiresAt.getTime() - Date.now()) / 1000)
    if (ttl > 0) {
      await CacheService.createSession(sessionId, sessionData, ttl)
    }
  }

  // Extend session expiration
  static async extendSession(sessionId: string, additionalTime: number = 24 * 60 * 60 * 1000): Promise<void> {
    const sessionData = await this.getSession(sessionId)
    if (!sessionData) {
      throw new NotFoundError('Session not found')
    }

    const newExpiresAt = new Date(sessionData.expiresAt.getTime() + additionalTime)
    sessionData.expiresAt = newExpiresAt

    // Update database
    await prisma.session.update({
      where: { id: sessionId },
      data: { expiresAt: newExpiresAt }
    })

    // Update cache
    const ttl = Math.floor((newExpiresAt.getTime() - Date.now()) / 1000)
    if (ttl > 0) {
      await CacheService.createSession(sessionId, sessionData, ttl)
    }

    // Log session extension
    await AuditService.logAuth('session_extended', sessionData.userId, undefined, undefined, {
      sessionId,
      newExpiresAt
    })
  }

  // Destroy a session
  static async destroySession(sessionId: string): Promise<void> {
    const sessionData = await this.getSession(sessionId)
    
    // Remove from cache
    await CacheService.deleteSession(sessionId)

    // Remove from database
    await prisma.session.delete({
      where: { id: sessionId }
    }).catch(() => {
      // Session might not exist in database, ignore error
    })

    // Log session destruction
    if (sessionData) {
      await AuditService.logAuth('session_destroyed', sessionData.userId, undefined, undefined, {
        sessionId
      })
    }
  }

  // Get all active sessions for a user
  static async getUserSessions(userId: string): Promise<SessionData[]> {
    const dbSessions = await prisma.session.findMany({
      where: {
        userId,
        expiresAt: {
          gt: new Date()
        }
      },
      include: { user: true },
      orderBy: { createdAt: 'desc' }
    })

    const sessions: SessionData[] = []

    for (const dbSession of dbSessions) {
      // Try to get from cache first
      let sessionData = await CacheService.getSession<SessionData>(dbSession.id)
      
      if (!sessionData) {
        // Reconstruct from database
        sessionData = {
          id: dbSession.id,
          userId: dbSession.userId,
          tenantId: dbSession.user.tenantId,
          createdAt: dbSession.createdAt,
          lastAccessedAt: new Date(),
          expiresAt: dbSession.expiresAt,
          isActive: true
        }
      }

      sessions.push(sessionData)
    }

    return sessions
  }

  // Destroy all sessions for a user (except current)
  static async destroyUserSessions(userId: string, exceptSessionId?: string): Promise<number> {
    const sessions = await this.getUserSessions(userId)
    let destroyedCount = 0

    for (const session of sessions) {
      if (session.id !== exceptSessionId) {
        await this.destroySession(session.id)
        destroyedCount++
      }
    }

    // Log bulk session destruction
    await AuditService.logAuth('sessions_destroyed_bulk', userId, undefined, undefined, {
      destroyedCount,
      exceptSessionId
    })

    return destroyedCount
  }

  // Clean up expired sessions
  static async cleanupExpiredSessions(): Promise<number> {
    const result = await prisma.session.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    })

    return result.count
  }

  // Validate session and return user info
  static async validateSession(sessionId: string): Promise<{
    isValid: boolean
    user?: any
    session?: SessionData
  }> {
    const sessionData = await this.getSession(sessionId)
    
    if (!sessionData || !sessionData.isActive) {
      return { isValid: false }
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: sessionData.userId },
      include: {
        tenant: true,
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    if (!user || user.status !== 'ACTIVE') {
      await this.destroySession(sessionId)
      return { isValid: false }
    }

    // Update last accessed time
    await this.touchSession(sessionId)

    return {
      isValid: true,
      user,
      session: sessionData
    }
  }

  // Get session statistics
  static async getSessionStats(userId?: string): Promise<{
    totalActiveSessions: number
    userActiveSessions?: number
    averageSessionDuration?: number
  }> {
    const now = new Date()
    
    // Total active sessions
    const totalActiveSessions = await prisma.session.count({
      where: {
        expiresAt: {
          gt: now
        }
      }
    })

    let userActiveSessions: number | undefined
    if (userId) {
      userActiveSessions = await prisma.session.count({
        where: {
          userId,
          expiresAt: {
            gt: now
          }
        }
      })
    }

    return {
      totalActiveSessions,
      userActiveSessions
    }
  }

  // Parse user agent for device info
  static parseUserAgent(userAgent?: string): any {
    if (!userAgent) return null

    // Simple user agent parsing (in production, use a proper library like ua-parser-js)
    const browser = userAgent.includes('Chrome') ? 'Chrome' :
                   userAgent.includes('Firefox') ? 'Firefox' :
                   userAgent.includes('Safari') ? 'Safari' :
                   userAgent.includes('Edge') ? 'Edge' : 'Unknown'

    const os = userAgent.includes('Windows') ? 'Windows' :
              userAgent.includes('Mac') ? 'macOS' :
              userAgent.includes('Linux') ? 'Linux' :
              userAgent.includes('Android') ? 'Android' :
              userAgent.includes('iOS') ? 'iOS' : 'Unknown'

    const device = userAgent.includes('Mobile') ? 'Mobile' :
                  userAgent.includes('Tablet') ? 'Tablet' : 'Desktop'

    return { browser, os, device }
  }
}
