import { prisma } from '@/infrastructure/database/prisma'
import { CacheService } from './cache.service'
import { AuditService } from './audit.service'
import { hashPassword, verifyPassword, generateSecureToken, createPasswordResetToken, verifyPasswordResetToken } from '@/shared/utils/encryption'
import { generateToken } from '@/shared/middleware/auth'
import { UnauthorizedError, ValidationError, NotFoundError } from '@/shared/middleware/error-handler'
import { v4 as uuidv4 } from 'uuid'

export interface LoginCredentials {
  email: string
  password: string
  tenantId?: string
  mfaCode?: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  tenantId: string
}

export interface AuthResult {
  user: {
    id: string
    email: string
    firstName: string
    lastName: string
    tenantId: string
    roles: string[]
    permissions: string[]
  }
  token: string
  sessionId: string
  requiresMfa: boolean
}

export class AuthService {
  // User registration
  static async register(data: RegisterData, ipAddress?: string): Promise<AuthResult> {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email }
    })

    if (existingUser) {
      throw new ValidationError('User with this email already exists')
    }

    // Verify tenant exists and is active
    const tenant = await prisma.tenant.findUnique({
      where: { id: data.tenantId }
    })

    if (!tenant || tenant.status !== 'ACTIVE') {
      throw new ValidationError('Invalid tenant')
    }

    // Hash password
    const hashedPassword = await hashPassword(data.password)

    // Create user
    const user = await prisma.user.create({
      data: {
        email: data.email,
        password: hashedPassword,
        firstName: data.firstName,
        lastName: data.lastName,
        tenantId: data.tenantId,
        status: 'ACTIVE',
        emailVerified: false
      },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    // Assign default role if no roles assigned
    if (user.userRoles.length === 0) {
      const defaultRole = await prisma.role.findFirst({
        where: {
          tenantId: data.tenantId,
          name: 'User'
        }
      })

      if (defaultRole) {
        await prisma.userRole.create({
          data: {
            userId: user.id,
            roleId: defaultRole.id
          }
        })
      }
    }

    // Log registration
    await AuditService.logAuth('register', user.id, ipAddress)

    // Generate session
    const sessionId = uuidv4()
    const token = generateToken({ userId: user.id, tenantId: user.tenantId, sessionId })

    // Store session
    await CacheService.createSession(sessionId, {
      userId: user.id,
      tenantId: user.tenantId,
      createdAt: new Date(),
      ipAddress
    })

    // Extract permissions
    const permissions = new Set<string>()
    const roles = user.userRoles.map(ur => {
      const rolePermissions = ur.role.permissions as string[]
      rolePermissions.forEach(permission => permissions.add(permission))
      return ur.role.name
    })

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        tenantId: user.tenantId,
        roles,
        permissions: Array.from(permissions)
      },
      token,
      sessionId,
      requiresMfa: false
    }
  }

  // User login
  static async login(credentials: LoginCredentials, ipAddress?: string, userAgent?: string): Promise<AuthResult> {
    // Find user
    const user = await prisma.user.findUnique({
      where: { email: credentials.email },
      include: {
        tenant: true,
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    if (!user) {
      await AuditService.logAuth('login_failed', 'unknown', ipAddress, userAgent, { email: credentials.email, reason: 'user_not_found' })
      throw new UnauthorizedError('Invalid credentials')
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      await AuditService.logAuth('login_failed', user.id, ipAddress, userAgent, { reason: 'user_inactive' })
      throw new UnauthorizedError('Account is not active')
    }

    // Check if tenant is active
    if (user.tenant.status !== 'ACTIVE') {
      await AuditService.logAuth('login_failed', user.id, ipAddress, userAgent, { reason: 'tenant_inactive' })
      throw new UnauthorizedError('Tenant account is not active')
    }

    // Verify password
    if (!user.password || !await verifyPassword(credentials.password, user.password)) {
      await AuditService.logAuth('login_failed', user.id, ipAddress, userAgent, { reason: 'invalid_password' })
      throw new UnauthorizedError('Invalid credentials')
    }

    // Check if MFA is enabled and required
    const mfaSecret = await this.getUserMfaSecret(user.id)
    if (mfaSecret && !credentials.mfaCode) {
      return {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          tenantId: user.tenantId,
          roles: [],
          permissions: []
        },
        token: '',
        sessionId: '',
        requiresMfa: true
      }
    }

    // Verify MFA if provided
    if (mfaSecret && credentials.mfaCode) {
      const isValidMfa = await this.verifyMfaCode(user.id, credentials.mfaCode)
      if (!isValidMfa) {
        await AuditService.logAuth('login_failed', user.id, ipAddress, userAgent, { reason: 'invalid_mfa' })
        throw new UnauthorizedError('Invalid MFA code')
      }
    }

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    })

    // Generate session
    const sessionId = uuidv4()
    const token = generateToken({ userId: user.id, tenantId: user.tenantId, sessionId })

    // Store session
    await CacheService.createSession(sessionId, {
      userId: user.id,
      tenantId: user.tenantId,
      createdAt: new Date(),
      ipAddress,
      userAgent
    })

    // Log successful login
    await AuditService.logAuth('login', user.id, ipAddress, userAgent)

    // Extract permissions
    const permissions = new Set<string>()
    const roles = user.userRoles.map(ur => {
      const rolePermissions = ur.role.permissions as string[]
      rolePermissions.forEach(permission => permissions.add(permission))
      return ur.role.name
    })

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        tenantId: user.tenantId,
        roles,
        permissions: Array.from(permissions)
      },
      token,
      sessionId,
      requiresMfa: false
    }
  }

  // User logout
  static async logout(sessionId: string, userId: string, ipAddress?: string): Promise<void> {
    // Remove session from cache
    await CacheService.deleteSession(sessionId)

    // Log logout
    await AuditService.logAuth('logout', userId, ipAddress)
  }

  // Refresh token
  static async refreshToken(sessionId: string): Promise<{ token: string }> {
    const session = await CacheService.getSession(sessionId)
    if (!session) {
      throw new UnauthorizedError('Invalid session')
    }

    // Generate new token
    const token = generateToken({
      userId: session.userId,
      tenantId: session.tenantId,
      sessionId
    })

    // Extend session
    await CacheService.refreshSession(sessionId)

    return { token }
  }

  // Request password reset
  static async requestPasswordReset(email: string, ipAddress?: string): Promise<void> {
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      // Don't reveal if user exists
      return
    }

    const { token, hashedToken, expiresAt } = createPasswordResetToken(user.id)

    // Store reset token (in a real app, you'd store this in database)
    await CacheService.set(`password_reset:${user.id}`, {
      hashedToken,
      expiresAt
    }, 3600) // 1 hour

    // Log password reset request
    await AuditService.logAuth('password_reset_requested', user.id, ipAddress)

    // TODO: Send email with reset token
    console.log(`Password reset token for ${email}: ${token}`)
  }

  // Reset password
  static async resetPassword(token: string, newPassword: string, ipAddress?: string): Promise<void> {
    // This is a simplified implementation
    // In a real app, you'd need to properly validate the token
    
    // Hash new password
    const hashedPassword = await hashPassword(newPassword)

    // TODO: Implement proper token validation and user update
    
    // Log password reset
    // await AuditService.logAuth('password_reset', userId, ipAddress)
  }

  // Get user MFA secret (placeholder)
  private static async getUserMfaSecret(userId: string): Promise<string | null> {
    // TODO: Implement MFA secret retrieval
    return null
  }

  // Verify MFA code (placeholder)
  private static async verifyMfaCode(userId: string, code: string): Promise<boolean> {
    // TODO: Implement MFA verification
    return false
  }
}
