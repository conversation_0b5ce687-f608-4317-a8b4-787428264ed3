import { prisma } from '@/infrastructure/database/prisma'
import { CacheService } from './cache.service'
import { AuditService } from './audit.service'
import { generateSecureToken, hashData } from '@/shared/utils/encryption'
import { ValidationError, NotFoundError, ConflictError } from '@/shared/middleware/error-handler'
import { v4 as uuidv4 } from 'uuid'

export interface CreateTenantData {
  name: string
  slug?: string
  subdomain: string
  domain?: string
  description?: string
  industry?: string
  size?: 'SMALL' | 'MEDIUM' | 'LARGE' | 'ENTERPRISE'
  plan?: 'STARTER' | 'PROFESSIONAL' | 'ENTERPRISE' | 'CUSTOM'
  billingEmail?: string
  settings?: any
  features?: any
  limits?: any
  branding?: any
}

export interface UpdateTenantData {
  name?: string
  description?: string
  logo?: string
  website?: string
  industry?: string
  size?: 'SMALL' | 'MEDIUM' | 'LARGE' | 'ENTERPRISE'
  plan?: 'STARTER' | 'PROFESSIONAL' | 'ENTERPRISE' | 'CUSTOM'
  billingEmail?: string
  settings?: any
  features?: any
  limits?: any
  branding?: any
}

export interface TenantInvitationData {
  email: string
  role: string
  invitedBy: string
}

export class TenantService {
  // Create a new tenant
  static async createTenant(data: CreateTenantData, createdBy?: string): Promise<any> {
    // Generate slug if not provided
    const slug = data.slug || this.generateSlug(data.name)
    
    // Validate slug and subdomain uniqueness
    await this.validateTenantIdentifiers(slug, data.subdomain, data.domain)

    // Create tenant
    const tenant = await prisma.tenant.create({
      data: {
        name: data.name,
        slug,
        subdomain: data.subdomain,
        domain: data.domain,
        description: data.description,
        industry: data.industry,
        size: data.size || 'SMALL',
        plan: data.plan || 'STARTER',
        billingEmail: data.billingEmail,
        settings: data.settings || {},
        features: data.features || this.getDefaultFeatures(data.plan || 'STARTER'),
        limits: data.limits || this.getDefaultLimits(data.plan || 'STARTER'),
        branding: data.branding || {},
        status: 'TRIAL',
        trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days trial
      }
    })

    // Create default roles for the tenant
    await this.createDefaultRoles(tenant.id)

    // Log tenant creation
    if (createdBy) {
      await AuditService.logCreate('tenant', tenant.id, createdBy, tenant)
    }

    // Cache tenant data
    await CacheService.set(`tenant:${tenant.id}`, tenant, 3600)
    await CacheService.set(`tenant:slug:${tenant.slug}`, tenant.id, 3600)
    await CacheService.set(`tenant:subdomain:${tenant.subdomain}`, tenant.id, 3600)

    return tenant
  }

  // Get tenant by ID
  static async getTenantById(tenantId: string): Promise<any> {
    // Try cache first
    let tenant = await CacheService.get(`tenant:${tenantId}`)
    
    if (!tenant) {
      tenant = await prisma.tenant.findUnique({
        where: { id: tenantId },
        include: {
          users: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              status: true
            }
          },
          _count: {
            select: {
              users: true,
              deals: true,
              documents: true
            }
          }
        }
      })

      if (tenant) {
        await CacheService.set(`tenant:${tenantId}`, tenant, 3600)
      }
    }

    if (!tenant || tenant.deletedAt) {
      throw new NotFoundError('Tenant not found')
    }

    return tenant
  }

  // Get tenant by slug
  static async getTenantBySlug(slug: string): Promise<any> {
    // Try cache first
    let tenantId = await CacheService.get(`tenant:slug:${slug}`)
    
    if (tenantId) {
      return this.getTenantById(tenantId)
    }

    const tenant = await prisma.tenant.findUnique({
      where: { slug },
      include: {
        users: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            status: true
          }
        }
      }
    })

    if (!tenant || tenant.deletedAt) {
      throw new NotFoundError('Tenant not found')
    }

    // Cache the mapping
    await CacheService.set(`tenant:slug:${slug}`, tenant.id, 3600)
    await CacheService.set(`tenant:${tenant.id}`, tenant, 3600)

    return tenant
  }

  // Get tenant by subdomain
  static async getTenantBySubdomain(subdomain: string): Promise<any> {
    // Try cache first
    let tenantId = await CacheService.get(`tenant:subdomain:${subdomain}`)
    
    if (tenantId) {
      return this.getTenantById(tenantId)
    }

    const tenant = await prisma.tenant.findUnique({
      where: { subdomain }
    })

    if (!tenant || tenant.deletedAt) {
      throw new NotFoundError('Tenant not found')
    }

    // Cache the mapping
    await CacheService.set(`tenant:subdomain:${subdomain}`, tenant.id, 3600)
    await CacheService.set(`tenant:${tenant.id}`, tenant, 3600)

    return tenant
  }

  // Update tenant
  static async updateTenant(tenantId: string, data: UpdateTenantData, updatedBy: string): Promise<any> {
    const existingTenant = await this.getTenantById(tenantId)

    const updatedTenant = await prisma.tenant.update({
      where: { id: tenantId },
      data: {
        ...data,
        updatedAt: new Date()
      }
    })

    // Log update
    await AuditService.logUpdate('tenant', tenantId, updatedBy, existingTenant, updatedTenant)

    // Update cache
    await CacheService.set(`tenant:${tenantId}`, updatedTenant, 3600)

    return updatedTenant
  }

  // Soft delete tenant
  static async deleteTenant(tenantId: string, deletedBy: string): Promise<void> {
    const tenant = await this.getTenantById(tenantId)

    await prisma.tenant.update({
      where: { id: tenantId },
      data: {
        status: 'DELETED',
        deletedAt: new Date()
      }
    })

    // Log deletion
    await AuditService.logDelete('tenant', tenantId, deletedBy, tenant)

    // Clear cache
    await CacheService.del(`tenant:${tenantId}`)
    await CacheService.del(`tenant:slug:${tenant.slug}`)
    await CacheService.del(`tenant:subdomain:${tenant.subdomain}`)
  }

  // Invite user to tenant
  static async inviteUser(tenantId: string, invitationData: TenantInvitationData): Promise<any> {
    const tenant = await this.getTenantById(tenantId)

    // Check if user is already invited or exists
    const existingInvitation = await prisma.tenantInvitation.findUnique({
      where: {
        tenantId_email: {
          tenantId,
          email: invitationData.email
        }
      }
    })

    if (existingInvitation && existingInvitation.status === 'PENDING') {
      throw new ConflictError('User already invited')
    }

    const existingUser = await prisma.user.findUnique({
      where: { email: invitationData.email }
    })

    if (existingUser && existingUser.tenantId === tenantId) {
      throw new ConflictError('User already exists in this tenant')
    }

    // Generate invitation token
    const token = generateSecureToken(32)
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

    const invitation = await prisma.tenantInvitation.create({
      data: {
        tenantId,
        email: invitationData.email,
        role: invitationData.role,
        token,
        expiresAt,
        invitedBy: invitationData.invitedBy,
        status: 'PENDING'
      }
    })

    // Log invitation
    await AuditService.logCreate('tenant_invitation', invitation.id, invitationData.invitedBy, invitation)

    // TODO: Send invitation email

    return invitation
  }

  // Accept tenant invitation
  static async acceptInvitation(token: string, userData: any): Promise<any> {
    const invitation = await prisma.tenantInvitation.findUnique({
      where: { token },
      include: { tenant: true }
    })

    if (!invitation) {
      throw new NotFoundError('Invitation not found')
    }

    if (invitation.status !== 'PENDING') {
      throw new ValidationError('Invitation already processed')
    }

    if (invitation.expiresAt < new Date()) {
      throw new ValidationError('Invitation expired')
    }

    // Create user account or link existing user
    let user = await prisma.user.findUnique({
      where: { email: invitation.email }
    })

    if (!user) {
      // Create new user
      user = await prisma.user.create({
        data: {
          email: invitation.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          password: userData.password, // Should be hashed
          tenantId: invitation.tenantId,
          emailVerified: true
        }
      })
    } else {
      // Update existing user's tenant
      user = await prisma.user.update({
        where: { id: user.id },
        data: { tenantId: invitation.tenantId }
      })
    }

    // Assign role to user
    const role = await prisma.role.findFirst({
      where: {
        tenantId: invitation.tenantId,
        name: invitation.role
      }
    })

    if (role) {
      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: role.id
        }
      })
    }

    // Mark invitation as accepted
    await prisma.tenantInvitation.update({
      where: { id: invitation.id },
      data: { status: 'ACCEPTED' }
    })

    return user
  }

  // Generate API key for tenant
  static async generateApiKey(tenantId: string, name: string, permissions?: any): Promise<any> {
    const tenant = await this.getTenantById(tenantId)

    const apiKey = generateSecureToken(32)
    const keyHash = hashData(apiKey)

    const apiKeyRecord = await prisma.tenantApiKey.create({
      data: {
        tenantId,
        name,
        keyHash,
        permissions: permissions || {},
        isActive: true
      }
    })

    return {
      ...apiKeyRecord,
      key: `map_${apiKey}` // Return the actual key only once
    }
  }

  // Validate tenant identifiers
  private static async validateTenantIdentifiers(slug: string, subdomain: string, domain?: string): Promise<void> {
    const existingSlug = await prisma.tenant.findUnique({ where: { slug } })
    if (existingSlug) {
      throw new ConflictError('Slug already exists')
    }

    const existingSubdomain = await prisma.tenant.findUnique({ where: { subdomain } })
    if (existingSubdomain) {
      throw new ConflictError('Subdomain already exists')
    }

    if (domain) {
      const existingDomain = await prisma.tenant.findUnique({ where: { domain } })
      if (existingDomain) {
        throw new ConflictError('Domain already exists')
      }
    }
  }

  // Generate URL-friendly slug
  private static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
      .substring(0, 50)
  }

  // Get default features for plan
  private static getDefaultFeatures(plan: string): any {
    const features = {
      STARTER: {
        maxUsers: 5,
        maxDeals: 10,
        maxStorage: 1024 * 1024 * 1024, // 1GB
        customBranding: false,
        apiAccess: false,
        advancedReporting: false
      },
      PROFESSIONAL: {
        maxUsers: 25,
        maxDeals: 100,
        maxStorage: 10 * 1024 * 1024 * 1024, // 10GB
        customBranding: true,
        apiAccess: true,
        advancedReporting: true
      },
      ENTERPRISE: {
        maxUsers: -1, // Unlimited
        maxDeals: -1, // Unlimited
        maxStorage: -1, // Unlimited
        customBranding: true,
        apiAccess: true,
        advancedReporting: true,
        sso: true,
        customIntegrations: true
      }
    }

    return features[plan as keyof typeof features] || features.STARTER
  }

  // Get default limits for plan
  private static getDefaultLimits(plan: string): any {
    return this.getDefaultFeatures(plan)
  }

  // Create default roles for tenant
  private static async createDefaultRoles(tenantId: string): Promise<void> {
    const defaultRoles = [
      {
        name: 'Admin',
        description: 'Full access to all features',
        permissions: ['*']
      },
      {
        name: 'Manager',
        description: 'Manage deals and users',
        permissions: ['deals:*', 'users:read', 'documents:*']
      },
      {
        name: 'User',
        description: 'Basic user access',
        permissions: ['deals:read', 'documents:read']
      }
    ]

    for (const roleData of defaultRoles) {
      await prisma.role.create({
        data: {
          ...roleData,
          tenantId
        }
      })
    }
  }
}
