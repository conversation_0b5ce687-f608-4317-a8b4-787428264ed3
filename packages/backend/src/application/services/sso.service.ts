import { prisma } from '@/infrastructure/database/prisma'
import { CacheService } from './cache.service'
import { AuditService } from './audit.service'
import { generateToken } from '@/shared/middleware/auth'
import { UnauthorizedError, ValidationError, NotFoundError } from '@/shared/middleware/error-handler'
import { v4 as uuidv4 } from 'uuid'
import { env } from '@/shared/config/env'

export interface SsoProvider {
  id: string
  name: string
  type: 'oauth' | 'saml' | 'oidc'
  clientId: string
  clientSecret?: string
  authUrl: string
  tokenUrl: string
  userInfoUrl: string
  scope: string[]
  enabled: boolean
}

export interface SsoUserProfile {
  id: string
  email: string
  firstName?: string
  lastName?: string
  avatar?: string
  provider: string
  providerUserId: string
}

export interface SsoAuthResult {
  user: {
    id: string
    email: string
    firstName: string
    lastName: string
    tenantId: string
    roles: string[]
    permissions: string[]
  }
  token: string
  sessionId: string
  isNewUser: boolean
}

export class SsoService {
  // Get available SSO providers for a tenant
  static async getProviders(tenantId: string): Promise<SsoProvider[]> {
    // In a real implementation, this would fetch from database
    // For now, return configured providers
    const providers: SsoProvider[] = []

    // Google OAuth
    if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
      providers.push({
        id: 'google',
        name: 'Google',
        type: 'oauth',
        clientId: process.env.GOOGLE_CLIENT_ID,
        authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
        tokenUrl: 'https://oauth2.googleapis.com/token',
        userInfoUrl: 'https://www.googleapis.com/oauth2/v2/userinfo',
        scope: ['openid', 'email', 'profile'],
        enabled: true
      })
    }

    // GitHub OAuth
    if (process.env.GITHUB_CLIENT_ID && process.env.GITHUB_CLIENT_SECRET) {
      providers.push({
        id: 'github',
        name: 'GitHub',
        type: 'oauth',
        clientId: process.env.GITHUB_CLIENT_ID,
        authUrl: 'https://github.com/login/oauth/authorize',
        tokenUrl: 'https://github.com/login/oauth/access_token',
        userInfoUrl: 'https://api.github.com/user',
        scope: ['user:email'],
        enabled: true
      })
    }

    // Microsoft OAuth
    if (process.env.MICROSOFT_CLIENT_ID && process.env.MICROSOFT_CLIENT_SECRET) {
      providers.push({
        id: 'microsoft',
        name: 'Microsoft',
        type: 'oauth',
        clientId: process.env.MICROSOFT_CLIENT_ID,
        authUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
        tokenUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
        userInfoUrl: 'https://graph.microsoft.com/v1.0/me',
        scope: ['openid', 'email', 'profile'],
        enabled: true
      })
    }

    return providers
  }

  // Generate OAuth authorization URL
  static async generateAuthUrl(
    providerId: string,
    tenantId: string,
    redirectUri: string,
    state?: string
  ): Promise<string> {
    const providers = await this.getProviders(tenantId)
    const provider = providers.find(p => p.id === providerId)

    if (!provider) {
      throw new NotFoundError('SSO provider not found')
    }

    const authState = state || uuidv4()
    
    // Store state for validation
    await CacheService.set(`sso_state:${authState}`, {
      providerId,
      tenantId,
      redirectUri,
      createdAt: new Date()
    }, 600) // 10 minutes

    const params = new URLSearchParams({
      client_id: provider.clientId,
      redirect_uri: redirectUri,
      scope: provider.scope.join(' '),
      response_type: 'code',
      state: authState
    })

    return `${provider.authUrl}?${params.toString()}`
  }

  // Handle OAuth callback
  static async handleCallback(
    code: string,
    state: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<SsoAuthResult> {
    // Validate state
    const stateData = await CacheService.get(`sso_state:${state}`)
    if (!stateData) {
      throw new ValidationError('Invalid or expired state parameter')
    }

    // Clean up state
    await CacheService.del(`sso_state:${state}`)

    const { providerId, tenantId, redirectUri } = stateData

    // Get provider configuration
    const providers = await this.getProviders(tenantId)
    const provider = providers.find(p => p.id === providerId)

    if (!provider) {
      throw new NotFoundError('SSO provider not found')
    }

    // Exchange code for access token
    const tokenResponse = await this.exchangeCodeForToken(provider, code, redirectUri)
    
    // Get user profile from provider
    const userProfile = await this.getUserProfile(provider, tokenResponse.access_token)

    // Find or create user
    const result = await this.findOrCreateUser(userProfile, tenantId, ipAddress, userAgent)

    return result
  }

  // Exchange authorization code for access token
  private static async exchangeCodeForToken(
    provider: SsoProvider,
    code: string,
    redirectUri: string
  ): Promise<any> {
    const params = new URLSearchParams({
      client_id: provider.clientId,
      client_secret: provider.clientSecret || '',
      code,
      redirect_uri: redirectUri,
      grant_type: 'authorization_code'
    })

    const response = await fetch(provider.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: params.toString()
    })

    if (!response.ok) {
      throw new UnauthorizedError('Failed to exchange code for token')
    }

    return response.json()
  }

  // Get user profile from provider
  private static async getUserProfile(
    provider: SsoProvider,
    accessToken: string
  ): Promise<SsoUserProfile> {
    const response = await fetch(provider.userInfoUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      throw new UnauthorizedError('Failed to get user profile')
    }

    const profile = await response.json()

    // Map provider-specific profile to standard format
    return this.mapProviderProfile(provider.id, profile)
  }

  // Map provider-specific profile to standard format
  private static mapProviderProfile(providerId: string, profile: any): SsoUserProfile {
    switch (providerId) {
      case 'google':
        return {
          id: profile.id,
          email: profile.email,
          firstName: profile.given_name,
          lastName: profile.family_name,
          avatar: profile.picture,
          provider: 'google',
          providerUserId: profile.id
        }

      case 'github':
        const [firstName, ...lastNameParts] = (profile.name || '').split(' ')
        return {
          id: profile.id.toString(),
          email: profile.email,
          firstName: firstName || profile.login,
          lastName: lastNameParts.join(' ') || '',
          avatar: profile.avatar_url,
          provider: 'github',
          providerUserId: profile.id.toString()
        }

      case 'microsoft':
        return {
          id: profile.id,
          email: profile.mail || profile.userPrincipalName,
          firstName: profile.givenName,
          lastName: profile.surname,
          avatar: null,
          provider: 'microsoft',
          providerUserId: profile.id
        }

      default:
        throw new ValidationError(`Unsupported provider: ${providerId}`)
    }
  }

  // Find or create user from SSO profile
  private static async findOrCreateUser(
    profile: SsoUserProfile,
    tenantId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<SsoAuthResult> {
    // Check if user exists by email
    let user = await prisma.user.findUnique({
      where: { email: profile.email },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    let isNewUser = false

    if (!user) {
      // Create new user
      user = await prisma.user.create({
        data: {
          email: profile.email,
          firstName: profile.firstName || '',
          lastName: profile.lastName || '',
          avatar: profile.avatar,
          tenantId,
          status: 'ACTIVE',
          emailVerified: true // SSO users are pre-verified
        },
        include: {
          userRoles: {
            include: {
              role: true
            }
          }
        }
      })

      isNewUser = true

      // Assign default role
      const defaultRole = await prisma.role.findFirst({
        where: {
          tenantId,
          name: 'User'
        }
      })

      if (defaultRole) {
        await prisma.userRole.create({
          data: {
            userId: user.id,
            roleId: defaultRole.id
          }
        })
      }

      await AuditService.logAuth('sso_register', user.id, ipAddress, userAgent, {
        provider: profile.provider,
        providerUserId: profile.providerUserId
      })
    } else {
      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() }
      })

      await AuditService.logAuth('sso_login', user.id, ipAddress, userAgent, {
        provider: profile.provider,
        providerUserId: profile.providerUserId
      })
    }

    // Generate session
    const sessionId = uuidv4()
    const token = generateToken({ userId: user.id, tenantId: user.tenantId, sessionId })

    // Store session
    await CacheService.createSession(sessionId, {
      userId: user.id,
      tenantId: user.tenantId,
      createdAt: new Date(),
      ipAddress,
      userAgent,
      ssoProvider: profile.provider
    })

    // Extract permissions
    const permissions = new Set<string>()
    const roles = user.userRoles.map((ur: any) => {
      const rolePermissions = ur.role.permissions as string[]
      rolePermissions.forEach(permission => permissions.add(permission))
      return ur.role.name
    })

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        tenantId: user.tenantId,
        roles,
        permissions: Array.from(permissions)
      },
      token,
      sessionId,
      isNewUser
    }
  }
}
