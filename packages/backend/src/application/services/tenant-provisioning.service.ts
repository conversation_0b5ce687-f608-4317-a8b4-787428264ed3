import { prisma } from '@/infrastructure/database/prisma'
import { TenantService, CreateTenantData } from './tenant.service'
import { MigrationService } from './migration.service'
import { CacheService } from './cache.service'
import { AuditService } from './audit.service'
import { ValidationError, ConflictError } from '@/shared/middleware/error-handler'
import { generateSecureToken, hashPassword } from '@/shared/utils/encryption'

export interface TenantProvisioningRequest {
  // Tenant details
  tenantData: CreateTenantData
  
  // Admin user details
  adminUser: {
    firstName: string
    lastName: string
    email: string
    password: string
  }
  
  // Provisioning options
  options?: {
    skipMigrations?: boolean
    skipDefaultRoles?: boolean
    skipWelcomeEmail?: boolean
    customSettings?: any
  }
}

export interface TenantProvisioningResult {
  tenant: any
  adminUser: any
  migrationResults?: any[]
  provisioningId: string
  status: 'success' | 'partial' | 'failed'
  errors?: string[]
}

export class TenantProvisioningService {
  // Provision a new tenant with all required setup
  static async provisionTenant(
    request: TenantProvisioningRequest,
    provisionedBy?: string
  ): Promise<TenantProvisioningResult> {
    const provisioningId = generateSecureToken(16)
    const errors: string[] = []
    let tenant: any = null
    let adminUser: any = null
    let migrationResults: any[] = []

    console.log(`Starting tenant provisioning: ${provisioningId}`)

    try {
      // Start transaction for atomic provisioning
      const result = await prisma.$transaction(async (tx) => {
        // 1. Validate provisioning request
        await this.validateProvisioningRequest(request)

        // 2. Create tenant
        console.log(`Creating tenant: ${request.tenantData.name}`)
        tenant = await this.createTenantInTransaction(tx, request.tenantData, provisionedBy)

        // 3. Create admin user
        console.log(`Creating admin user: ${request.adminUser.email}`)
        adminUser = await this.createAdminUserInTransaction(tx, tenant.id, request.adminUser)

        // 4. Create default roles (if not skipped)
        if (!request.options?.skipDefaultRoles) {
          console.log(`Creating default roles for tenant: ${tenant.id}`)
          await this.createDefaultRolesInTransaction(tx, tenant.id)
        }

        // 5. Assign admin role to admin user
        await this.assignAdminRoleInTransaction(tx, tenant.id, adminUser.id)

        return { tenant, adminUser }
      })

      tenant = result.tenant
      adminUser = result.adminUser

      // 6. Run tenant migrations (outside transaction for better error handling)
      if (!request.options?.skipMigrations) {
        try {
          console.log(`Running migrations for tenant: ${tenant.id}`)
          migrationResults = await MigrationService.initializeTenantMigrations(
            tenant.id,
            provisionedBy || 'system'
          )
        } catch (migrationError) {
          console.error('Migration failed during provisioning:', migrationError)
          errors.push(`Migration failed: ${migrationError}`)
        }
      }

      // 7. Apply custom settings if provided
      if (request.options?.customSettings) {
        try {
          await this.applyCustomSettings(tenant.id, request.options.customSettings)
        } catch (settingsError) {
          console.error('Custom settings failed:', settingsError)
          errors.push(`Custom settings failed: ${settingsError}`)
        }
      }

      // 8. Send welcome email (if not skipped)
      if (!request.options?.skipWelcomeEmail) {
        try {
          await this.sendWelcomeEmail(tenant, adminUser)
        } catch (emailError) {
          console.error('Welcome email failed:', emailError)
          errors.push(`Welcome email failed: ${emailError}`)
        }
      }

      // 9. Log successful provisioning
      await AuditService.logCreate('tenant_provisioning', provisioningId, provisionedBy || 'system', {
        tenantId: tenant.id,
        adminUserId: adminUser.id,
        status: errors.length > 0 ? 'partial' : 'success',
        errors
      })

      console.log(`Tenant provisioning completed: ${provisioningId}`)

      return {
        tenant,
        adminUser,
        migrationResults,
        provisioningId,
        status: errors.length > 0 ? 'partial' : 'success',
        errors: errors.length > 0 ? errors : undefined
      }

    } catch (error) {
      console.error(`Tenant provisioning failed: ${provisioningId}`, error)

      // Log failed provisioning
      await AuditService.logCreate('tenant_provisioning', provisioningId, provisionedBy || 'system', {
        tenantId: tenant?.id,
        adminUserId: adminUser?.id,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      // Cleanup on failure
      if (tenant?.id) {
        await this.cleanupFailedProvisioning(tenant.id)
      }

      throw error
    }
  }

  // Deprovision a tenant (soft delete with cleanup)
  static async deprovisionTenant(
    tenantId: string,
    deprovisionedBy: string,
    options?: {
      hardDelete?: boolean
      preserveData?: boolean
      notifyUsers?: boolean
    }
  ): Promise<void> {
    console.log(`Starting tenant deprovisioning: ${tenantId}`)

    const tenant = await TenantService.getTenantById(tenantId)

    try {
      // 1. Notify users if requested
      if (options?.notifyUsers) {
        await this.notifyUsersOfDeprovisioning(tenantId)
      }

      // 2. Revoke all API keys
      await this.revokeAllApiKeys(tenantId)

      // 3. Invalidate all sessions
      await this.invalidateAllSessions(tenantId)

      // 4. Archive or delete data
      if (options?.hardDelete && !options?.preserveData) {
        await this.hardDeleteTenantData(tenantId)
      } else {
        await this.softDeleteTenant(tenantId)
      }

      // 5. Clear caches
      await this.clearTenantCaches(tenantId)

      // 6. Log deprovisioning
      await AuditService.logDelete('tenant', tenantId, deprovisionedBy, tenant)

      console.log(`Tenant deprovisioning completed: ${tenantId}`)

    } catch (error) {
      console.error(`Tenant deprovisioning failed: ${tenantId}`, error)
      throw error
    }
  }

  // Validate provisioning request
  private static async validateProvisioningRequest(request: TenantProvisioningRequest): Promise<void> {
    // Validate tenant data
    if (!request.tenantData.name || request.tenantData.name.trim().length === 0) {
      throw new ValidationError('Tenant name is required')
    }

    if (!request.tenantData.subdomain || request.tenantData.subdomain.trim().length === 0) {
      throw new ValidationError('Tenant subdomain is required')
    }

    // Validate subdomain format
    const subdomainRegex = /^[a-z0-9-]+$/
    if (!subdomainRegex.test(request.tenantData.subdomain)) {
      throw new ValidationError('Subdomain must contain only lowercase letters, numbers, and hyphens')
    }

    // Check subdomain availability
    const existingTenant = await prisma.tenant.findUnique({
      where: { subdomain: request.tenantData.subdomain }
    })

    if (existingTenant) {
      throw new ConflictError('Subdomain already exists')
    }

    // Validate admin user data
    if (!request.adminUser.email || !request.adminUser.firstName || !request.adminUser.lastName) {
      throw new ValidationError('Admin user details are incomplete')
    }

    if (!request.adminUser.password || request.adminUser.password.length < 8) {
      throw new ValidationError('Admin user password must be at least 8 characters')
    }

    // Check if admin email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: request.adminUser.email }
    })

    if (existingUser) {
      throw new ConflictError('Admin email already exists')
    }
  }

  // Create tenant within transaction
  private static async createTenantInTransaction(
    tx: any,
    tenantData: CreateTenantData,
    createdBy?: string
  ): Promise<any> {
    const slug = tenantData.slug || this.generateSlug(tenantData.name)

    return await tx.tenant.create({
      data: {
        name: tenantData.name,
        slug,
        subdomain: tenantData.subdomain,
        domain: tenantData.domain,
        description: tenantData.description,
        industry: tenantData.industry,
        size: tenantData.size || 'SMALL',
        plan: tenantData.plan || 'STARTER',
        billingEmail: tenantData.billingEmail,
        status: 'TRIAL',
        trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days trial
        settings: tenantData.settings || {},
        features: tenantData.features || this.getDefaultFeatures(tenantData.plan || 'STARTER'),
        limits: tenantData.limits || this.getDefaultLimits(tenantData.plan || 'STARTER'),
        branding: tenantData.branding || {}
      }
    })
  }

  // Create admin user within transaction
  private static async createAdminUserInTransaction(
    tx: any,
    tenantId: string,
    adminUserData: any
  ): Promise<any> {
    const hashedPassword = await hashPassword(adminUserData.password)

    return await tx.user.create({
      data: {
        email: adminUserData.email,
        firstName: adminUserData.firstName,
        lastName: adminUserData.lastName,
        password: hashedPassword,
        tenantId,
        emailVerified: true, // Admin user is pre-verified
        status: 'ACTIVE'
      }
    })
  }

  // Create default roles within transaction
  private static async createDefaultRolesInTransaction(tx: any, tenantId: string): Promise<void> {
    const defaultRoles = [
      {
        name: 'Admin',
        description: 'Full access to all features and settings',
        permissions: ['*'],
        isDefault: false,
        isSystemRole: true
      },
      {
        name: 'Manager',
        description: 'Manage deals, users, and documents',
        permissions: ['deals:*', 'users:read', 'users:invite', 'documents:*', 'reports:read'],
        isDefault: false,
        isSystemRole: true
      },
      {
        name: 'User',
        description: 'Basic user access to deals and documents',
        permissions: ['deals:read', 'deals:create', 'deals:update', 'documents:read', 'documents:create'],
        isDefault: true,
        isSystemRole: true
      }
    ]

    for (const roleData of defaultRoles) {
      await tx.role.create({
        data: {
          ...roleData,
          tenantId
        }
      })
    }
  }

  // Assign admin role within transaction
  private static async assignAdminRoleInTransaction(
    tx: any,
    tenantId: string,
    userId: string
  ): Promise<void> {
    const adminRole = await tx.role.findFirst({
      where: {
        tenantId,
        name: 'Admin'
      }
    })

    if (adminRole) {
      await tx.userRole.create({
        data: {
          userId,
          roleId: adminRole.id
        }
      })
    }
  }

  // Apply custom settings
  private static async applyCustomSettings(tenantId: string, customSettings: any): Promise<void> {
    await prisma.tenant.update({
      where: { id: tenantId },
      data: {
        settings: customSettings
      }
    })
  }

  // Send welcome email
  private static async sendWelcomeEmail(tenant: any, adminUser: any): Promise<void> {
    // TODO: Implement email sending
    console.log(`Welcome email would be sent to ${adminUser.email} for tenant ${tenant.name}`)
  }

  // Cleanup failed provisioning
  private static async cleanupFailedProvisioning(tenantId: string): Promise<void> {
    try {
      // Delete tenant and all related data
      await prisma.tenant.delete({
        where: { id: tenantId }
      })
      console.log(`Cleaned up failed provisioning for tenant: ${tenantId}`)
    } catch (error) {
      console.error(`Failed to cleanup tenant: ${tenantId}`, error)
    }
  }

  // Utility methods
  private static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
      .substring(0, 50)
  }

  private static getDefaultFeatures(plan: string): any {
    const features = {
      STARTER: { apiAccess: false, customBranding: false, advancedReporting: false },
      PROFESSIONAL: { apiAccess: true, customBranding: true, advancedReporting: true },
      ENTERPRISE: { apiAccess: true, customBranding: true, advancedReporting: true, sso: true }
    }
    return features[plan as keyof typeof features] || features.STARTER
  }

  private static getDefaultLimits(plan: string): any {
    const limits = {
      STARTER: { maxUsers: 5, maxDeals: 10, maxStorage: 1024 * 1024 * 1024 },
      PROFESSIONAL: { maxUsers: 25, maxDeals: 100, maxStorage: 10 * 1024 * 1024 * 1024 },
      ENTERPRISE: { maxUsers: -1, maxDeals: -1, maxStorage: -1 }
    }
    return limits[plan as keyof typeof limits] || limits.STARTER
  }

  // Deprovisioning helper methods
  private static async notifyUsersOfDeprovisioning(tenantId: string): Promise<void> {
    // TODO: Implement user notification
    console.log(`Users would be notified of deprovisioning for tenant: ${tenantId}`)
  }

  private static async revokeAllApiKeys(tenantId: string): Promise<void> {
    await prisma.tenantApiKey.updateMany({
      where: { tenantId },
      data: { isActive: false }
    })
  }

  private static async invalidateAllSessions(tenantId: string): Promise<void> {
    // TODO: Implement session invalidation
    console.log(`Sessions would be invalidated for tenant: ${tenantId}`)
  }

  private static async softDeleteTenant(tenantId: string): Promise<void> {
    await prisma.tenant.update({
      where: { id: tenantId },
      data: {
        status: 'DELETED',
        deletedAt: new Date()
      }
    })
  }

  private static async hardDeleteTenantData(tenantId: string): Promise<void> {
    // Delete in correct order to respect foreign key constraints
    await prisma.userRole.deleteMany({ where: { user: { tenantId } } })
    await prisma.user.deleteMany({ where: { tenantId } })
    await prisma.role.deleteMany({ where: { tenantId } })
    await prisma.tenantApiKey.deleteMany({ where: { tenantId } })
    await prisma.tenantInvitation.deleteMany({ where: { tenantId } })
    await prisma.tenant.delete({ where: { id: tenantId } })
  }

  private static async clearTenantCaches(tenantId: string): Promise<void> {
    const tenant = await prisma.tenant.findUnique({ where: { id: tenantId } })
    if (tenant) {
      await CacheService.del(`tenant:${tenantId}`)
      await CacheService.del(`tenant:slug:${tenant.slug}`)
      await CacheService.del(`tenant:subdomain:${tenant.subdomain}`)
      if (tenant.domain) {
        await CacheService.del(`tenant:domain:${tenant.domain}`)
      }
    }
  }
}
