import { redis } from '@/infrastructure/cache/redis'
import { logger } from '@/shared/logger'

export class CacheService {
  // User cache
  static async cacheUser(userId: string, userData: any, ttl: number = 3600): Promise<void> {
    await redis.set(`user:${userId}`, userData, ttl)
  }

  static async getCachedUser<T>(userId: string): Promise<T | null> {
    return redis.get<T>(`user:${userId}`)
  }

  static async invalidateUser(userId: string): Promise<void> {
    await redis.del(`user:${userId}`)
  }

  // Tenant cache
  static async cacheTenant(tenantId: string, tenantData: any, ttl: number = 7200): Promise<void> {
    await redis.set(`tenant:${tenantId}`, tenantData, ttl)
  }

  static async getCachedTenant<T>(tenantId: string): Promise<T | null> {
    return redis.get<T>(`tenant:${tenantId}`)
  }

  static async invalidateTenant(tenantId: string): Promise<void> {
    await redis.del(`tenant:${tenantId}`)
  }

  // Deal cache
  static async cacheDeal(dealId: string, dealData: any, ttl: number = 1800): Promise<void> {
    await redis.set(`deal:${dealId}`, dealData, ttl)
  }

  static async getCachedDeal<T>(dealId: string): Promise<T | null> {
    return redis.get<T>(`deal:${dealId}`)
  }

  static async invalidateDeal(dealId: string): Promise<void> {
    await redis.del(`deal:${dealId}`)
  }

  // Permission cache
  static async cacheUserPermissions(userId: string, permissions: string[], ttl: number = 3600): Promise<void> {
    await redis.set(`permissions:${userId}`, permissions, ttl)
  }

  static async getCachedUserPermissions(userId: string): Promise<string[] | null> {
    return redis.get<string[]>(`permissions:${userId}`)
  }

  static async invalidateUserPermissions(userId: string): Promise<void> {
    await redis.del(`permissions:${userId}`)
  }

  // Rate limiting
  static async checkRateLimit(identifier: string, limit: number, windowSeconds: number = 60): Promise<{
    allowed: boolean
    remaining: number
    resetTime: number
  }> {
    const key = `rate_limit:${identifier}`
    const current = await redis.incrementCounter(key, windowSeconds)
    
    const allowed = current <= limit
    const remaining = Math.max(0, limit - current)
    const resetTime = Date.now() + (windowSeconds * 1000)

    return {
      allowed,
      remaining,
      resetTime
    }
  }

  // Session management
  static async createSession(sessionId: string, sessionData: any, ttl: number = 86400): Promise<void> {
    await redis.setSession(sessionId, sessionData, ttl)
  }

  static async getSession<T>(sessionId: string): Promise<T | null> {
    return redis.getSession<T>(sessionId)
  }

  static async deleteSession(sessionId: string): Promise<void> {
    await redis.deleteSession(sessionId)
  }

  static async refreshSession(sessionId: string, ttl: number = 86400): Promise<void> {
    await redis.expire(`session:${sessionId}`, ttl)
  }

  // Analytics cache
  static async cacheAnalytics(key: string, data: any, ttl: number = 900): Promise<void> {
    await redis.set(`analytics:${key}`, data, ttl)
  }

  static async getCachedAnalytics<T>(key: string): Promise<T | null> {
    return redis.get<T>(`analytics:${key}`)
  }

  // Document cache
  static async cacheDocumentMetadata(documentId: string, metadata: any, ttl: number = 1800): Promise<void> {
    await redis.set(`document:${documentId}`, metadata, ttl)
  }

  static async getCachedDocumentMetadata<T>(documentId: string): Promise<T | null> {
    return redis.get<T>(`document:${documentId}`)
  }

  static async invalidateDocumentMetadata(documentId: string): Promise<void> {
    await redis.del(`document:${documentId}`)
  }

  // Bulk operations
  static async invalidatePattern(pattern: string): Promise<void> {
    try {
      const client = redis.getClient()
      const keys = await client.keys(pattern)
      if (keys.length > 0) {
        await client.del(keys)
      }
    } catch (error) {
      logger.error('Error invalidating cache pattern:', error)
    }
  }

  // Health check
  static async healthCheck(): Promise<boolean> {
    return redis.ping()
  }

  // Clear all cache (use with caution)
  static async clearAll(): Promise<void> {
    await redis.flushAll()
  }
}
