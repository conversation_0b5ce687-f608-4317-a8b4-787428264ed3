import { 
  ComplianceFramework, 
  ComplianceCategory, 
  TransactionType, 
  EntityType,
  RequirementType,
  CompliancePriority,
  CompliancePhase,
  PenaltyType,
  PenaltySeverity,
  ReportingFrequency
} from '@/shared/types/compliance'

export const REGULATORY_FRAMEWORKS: ComplianceFramework[] = [
  {
    id: 'sec-ma-rules',
    name: 'SEC M&A Disclosure Rules',
    description: 'Securities and Exchange Commission merger and acquisition disclosure requirements',
    jurisdiction: 'United States',
    category: ComplianceCategory.SECURITIES,
    applicability: {
      transactionTypes: [
        TransactionType.MERGER,
        TransactionType.ACQUISITION,
        TransactionType.TENDER_OFFER,
        TransactionType.PROXY_CONTEST
      ],
      transactionSizeThresholds: [
        {
          type: 'VALUE',
          amount: 5000000,
          currency: 'USD',
          description: 'Transactions over $5M require enhanced disclosure'
        }
      ],
      geographicScope: ['US'],
      industryScope: ['ALL'],
      entityTypes: [EntityType.PUBLIC_COMPANY],
      timeframes: [
        {
          phase: CompliancePhase.PRE_SIGNING,
          description: 'Initial disclosure requirements',
          daysFromTrigger: 0,
          triggerEvent: 'Agreement execution',
          isHardDeadline: true,
          extensions: []
        }
      ]
    },
    requirements: [
      {
        id: 'sec-8k-filing',
        name: 'Form 8-K Filing',
        description: 'Current report filing for material definitive agreements',
        type: RequirementType.FILING,
        priority: CompliancePriority.CRITICAL,
        mandatory: true,
        filingRequired: true,
        filingDeadline: {
          phase: CompliancePhase.PRE_SIGNING,
          description: 'Within 4 business days of agreement execution',
          daysFromTrigger: 4,
          triggerEvent: 'Agreement execution',
          isHardDeadline: true,
          extensions: []
        },
        filingFee: 0,
        filingDocuments: [
          {
            name: 'Form 8-K',
            description: 'Current report form',
            format: ['PDF', 'HTML'],
            required: true,
            examples: ['8-K merger example', '8-K acquisition example']
          }
        ],
        approvalRequired: false,
        approvalAuthority: '',
        approvalTimeframe: undefined,
        approvalCriteria: [],
        notificationRequired: false,
        notificationParties: [],
        notificationTimeframe: undefined,
        notificationMethod: [],
        documentationRequired: [
          {
            name: 'Merger Agreement',
            description: 'Executed merger or acquisition agreement',
            format: ['PDF'],
            required: true,
            examples: ['Sample merger agreement']
          }
        ],
        retentionPeriod: 7,
        ongoingMonitoring: false,
        reportingFrequency: undefined,
        dependencies: [],
        exemptions: []
      },
      {
        id: 'sec-proxy-statement',
        name: 'Proxy Statement Filing',
        description: 'Proxy statement for shareholder approval',
        type: RequirementType.FILING,
        priority: CompliancePriority.HIGH,
        mandatory: true,
        filingRequired: true,
        filingDeadline: {
          phase: CompliancePhase.PRE_CLOSING,
          description: 'At least 20 days before shareholder meeting',
          daysFromTrigger: 20,
          triggerEvent: 'Shareholder meeting date',
          isHardDeadline: true,
          extensions: []
        },
        filingFee: 0,
        filingDocuments: [
          {
            name: 'Schedule DEF 14A',
            description: 'Definitive proxy statement',
            format: ['PDF', 'HTML'],
            required: true,
            examples: ['DEF 14A merger example']
          }
        ],
        approvalRequired: false,
        approvalAuthority: '',
        approvalTimeframe: undefined,
        approvalCriteria: [],
        notificationRequired: false,
        notificationParties: [],
        notificationTimeframe: undefined,
        notificationMethod: [],
        documentationRequired: [
          {
            name: 'Fairness Opinion',
            description: 'Investment banker fairness opinion',
            format: ['PDF'],
            required: true,
            examples: ['Sample fairness opinion']
          }
        ],
        retentionPeriod: 7,
        ongoingMonitoring: false,
        reportingFrequency: undefined,
        dependencies: ['sec-8k-filing'],
        exemptions: []
      }
    ],
    penalties: [
      {
        type: PenaltyType.MONETARY,
        description: 'Civil monetary penalties for disclosure violations',
        monetaryPenalty: {
          baseAmount: 100000,
          maxAmount: ********,
          currency: 'USD',
          calculation: 'Based on violation severity and company size'
        },
        severity: PenaltySeverity.MAJOR
      }
    ],
    lastUpdated: new Date('2024-01-01'),
    version: '2024.1',
    isActive: true
  },
  
  {
    id: 'hsr-act',
    name: 'Hart-Scott-Rodino Antitrust Improvements Act',
    description: 'Federal antitrust pre-merger notification requirements',
    jurisdiction: 'United States',
    category: ComplianceCategory.ANTITRUST,
    applicability: {
      transactionTypes: [
        TransactionType.MERGER,
        TransactionType.ACQUISITION,
        TransactionType.ASSET_PURCHASE,
        TransactionType.STOCK_PURCHASE
      ],
      transactionSizeThresholds: [
        {
          type: 'VALUE',
          amount: *********,
          currency: 'USD',
          description: 'Size of transaction threshold (adjusted annually)'
        },
        {
          type: 'ASSETS',
          amount: 20200000,
          currency: 'USD',
          description: 'Size of person threshold (adjusted annually)'
        }
      ],
      geographicScope: ['US'],
      industryScope: ['ALL'],
      entityTypes: [EntityType.PUBLIC_COMPANY, EntityType.PRIVATE_COMPANY],
      timeframes: [
        {
          phase: CompliancePhase.PRE_CLOSING,
          description: 'HSR filing and waiting period',
          daysFromTrigger: 30,
          triggerEvent: 'HSR filing acceptance',
          isHardDeadline: true,
          extensions: [
            {
              maxDays: 30,
              conditions: ['Second request issued'],
              approvalRequired: false
            }
          ]
        }
      ]
    },
    requirements: [
      {
        id: 'hsr-notification',
        name: 'HSR Notification Filing',
        description: 'Pre-merger notification filing with FTC and DOJ',
        type: RequirementType.FILING,
        priority: CompliancePriority.CRITICAL,
        mandatory: true,
        filingRequired: true,
        filingDeadline: {
          phase: CompliancePhase.PRE_CLOSING,
          description: 'Before closing, typically filed after agreement execution',
          daysFromTrigger: 0,
          triggerEvent: 'Agreement execution',
          isHardDeadline: false,
          extensions: []
        },
        filingFee: 45000,
        filingDocuments: [
          {
            name: 'HSR Notification Form',
            description: 'Notification and Report Form for Certain Mergers and Acquisitions',
            format: ['Electronic'],
            required: true,
            examples: ['HSR form example']
          }
        ],
        approvalRequired: true,
        approvalAuthority: 'FTC/DOJ',
        approvalTimeframe: {
          phase: CompliancePhase.PRE_CLOSING,
          description: 'Initial 30-day waiting period',
          daysFromTrigger: 30,
          triggerEvent: 'Filing acceptance',
          isHardDeadline: true,
          extensions: [
            {
              maxDays: 30,
              conditions: ['Second request issued'],
              approvalRequired: false
            }
          ]
        },
        approvalCriteria: [
          'No substantial lessening of competition',
          'No creation of monopoly',
          'Compliance with antitrust laws'
        ],
        notificationRequired: false,
        notificationParties: [],
        notificationTimeframe: undefined,
        notificationMethod: [],
        documentationRequired: [
          {
            name: 'Transaction Documents',
            description: 'All agreements related to the transaction',
            format: ['PDF'],
            required: true,
            examples: ['Merger agreement', 'Asset purchase agreement']
          },
          {
            name: 'Financial Statements',
            description: 'Recent financial statements of all parties',
            format: ['PDF', 'Excel'],
            required: true,
            examples: ['10-K', 'Audited financials']
          }
        ],
        retentionPeriod: 10,
        ongoingMonitoring: false,
        reportingFrequency: undefined,
        dependencies: [],
        exemptions: [
          {
            name: 'Intracompany Transaction',
            description: 'Transactions within the same corporate family',
            conditions: ['Same ultimate parent entity'],
            documentation: []
          }
        ]
      },
      {
        id: 'hsr-waiting-period',
        name: 'HSR Waiting Period Compliance',
        description: 'Compliance with mandatory waiting period before closing',
        type: RequirementType.WAITING_PERIOD,
        priority: CompliancePriority.CRITICAL,
        mandatory: true,
        filingRequired: false,
        filingDeadline: undefined,
        filingFee: 0,
        filingDocuments: [],
        approvalRequired: false,
        approvalAuthority: '',
        approvalTimeframe: undefined,
        approvalCriteria: [],
        notificationRequired: false,
        notificationParties: [],
        notificationTimeframe: undefined,
        notificationMethod: [],
        documentationRequired: [],
        retentionPeriod: 10,
        ongoingMonitoring: true,
        reportingFrequency: ReportingFrequency.AS_NEEDED,
        dependencies: ['hsr-notification'],
        exemptions: []
      }
    ],
    penalties: [
      {
        type: PenaltyType.MONETARY,
        description: 'Civil penalties for HSR violations',
        monetaryPenalty: {
          dailyAmount: 46517,
          maxAmount: 46517000,
          currency: 'USD',
          calculation: 'Daily penalty for each day of violation'
        },
        severity: PenaltySeverity.SEVERE
      },
      {
        type: PenaltyType.INJUNCTION,
        description: 'Injunctive relief to prevent or unwind transaction',
        severity: PenaltySeverity.CRITICAL
      }
    ],
    lastUpdated: new Date('2024-02-01'),
    version: '2024.1',
    isActive: true
  },

  {
    id: 'cfius-review',
    name: 'Committee on Foreign Investment in the United States (CFIUS)',
    description: 'Foreign investment review for national security implications',
    jurisdiction: 'United States',
    category: ComplianceCategory.FOREIGN_INVESTMENT,
    applicability: {
      transactionTypes: [
        TransactionType.MERGER,
        TransactionType.ACQUISITION,
        TransactionType.JOINT_VENTURE
      ],
      transactionSizeThresholds: [
        {
          type: 'VALUE',
          amount: 1,
          currency: 'USD',
          description: 'Any transaction involving foreign investment'
        }
      ],
      geographicScope: ['US'],
      industryScope: [
        'Defense',
        'Technology',
        'Telecommunications',
        'Energy',
        'Critical Infrastructure'
      ],
      entityTypes: [EntityType.FOREIGN_ENTITY],
      timeframes: [
        {
          phase: CompliancePhase.PRE_CLOSING,
          description: 'CFIUS review process',
          daysFromTrigger: 45,
          triggerEvent: 'Filing acceptance',
          isHardDeadline: false,
          extensions: [
            {
              maxDays: 15,
              conditions: ['Investigation phase'],
              approvalRequired: false
            }
          ]
        }
      ]
    },
    requirements: [
      {
        id: 'cfius-filing',
        name: 'CFIUS Declaration or Notice',
        description: 'Filing for review of foreign investment transaction',
        type: RequirementType.FILING,
        priority: CompliancePriority.HIGH,
        mandatory: false,
        filingRequired: true,
        filingDeadline: {
          phase: CompliancePhase.PRE_CLOSING,
          description: 'Voluntary filing, but recommended before closing',
          daysFromTrigger: 0,
          triggerEvent: 'Agreement execution',
          isHardDeadline: false,
          extensions: []
        },
        filingFee: 0,
        filingDocuments: [
          {
            name: 'CFIUS Declaration',
            description: 'Short-form declaration for certain transactions',
            format: ['Electronic'],
            required: false,
            examples: ['CFIUS declaration example']
          },
          {
            name: 'CFIUS Notice',
            description: 'Full notice for comprehensive review',
            format: ['Electronic'],
            required: false,
            examples: ['CFIUS notice example']
          }
        ],
        approvalRequired: true,
        approvalAuthority: 'CFIUS',
        approvalTimeframe: {
          phase: CompliancePhase.PRE_CLOSING,
          description: '30-day review period, possible 45-day investigation',
          daysFromTrigger: 75,
          triggerEvent: 'Filing acceptance',
          isHardDeadline: false,
          extensions: []
        },
        approvalCriteria: [
          'No threat to national security',
          'Compliance with mitigation measures if required'
        ],
        notificationRequired: false,
        notificationParties: [],
        notificationTimeframe: undefined,
        notificationMethod: [],
        documentationRequired: [
          {
            name: 'Transaction Structure',
            description: 'Detailed description of transaction structure',
            format: ['PDF'],
            required: true,
            examples: ['Transaction diagram']
          },
          {
            name: 'Business Information',
            description: 'Information about target business operations',
            format: ['PDF'],
            required: true,
            examples: ['Business description']
          }
        ],
        retentionPeriod: 10,
        ongoingMonitoring: true,
        reportingFrequency: ReportingFrequency.ANNUALLY,
        dependencies: [],
        exemptions: [
          {
            name: 'Passive Investment',
            description: 'Purely passive investments below 10%',
            conditions: ['Less than 10% ownership', 'No control rights'],
            documentation: []
          }
        ]
      }
    ],
    penalties: [
      {
        type: PenaltyType.MONETARY,
        description: 'Civil monetary penalties for CFIUS violations',
        monetaryPenalty: {
          baseAmount: 250000,
          percentage: 100,
          currency: 'USD',
          calculation: 'Greater of $250,000 or transaction value'
        },
        severity: PenaltySeverity.SEVERE
      },
      {
        type: PenaltyType.DIVESTITURE_ORDER,
        description: 'Forced divestiture of acquired assets',
        severity: PenaltySeverity.CRITICAL
      }
    ],
    lastUpdated: new Date('2024-01-15'),
    version: '2024.1',
    isActive: true
  },

  {
    id: 'eu-merger-regulation',
    name: 'EU Merger Regulation',
    description: 'European Union merger control regulation',
    jurisdiction: 'European Union',
    category: ComplianceCategory.ANTITRUST,
    applicability: {
      transactionTypes: [
        TransactionType.MERGER,
        TransactionType.ACQUISITION,
        TransactionType.JOINT_VENTURE
      ],
      transactionSizeThresholds: [
        {
          type: 'REVENUE',
          amount: 5000000000,
          currency: 'EUR',
          description: 'Combined worldwide turnover threshold'
        },
        {
          type: 'REVENUE',
          amount: *********,
          currency: 'EUR',
          description: 'EU-wide turnover threshold for each party'
        }
      ],
      geographicScope: ['EU', 'EEA'],
      industryScope: ['ALL'],
      entityTypes: [EntityType.PUBLIC_COMPANY, EntityType.PRIVATE_COMPANY],
      timeframes: [
        {
          phase: CompliancePhase.PRE_CLOSING,
          description: 'EU merger review process',
          daysFromTrigger: 25,
          triggerEvent: 'Filing acceptance',
          isHardDeadline: true,
          extensions: [
            {
              maxDays: 90,
              conditions: ['Phase II investigation'],
              approvalRequired: false
            }
          ]
        }
      ]
    },
    requirements: [
      {
        id: 'eu-merger-notification',
        name: 'EU Merger Notification',
        description: 'Notification to European Commission for merger review',
        type: RequirementType.FILING,
        priority: CompliancePriority.CRITICAL,
        mandatory: true,
        filingRequired: true,
        filingDeadline: {
          phase: CompliancePhase.PRE_CLOSING,
          description: 'Within 7 days of agreement execution or public announcement',
          daysFromTrigger: 7,
          triggerEvent: 'Agreement execution or public announcement',
          isHardDeadline: true,
          extensions: []
        },
        filingFee: 0,
        filingDocuments: [
          {
            name: 'Form CO',
            description: 'Notification form for concentrations',
            format: ['Electronic'],
            required: true,
            examples: ['Form CO example']
          }
        ],
        approvalRequired: true,
        approvalAuthority: 'European Commission',
        approvalTimeframe: {
          phase: CompliancePhase.PRE_CLOSING,
          description: 'Phase I: 25 working days, Phase II: 90 working days',
          daysFromTrigger: 115,
          triggerEvent: 'Filing acceptance',
          isHardDeadline: true,
          extensions: []
        },
        approvalCriteria: [
          'Compatible with internal market',
          'No significant impediment to effective competition'
        ],
        notificationRequired: false,
        notificationParties: [],
        notificationTimeframe: undefined,
        notificationMethod: [],
        documentationRequired: [
          {
            name: 'Market Analysis',
            description: 'Analysis of relevant product and geographic markets',
            format: ['PDF'],
            required: true,
            examples: ['Market definition study']
          }
        ],
        retentionPeriod: 10,
        ongoingMonitoring: false,
        reportingFrequency: undefined,
        dependencies: [],
        exemptions: []
      }
    ],
    penalties: [
      {
        type: PenaltyType.MONETARY,
        description: 'Fines for procedural violations',
        monetaryPenalty: {
          percentage: 10,
          currency: 'EUR',
          calculation: 'Up to 10% of worldwide turnover'
        },
        severity: PenaltySeverity.SEVERE
      }
    ],
    lastUpdated: new Date('2024-01-01'),
    version: '2024.1',
    isActive: true
  }
]

export const getApplicableFrameworks = (
  transactionType: TransactionType,
  transactionValue: number,
  jurisdiction: string[],
  industry: string[],
  entityTypes: EntityType[]
): ComplianceFramework[] => {
  return REGULATORY_FRAMEWORKS.filter(framework => {
    // Check transaction type
    if (!framework.applicability.transactionTypes.includes(transactionType)) {
      return false
    }

    // Check jurisdiction
    if (!framework.applicability.geographicScope.some(scope => 
      jurisdiction.includes(scope) || scope === 'ALL'
    )) {
      return false
    }

    // Check industry scope
    if (framework.applicability.industryScope.length > 0 && 
        !framework.applicability.industryScope.includes('ALL') &&
        !framework.applicability.industryScope.some(scope => 
          industry.includes(scope)
        )) {
      return false
    }

    // Check entity types
    if (framework.applicability.entityTypes.length > 0 &&
        !framework.applicability.entityTypes.some(type => 
          entityTypes.includes(type)
        )) {
      return false
    }

    // Check transaction size thresholds
    if (framework.applicability.transactionSizeThresholds.length > 0) {
      const meetsThreshold = framework.applicability.transactionSizeThresholds.some(threshold => {
        if (threshold.type === 'VALUE') {
          return transactionValue >= threshold.amount
        }
        // Add other threshold type checks as needed
        return true
      })
      
      if (!meetsThreshold) {
        return false
      }
    }

    return framework.isActive
  })
}

export const getRequirementsByPhase = (
  framework: ComplianceFramework,
  phase: CompliancePhase
): ComplianceRequirement[] => {
  return framework.requirements.filter(requirement => {
    if (requirement.filingDeadline?.phase === phase) return true
    if (requirement.approvalTimeframe?.phase === phase) return true
    if (requirement.notificationTimeframe?.phase === phase) return true
    return false
  })
}
