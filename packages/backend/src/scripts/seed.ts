import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create default tenant
  const defaultTenant = await prisma.tenant.upsert({
    where: { domain: 'demo.ma-platform.com' },
    update: {},
    create: {
      name: 'Demo Corporation',
      domain: 'demo.ma-platform.com',
      subdomain: 'demo',
      status: 'ACTIVE',
      settings: {
        theme: 'default',
        features: ['deals', 'vdr', 'due-diligence', 'analytics'],
        limits: {
          users: 100,
          deals: 1000,
          storage: '100GB'
        }
      }
    }
  })

  console.log('✅ Created default tenant:', defaultTenant.name)

  // Create system roles
  const adminRole = await prisma.role.upsert({
    where: { 
      tenantId_name: { 
        tenantId: defaultTenant.id, 
        name: 'Admin' 
      } 
    },
    update: {},
    create: {
      name: 'Admin',
      description: 'Full system access',
      tenantId: defaultTenant.id,
      isSystem: true,
      permissions: [
        'users:read', 'users:write', 'users:delete',
        'deals:read', 'deals:write', 'deals:delete',
        'documents:read', 'documents:write', 'documents:delete',
        'settings:read', 'settings:write',
        'analytics:read'
      ]
    }
  })

  const managerRole = await prisma.role.upsert({
    where: { 
      tenantId_name: { 
        tenantId: defaultTenant.id, 
        name: 'Manager' 
      } 
    },
    update: {},
    create: {
      name: 'Manager',
      description: 'Deal and team management',
      tenantId: defaultTenant.id,
      isSystem: true,
      permissions: [
        'users:read',
        'deals:read', 'deals:write',
        'documents:read', 'documents:write',
        'analytics:read'
      ]
    }
  })

  const analystRole = await prisma.role.upsert({
    where: { 
      tenantId_name: { 
        tenantId: defaultTenant.id, 
        name: 'Analyst' 
      } 
    },
    update: {},
    create: {
      name: 'Analyst',
      description: 'Deal analysis and documentation',
      tenantId: defaultTenant.id,
      isSystem: true,
      permissions: [
        'deals:read', 'deals:write',
        'documents:read', 'documents:write'
      ]
    }
  })

  console.log('✅ Created system roles')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'System',
      lastName: 'Administrator',
      password: hashedPassword,
      emailVerified: true,
      tenantId: defaultTenant.id,
      status: 'ACTIVE'
    }
  })

  // Assign admin role
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: adminRole.id
      }
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: adminRole.id
    }
  })

  console.log('✅ Created admin user:', adminUser.email)

  // Create sample deal
  const sampleDeal = await prisma.deal.upsert({
    where: { id: 'sample-deal-1' },
    update: {},
    create: {
      id: 'sample-deal-1',
      title: 'Acquisition of TechCorp Inc.',
      description: 'Strategic acquisition of leading SaaS company',
      status: 'PIPELINE',
      stage: 'Initial Review',
      value: 50000000,
      currency: 'USD',
      priority: 'HIGH',
      targetCompany: 'TechCorp Inc.',
      targetIndustry: 'Software',
      targetRevenue: 10000000,
      expectedCloseDate: new Date('2024-06-30'),
      tenantId: defaultTenant.id,
      createdById: adminUser.id,
      assignedToId: adminUser.id
    }
  })

  console.log('✅ Created sample deal:', sampleDeal.title)

  console.log('🎉 Database seed completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
