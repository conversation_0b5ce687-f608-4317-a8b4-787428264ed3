#!/usr/bin/env tsx

import { MigrationService } from '@/application/services/migration.service'
import { TenantService } from '@/application/services/tenant.service'
import { registerMigrations } from '@/migrations'

interface MigrationOptions {
  type: 'global' | 'tenant' | 'all'
  tenantId?: string
  tenantSlug?: string
  dryRun?: boolean
  force?: boolean
}

async function runMigrations(options: MigrationOptions) {
  console.log('🚀 Starting migration process...')
  
  // Register all migrations
  registerMigrations()
  
  const executedBy = 'system' // In a real scenario, this would be the current user
  
  try {
    if (options.type === 'global' || options.type === 'all') {
      console.log('\n📋 Checking global migrations...')
      
      if (options.dryRun) {
        const status = await MigrationService.getGlobalMigrationStatus()
        console.log(`Found ${status.pending.length} pending global migrations:`)
        status.pending.forEach(m => {
          console.log(`  - ${m.id}: ${m.name}`)
        })
      } else {
        const results = await MigrationService.executeGlobalMigrations(executedBy)
        console.log(`✅ Executed ${results.length} global migrations`)
        results.forEach(r => {
          console.log(`  - ${r.name}: ${r.success ? '✅ Success' : '❌ Failed'}`)
          if (!r.success && r.error) {
            console.log(`    Error: ${r.error}`)
          }
        })
      }
    }
    
    if (options.type === 'tenant' || options.type === 'all') {
      let tenantIds: string[] = []
      
      if (options.tenantId) {
        tenantIds = [options.tenantId]
      } else if (options.tenantSlug) {
        const tenant = await TenantService.getTenantBySlug(options.tenantSlug)
        tenantIds = [tenant.id]
      } else if (options.type === 'all') {
        // Get all active tenants
        const tenants = await getAllActiveTenants()
        tenantIds = tenants.map(t => t.id)
      } else {
        throw new Error('Tenant ID or slug is required for tenant migrations')
      }
      
      console.log(`\n🏢 Processing ${tenantIds.length} tenant(s)...`)
      
      for (const tenantId of tenantIds) {
        try {
          const tenant = await TenantService.getTenantById(tenantId)
          console.log(`\n📋 Checking migrations for tenant: ${tenant.name} (${tenant.id})`)
          
          if (options.dryRun) {
            const status = await MigrationService.getTenantMigrationStatus(tenantId)
            console.log(`  Found ${status.pending.length} pending migrations:`)
            status.pending.forEach(m => {
              console.log(`    - ${m.id}: ${m.name}`)
            })
          } else {
            const results = await MigrationService.executePendingMigrations(tenantId, executedBy)
            console.log(`  ✅ Executed ${results.length} migrations`)
            results.forEach(r => {
              console.log(`    - ${r.name}: ${r.success ? '✅ Success' : '❌ Failed'}`)
              if (!r.success && r.error) {
                console.log(`      Error: ${r.error}`)
              }
            })
          }
        } catch (error) {
          console.error(`❌ Error processing tenant ${tenantId}:`, error)
          if (!options.force) {
            throw error
          }
        }
      }
    }
    
    console.log('\n🎉 Migration process completed successfully!')
    
  } catch (error) {
    console.error('\n❌ Migration process failed:', error)
    process.exit(1)
  }
}

async function getAllActiveTenants() {
  // This would typically use a service method to get all tenants
  // For now, we'll use a direct Prisma query
  const { prisma } = await import('@/infrastructure/database/prisma')
  
  return await prisma.tenant.findMany({
    where: {
      status: {
        in: ['ACTIVE', 'TRIAL']
      },
      deletedAt: null
    },
    select: {
      id: true,
      name: true,
      slug: true,
      status: true
    }
  })
}

async function showMigrationStatus() {
  console.log('📊 Migration Status Report\n')
  
  // Register migrations
  registerMigrations()
  
  // Global migrations
  console.log('🌍 Global Migrations:')
  const globalStatus = await MigrationService.getGlobalMigrationStatus()
  console.log(`  Executed: ${globalStatus.executed.length}`)
  console.log(`  Pending: ${globalStatus.pending.length}`)
  console.log(`  Failed: ${globalStatus.failed.length}`)
  
  if (globalStatus.pending.length > 0) {
    console.log('\n  Pending migrations:')
    globalStatus.pending.forEach(m => {
      console.log(`    - ${m.id}: ${m.name}`)
    })
  }
  
  // Tenant migrations
  console.log('\n🏢 Tenant Migrations:')
  const tenants = await getAllActiveTenants()
  
  for (const tenant of tenants.slice(0, 5)) { // Show first 5 tenants
    const status = await MigrationService.getTenantMigrationStatus(tenant.id)
    console.log(`\n  ${tenant.name} (${tenant.slug}):`)
    console.log(`    Executed: ${status.executed.length}`)
    console.log(`    Pending: ${status.pending.length}`)
    console.log(`    Failed: ${status.failed.length}`)
  }
  
  if (tenants.length > 5) {
    console.log(`\n  ... and ${tenants.length - 5} more tenants`)
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2)
  const command = args[0]
  
  switch (command) {
    case 'run':
      const options: MigrationOptions = {
        type: (args[1] as 'global' | 'tenant' | 'all') || 'all',
        tenantId: args.find(arg => arg.startsWith('--tenant-id='))?.split('=')[1],
        tenantSlug: args.find(arg => arg.startsWith('--tenant-slug='))?.split('=')[1],
        dryRun: args.includes('--dry-run'),
        force: args.includes('--force')
      }
      
      await runMigrations(options)
      break
      
    case 'status':
      await showMigrationStatus()
      break
      
    case 'init':
      const tenantId = args.find(arg => arg.startsWith('--tenant-id='))?.split('=')[1]
      const tenantSlug = args.find(arg => arg.startsWith('--tenant-slug='))?.split('=')[1]
      
      if (!tenantId && !tenantSlug) {
        console.error('❌ Tenant ID or slug is required for initialization')
        process.exit(1)
      }
      
      registerMigrations()
      
      let targetTenantId = tenantId
      if (tenantSlug) {
        const tenant = await TenantService.getTenantBySlug(tenantSlug)
        targetTenantId = tenant.id
      }
      
      await MigrationService.initializeTenantMigrations(targetTenantId!, 'system')
      console.log('✅ Tenant migrations initialized')
      break
      
    default:
      console.log(`
🔧 Migration CLI

Usage:
  npm run migrate <command> [options]

Commands:
  run [type]     Run migrations (type: global|tenant|all, default: all)
  status         Show migration status
  init           Initialize migrations for a new tenant

Options:
  --tenant-id=<id>     Target specific tenant by ID
  --tenant-slug=<slug> Target specific tenant by slug
  --dry-run            Show what would be executed without running
  --force              Continue on errors

Examples:
  npm run migrate run global
  npm run migrate run tenant --tenant-slug=acme
  npm run migrate run all --dry-run
  npm run migrate status
  npm run migrate init --tenant-slug=acme
      `)
      break
  }
}

// Run the CLI
if (require.main === module) {
  main().catch(error => {
    console.error('❌ CLI error:', error)
    process.exit(1)
  })
}
