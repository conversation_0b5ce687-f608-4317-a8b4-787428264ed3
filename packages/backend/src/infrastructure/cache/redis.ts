import { createClient, RedisClientType } from 'redis'
import { logger } from '@/shared/logger'
import { env } from '@/shared/config/env'

class RedisCache {
  private client: RedisClientType
  private isConnected = false

  constructor() {
    this.client = createClient({
      url: env.REDIS_URL,
      socket: {
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            logger.error('Redis connection failed after 10 retries')
            return false
          }
          return Math.min(retries * 100, 3000)
        }
      }
    })

    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    this.client.on('connect', () => {
      logger.info('Redis client connected')
    })

    this.client.on('ready', () => {
      logger.info('Redis client ready')
      this.isConnected = true
    })

    this.client.on('error', (err) => {
      logger.error('Redis client error:', err)
      this.isConnected = false
    })

    this.client.on('end', () => {
      logger.info('Redis client disconnected')
      this.isConnected = false
    })
  }

  async connect(): Promise<void> {
    if (!this.isConnected) {
      await this.client.connect()
    }
  }

  async disconnect(): Promise<void> {
    if (this.isConnected) {
      await this.client.disconnect()
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      logger.error('Redis GET error:', error)
      return null
    }
  }

  async set(key: string, value: any, ttlSeconds?: number): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value)
      if (ttlSeconds) {
        await this.client.setEx(key, ttlSeconds, serialized)
      } else {
        await this.client.set(key, serialized)
      }
      return true
    } catch (error) {
      logger.error('Redis SET error:', error)
      return false
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      await this.client.del(key)
      return true
    } catch (error) {
      logger.error('Redis DEL error:', error)
      return false
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key)
      return result === 1
    } catch (error) {
      logger.error('Redis EXISTS error:', error)
      return false
    }
  }

  async expire(key: string, ttlSeconds: number): Promise<boolean> {
    try {
      await this.client.expire(key, ttlSeconds)
      return true
    } catch (error) {
      logger.error('Redis EXPIRE error:', error)
      return false
    }
  }

  async flushAll(): Promise<boolean> {
    try {
      await this.client.flushAll()
      return true
    } catch (error) {
      logger.error('Redis FLUSHALL error:', error)
      return false
    }
  }

  // Session management
  async setSession(sessionId: string, sessionData: any, ttlSeconds: number = 86400): Promise<boolean> {
    return this.set(`session:${sessionId}`, sessionData, ttlSeconds)
  }

  async getSession<T>(sessionId: string): Promise<T | null> {
    return this.get<T>(`session:${sessionId}`)
  }

  async deleteSession(sessionId: string): Promise<boolean> {
    return this.del(`session:${sessionId}`)
  }

  // Cache patterns
  async cacheWithTTL<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttlSeconds: number = 300
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key)
    if (cached !== null) {
      return cached
    }

    // Fetch fresh data
    const freshData = await fetchFunction()
    
    // Cache the result
    await this.set(key, freshData, ttlSeconds)
    
    return freshData
  }

  // Rate limiting
  async incrementCounter(key: string, windowSeconds: number = 60): Promise<number> {
    try {
      const multi = this.client.multi()
      multi.incr(key)
      multi.expire(key, windowSeconds)
      const results = await multi.exec()
      return results?.[0] as number || 0
    } catch (error) {
      logger.error('Redis counter error:', error)
      return 0
    }
  }

  // Health check
  async ping(): Promise<boolean> {
    try {
      const result = await this.client.ping()
      return result === 'PONG'
    } catch (error) {
      logger.error('Redis ping error:', error)
      return false
    }
  }

  // Get client for advanced operations
  getClient(): RedisClientType {
    return this.client
  }
}

// Create singleton instance
export const redis = new RedisCache()

// Graceful shutdown
process.on('SIGTERM', async () => {
  await redis.disconnect()
})

process.on('SIGINT', async () => {
  await redis.disconnect()
})
