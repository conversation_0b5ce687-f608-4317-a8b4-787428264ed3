import { PrismaClient } from '@prisma/client'
import { Express } from 'express'
import { E2ETestUtils, E2ETestContext } from './e2e-test-utils'
import { createApp } from '@/app'

describe('M&A Platform End-to-End Tests', () => {
  let app: Express
  let prisma: PrismaClient
  let e2eContext: E2ETestContext

  beforeAll(async () => {
    app = createApp()
    prisma = global.__PRISMA__
    E2ETestUtils.initialize(app, prisma)
  })

  beforeEach(async () => {
    // Clean database before each test
    await prisma.migrationRecord.deleteMany()
    await prisma.auditLog.deleteMany()
    await prisma.session.deleteMany()
    await prisma.userRole.deleteMany()
    await prisma.document.deleteMany()
    await prisma.dueDiligenceItem.deleteMany()
    await prisma.valuation.deleteMany()
    await prisma.deal.deleteMany()
    await prisma.tenantApiKey.deleteMany()
    await prisma.tenantInvitation.deleteMany()
    await prisma.user.deleteMany()
    await prisma.role.deleteMany()
    await prisma.tenant.deleteMany()

    // Create fresh E2E context
    e2eContext = await E2ETestUtils.createE2ETestContext()
  })

  afterEach(async () => {
    if (e2eContext) {
      await E2ETestUtils.cleanupE2ETestContext(e2eContext)
    }
  })

  describe('Complete User Authentication Flow', () => {
    it('should execute full authentication scenario', async () => {
      const scenario = E2ETestUtils.createAuthenticationScenario(e2eContext)
      await E2ETestUtils.executeScenario(e2eContext, scenario)
    })

    it('should handle authentication failures gracefully', async () => {
      // Test invalid login
      const response = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'post',
        '/api/auth/login',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin,
          body: {
            email: e2eContext.users.acmeAdmin.email,
            password: 'wrongpassword'
          },
          expectedStatus: 401
        }
      )

      expect(response.body.success).toBe(false)
    })

    it('should enforce tenant context in authentication', async () => {
      // Try to login with wrong tenant context
      const response = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'post',
        '/api/auth/login',
        {
          tenant: e2eContext.tenants.demo, // Wrong tenant
          user: e2eContext.users.acmeAdmin,
          body: {
            email: e2eContext.users.acmeAdmin.email,
            password: 'password123'
          },
          expectedStatus: 401
        }
      )

      expect(response.body.success).toBe(false)
    })
  })

  describe('Complete Deal Management Flow', () => {
    it('should execute full deal management scenario', async () => {
      const scenario = E2ETestUtils.createDealManagementScenario(e2eContext)
      await E2ETestUtils.executeScenario(e2eContext, scenario)
    })

    it('should handle deal validation errors', async () => {
      const response = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'post',
        '/api/deals',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin,
          body: {
            // Missing required fields
            title: '',
            dealValue: -1000000 // Invalid value
          },
          expectedStatus: 400
        }
      )

      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('should support deal search and filtering', async () => {
      // Search for deals
      const searchResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'get',
        '/api/deals?search=Acme&type=ACQUISITION',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin
        }
      )

      expect(searchResponse.body.success).toBe(true)
      expect(searchResponse.body.data.items.length).toBeGreaterThan(0)
      expect(searchResponse.body.data.items.every((deal: any) => 
        deal.title.includes('Acme') && deal.type === 'ACQUISITION'
      )).toBe(true)
    })

    it('should support deal pagination', async () => {
      // Test pagination
      const page1Response = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'get',
        '/api/deals?page=1&limit=1',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin
        }
      )

      expect(page1Response.body.success).toBe(true)
      expect(page1Response.body.data.items).toHaveLength(1)
      expect(page1Response.body.data.pagination.page).toBe(1)
      expect(page1Response.body.data.pagination.limit).toBe(1)
      expect(page1Response.body.data.pagination.total).toBeGreaterThan(0)
    })
  })

  describe('Complete Document Management Flow', () => {
    it('should execute full document management scenario', async () => {
      const scenario = E2ETestUtils.createDocumentManagementScenario(e2eContext)
      await E2ETestUtils.executeScenario(e2eContext, scenario)
    })

    it('should handle document validation', async () => {
      const response = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'post',
        '/api/documents',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin,
          body: {
            name: '', // Empty name
            type: 'INVALID_TYPE', // Invalid type
            size: -1 // Invalid size
          },
          expectedStatus: 400
        }
      )

      expect(response.body.success).toBe(false)
    })

    it('should support document search', async () => {
      const searchResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'get',
        '/api/documents?search=Contract&type=CONTRACT',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin
        }
      )

      expect(searchResponse.body.success).toBe(true)
      expect(searchResponse.body.data.items.every((doc: any) => 
        doc.type === 'CONTRACT'
      )).toBe(true)
    })
  })

  describe('Multi-Tenant Isolation Flow', () => {
    it('should execute full multi-tenant isolation scenario', async () => {
      const scenario = E2ETestUtils.createMultiTenantIsolationScenario(e2eContext)
      await E2ETestUtils.executeScenario(e2eContext, scenario)
    })

    it('should prevent cross-tenant data creation', async () => {
      // Try to create deal in wrong tenant context
      const response = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'post',
        '/api/deals',
        {
          tenant: e2eContext.tenants.demo,
          user: e2eContext.users.acmeAdmin, // Wrong user for this tenant
          body: {
            title: 'Cross Tenant Deal',
            type: 'ACQUISITION',
            status: 'ACTIVE',
            targetCompany: 'Cross Target',
            dealValue: 1000000,
            currency: 'USD'
          },
          expectedStatus: 403
        }
      )

      expect(response.body.success).toBe(false)
    })

    it('should isolate tenant statistics', async () => {
      // Get stats for each tenant
      const acmeStatsResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'get',
        '/api/tenants/stats',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin
        }
      )

      const demoStatsResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'get',
        '/api/tenants/stats',
        {
          tenant: e2eContext.tenants.demo,
          user: e2eContext.users.demoAdmin
        }
      )

      expect(acmeStatsResponse.body.success).toBe(true)
      expect(demoStatsResponse.body.success).toBe(true)

      // Stats should be different
      expect(acmeStatsResponse.body.data.totalDeals).not.toBe(
        demoStatsResponse.body.data.totalDeals
      )
    })
  })

  describe('Permission-Based Access Control Flow', () => {
    it('should execute full permission control scenario', async () => {
      const scenario = E2ETestUtils.createPermissionControlScenario(e2eContext)
      await E2ETestUtils.executeScenario(e2eContext, scenario)
    })

    it('should enforce read-only permissions', async () => {
      // Regular user should be able to read but not modify
      const readResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'get',
        `/api/deals/${e2eContext.testData.acmeDeals[0].id}`,
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeUser
        }
      )

      expect(readResponse.body.success).toBe(true)

      // But cannot update
      const updateResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'put',
        `/api/deals/${e2eContext.testData.acmeDeals[0].id}`,
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeUser,
          body: { title: 'Updated Title' },
          expectedStatus: 403
        }
      )

      expect(updateResponse.body.success).toBe(false)
    })
  })

  describe('Complex Business Workflows', () => {
    it('should handle complete deal lifecycle', async () => {
      let dealId: string

      // 1. Create deal
      const createResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'post',
        '/api/deals',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin,
          body: {
            title: 'Lifecycle Test Deal',
            type: 'ACQUISITION',
            status: 'ACTIVE',
            targetCompany: 'Lifecycle Target',
            dealValue: 20000000,
            currency: 'USD'
          }
        }
      )

      expect(createResponse.body.success).toBe(true)
      dealId = createResponse.body.data.id

      // 2. Add documents
      const docResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'post',
        '/api/documents',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin,
          body: {
            name: 'Lifecycle Contract.pdf',
            type: 'CONTRACT',
            size: 2048000,
            mimeType: 'application/pdf',
            dealId
          }
        }
      )

      expect(docResponse.body.success).toBe(true)

      // 3. Add valuation
      const valuationResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'post',
        '/api/valuations',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin,
          body: {
            dealId,
            method: 'DCF',
            value: 22000000,
            currency: 'USD'
          }
        }
      )

      expect(valuationResponse.body.success).toBe(true)

      // 4. Update deal status
      const statusResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'put',
        `/api/deals/${dealId}`,
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin,
          body: { status: 'COMPLETED' }
        }
      )

      expect(statusResponse.body.success).toBe(true)
      expect(statusResponse.body.data.status).toBe('COMPLETED')

      // 5. Verify complete deal with all relationships
      const finalResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'get',
        `/api/deals/${dealId}?include=documents,valuations`,
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin
        }
      )

      expect(finalResponse.body.success).toBe(true)
      expect(finalResponse.body.data.documents.length).toBeGreaterThan(0)
      expect(finalResponse.body.data.valuations.length).toBeGreaterThan(0)
    })

    it('should handle tenant invitation workflow', async () => {
      // 1. Admin creates invitation
      const inviteResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'post',
        '/api/tenants/invite',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin,
          body: {
            email: '<EMAIL>',
            role: 'User'
          }
        }
      )

      expect(inviteResponse.body.success).toBe(true)
      expect(inviteResponse.body.data).toHaveProperty('token')

      // 2. List pending invitations
      const listResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'get',
        '/api/tenants/invitations',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin
        }
      )

      expect(listResponse.body.success).toBe(true)
      expect(listResponse.body.data.items.some((inv: any) => 
        inv.email === '<EMAIL>'
      )).toBe(true)
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle malformed requests gracefully', async () => {
      const response = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'post',
        '/api/deals',
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin,
          body: 'invalid json',
          expectedStatus: 400
        }
      )

      expect(response.body.success).toBe(false)
    })

    it('should handle missing tenant context', async () => {
      const response = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'get',
        '/api/deals',
        {
          tenant: { ...e2eContext.tenants.acme, id: 'invalid-tenant-id' },
          user: e2eContext.users.acmeAdmin,
          expectedStatus: 400
        }
      )

      expect(response.body.success).toBe(false)
    })

    it('should handle rate limiting', async () => {
      // Make multiple rapid requests
      const requests = Array.from({ length: 20 }, () =>
        E2ETestUtils.makeAuthenticatedRequest(
          e2eContext.app,
          'get',
          '/api/deals',
          {
            tenant: e2eContext.tenants.acme,
            user: e2eContext.users.acmeAdmin
          }
        ).catch(() => ({ status: 429 })) // Catch rate limit errors
      )

      const responses = await Promise.all(requests)
      
      // Some requests should be rate limited
      const rateLimited = responses.filter(r => r.status === 429)
      expect(rateLimited.length).toBeGreaterThan(0)
    })
  })

  describe('Performance Under Load', () => {
    it('should handle concurrent requests efficiently', async () => {
      const concurrentRequests = Array.from({ length: 10 }, () =>
        E2ETestUtils.makeAuthenticatedRequest(
          e2eContext.app,
          'get',
          '/api/deals',
          {
            tenant: e2eContext.tenants.acme,
            user: e2eContext.users.acmeAdmin
          }
        )
      )

      const startTime = Date.now()
      const responses = await Promise.all(concurrentRequests)
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds
      expect(responses.every(r => r.body.success)).toBe(true)
    })

    it('should maintain data consistency under concurrent modifications', async () => {
      const dealId = e2eContext.testData.acmeDeals[0].id

      // Concurrent updates to the same deal
      const updates = Array.from({ length: 5 }, (_, i) =>
        E2ETestUtils.makeAuthenticatedRequest(
          e2eContext.app,
          'put',
          `/api/deals/${dealId}`,
          {
            tenant: e2eContext.tenants.acme,
            user: e2eContext.users.acmeAdmin,
            body: { notes: `Update ${i}` }
          }
        )
      )

      const responses = await Promise.all(updates)
      
      // All updates should succeed
      expect(responses.every(r => r.body.success)).toBe(true)

      // Final state should be consistent
      const finalResponse = await E2ETestUtils.makeAuthenticatedRequest(
        e2eContext.app,
        'get',
        `/api/deals/${dealId}`,
        {
          tenant: e2eContext.tenants.acme,
          user: e2eContext.users.acmeAdmin
        }
      )

      expect(finalResponse.body.success).toBe(true)
      expect(finalResponse.body.data.notes).toMatch(/Update \d/)
    })
  })
})
