import { PrismaClient } from '@prisma/client'
import { Express } from 'express'
import request from 'supertest'
import { TestHelpers, TestTenant, TestUser } from '../utils/test-helpers'
import { TestDataFactory } from '../data/test-data-factory'
import { createApp } from '@/app'

export interface E2ETestContext {
  app: Express
  prisma: PrismaClient
  tenants: {
    acme: TestTenant
    demo: TestTenant
  }
  users: {
    acmeAdmin: TestUser
    acmeUser: TestUser
    demoAdmin: TestUser
    demoUser: TestUser
  }
  testData: {
    acmeDeals: any[]
    demoDeals: any[]
    acmeDocuments: any[]
    demoDocuments: any[]
  }
}

export interface E2ETestScenario {
  name: string
  description: string
  steps: E2ETestStep[]
}

export interface E2ETestStep {
  name: string
  action: () => Promise<any>
  validate: (result: any) => void | Promise<void>
}

export class E2ETestUtils {
  private static app: Express
  private static prisma: PrismaClient

  static initialize(app: Express, prisma: PrismaClient) {
    this.app = app
    this.prisma = prisma
    TestHelpers.initialize(prisma)
    TestDataFactory.initialize(prisma)
  }

  // Create comprehensive E2E test context
  static async createE2ETestContext(): Promise<E2ETestContext> {
    const app = createApp()
    const prisma = global.__PRISMA__

    // Create test tenants
    const acmeTenant = await TestHelpers.createTestTenant({
      name: 'Acme Corporation',
      subdomain: 'acme',
      slug: 'acme-corp',
      plan: 'ENTERPRISE'
    })

    const demoTenant = await TestHelpers.createTestTenant({
      name: 'Demo Company',
      subdomain: 'demo',
      slug: 'demo-company',
      plan: 'PROFESSIONAL'
    })

    // Create test users
    const acmeAdmin = await TestHelpers.createTestUser(acmeTenant.id, {
      email: '<EMAIL>',
      firstName: 'Acme',
      lastName: 'Admin',
      roles: ['Admin']
    })

    const acmeUser = await TestHelpers.createTestUser(acmeTenant.id, {
      email: '<EMAIL>',
      firstName: 'Acme',
      lastName: 'User',
      roles: ['User']
    })

    const demoAdmin = await TestHelpers.createTestUser(demoTenant.id, {
      email: '<EMAIL>',
      firstName: 'Demo',
      lastName: 'Admin',
      roles: ['Admin']
    })

    const demoUser = await TestHelpers.createTestUser(demoTenant.id, {
      email: '<EMAIL>',
      firstName: 'Demo',
      lastName: 'User',
      roles: ['User']
    })

    // Create test data
    const acmeDeals = await Promise.all([
      TestHelpers.createTestDeal(acmeTenant.id, {
        title: 'Acme Acquisition Deal',
        type: 'ACQUISITION',
        status: 'ACTIVE',
        dealValue: 10000000
      }),
      TestHelpers.createTestDeal(acmeTenant.id, {
        title: 'Acme Merger Deal',
        type: 'MERGER',
        status: 'PENDING',
        dealValue: 25000000
      })
    ])

    const demoDeals = await Promise.all([
      TestHelpers.createTestDeal(demoTenant.id, {
        title: 'Demo Investment Deal',
        type: 'INVESTMENT',
        status: 'ACTIVE',
        dealValue: 5000000
      })
    ])

    const acmeDocuments = await Promise.all([
      TestHelpers.createTestDocument(acmeTenant.id, acmeDeals[0].id, {
        name: 'Acme Contract.pdf',
        type: 'CONTRACT'
      }),
      TestHelpers.createTestDocument(acmeTenant.id, acmeDeals[1].id, {
        name: 'Acme Financial Report.pdf',
        type: 'FINANCIAL'
      })
    ])

    const demoDocuments = await Promise.all([
      TestHelpers.createTestDocument(demoTenant.id, demoDeals[0].id, {
        name: 'Demo Investment Terms.pdf',
        type: 'LEGAL'
      })
    ])

    return {
      app,
      prisma,
      tenants: {
        acme: acmeTenant,
        demo: demoTenant
      },
      users: {
        acmeAdmin,
        acmeUser,
        demoAdmin,
        demoUser
      },
      testData: {
        acmeDeals,
        demoDeals,
        acmeDocuments,
        demoDocuments
      }
    }
  }

  // Make authenticated API request
  static async makeAuthenticatedRequest(
    app: Express,
    method: 'get' | 'post' | 'put' | 'patch' | 'delete',
    path: string,
    options: {
      tenant: TestTenant
      user: TestUser
      body?: any
      headers?: Record<string, string>
      expectedStatus?: number
    }
  ): Promise<any> {
    const { tenant, user, body, headers = {}, expectedStatus = 200 } = options

    let req = request(app)[method](path)

    // Add tenant headers
    req = req
      .set('X-Tenant-ID', tenant.id)
      .set('X-Subdomain', tenant.subdomain)
      .set('X-Original-Host', `${tenant.subdomain}.localhost`)

    // Add authentication
    req = req.set('Authorization', `Bearer ${user.token}`)

    // Add custom headers
    Object.entries(headers).forEach(([key, value]) => {
      req = req.set(key, value)
    })

    // Add body for POST/PUT/PATCH requests
    if (body && ['post', 'put', 'patch'].includes(method)) {
      req = req.send(body)
    }

    return await req.expect(expectedStatus)
  }

  // Execute E2E test scenario
  static async executeScenario(
    context: E2ETestContext,
    scenario: E2ETestScenario
  ): Promise<void> {
    console.log(`🚀 Executing E2E scenario: ${scenario.name}`)
    console.log(`📝 Description: ${scenario.description}`)

    for (let i = 0; i < scenario.steps.length; i++) {
      const step = scenario.steps[i]
      console.log(`  Step ${i + 1}: ${step.name}`)

      try {
        const result = await step.action()
        await step.validate(result)
        console.log(`  ✅ Step ${i + 1} completed successfully`)
      } catch (error) {
        console.error(`  ❌ Step ${i + 1} failed:`, error)
        throw new Error(`E2E scenario "${scenario.name}" failed at step ${i + 1}: ${step.name}`)
      }
    }

    console.log(`🎉 E2E scenario "${scenario.name}" completed successfully`)
  }

  // User authentication flow
  static createAuthenticationScenario(context: E2ETestContext): E2ETestScenario {
    return {
      name: 'User Authentication Flow',
      description: 'Complete user authentication flow including login, token validation, and logout',
      steps: [
        {
          name: 'Login with valid credentials',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'post',
              '/api/auth/login',
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin,
                body: {
                  email: context.users.acmeAdmin.email,
                  password: 'password123'
                }
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data).toHaveProperty('token')
            expect(response.body.data).toHaveProperty('user')
            expect(response.body.data).toHaveProperty('tenant')
          }
        },
        {
          name: 'Validate token with /auth/me',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'get',
              '/api/auth/me',
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data.email).toBe(context.users.acmeAdmin.email)
            expect(response.body.data.tenantId).toBe(context.tenants.acme.id)
          }
        },
        {
          name: 'Logout user',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'post',
              '/api/auth/logout',
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
          }
        }
      ]
    }
  }

  // Deal management flow
  static createDealManagementScenario(context: E2ETestContext): E2ETestScenario {
    let createdDealId: string

    return {
      name: 'Deal Management Flow',
      description: 'Complete deal lifecycle from creation to completion',
      steps: [
        {
          name: 'Create new deal',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'post',
              '/api/deals',
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin,
                body: {
                  title: 'E2E Test Deal',
                  type: 'ACQUISITION',
                  status: 'ACTIVE',
                  targetCompany: 'E2E Target Corp',
                  dealValue: 15000000,
                  currency: 'USD'
                }
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data).toHaveProperty('id')
            expect(response.body.data.title).toBe('E2E Test Deal')
            createdDealId = response.body.data.id
          }
        },
        {
          name: 'Retrieve deal details',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'get',
              `/api/deals/${createdDealId}`,
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data.id).toBe(createdDealId)
            expect(response.body.data.title).toBe('E2E Test Deal')
          }
        },
        {
          name: 'Update deal status',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'put',
              `/api/deals/${createdDealId}`,
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin,
                body: {
                  status: 'UNDER_REVIEW',
                  notes: 'Updated via E2E test'
                }
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data.status).toBe('UNDER_REVIEW')
          }
        },
        {
          name: 'List deals with filters',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'get',
              '/api/deals?status=UNDER_REVIEW',
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data.items.some((deal: any) => deal.id === createdDealId)).toBe(true)
          }
        }
      ]
    }
  }

  // Document management flow
  static createDocumentManagementScenario(context: E2ETestContext): E2ETestScenario {
    let uploadedDocumentId: string

    return {
      name: 'Document Management Flow',
      description: 'Complete document lifecycle including upload, association, and retrieval',
      steps: [
        {
          name: 'Upload document',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'post',
              '/api/documents',
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin,
                body: {
                  name: 'E2E Test Document.pdf',
                  type: 'CONTRACT',
                  size: 1024000,
                  mimeType: 'application/pdf',
                  dealId: context.testData.acmeDeals[0].id
                }
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data).toHaveProperty('id')
            expect(response.body.data.name).toBe('E2E Test Document.pdf')
            uploadedDocumentId = response.body.data.id
          }
        },
        {
          name: 'Retrieve document details',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'get',
              `/api/documents/${uploadedDocumentId}`,
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data.id).toBe(uploadedDocumentId)
            expect(response.body.data.dealId).toBe(context.testData.acmeDeals[0].id)
          }
        },
        {
          name: 'List documents for deal',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'get',
              `/api/deals/${context.testData.acmeDeals[0].id}/documents`,
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data.items.some((doc: any) => doc.id === uploadedDocumentId)).toBe(true)
          }
        }
      ]
    }
  }

  // Multi-tenant isolation flow
  static createMultiTenantIsolationScenario(context: E2ETestContext): E2ETestScenario {
    return {
      name: 'Multi-Tenant Isolation Flow',
      description: 'Verify complete isolation between different tenants',
      steps: [
        {
          name: 'Acme admin lists deals',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'get',
              '/api/deals',
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data.items.every((deal: any) => 
              deal.tenantId === context.tenants.acme.id
            )).toBe(true)
          }
        },
        {
          name: 'Demo admin lists deals',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'get',
              '/api/deals',
              {
                tenant: context.tenants.demo,
                user: context.users.demoAdmin
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data.items.every((deal: any) => 
              deal.tenantId === context.tenants.demo.id
            )).toBe(true)
          }
        },
        {
          name: 'Demo admin cannot access Acme deal',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'get',
              `/api/deals/${context.testData.acmeDeals[0].id}`,
              {
                tenant: context.tenants.demo,
                user: context.users.demoAdmin,
                expectedStatus: 404
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(false)
          }
        },
        {
          name: 'Acme user cannot access Demo documents',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'get',
              `/api/documents/${context.testData.demoDocuments[0].id}`,
              {
                tenant: context.tenants.acme,
                user: context.users.acmeUser,
                expectedStatus: 404
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(false)
          }
        }
      ]
    }
  }

  // Permission-based access control flow
  static createPermissionControlScenario(context: E2ETestContext): E2ETestScenario {
    return {
      name: 'Permission-Based Access Control Flow',
      description: 'Verify role-based permissions are properly enforced',
      steps: [
        {
          name: 'Admin can create deals',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'post',
              '/api/deals',
              {
                tenant: context.tenants.acme,
                user: context.users.acmeAdmin,
                body: {
                  title: 'Admin Created Deal',
                  type: 'ACQUISITION',
                  status: 'ACTIVE',
                  targetCompany: 'Admin Target',
                  dealValue: 1000000,
                  currency: 'USD'
                }
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
          }
        },
        {
          name: 'Regular user can read deals',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'get',
              '/api/deals',
              {
                tenant: context.tenants.acme,
                user: context.users.acmeUser
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(true)
            expect(response.body.data.items.length).toBeGreaterThan(0)
          }
        },
        {
          name: 'Regular user cannot delete deals',
          action: async () => {
            return await this.makeAuthenticatedRequest(
              context.app,
              'delete',
              `/api/deals/${context.testData.acmeDeals[0].id}`,
              {
                tenant: context.tenants.acme,
                user: context.users.acmeUser,
                expectedStatus: 403
              }
            )
          },
          validate: (response) => {
            expect(response.body.success).toBe(false)
          }
        }
      ]
    }
  }

  // Get all available scenarios
  static getAllScenarios(context: E2ETestContext): E2ETestScenario[] {
    return [
      this.createAuthenticationScenario(context),
      this.createDealManagementScenario(context),
      this.createDocumentManagementScenario(context),
      this.createMultiTenantIsolationScenario(context),
      this.createPermissionControlScenario(context)
    ]
  }

  // Clean up E2E test context
  static async cleanupE2ETestContext(context: E2ETestContext): Promise<void> {
    try {
      // Clean up in reverse order of creation
      await TestDataFactory.cleanupTenantData(context.tenants.demo.id)
      await TestDataFactory.cleanupTenantData(context.tenants.acme.id)
    } catch (error) {
      console.warn('Warning: Failed to cleanup E2E test context:', error)
    }
  }
}
