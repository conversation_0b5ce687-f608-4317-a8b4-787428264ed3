import { PrismaClient } from '@prisma/client'

// Mock environment variables for testing
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5433/ma_platform_test'
process.env.REDIS_URL = 'redis://localhost:6380'
process.env.JWT_SECRET = 'test-jwt-secret-key-at-least-32-characters-long'
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters'

// Global test database instance
declare global {
  var __PRISMA__: PrismaClient | undefined
}

// Setup test database
beforeAll(async () => {
  // Database setup will be handled by global setup
})

afterAll(async () => {
  // Cleanup will be handled by global teardown
})

// Clean up database between tests
beforeEach(async () => {
  if (global.__PRISMA__) {
    // Clean up test data
    await global.__PRISMA__.auditLog.deleteMany()
    await global.__PRISMA__.session.deleteMany()
    await global.__PRISMA__.userRole.deleteMany()
    await global.__PRISMA__.document.deleteMany()
    await global.__PRISMA__.dueDiligenceItem.deleteMany()
    await global.__PRISMA__.valuation.deleteMany()
    await global.__PRISMA__.deal.deleteMany()
    await global.__PRISMA__.user.deleteMany()
    await global.__PRISMA__.role.deleteMany()
    await global.__PRISMA__.tenant.deleteMany()
  }
})

// Mock console methods in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}
