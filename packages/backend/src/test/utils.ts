import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

export const testDb = global.__PRISMA__ as PrismaClient

// Test data factories
export const createTestTenant = async (overrides: any = {}) => {
  return testDb.tenant.create({
    data: {
      name: 'Test Company',
      domain: 'test.example.com',
      subdomain: 'test',
      status: 'ACTIVE',
      ...overrides,
    },
  })
}

export const createTestUser = async (tenantId: string, overrides: any = {}) => {
  const hashedPassword = await bcrypt.hash('password123', 12)
  
  return testDb.user.create({
    data: {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      password: hashedPassword,
      emailVerified: true,
      tenantId,
      status: 'ACTIVE',
      ...overrides,
    },
  })
}

export const createTestRole = async (tenantId: string, overrides: any = {}) => {
  return testDb.role.create({
    data: {
      name: 'Test Role',
      description: 'Test role description',
      tenantId,
      permissions: ['test:read', 'test:write'],
      ...overrides,
    },
  })
}

export const createTestDeal = async (tenantId: string, createdById: string, overrides: any = {}) => {
  return testDb.deal.create({
    data: {
      title: 'Test Deal',
      description: 'Test deal description',
      status: 'PIPELINE',
      stage: 'Initial Review',
      value: 1000000,
      currency: 'USD',
      priority: 'MEDIUM',
      targetCompany: 'Target Corp',
      tenantId,
      createdById,
      ...overrides,
    },
  })
}

// Auth helpers
export const generateTestToken = (userId: string, tenantId: string) => {
  return jwt.sign(
    { userId, tenantId },
    process.env.JWT_SECRET!,
    { expiresIn: '1h' }
  )
}

export const createAuthHeaders = (token: string) => ({
  Authorization: `Bearer ${token}`,
})

// Database helpers
export const clearDatabase = async () => {
  await testDb.auditLog.deleteMany()
  await testDb.session.deleteMany()
  await testDb.userRole.deleteMany()
  await testDb.document.deleteMany()
  await testDb.dueDiligenceItem.deleteMany()
  await testDb.valuation.deleteMany()
  await testDb.deal.deleteMany()
  await testDb.user.deleteMany()
  await testDb.role.deleteMany()
  await testDb.tenant.deleteMany()
}

// Test setup helpers
export const setupTestData = async () => {
  const tenant = await createTestTenant()
  const user = await createTestUser(tenant.id)
  const role = await createTestRole(tenant.id)
  
  await testDb.userRole.create({
    data: {
      userId: user.id,
      roleId: role.id,
    },
  })

  const token = generateTestToken(user.id, tenant.id)

  return {
    tenant,
    user,
    role,
    token,
    authHeaders: createAuthHeaders(token),
  }
}

// API testing helpers
export const expectValidationError = (response: any, field?: string) => {
  expect(response.status).toBe(400)
  expect(response.body.success).toBe(false)
  expect(response.body.message).toContain('Validation')
  
  if (field) {
    expect(response.body.errors).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ field })
      ])
    )
  }
}

export const expectUnauthorized = (response: any) => {
  expect(response.status).toBe(401)
  expect(response.body.success).toBe(false)
  expect(response.body.message).toContain('Unauthorized')
}

export const expectForbidden = (response: any) => {
  expect(response.status).toBe(403)
  expect(response.body.success).toBe(false)
  expect(response.body.message).toContain('Forbidden')
}

export const expectNotFound = (response: any) => {
  expect(response.status).toBe(404)
  expect(response.body.success).toBe(false)
  expect(response.body.message).toContain('not found')
}
