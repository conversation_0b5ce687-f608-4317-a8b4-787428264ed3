import { PrismaClient } from '@prisma/client'
import { AuthTestUtils, AuthTestContext } from './auth-test-utils'
import { TestHelpers } from '../utils/test-helpers'

describe('Authentication Integration Tests', () => {
  let prisma: PrismaClient
  let authContext: AuthTestContext

  beforeAll(async () => {
    prisma = global.__PRISMA__
    AuthTestUtils.initialize(prisma)
  })

  beforeEach(async () => {
    // Clean database before each test
    await prisma.migrationRecord.deleteMany()
    await prisma.auditLog.deleteMany()
    await prisma.session.deleteMany()
    await prisma.userRole.deleteMany()
    await prisma.document.deleteMany()
    await prisma.dueDiligenceItem.deleteMany()
    await prisma.valuation.deleteMany()
    await prisma.deal.deleteMany()
    await prisma.tenantApiKey.deleteMany()
    await prisma.tenantInvitation.deleteMany()
    await prisma.user.deleteMany()
    await prisma.role.deleteMany()
    await prisma.tenant.deleteMany()

    // Create fresh auth context for each test
    authContext = await AuthTestUtils.createAuthTestContext()
  })

  afterEach(async () => {
    if (authContext) {
      await AuthTestUtils.cleanupAuthTestData(authContext)
    }
  })

  describe('JWT Token Management', () => {
    it('should generate valid JWT tokens', async () => {
      const payload = {
        userId: authContext.users.admin.id,
        email: authContext.users.admin.email,
        tenantId: authContext.tenant.id
      }

      const token = AuthTestUtils.generateToken(payload)
      expect(token).toBeDefined()
      expect(typeof token).toBe('string')
      expect(token.split('.')).toHaveLength(3) // JWT has 3 parts

      // Verify token can be decoded
      await AuthTestUtils.testTokenValidation(token, true)
    })

    it('should reject invalid JWT tokens', async () => {
      await AuthTestUtils.testTokenValidation(authContext.tokens.invalid, false)
    })

    it('should reject expired JWT tokens', async () => {
      await AuthTestUtils.testTokenValidation(authContext.tokens.expired, false)
    })

    it('should validate token payload structure', async () => {
      const decoded = AuthTestUtils.verifyToken(authContext.tokens.valid)
      
      expect(decoded).toHaveProperty('userId')
      expect(decoded).toHaveProperty('email')
      expect(decoded).toHaveProperty('tenantId')
      expect(decoded).toHaveProperty('iat')
      expect(decoded).toHaveProperty('exp')

      expect(decoded.userId).toBe(authContext.users.admin.id)
      expect(decoded.email).toBe(authContext.users.admin.email)
      expect(decoded.tenantId).toBe(authContext.tenant.id)
    })

    it('should handle token refresh', async () => {
      const originalPayload = AuthTestUtils.verifyToken(authContext.tokens.valid)
      
      // Generate new token with same payload
      const refreshedToken = AuthTestUtils.generateToken({
        userId: originalPayload.userId,
        email: originalPayload.email,
        tenantId: originalPayload.tenantId
      })

      expect(refreshedToken).not.toBe(authContext.tokens.valid)
      await AuthTestUtils.testTokenValidation(refreshedToken, true)

      const refreshedPayload = AuthTestUtils.verifyToken(refreshedToken)
      expect(refreshedPayload.userId).toBe(originalPayload.userId)
      expect(refreshedPayload.email).toBe(originalPayload.email)
      expect(refreshedPayload.tenantId).toBe(originalPayload.tenantId)
    })
  })

  describe('Password Security', () => {
    it('should hash passwords securely', async () => {
      await AuthTestUtils.testPasswordSecurity()
    })

    it('should validate password strength requirements', async () => {
      const weakPasswords = [
        '123',
        'password',
        'abc123',
        '12345678',
        'qwerty'
      ]

      const strongPasswords = [
        'StrongP@ssw0rd123!',
        'MySecure#Password2024',
        'C0mpl3x!P@ssw0rd'
      ]

      // Note: In a real implementation, you would have password validation logic
      // For testing purposes, we're just checking that different passwords produce different hashes
      for (const password of [...weakPasswords, ...strongPasswords]) {
        const hash1 = await AuthTestUtils.hashPassword(password)
        const hash2 = await AuthTestUtils.hashPassword(password)
        
        // Same password should produce different hashes (due to salt)
        expect(hash1).not.toBe(hash2)
        
        // But both should validate correctly
        expect(await AuthTestUtils.comparePassword(password, hash1)).toBe(true)
        expect(await AuthTestUtils.comparePassword(password, hash2)).toBe(true)
      }
    })

    it('should handle password comparison correctly', async () => {
      const password = 'testPassword123!'
      const hash = await AuthTestUtils.hashPassword(password)

      // Correct password should validate
      expect(await AuthTestUtils.comparePassword(password, hash)).toBe(true)

      // Incorrect passwords should not validate
      const wrongPasswords = [
        'wrongPassword',
        'testPassword123',
        'TestPassword123!',
        'testPassword123!!',
        ''
      ]

      for (const wrongPassword of wrongPasswords) {
        expect(await AuthTestUtils.comparePassword(wrongPassword, hash)).toBe(false)
      }
    })
  })

  describe('Session Management', () => {
    it('should create and manage user sessions', async () => {
      await AuthTestUtils.testSessionManagement(
        authContext.users.admin.id,
        authContext.tenant.id
      )
    })

    it('should handle session expiration', async () => {
      const sessionToken = AuthTestUtils.generateRandomString(32)
      
      // Create expired session
      const expiredSession = await prisma.session.create({
        data: {
          token: sessionToken,
          userId: authContext.users.admin.id,
          tenantId: authContext.tenant.id,
          expiresAt: new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
        }
      })

      // Check if session is expired
      const now = new Date()
      const isExpired = expiredSession.expiresAt < now
      expect(isExpired).toBe(true)

      // Clean up
      await prisma.session.delete({ where: { id: expiredSession.id } })
    })

    it('should isolate sessions by tenant', async () => {
      const otherTenant = await TestHelpers.createTestTenant({
        subdomain: 'othertenant',
        slug: 'other-tenant'
      })

      const session1Token = AuthTestUtils.generateRandomString(32)
      const session2Token = AuthTestUtils.generateRandomString(32)

      // Create sessions for different tenants
      const session1 = await prisma.session.create({
        data: {
          token: session1Token,
          userId: authContext.users.admin.id,
          tenantId: authContext.tenant.id,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
        }
      })

      const otherUser = await TestHelpers.createTestUser(otherTenant.id, {
        email: '<EMAIL>'
      })

      const session2 = await prisma.session.create({
        data: {
          token: session2Token,
          userId: otherUser.id,
          tenantId: otherTenant.id,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
        }
      })

      // Verify sessions are isolated by tenant
      const tenant1Sessions = await prisma.session.findMany({
        where: { tenantId: authContext.tenant.id }
      })

      const tenant2Sessions = await prisma.session.findMany({
        where: { tenantId: otherTenant.id }
      })

      expect(tenant1Sessions).toHaveLength(1)
      expect(tenant1Sessions[0].token).toBe(session1Token)
      expect(tenant2Sessions).toHaveLength(1)
      expect(tenant2Sessions[0].token).toBe(session2Token)

      // Clean up
      await prisma.session.deleteMany({
        where: { id: { in: [session1.id, session2.id] } }
      })
      await prisma.user.delete({ where: { id: otherUser.id } })
      await prisma.tenant.delete({ where: { id: otherTenant.id } })
    })
  })

  describe('Role-Based Access Control', () => {
    it('should validate user permissions correctly', async () => {
      // Test admin permissions
      await AuthTestUtils.testRoleBasedAccess(
        authContext.users.admin,
        ['users:create', 'deals:delete', 'settings:update'],
        true // Admin should have all permissions
      )

      // Test manager permissions
      await AuthTestUtils.testRoleBasedAccess(
        authContext.users.manager,
        ['deals:create', 'deals:read', 'users:read'],
        true // Manager should have these permissions
      )

      // Test regular user permissions
      await AuthTestUtils.testRoleBasedAccess(
        authContext.users.user,
        ['deals:read', 'documents:read'],
        true // User should have read permissions
      )

      // Test unauthorized access
      await AuthTestUtils.testRoleBasedAccess(
        authContext.users.user,
        ['users:delete', 'settings:update'],
        false // Regular user should not have admin permissions
      )
    })

    it('should handle role inheritance and hierarchies', async () => {
      // Create custom role with specific permissions
      const customRole = await prisma.role.create({
        data: {
          name: 'Custom Role',
          description: 'Custom role for testing',
          permissions: ['custom:permission', 'deals:read'],
          tenantId: authContext.tenant.id
        }
      })

      // Assign custom role to user
      await prisma.userRole.create({
        data: {
          userId: authContext.users.user.id,
          roleId: customRole.id
        }
      })

      // Test that user now has custom permissions
      await AuthTestUtils.testRoleBasedAccess(
        authContext.users.user,
        ['custom:permission'],
        true
      )

      // Clean up
      await prisma.userRole.deleteMany({
        where: { roleId: customRole.id }
      })
      await prisma.role.delete({ where: { id: customRole.id } })
    })

    it('should handle multiple roles per user', async () => {
      // Create additional role
      const additionalRole = await prisma.role.create({
        data: {
          name: 'Additional Role',
          description: 'Additional role for testing',
          permissions: ['additional:permission'],
          tenantId: authContext.tenant.id
        }
      })

      // Assign additional role to user (who already has User role)
      await prisma.userRole.create({
        data: {
          userId: authContext.users.user.id,
          roleId: additionalRole.id
        }
      })

      // User should have permissions from both roles
      const userWithRoles = await prisma.user.findUnique({
        where: { id: authContext.users.user.id },
        include: {
          roles: {
            include: {
              role: true
            }
          }
        }
      })

      const allPermissions = userWithRoles?.roles.flatMap(ur => ur.role.permissions) || []
      expect(allPermissions).toContain('additional:permission')

      // Clean up
      await prisma.userRole.deleteMany({
        where: { roleId: additionalRole.id }
      })
      await prisma.role.delete({ where: { id: additionalRole.id } })
    })
  })

  describe('Tenant Isolation in Authentication', () => {
    it('should isolate user authentication by tenant', async () => {
      const otherTenant = await TestHelpers.createTestTenant({
        subdomain: 'isolation',
        slug: 'isolation-test'
      })

      await AuthTestUtils.testTenantIsolationInAuth(
        authContext.users.admin,
        authContext.tenant,
        otherTenant
      )

      // Clean up
      await prisma.tenant.delete({ where: { id: otherTenant.id } })
    })

    it('should prevent cross-tenant token usage', async () => {
      // Token from wrong tenant should not be valid for this tenant's operations
      const wrongTenantPayload = AuthTestUtils.verifyToken(authContext.tokens.wrongTenant)
      
      expect(wrongTenantPayload.tenantId).not.toBe(authContext.tenant.id)
      
      // In a real application, middleware would check that the token's tenantId
      // matches the request's tenant context
    })

    it('should handle tenant-specific user lookups', async () => {
      const email = '<EMAIL>'
      
      // Create users with same email in different tenants
      const user1 = await TestHelpers.createTestUser(authContext.tenant.id, {
        email,
        firstName: 'User1'
      })

      const otherTenant = await TestHelpers.createTestTenant({
        subdomain: 'shared',
        slug: 'shared-email'
      })

      const user2 = await TestHelpers.createTestUser(otherTenant.id, {
        email,
        firstName: 'User2'
      })

      // Lookup should be tenant-specific
      const foundUser1 = await prisma.user.findFirst({
        where: {
          email,
          tenantId: authContext.tenant.id
        }
      })

      const foundUser2 = await prisma.user.findFirst({
        where: {
          email,
          tenantId: otherTenant.id
        }
      })

      expect(foundUser1?.id).toBe(user1.id)
      expect(foundUser1?.firstName).toBe('User1')
      expect(foundUser2?.id).toBe(user2.id)
      expect(foundUser2?.firstName).toBe('User2')

      // Clean up
      await prisma.user.deleteMany({
        where: { id: { in: [user1.id, user2.id] } }
      })
      await prisma.tenant.delete({ where: { id: otherTenant.id } })
    })
  })

  describe('Account Security Features', () => {
    it('should handle account lockout after failed attempts', async () => {
      await AuthTestUtils.testAccountLockout(authContext.users.user)
    })

    it('should manage email verification', async () => {
      await AuthTestUtils.testEmailVerification(authContext.users.user)
    })

    it('should handle password reset flow', async () => {
      await AuthTestUtils.testPasswordReset(authContext.users.user)
    })

    it('should support two-factor authentication', async () => {
      await AuthTestUtils.testTwoFactorAuth(authContext.users.admin)
    })

    it('should handle inactive user accounts', async () => {
      // Verify inactive user exists but is marked as inactive
      const inactiveUser = await prisma.user.findUnique({
        where: { id: authContext.users.inactive.id }
      })

      expect(inactiveUser?.status).toBe('INACTIVE')
      
      // In a real application, authentication middleware would check user status
      // and reject authentication for inactive users
    })
  })

  describe('Security Edge Cases', () => {
    it('should handle malformed authentication data', async () => {
      const malformedTokens = [
        '',
        'not.a.jwt',
        'header.payload', // Missing signature
        'too.many.parts.in.this.token',
        null,
        undefined
      ]

      for (const token of malformedTokens) {
        try {
          if (token) {
            AuthTestUtils.verifyToken(token)
            fail('Should have thrown an error for malformed token')
          }
        } catch (error) {
          // Expected to fail
          expect(error).toBeDefined()
        }
      }
    })

    it('should handle concurrent authentication attempts', async () => {
      const user = authContext.users.admin
      
      // Simulate multiple concurrent login attempts
      const concurrentAttempts = Array.from({ length: 10 }, () =>
        prisma.session.create({
          data: {
            token: AuthTestUtils.generateRandomString(32),
            userId: user.id,
            tenantId: authContext.tenant.id,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
          }
        })
      )

      const sessions = await Promise.all(concurrentAttempts)
      expect(sessions).toHaveLength(10)

      // All sessions should be valid and belong to the same user
      sessions.forEach(session => {
        expect(session.userId).toBe(user.id)
        expect(session.tenantId).toBe(authContext.tenant.id)
      })

      // Clean up
      await prisma.session.deleteMany({
        where: { id: { in: sessions.map(s => s.id) } }
      })
    })

    it('should handle session cleanup and garbage collection', async () => {
      // Create expired sessions
      const expiredSessions = await Promise.all(
        Array.from({ length: 5 }, (_, i) =>
          prisma.session.create({
            data: {
              token: `expired_${i}`,
              userId: authContext.users.admin.id,
              tenantId: authContext.tenant.id,
              expiresAt: new Date(Date.now() - (i + 1) * 60 * 60 * 1000) // Expired hours ago
            }
          })
        )
      )

      // Simulate cleanup of expired sessions
      const deletedCount = await prisma.session.deleteMany({
        where: {
          expiresAt: { lt: new Date() }
        }
      })

      expect(deletedCount.count).toBe(5)

      // Verify sessions were deleted
      const remainingSessions = await prisma.session.findMany({
        where: { id: { in: expiredSessions.map(s => s.id) } }
      })

      expect(remainingSessions).toHaveLength(0)
    })
  })
})
