import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import { PrismaClient } from '@prisma/client'
import { TestHelpers, TestTenant, TestUser } from '../utils/test-helpers'

export interface AuthTestContext {
  tenant: TestTenant
  users: {
    admin: TestUser
    manager: TestUser
    user: TestUser
    inactive: TestUser
  }
  tokens: {
    valid: string
    expired: string
    invalid: string
    wrongTenant: string
  }
}

export class AuthTestUtils {
  private static prisma: PrismaClient

  static initialize(prisma: PrismaClient) {
    this.prisma = prisma
  }

  // Create comprehensive auth test context
  static async createAuthTestContext(): Promise<AuthTestContext> {
    const tenant = await TestHelpers.createTestTenant({
      name: 'Auth Test Tenant',
      subdomain: 'authtest',
      slug: 'auth-test'
    })

    // Create users with different roles and statuses
    const admin = await TestHelpers.createTestUser(tenant.id, {
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      roles: ['Admin']
    })

    const manager = await TestHelpers.createTestUser(tenant.id, {
      email: '<EMAIL>',
      firstName: 'Manager',
      lastName: 'User',
      roles: ['Manager']
    })

    const user = await TestHelpers.createTestUser(tenant.id, {
      email: '<EMAIL>',
      firstName: 'Regular',
      lastName: 'User',
      roles: ['User']
    })

    // Create inactive user
    const inactiveUser = await TestHelpers.createTestUser(tenant.id, {
      email: '<EMAIL>',
      firstName: 'Inactive',
      lastName: 'User',
      roles: ['User']
    })

    // Update inactive user status
    await this.prisma.user.update({
      where: { id: inactiveUser.id },
      data: { status: 'INACTIVE' }
    })

    // Create different types of tokens
    const validToken = this.generateToken({
      userId: admin.id,
      email: admin.email,
      tenantId: tenant.id
    })

    const expiredToken = this.generateToken({
      userId: admin.id,
      email: admin.email,
      tenantId: tenant.id
    }, { expiresIn: '-1h' }) // Already expired

    const invalidToken = 'invalid.jwt.token'

    // Create token for different tenant
    const otherTenant = await TestHelpers.createTestTenant({
      subdomain: 'other',
      slug: 'other'
    })
    const wrongTenantToken = this.generateToken({
      userId: admin.id,
      email: admin.email,
      tenantId: otherTenant.id
    })

    return {
      tenant,
      users: {
        admin,
        manager,
        user,
        inactive: { ...inactiveUser, status: 'INACTIVE' }
      },
      tokens: {
        valid: validToken,
        expired: expiredToken,
        invalid: invalidToken,
        wrongTenant: wrongTenantToken
      }
    }
  }

  // Generate JWT token
  static generateToken(payload: any, options: any = {}): string {
    return jwt.sign(
      payload,
      process.env.JWT_SECRET!,
      { expiresIn: '1h', ...options }
    )
  }

  // Verify JWT token
  static verifyToken(token: string): any {
    try {
      return jwt.verify(token, process.env.JWT_SECRET!)
    } catch (error) {
      throw new Error('Invalid token')
    }
  }

  // Hash password
  static async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, 10)
  }

  // Compare password
  static async comparePassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash)
  }

  // Test token validation
  static async testTokenValidation(token: string, expectedValid: boolean): Promise<void> {
    try {
      const decoded = this.verifyToken(token)
      if (!expectedValid) {
        throw new Error('Expected token to be invalid but it was valid')
      }
      expect(decoded).toBeDefined()
      expect(decoded.userId).toBeDefined()
      expect(decoded.email).toBeDefined()
      expect(decoded.tenantId).toBeDefined()
    } catch (error) {
      if (expectedValid) {
        throw new Error(`Expected token to be valid but got error: ${error}`)
      }
      // Expected to fail
    }
  }

  // Test password hashing and comparison
  static async testPasswordSecurity(): Promise<void> {
    const password = 'testPassword123!'
    const hash = await this.hashPassword(password)

    // Hash should be different from original password
    expect(hash).not.toBe(password)
    expect(hash.length).toBeGreaterThan(50) // bcrypt hashes are long

    // Should be able to verify correct password
    const isValid = await this.comparePassword(password, hash)
    expect(isValid).toBe(true)

    // Should reject incorrect password
    const isInvalid = await this.comparePassword('wrongPassword', hash)
    expect(isInvalid).toBe(false)
  }

  // Test session management
  static async testSessionManagement(userId: string, tenantId: string): Promise<void> {
    // Create session
    const sessionToken = this.generateRandomString(32)
    const session = await this.prisma.session.create({
      data: {
        token: sessionToken,
        userId,
        tenantId,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      }
    })

    expect(session.token).toBe(sessionToken)
    expect(session.userId).toBe(userId)
    expect(session.tenantId).toBe(tenantId)

    // Verify session exists
    const foundSession = await this.prisma.session.findUnique({
      where: { token: sessionToken }
    })
    expect(foundSession).toBeTruthy()

    // Clean up session
    await this.prisma.session.delete({
      where: { id: session.id }
    })

    // Verify session was deleted
    const deletedSession = await this.prisma.session.findUnique({
      where: { token: sessionToken }
    })
    expect(deletedSession).toBeNull()
  }

  // Test role-based access control
  static async testRoleBasedAccess(
    user: TestUser,
    requiredPermissions: string[],
    expectedAccess: boolean
  ): Promise<void> {
    // Get user's roles and permissions
    const userWithRoles = await this.prisma.user.findUnique({
      where: { id: user.id },
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    })

    const userPermissions = userWithRoles?.roles.flatMap(ur => ur.role.permissions) || []

    // Check if user has required permissions
    const hasAccess = requiredPermissions.every(permission => {
      return userPermissions.includes(permission) || userPermissions.includes('*')
    })

    expect(hasAccess).toBe(expectedAccess)
  }

  // Test tenant isolation in authentication
  static async testTenantIsolationInAuth(
    user: TestUser,
    correctTenant: TestTenant,
    wrongTenant: TestTenant
  ): Promise<void> {
    // User should be able to authenticate in correct tenant
    const userInCorrectTenant = await this.prisma.user.findFirst({
      where: {
        id: user.id,
        tenantId: correctTenant.id
      }
    })
    expect(userInCorrectTenant).toBeTruthy()

    // User should NOT be found in wrong tenant
    const userInWrongTenant = await this.prisma.user.findFirst({
      where: {
        id: user.id,
        tenantId: wrongTenant.id
      }
    })
    expect(userInWrongTenant).toBeNull()
  }

  // Test account lockout functionality
  static async testAccountLockout(user: TestUser): Promise<void> {
    const maxAttempts = 5
    const lockoutDuration = 15 * 60 * 1000 // 15 minutes

    // Simulate failed login attempts
    for (let i = 0; i < maxAttempts; i++) {
      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          failedLoginAttempts: i + 1,
          lastFailedLogin: new Date()
        }
      })
    }

    // User should be locked out
    const lockedUser = await this.prisma.user.findUnique({
      where: { id: user.id }
    })

    expect(lockedUser?.failedLoginAttempts).toBe(maxAttempts)
    expect(lockedUser?.lastFailedLogin).toBeTruthy()

    // Check if account is locked (this would be implemented in auth middleware)
    const isLocked = lockedUser?.failedLoginAttempts >= maxAttempts &&
      lockedUser?.lastFailedLogin &&
      (Date.now() - lockedUser.lastFailedLogin.getTime()) < lockoutDuration

    expect(isLocked).toBe(true)

    // Reset failed attempts (simulate successful login after lockout period)
    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        failedLoginAttempts: 0,
        lastFailedLogin: null
      }
    })
  }

  // Test email verification
  static async testEmailVerification(user: TestUser): Promise<void> {
    // Create unverified user
    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: false,
        emailVerificationToken: this.generateRandomString(32),
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000)
      }
    })

    const unverifiedUser = await this.prisma.user.findUnique({
      where: { id: user.id }
    })

    expect(unverifiedUser?.emailVerified).toBe(false)
    expect(unverifiedUser?.emailVerificationToken).toBeTruthy()

    // Simulate email verification
    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        emailVerificationToken: null,
        emailVerificationExpires: null
      }
    })

    const verifiedUser = await this.prisma.user.findUnique({
      where: { id: user.id }
    })

    expect(verifiedUser?.emailVerified).toBe(true)
    expect(verifiedUser?.emailVerificationToken).toBeNull()
  }

  // Test password reset functionality
  static async testPasswordReset(user: TestUser): Promise<void> {
    const resetToken = this.generateRandomString(32)
    const resetExpires = new Date(Date.now() + 60 * 60 * 1000) // 1 hour

    // Create password reset request
    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        passwordResetToken: resetToken,
        passwordResetExpires: resetExpires
      }
    })

    const userWithReset = await this.prisma.user.findUnique({
      where: { id: user.id }
    })

    expect(userWithReset?.passwordResetToken).toBe(resetToken)
    expect(userWithReset?.passwordResetExpires).toEqual(resetExpires)

    // Simulate password reset completion
    const newPasswordHash = await this.hashPassword('newPassword123!')
    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        password: newPasswordHash,
        passwordResetToken: null,
        passwordResetExpires: null
      }
    })

    const userAfterReset = await this.prisma.user.findUnique({
      where: { id: user.id }
    })

    expect(userAfterReset?.passwordResetToken).toBeNull()
    expect(userAfterReset?.passwordResetExpires).toBeNull()
    expect(userAfterReset?.password).toBe(newPasswordHash)
  }

  // Test two-factor authentication
  static async testTwoFactorAuth(user: TestUser): Promise<void> {
    const secret = this.generateRandomString(32)
    const backupCodes = Array.from({ length: 10 }, () => this.generateRandomString(8))

    // Enable 2FA
    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        twoFactorEnabled: true,
        twoFactorSecret: secret,
        twoFactorBackupCodes: backupCodes
      }
    })

    const userWith2FA = await this.prisma.user.findUnique({
      where: { id: user.id }
    })

    expect(userWith2FA?.twoFactorEnabled).toBe(true)
    expect(userWith2FA?.twoFactorSecret).toBe(secret)
    expect(userWith2FA?.twoFactorBackupCodes).toEqual(backupCodes)

    // Disable 2FA
    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        twoFactorEnabled: false,
        twoFactorSecret: null,
        twoFactorBackupCodes: []
      }
    })

    const userWithout2FA = await this.prisma.user.findUnique({
      where: { id: user.id }
    })

    expect(userWithout2FA?.twoFactorEnabled).toBe(false)
    expect(userWithout2FA?.twoFactorSecret).toBeNull()
  }

  // Generate random string
  static generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  // Clean up auth test data
  static async cleanupAuthTestData(context: AuthTestContext): Promise<void> {
    // Delete sessions
    await this.prisma.session.deleteMany({
      where: { tenantId: context.tenant.id }
    })

    // Delete user roles
    await this.prisma.userRole.deleteMany({
      where: {
        user: { tenantId: context.tenant.id }
      }
    })

    // Delete users
    await this.prisma.user.deleteMany({
      where: { tenantId: context.tenant.id }
    })

    // Delete roles
    await this.prisma.role.deleteMany({
      where: { tenantId: context.tenant.id }
    })

    // Delete tenant
    await this.prisma.tenant.delete({
      where: { id: context.tenant.id }
    })
  }
}
