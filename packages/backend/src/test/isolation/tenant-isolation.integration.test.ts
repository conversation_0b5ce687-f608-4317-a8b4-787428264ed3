import { PrismaClient } from '@prisma/client'
import { TenantIsolationUtils, IsolationTestContext } from './tenant-isolation-utils'
import { BaseApiTest } from '../integration/base-api.test'

describe('Tenant Isolation Integration Tests', () => {
  let prisma: PrismaClient
  let isolationContext: IsolationTestContext
  let apiTest: BaseApiTest

  beforeAll(async () => {
    prisma = global.__PRISMA__
    TenantIsolationUtils.initialize(prisma)
    
    apiTest = new BaseApiTest()
    await apiTest.setup()
  })

  beforeEach(async () => {
    // Clean database before each test
    await prisma.migrationRecord.deleteMany()
    await prisma.auditLog.deleteMany()
    await prisma.session.deleteMany()
    await prisma.userRole.deleteMany()
    await prisma.document.deleteMany()
    await prisma.dueDiligenceItem.deleteMany()
    await prisma.valuation.deleteMany()
    await prisma.deal.deleteMany()
    await prisma.tenantApiKey.deleteMany()
    await prisma.tenantInvitation.deleteMany()
    await prisma.user.deleteMany()
    await prisma.role.deleteMany()
    await prisma.tenant.deleteMany()

    // Create fresh isolation context
    isolationContext = await TenantIsolationUtils.createIsolationTestContext()
  })

  afterEach(async () => {
    if (isolationContext) {
      await TenantIsolationUtils.cleanupIsolationTestContext(isolationContext)
    }
  })

  describe('Database-Level Isolation', () => {
    it('should isolate user data between tenants', async () => {
      await TenantIsolationUtils.testDataIsolation(
        isolationContext,
        'Users',
        async (tenantId: string) => {
          return await prisma.user.findMany({
            where: { tenantId }
          })
        }
      )
    })

    it('should isolate deal data between tenants', async () => {
      await TenantIsolationUtils.testDataIsolation(
        isolationContext,
        'Deals',
        async (tenantId: string) => {
          return await prisma.deal.findMany({
            where: { tenantId }
          })
        }
      )
    })

    it('should isolate document data between tenants', async () => {
      await TenantIsolationUtils.testDataIsolation(
        isolationContext,
        'Documents',
        async (tenantId: string) => {
          return await prisma.document.findMany({
            where: { tenantId }
          })
        }
      )
    })

    it('should isolate role data between tenants', async () => {
      await TenantIsolationUtils.testDataIsolation(
        isolationContext,
        'Roles',
        async (tenantId: string) => {
          return await prisma.role.findMany({
            where: { tenantId }
          })
        }
      )
    })

    it('should prevent cross-tenant data access', async () => {
      const { tenant1, tenant2 } = isolationContext.tenants
      const tenant1Deals = await prisma.deal.findMany({
        where: { tenantId: tenant1.id }
      })

      expect(tenant1Deals.length).toBeGreaterThan(0)

      // Try to access tenant1 deal from tenant2 context
      const dealId = tenant1Deals[0].id
      await TenantIsolationUtils.testCrossTenantAccessPrevention(
        isolationContext,
        dealId,
        'Deal',
        async (tenantId: string, resourceId: string) => {
          return await prisma.deal.findFirst({
            where: { id: resourceId, tenantId }
          })
        }
      )
    })

    it('should maintain isolation in complex queries', async () => {
      await TenantIsolationUtils.testTenantSpecificQueries(isolationContext)
    })

    it('should handle concurrent access with isolation', async () => {
      await TenantIsolationUtils.testConcurrentAccessIsolation(isolationContext)
    })

    it('should maintain isolation in transactions', async () => {
      await TenantIsolationUtils.testTransactionIsolation(isolationContext)
    })

    it('should maintain isolation in bulk operations', async () => {
      await TenantIsolationUtils.testBulkOperationIsolation(isolationContext)
    })
  })

  describe('API-Level Isolation', () => {
    it('should isolate tenant current endpoint', async () => {
      await TenantIsolationUtils.testApiIsolation(
        apiTest,
        isolationContext,
        '/api/tenants/current',
        'get'
      )
    })

    it('should isolate deals API endpoints', async () => {
      await TenantIsolationUtils.testApiIsolation(
        apiTest,
        isolationContext,
        '/api/deals',
        'get'
      )
    })

    it('should isolate documents API endpoints', async () => {
      await TenantIsolationUtils.testApiIsolation(
        apiTest,
        isolationContext,
        '/api/documents',
        'get'
      )
    })

    it('should isolate users API endpoints', async () => {
      await TenantIsolationUtils.testApiIsolation(
        apiTest,
        isolationContext,
        '/api/users',
        'get'
      )
    })

    it('should prevent cross-tenant API access', async () => {
      const { tenants, users } = isolationContext

      // Get a deal from tenant1
      const tenant1Deals = await prisma.deal.findMany({
        where: { tenantId: tenants.tenant1.id }
      })

      expect(tenant1Deals.length).toBeGreaterThan(0)
      const dealId = tenant1Deals[0].id

      // Try to access tenant1 deal from tenant2 user
      const response = await apiTest.makeRequest('get', `/api/deals/${dealId}`, {
        tenant: tenants.tenant2,
        user: users.tenant2Admin,
        expectStatus: 404
      })

      expect(response.body.success).toBe(false)
    })

    it('should isolate search results by tenant', async () => {
      const { tenants, users } = isolationContext

      // Search for deals in each tenant
      const tenant1Search = await apiTest.makeRequest('get', '/api/deals?search=Deal', {
        tenant: tenants.tenant1,
        user: users.tenant1Admin
      })

      const tenant2Search = await apiTest.makeRequest('get', '/api/deals?search=Deal', {
        tenant: tenants.tenant2,
        user: users.tenant2Admin
      })

      // Results should be different and tenant-specific
      expect(tenant1Search.body.data.items.every((deal: any) => 
        deal.tenantId === tenants.tenant1.id
      )).toBe(true)

      expect(tenant2Search.body.data.items.every((deal: any) => 
        deal.tenantId === tenants.tenant2.id
      )).toBe(true)

      // No overlap in results
      const tenant1Ids = tenant1Search.body.data.items.map((deal: any) => deal.id)
      const tenant2Ids = tenant2Search.body.data.items.map((deal: any) => deal.id)
      
      expect(tenant1Ids.some((id: string) => tenant2Ids.includes(id))).toBe(false)
    })

    it('should isolate pagination results by tenant', async () => {
      const { tenants, users } = isolationContext

      // Get paginated results for each tenant
      const tenant1Page1 = await apiTest.makeRequest('get', '/api/deals?page=1&limit=10', {
        tenant: tenants.tenant1,
        user: users.tenant1Admin
      })

      const tenant2Page1 = await apiTest.makeRequest('get', '/api/deals?page=1&limit=10', {
        tenant: tenants.tenant2,
        user: users.tenant2Admin
      })

      // Verify pagination metadata is tenant-specific
      expect(tenant1Page1.body.data.pagination.total).not.toBe(
        tenant2Page1.body.data.pagination.total
      )

      // Verify all items belong to correct tenant
      tenant1Page1.body.data.items.forEach((item: any) => {
        expect(item.tenantId).toBe(tenants.tenant1.id)
      })

      tenant2Page1.body.data.items.forEach((item: any) => {
        expect(item.tenantId).toBe(tenants.tenant2.id)
      })
    })
  })

  describe('Authentication and Authorization Isolation', () => {
    it('should prevent cross-tenant token usage', async () => {
      const { tenants, users } = isolationContext

      // Try to use tenant1 user token in tenant2 context
      const response = await apiTest.makeRequest('get', '/api/auth/me', {
        tenant: tenants.tenant2,
        user: users.tenant1Admin,
        expectStatus: 403
      })

      expect(response.body.success).toBe(false)
    })

    it('should isolate user sessions by tenant', async () => {
      const { tenants, users } = isolationContext

      // Create sessions for users in different tenants
      const session1 = await prisma.session.create({
        data: {
          token: 'session_token_1',
          userId: users.tenant1Admin.id,
          tenantId: tenants.tenant1.id,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
        }
      })

      const session2 = await prisma.session.create({
        data: {
          token: 'session_token_2',
          userId: users.tenant2Admin.id,
          tenantId: tenants.tenant2.id,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
        }
      })

      // Verify sessions are isolated by tenant
      const tenant1Sessions = await prisma.session.findMany({
        where: { tenantId: tenants.tenant1.id }
      })

      const tenant2Sessions = await prisma.session.findMany({
        where: { tenantId: tenants.tenant2.id }
      })

      expect(tenant1Sessions.some(s => s.id === session1.id)).toBe(true)
      expect(tenant1Sessions.some(s => s.id === session2.id)).toBe(false)
      expect(tenant2Sessions.some(s => s.id === session2.id)).toBe(true)
      expect(tenant2Sessions.some(s => s.id === session1.id)).toBe(false)
    })

    it('should isolate role assignments by tenant', async () => {
      const { tenants, users } = isolationContext

      // Get roles for each tenant
      const tenant1Roles = await prisma.role.findMany({
        where: { tenantId: tenants.tenant1.id }
      })

      const tenant2Roles = await prisma.role.findMany({
        where: { tenantId: tenants.tenant2.id }
      })

      // Verify no role overlap
      const tenant1RoleIds = tenant1Roles.map(r => r.id)
      const tenant2RoleIds = tenant2Roles.map(r => r.id)

      expect(tenant1RoleIds.some(id => tenant2RoleIds.includes(id))).toBe(false)

      // Verify user role assignments are tenant-specific
      const tenant1UserRoles = await prisma.userRole.findMany({
        where: { userId: users.tenant1Admin.id },
        include: { role: true }
      })

      tenant1UserRoles.forEach(userRole => {
        expect(userRole.role.tenantId).toBe(tenants.tenant1.id)
      })
    })
  })

  describe('Data Modification Isolation', () => {
    it('should isolate create operations by tenant', async () => {
      const { tenants, users } = isolationContext

      // Create deals in different tenants
      const createData = {
        title: 'Isolation Test Deal',
        type: 'ACQUISITION',
        status: 'ACTIVE',
        targetCompany: 'Test Target',
        dealValue: 1000000,
        currency: 'USD'
      }

      const tenant1Response = await apiTest.makeRequest('post', '/api/deals', {
        tenant: tenants.tenant1,
        user: users.tenant1Admin,
        body: createData
      })

      const tenant2Response = await apiTest.makeRequest('post', '/api/deals', {
        tenant: tenants.tenant2,
        user: users.tenant2Admin,
        body: createData
      })

      // Both should succeed but create separate entities
      expect(tenant1Response.body.success).toBe(true)
      expect(tenant2Response.body.success).toBe(true)
      expect(tenant1Response.body.data.id).not.toBe(tenant2Response.body.data.id)
      expect(tenant1Response.body.data.tenantId).toBe(tenants.tenant1.id)
      expect(tenant2Response.body.data.tenantId).toBe(tenants.tenant2.id)
    })

    it('should isolate update operations by tenant', async () => {
      const { tenants, users } = isolationContext

      // Get deals from each tenant
      const tenant1Deals = await prisma.deal.findMany({
        where: { tenantId: tenants.tenant1.id }
      })

      const tenant2Deals = await prisma.deal.findMany({
        where: { tenantId: tenants.tenant2.id }
      })

      expect(tenant1Deals.length).toBeGreaterThan(0)
      expect(tenant2Deals.length).toBeGreaterThan(0)

      // Try to update tenant1 deal from tenant2 context
      const updateData = { title: 'Updated Title' }

      const response = await apiTest.makeRequest('put', `/api/deals/${tenant1Deals[0].id}`, {
        tenant: tenants.tenant2,
        user: users.tenant2Admin,
        body: updateData,
        expectStatus: 404
      })

      expect(response.body.success).toBe(false)

      // Verify original deal was not modified
      const originalDeal = await prisma.deal.findUnique({
        where: { id: tenant1Deals[0].id }
      })

      expect(originalDeal?.title).not.toBe('Updated Title')
    })

    it('should isolate delete operations by tenant', async () => {
      const { tenants, users } = isolationContext

      // Get deals from each tenant
      const tenant1Deals = await prisma.deal.findMany({
        where: { tenantId: tenants.tenant1.id }
      })

      expect(tenant1Deals.length).toBeGreaterThan(0)

      // Try to delete tenant1 deal from tenant2 context
      const response = await apiTest.makeRequest('delete', `/api/deals/${tenant1Deals[0].id}`, {
        tenant: tenants.tenant2,
        user: users.tenant2Admin,
        expectStatus: 404
      })

      expect(response.body.success).toBe(false)

      // Verify deal still exists
      const dealStillExists = await prisma.deal.findUnique({
        where: { id: tenant1Deals[0].id }
      })

      expect(dealStillExists).toBeTruthy()
    })
  })

  describe('Performance Under Isolation', () => {
    it('should maintain performance with tenant filtering', async () => {
      const { tenants, users } = isolationContext

      // Create additional data to test performance
      const additionalDeals = Array.from({ length: 100 }, (_, i) => ({
        title: `Performance Deal ${i}`,
        type: 'ACQUISITION' as const,
        status: 'ACTIVE' as const,
        targetCompany: `Performance Target ${i}`,
        dealValue: 1000000 + i,
        currency: 'USD',
        tenantId: tenants.tenant1.id
      }))

      await prisma.deal.createMany({ data: additionalDeals })

      // Test query performance with tenant filtering
      const startTime = Date.now()
      const response = await apiTest.makeRequest('get', '/api/deals?limit=50', {
        tenant: tenants.tenant1,
        user: users.tenant1Admin
      })
      const duration = Date.now() - startTime

      expect(response.body.success).toBe(true)
      expect(duration).toBeLessThan(1000) // Should complete within 1 second
      expect(response.body.data.items.every((deal: any) => 
        deal.tenantId === tenants.tenant1.id
      )).toBe(true)
    })

    it('should handle concurrent tenant operations efficiently', async () => {
      const { tenants, users } = isolationContext

      // Create concurrent operations across different tenants
      const operations = [
        // Tenant 1 operations
        ...Array.from({ length: 10 }, () =>
          apiTest.makeRequest('get', '/api/deals', {
            tenant: tenants.tenant1,
            user: users.tenant1Admin
          })
        ),
        // Tenant 2 operations
        ...Array.from({ length: 10 }, () =>
          apiTest.makeRequest('get', '/api/deals', {
            tenant: tenants.tenant2,
            user: users.tenant2Admin
          })
        )
      ]

      const startTime = Date.now()
      const results = await Promise.all(operations)
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds
      expect(results.every(r => r.body.success)).toBe(true)

      // Verify isolation was maintained under load
      const tenant1Results = results.slice(0, 10)
      const tenant2Results = results.slice(10, 20)

      tenant1Results.forEach(result => {
        expect(result.body.data.items.every((item: any) => 
          item.tenantId === tenants.tenant1.id
        )).toBe(true)
      })

      tenant2Results.forEach(result => {
        expect(result.body.data.items.every((item: any) => 
          item.tenantId === tenants.tenant2.id
        )).toBe(true)
      })
    })
  })
})
