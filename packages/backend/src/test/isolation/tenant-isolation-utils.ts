import { PrismaClient } from '@prisma/client'
import { TestHelpers, TestTenant, TestUser } from '../utils/test-helpers'
import { BaseApiTest } from '../integration/base-api.test'

export interface IsolationTestContext {
  tenants: {
    tenant1: TestTenant
    tenant2: TestTenant
    tenant3: TestTenant
  }
  users: {
    tenant1Admin: TestUser
    tenant1User: TestUser
    tenant2Admin: TestUser
    tenant2User: TestUser
    tenant3Admin: TestUser
  }
  data: {
    tenant1Data: any[]
    tenant2Data: any[]
    tenant3Data: any[]
  }
}

export class TenantIsolationUtils {
  private static prisma: PrismaClient

  static initialize(prisma: PrismaClient) {
    this.prisma = prisma
  }

  // Create comprehensive isolation test context
  static async createIsolationTestContext(): Promise<IsolationTestContext> {
    // Create three tenants for comprehensive isolation testing
    const tenant1 = await TestHelpers.createTestTenant({
      name: 'Isolation Tenant 1',
      subdomain: 'isolation1',
      slug: 'isolation-tenant-1',
      plan: 'ENTERPRISE'
    })

    const tenant2 = await TestHelpers.createTestTenant({
      name: 'Isolation Tenant 2',
      subdomain: 'isolation2',
      slug: 'isolation-tenant-2',
      plan: 'PROFESSIONAL'
    })

    const tenant3 = await TestHelpers.createTestTenant({
      name: 'Isolation Tenant 3',
      subdomain: 'isolation3',
      slug: 'isolation-tenant-3',
      plan: 'STARTER'
    })

    // Create users for each tenant
    const tenant1Admin = await TestHelpers.createTestUser(tenant1.id, {
      email: '<EMAIL>',
      firstName: 'Admin1',
      lastName: 'User',
      roles: ['Admin']
    })

    const tenant1User = await TestHelpers.createTestUser(tenant1.id, {
      email: '<EMAIL>',
      firstName: 'User1',
      lastName: 'Regular',
      roles: ['User']
    })

    const tenant2Admin = await TestHelpers.createTestUser(tenant2.id, {
      email: '<EMAIL>',
      firstName: 'Admin2',
      lastName: 'User',
      roles: ['Admin']
    })

    const tenant2User = await TestHelpers.createTestUser(tenant2.id, {
      email: '<EMAIL>',
      firstName: 'User2',
      lastName: 'Regular',
      roles: ['User']
    })

    const tenant3Admin = await TestHelpers.createTestUser(tenant3.id, {
      email: '<EMAIL>',
      firstName: 'Admin3',
      lastName: 'User',
      roles: ['Admin']
    })

    // Create test data for each tenant
    const tenant1Data = await this.createTenantTestData(tenant1.id, 'Tenant1')
    const tenant2Data = await this.createTenantTestData(tenant2.id, 'Tenant2')
    const tenant3Data = await this.createTenantTestData(tenant3.id, 'Tenant3')

    return {
      tenants: { tenant1, tenant2, tenant3 },
      users: {
        tenant1Admin,
        tenant1User,
        tenant2Admin,
        tenant2User,
        tenant3Admin
      },
      data: {
        tenant1Data,
        tenant2Data,
        tenant3Data
      }
    }
  }

  // Create test data for a specific tenant
  private static async createTenantTestData(tenantId: string, prefix: string): Promise<any[]> {
    const data = []

    // Create deals
    const deals = await Promise.all([
      TestHelpers.createTestDeal(tenantId, {
        title: `${prefix} Deal 1`,
        targetCompany: `${prefix} Target 1`,
        dealValue: 1000000
      }),
      TestHelpers.createTestDeal(tenantId, {
        title: `${prefix} Deal 2`,
        targetCompany: `${prefix} Target 2`,
        dealValue: 2000000
      })
    ])
    data.push(...deals)

    // Create documents
    const documents = await Promise.all([
      TestHelpers.createTestDocument(tenantId, deals[0].id, {
        name: `${prefix} Document 1`,
        type: 'CONTRACT'
      }),
      TestHelpers.createTestDocument(tenantId, deals[1].id, {
        name: `${prefix} Document 2`,
        type: 'FINANCIAL'
      }),
      TestHelpers.createTestDocument(tenantId, undefined, {
        name: `${prefix} Standalone Document`,
        type: 'LEGAL'
      })
    ])
    data.push(...documents)

    return data
  }

  // Test data isolation between tenants
  static async testDataIsolation(
    context: IsolationTestContext,
    entityType: string,
    findFunction: (tenantId: string) => Promise<any[]>
  ): Promise<void> {
    const { tenant1, tenant2, tenant3 } = context.tenants

    // Get data for each tenant
    const tenant1Data = await findFunction(tenant1.id)
    const tenant2Data = await findFunction(tenant2.id)
    const tenant3Data = await findFunction(tenant3.id)

    // Verify each tenant only sees its own data
    expect(tenant1Data.every(item => item.tenantId === tenant1.id)).toBe(true)
    expect(tenant2Data.every(item => item.tenantId === tenant2.id)).toBe(true)
    expect(tenant3Data.every(item => item.tenantId === tenant3.id)).toBe(true)

    // Verify no cross-tenant data leakage
    const allTenant1Ids = tenant1Data.map(item => item.id)
    const allTenant2Ids = tenant2Data.map(item => item.id)
    const allTenant3Ids = tenant3Data.map(item => item.id)

    expect(allTenant1Ids.some(id => allTenant2Ids.includes(id))).toBe(false)
    expect(allTenant1Ids.some(id => allTenant3Ids.includes(id))).toBe(false)
    expect(allTenant2Ids.some(id => allTenant3Ids.includes(id))).toBe(false)

    console.log(`✅ ${entityType} isolation verified: T1=${tenant1Data.length}, T2=${tenant2Data.length}, T3=${tenant3Data.length}`)
  }

  // Test API endpoint isolation
  static async testApiIsolation(
    apiTest: BaseApiTest,
    context: IsolationTestContext,
    endpoint: string,
    method: 'get' | 'post' | 'put' | 'delete' = 'get'
  ): Promise<void> {
    const { tenants, users } = context

    // Test that each tenant only sees their own data
    const tenant1Response = await apiTest.makeRequest(method, endpoint, {
      tenant: tenants.tenant1,
      user: users.tenant1Admin
    })

    const tenant2Response = await apiTest.makeRequest(method, endpoint, {
      tenant: tenants.tenant2,
      user: users.tenant2Admin
    })

    // Verify responses are different (indicating isolation)
    if (tenant1Response.body.data && tenant2Response.body.data) {
      if (Array.isArray(tenant1Response.body.data)) {
        // For list endpoints
        const tenant1Ids = tenant1Response.body.data.map((item: any) => item.id)
        const tenant2Ids = tenant2Response.body.data.map((item: any) => item.id)
        
        expect(tenant1Ids.some((id: string) => tenant2Ids.includes(id))).toBe(false)
      } else {
        // For single item endpoints
        expect(tenant1Response.body.data.id).not.toBe(tenant2Response.body.data.id)
      }
    }

    console.log(`✅ API isolation verified for ${method.toUpperCase()} ${endpoint}`)
  }

  // Test cross-tenant access prevention
  static async testCrossTenantAccessPrevention(
    context: IsolationTestContext,
    resourceId: string,
    resourceType: string,
    accessFunction: (tenantId: string, resourceId: string) => Promise<any>
  ): Promise<void> {
    const { tenant1, tenant2 } = context.tenants

    // Try to access tenant1 resource from tenant2 context
    const crossTenantAccess = await accessFunction(tenant2.id, resourceId)
    
    // Should not find the resource or should return null/empty
    expect(crossTenantAccess).toBeNull()

    console.log(`✅ Cross-tenant access prevention verified for ${resourceType}`);
  }

  // Test tenant-specific queries
  static async testTenantSpecificQueries(context: IsolationTestContext): Promise<void> {
    const { tenant1, tenant2 } = context.tenants

    // Test various query patterns that should be tenant-isolated
    const queries = [
      {
        name: 'User count by tenant',
        query: async (tenantId: string) => 
          await this.prisma.user.count({ where: { tenantId } })
      },
      {
        name: 'Deal aggregation by tenant',
        query: async (tenantId: string) => 
          await this.prisma.deal.aggregate({
            where: { tenantId },
            _sum: { dealValue: true },
            _count: { id: true }
          })
      },
      {
        name: 'Document search by tenant',
        query: async (tenantId: string) => 
          await this.prisma.document.findMany({
            where: { 
              tenantId,
              name: { contains: 'Document' }
            }
          })
      }
    ]

    for (const { name, query } of queries) {
      const tenant1Result = await query(tenant1.id)
      const tenant2Result = await query(tenant2.id)

      // Results should be different (indicating proper isolation)
      expect(tenant1Result).not.toEqual(tenant2Result)
      console.log(`✅ ${name} isolation verified`)
    }
  }

  // Test concurrent access isolation
  static async testConcurrentAccessIsolation(context: IsolationTestContext): Promise<void> {
    const { tenant1, tenant2 } = context.tenants

    // Create concurrent operations on different tenants
    const concurrentOperations = [
      // Tenant 1 operations
      ...Array.from({ length: 5 }, (_, i) =>
        TestHelpers.createTestDeal(tenant1.id, {
          title: `Concurrent T1 Deal ${i}`,
          targetCompany: `T1 Target ${i}`
        })
      ),
      // Tenant 2 operations
      ...Array.from({ length: 5 }, (_, i) =>
        TestHelpers.createTestDeal(tenant2.id, {
          title: `Concurrent T2 Deal ${i}`,
          targetCompany: `T2 Target ${i}`
        })
      )
    ]

    const results = await Promise.all(concurrentOperations)

    // Verify all operations completed successfully
    expect(results).toHaveLength(10)

    // Verify tenant isolation was maintained
    const tenant1Deals = await this.prisma.deal.findMany({
      where: { tenantId: tenant1.id, title: { contains: 'Concurrent T1' } }
    })

    const tenant2Deals = await this.prisma.deal.findMany({
      where: { tenantId: tenant2.id, title: { contains: 'Concurrent T2' } }
    })

    expect(tenant1Deals).toHaveLength(5)
    expect(tenant2Deals).toHaveLength(5)
    expect(tenant1Deals.every(d => d.tenantId === tenant1.id)).toBe(true)
    expect(tenant2Deals.every(d => d.tenantId === tenant2.id)).toBe(true)

    console.log('✅ Concurrent access isolation verified')
  }

  // Test transaction isolation
  static async testTransactionIsolation(context: IsolationTestContext): Promise<void> {
    const { tenant1, tenant2 } = context.tenants

    // Test that transactions are properly isolated by tenant
    try {
      await this.prisma.$transaction(async (tx) => {
        // Create data in tenant1
        const deal1 = await tx.deal.create({
          data: {
            title: 'Transaction Deal T1',
            type: 'ACQUISITION',
            status: 'ACTIVE',
            targetCompany: 'Transaction Target T1',
            dealValue: 1000000,
            currency: 'USD',
            tenantId: tenant1.id
          }
        })

        // Create data in tenant2
        const deal2 = await tx.deal.create({
          data: {
            title: 'Transaction Deal T2',
            type: 'MERGER',
            status: 'ACTIVE',
            targetCompany: 'Transaction Target T2',
            dealValue: 2000000,
            currency: 'USD',
            tenantId: tenant2.id
          }
        })

        // Verify isolation within transaction
        const t1Deals = await tx.deal.findMany({
          where: { tenantId: tenant1.id, title: { contains: 'Transaction' } }
        })

        const t2Deals = await tx.deal.findMany({
          where: { tenantId: tenant2.id, title: { contains: 'Transaction' } }
        })

        expect(t1Deals).toHaveLength(1)
        expect(t2Deals).toHaveLength(1)
        expect(t1Deals[0].id).toBe(deal1.id)
        expect(t2Deals[0].id).toBe(deal2.id)
      })

      console.log('✅ Transaction isolation verified')
    } catch (error) {
      console.error('❌ Transaction isolation test failed:', error)
      throw error
    }
  }

  // Test bulk operation isolation
  static async testBulkOperationIsolation(context: IsolationTestContext): Promise<void> {
    const { tenant1, tenant2 } = context.tenants

    // Create bulk data for each tenant
    const tenant1BulkData = Array.from({ length: 10 }, (_, i) => ({
      title: `Bulk Deal T1 ${i}`,
      type: 'ACQUISITION' as const,
      status: 'ACTIVE' as const,
      targetCompany: `Bulk Target T1 ${i}`,
      dealValue: 1000000 + i,
      currency: 'USD',
      tenantId: tenant1.id
    }))

    const tenant2BulkData = Array.from({ length: 10 }, (_, i) => ({
      title: `Bulk Deal T2 ${i}`,
      type: 'MERGER' as const,
      status: 'ACTIVE' as const,
      targetCompany: `Bulk Target T2 ${i}`,
      dealValue: 2000000 + i,
      currency: 'USD',
      tenantId: tenant2.id
    }))

    // Execute bulk operations
    await Promise.all([
      this.prisma.deal.createMany({ data: tenant1BulkData }),
      this.prisma.deal.createMany({ data: tenant2BulkData })
    ])

    // Verify isolation
    const tenant1Count = await this.prisma.deal.count({
      where: { tenantId: tenant1.id, title: { contains: 'Bulk Deal T1' } }
    })

    const tenant2Count = await this.prisma.deal.count({
      where: { tenantId: tenant2.id, title: { contains: 'Bulk Deal T2' } }
    })

    expect(tenant1Count).toBe(10)
    expect(tenant2Count).toBe(10)

    console.log('✅ Bulk operation isolation verified')
  }

  // Clean up isolation test context
  static async cleanupIsolationTestContext(context: IsolationTestContext): Promise<void> {
    const { tenant1, tenant2, tenant3 } = context.tenants

    // Clean up in reverse order of creation
    await this.cleanupTenantData(tenant3.id)
    await this.cleanupTenantData(tenant2.id)
    await this.cleanupTenantData(tenant1.id)
  }

  // Clean up tenant data
  private static async cleanupTenantData(tenantId: string): Promise<void> {
    try {
      await this.prisma.migrationRecord.deleteMany({ where: { tenantId } })
      await this.prisma.auditLog.deleteMany({ where: { tenantId } })
      await this.prisma.session.deleteMany({ where: { tenantId } })
      await this.prisma.userRole.deleteMany({
        where: { user: { tenantId } }
      })
      await this.prisma.document.deleteMany({ where: { tenantId } })
      await this.prisma.dueDiligenceItem.deleteMany({ where: { tenantId } })
      await this.prisma.valuation.deleteMany({ where: { tenantId } })
      await this.prisma.deal.deleteMany({ where: { tenantId } })
      await this.prisma.tenantApiKey.deleteMany({ where: { tenantId } })
      await this.prisma.tenantInvitation.deleteMany({ where: { tenantId } })
      await this.prisma.user.deleteMany({ where: { tenantId } })
      await this.prisma.role.deleteMany({ where: { tenantId } })
      await this.prisma.tenant.delete({ where: { id: tenantId } })
    } catch (error) {
      console.warn(`Warning: Failed to cleanup tenant ${tenantId}:`, error)
    }
  }
}
