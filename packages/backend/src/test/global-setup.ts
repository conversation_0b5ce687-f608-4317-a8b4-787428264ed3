import { PrismaClient } from '@prisma/client'
import { PostgreSqlContainer } from '@testcontainers/postgresql'
import { GenericContainer } from 'testcontainers'

export default async function globalSetup() {
  console.log('🚀 Setting up test environment...')

  // Start PostgreSQL container
  const postgresContainer = await new PostgreSqlContainer('postgres:15-alpine')
    .withDatabase('ma_platform_test')
    .withUsername('test')
    .withPassword('test')
    .start()

  // Start Redis container
  const redisContainer = await new GenericContainer('redis:7-alpine')
    .withExposedPorts(6379)
    .start()

  // Store container references for cleanup
  global.__POSTGRES_CONTAINER__ = postgresContainer
  global.__REDIS_CONTAINER__ = redisContainer

  // Update environment variables
  process.env.DATABASE_URL = postgresContainer.getConnectionUri()
  process.env.REDIS_URL = `redis://localhost:${redisContainer.getMappedPort(6379)}`

  // Initialize Prisma client
  const prisma = new PrismaClient()
  global.__PRISMA__ = prisma

  // Run migrations
  const { execSync } = require('child_process')
  execSync('pnpm prisma migrate deploy', {
    env: { ...process.env, DATABASE_URL: postgresContainer.getConnectionUri() }
  })

  // Register migrations for testing
  registerMigrations()

  // Initialize test helpers
  TestHelpers.initialize(prisma)

  console.log('✅ Test environment setup complete')
}
