export default async function globalTeardown() {
  console.log('🧹 Cleaning up test environment...')

  // Disconnect Prisma
  if (global.__PRISMA__) {
    await global.__PRISMA__.$disconnect()
  }

  // Stop containers
  if (global.__POSTGRES_CONTAINER__) {
    await global.__POSTGRES_CONTAINER__.stop()
  }

  if (global.__REDIS_CONTAINER__) {
    await global.__REDIS_CONTAINER__.stop()
  }

  console.log('✅ Test environment cleanup complete')
}
