import { PrismaClient } from '@prisma/client'
import { hashPassword } from '@/shared/utils/encryption'

export interface TestDataOptions {
  tenantId: string
  count?: number
  overrides?: any
}

export interface TestDataSet {
  tenants: any[]
  users: any[]
  roles: any[]
  deals: any[]
  documents: any[]
  valuations: any[]
  dueDiligenceItems: any[]
  auditLogs: any[]
  sessions: any[]
  invitations: any[]
  apiKeys: any[]
}

export class TestDataFactory {
  private static prisma: PrismaClient
  private static seedCounter = 0

  static initialize(prisma: PrismaClient) {
    this.prisma = prisma
  }

  // Generate unique seed for consistent but varied test data
  private static getNextSeed(): number {
    return ++this.seedCounter
  }

  // Create tenant data
  static async createTenant(overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      name: `Test Tenant ${seed}`,
      slug: `test-tenant-${seed}`,
      subdomain: `test${seed}`,
      status: 'ACTIVE',
      plan: 'PROFESSIONAL',
      description: `Test tenant ${seed} for integration testing`,
      website: `https://test${seed}.example.com`,
      settings: {
        timezone: 'UTC',
        currency: 'USD',
        dateFormat: 'MM/DD/YYYY'
      },
      features: {
        apiAccess: true,
        customBranding: false,
        advancedReporting: true,
        multiFactorAuth: false
      },
      limits: {
        maxUsers: 50,
        maxDeals: 100,
        maxStorage: 5 * 1024 * 1024 * 1024, // 5GB
        maxApiCalls: 10000
      },
      branding: {
        primaryColor: '#007bff',
        secondaryColor: '#6c757d',
        logo: null,
        favicon: null
      }
    }

    return await this.prisma.tenant.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create user data
  static async createUser(tenantId: string, overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      email: `testuser${seed}@tenant.com`,
      firstName: `User${seed}`,
      lastName: 'Test',
      password: await hashPassword('password123'),
      status: 'ACTIVE',
      emailVerified: true,
      tenantId,
      profile: {
        title: 'Test User',
        department: 'Testing',
        phone: `******-${String(seed).padStart(4, '0')}`,
        bio: `Test user ${seed} for integration testing`
      },
      preferences: {
        theme: 'light',
        language: 'en',
        notifications: {
          email: true,
          push: false,
          sms: false
        }
      }
    }

    return await this.prisma.user.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create role data
  static async createRole(tenantId: string, overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      name: `Test Role ${seed}`,
      description: `Test role ${seed} for integration testing`,
      permissions: ['test:read', 'test:write'],
      tenantId,
      isDefault: false,
      isSystemRole: false
    }

    return await this.prisma.role.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create deal data
  static async createDeal(tenantId: string, overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      title: `Test Deal ${seed}`,
      description: `Test deal ${seed} for integration testing`,
      type: seed % 2 === 0 ? 'ACQUISITION' : 'MERGER',
      status: 'ACTIVE',
      targetCompany: `Target Company ${seed}`,
      dealValue: 1000000 + (seed * 100000),
      currency: 'USD',
      expectedCloseDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
      tenantId,
      details: {
        industry: 'Technology',
        employees: 50 + seed,
        revenue: 5000000 + (seed * 500000),
        location: 'San Francisco, CA'
      },
      terms: {
        paymentStructure: 'Cash',
        earnoutPeriod: 24,
        warranties: true,
        nonCompete: true
      }
    }

    return await this.prisma.deal.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create document data
  static async createDocument(tenantId: string, dealId?: string, overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      name: `Test Document ${seed}`,
      type: seed % 3 === 0 ? 'CONTRACT' : seed % 3 === 1 ? 'FINANCIAL' : 'LEGAL',
      size: 1024 * (seed + 1),
      mimeType: 'application/pdf',
      path: `/test/documents/doc${seed}.pdf`,
      tenantId,
      dealId: dealId || null,
      metadata: {
        author: `Author ${seed}`,
        version: '1.0',
        tags: ['test', 'document'],
        confidential: seed % 2 === 0
      },
      permissions: {
        read: ['Admin', 'Manager', 'User'],
        write: ['Admin', 'Manager'],
        delete: ['Admin']
      }
    }

    return await this.prisma.document.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create valuation data
  static async createValuation(dealId: string, tenantId: string, overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      method: seed % 3 === 0 ? 'DCF' : seed % 3 === 1 ? 'COMPARABLE' : 'PRECEDENT',
      value: 5000000 + (seed * 1000000),
      currency: 'USD',
      date: new Date(),
      dealId,
      tenantId,
      assumptions: {
        growthRate: 0.15,
        discountRate: 0.12,
        terminalValue: 50000000,
        synergies: 2000000
      },
      details: {
        analyst: `Analyst ${seed}`,
        confidence: 'High',
        scenario: 'Base Case',
        notes: `Valuation ${seed} for testing purposes`
      }
    }

    return await this.prisma.valuation.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create due diligence item data
  static async createDueDiligenceItem(dealId: string, tenantId: string, overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      title: `Due Diligence Item ${seed}`,
      description: `Test due diligence item ${seed}`,
      category: seed % 4 === 0 ? 'FINANCIAL' : seed % 4 === 1 ? 'LEGAL' : seed % 4 === 2 ? 'OPERATIONAL' : 'TECHNICAL',
      status: 'PENDING',
      priority: seed % 3 === 0 ? 'HIGH' : seed % 3 === 1 ? 'MEDIUM' : 'LOW',
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      dealId,
      tenantId,
      requirements: {
        documents: [`Document ${seed}`, `Report ${seed}`],
        interviews: [`Interview ${seed}`],
        analysis: [`Analysis ${seed}`]
      },
      findings: {
        summary: `Findings summary ${seed}`,
        risks: [`Risk ${seed}`],
        recommendations: [`Recommendation ${seed}`]
      }
    }

    return await this.prisma.dueDiligenceItem.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create audit log data
  static async createAuditLog(userId: string, tenantId: string, overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      action: seed % 4 === 0 ? 'CREATE' : seed % 4 === 1 ? 'UPDATE' : seed % 4 === 2 ? 'DELETE' : 'READ',
      entityType: seed % 3 === 0 ? 'Deal' : seed % 3 === 1 ? 'Document' : 'User',
      entityId: `entity-${seed}`,
      userId,
      tenantId,
      changes: {
        field: `field${seed}`,
        oldValue: `old${seed}`,
        newValue: `new${seed}`
      },
      metadata: {
        ip: '***********',
        userAgent: 'Test Agent',
        timestamp: new Date().toISOString()
      }
    }

    return await this.prisma.auditLog.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create session data
  static async createSession(userId: string, tenantId: string, overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      token: `session_token_${seed}_${Date.now()}`,
      userId,
      tenantId,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      metadata: {
        ip: '***********',
        userAgent: 'Test Browser',
        location: 'Test Location'
      }
    }

    return await this.prisma.session.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create invitation data
  static async createInvitation(tenantId: string, overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      email: `invite${seed}@tenant.com`,
      role: 'User',
      token: `invite_token_${seed}_${Date.now()}`,
      status: 'PENDING',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      tenantId,
      metadata: {
        invitedBy: 'Test Admin',
        message: `Welcome to our platform! Invitation ${seed}`
      }
    }

    return await this.prisma.tenantInvitation.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create API key data
  static async createApiKey(tenantId: string, overrides: any = {}): Promise<any> {
    const seed = this.getNextSeed()
    
    const defaultData = {
      name: `Test API Key ${seed}`,
      key: `api_key_${seed}_${Date.now()}`,
      permissions: ['read', 'write'],
      tenantId,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      metadata: {
        description: `API key ${seed} for testing`,
        environment: 'test',
        rateLimits: {
          requestsPerMinute: 100,
          requestsPerHour: 1000
        }
      }
    }

    return await this.prisma.tenantApiKey.create({
      data: { ...defaultData, ...overrides }
    })
  }

  // Create complete test dataset for a tenant
  static async createCompleteDataset(options: TestDataOptions): Promise<TestDataSet> {
    const { tenantId, count = 10 } = options

    // Create users first (needed for other entities)
    const users = await Promise.all(
      Array.from({ length: count }, () => this.createUser(tenantId))
    )

    // Create roles
    const roles = await Promise.all(
      Array.from({ length: 3 }, (_, i) => 
        this.createRole(tenantId, {
          name: ['Admin', 'Manager', 'User'][i],
          permissions: [
            ['*'],
            ['deals:*', 'users:read', 'documents:*'],
            ['deals:read', 'documents:read']
          ][i]
        })
      )
    )

    // Assign roles to users
    for (let i = 0; i < users.length; i++) {
      const roleIndex = i % roles.length
      await this.prisma.userRole.create({
        data: {
          userId: users[i].id,
          roleId: roles[roleIndex].id
        }
      })
    }

    // Create deals
    const deals = await Promise.all(
      Array.from({ length: count }, () => this.createDeal(tenantId))
    )

    // Create documents (some linked to deals)
    const documents = await Promise.all(
      Array.from({ length: count * 2 }, (_, i) => 
        this.createDocument(
          tenantId,
          i < deals.length ? deals[i].id : undefined
        )
      )
    )

    // Create valuations for deals
    const valuations = await Promise.all(
      deals.map(deal => this.createValuation(deal.id, tenantId))
    )

    // Create due diligence items for deals
    const dueDiligenceItems = await Promise.all(
      deals.flatMap(deal => 
        Array.from({ length: 3 }, () => 
          this.createDueDiligenceItem(deal.id, tenantId)
        )
      )
    )

    // Create audit logs
    const auditLogs = await Promise.all(
      Array.from({ length: count * 5 }, (_, i) => 
        this.createAuditLog(users[i % users.length].id, tenantId)
      )
    )

    // Create sessions for some users
    const sessions = await Promise.all(
      users.slice(0, Math.ceil(users.length / 2)).map(user => 
        this.createSession(user.id, tenantId)
      )
    )

    // Create invitations
    const invitations = await Promise.all(
      Array.from({ length: 5 }, () => this.createInvitation(tenantId))
    )

    // Create API keys
    const apiKeys = await Promise.all(
      Array.from({ length: 3 }, () => this.createApiKey(tenantId))
    )

    return {
      tenants: [], // Tenant should be created separately
      users,
      roles,
      deals,
      documents,
      valuations,
      dueDiligenceItems,
      auditLogs,
      sessions,
      invitations,
      apiKeys
    }
  }

  // Create minimal dataset for basic testing
  static async createMinimalDataset(tenantId: string): Promise<TestDataSet> {
    const admin = await this.createUser(tenantId, {
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User'
    })

    const adminRole = await this.createRole(tenantId, {
      name: 'Admin',
      permissions: ['*']
    })

    await this.prisma.userRole.create({
      data: {
        userId: admin.id,
        roleId: adminRole.id
      }
    })

    const deal = await this.createDeal(tenantId)
    const document = await this.createDocument(tenantId, deal.id)

    return {
      tenants: [],
      users: [admin],
      roles: [adminRole],
      deals: [deal],
      documents: [document],
      valuations: [],
      dueDiligenceItems: [],
      auditLogs: [],
      sessions: [],
      invitations: [],
      apiKeys: []
    }
  }

  // Clean up all test data for a tenant
  static async cleanupTenantData(tenantId: string): Promise<void> {
    // Delete in correct order to respect foreign key constraints
    await this.prisma.migrationRecord.deleteMany({ where: { tenantId } })
    await this.prisma.auditLog.deleteMany({ where: { tenantId } })
    await this.prisma.session.deleteMany({ where: { tenantId } })
    await this.prisma.userRole.deleteMany({
      where: { user: { tenantId } }
    })
    await this.prisma.document.deleteMany({ where: { tenantId } })
    await this.prisma.dueDiligenceItem.deleteMany({ where: { tenantId } })
    await this.prisma.valuation.deleteMany({ where: { tenantId } })
    await this.prisma.deal.deleteMany({ where: { tenantId } })
    await this.prisma.tenantApiKey.deleteMany({ where: { tenantId } })
    await this.prisma.tenantInvitation.deleteMany({ where: { tenantId } })
    await this.prisma.user.deleteMany({ where: { tenantId } })
    await this.prisma.role.deleteMany({ where: { tenantId } })
    await this.prisma.tenant.delete({ where: { id: tenantId } })
  }

  // Reset seed counter (useful for consistent test data)
  static resetSeed(): void {
    this.seedCounter = 0
  }
}
