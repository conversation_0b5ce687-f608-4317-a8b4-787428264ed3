import { PrismaClient } from '@prisma/client'
import { TestDataFactory, TestDataSet } from './test-data-factory'
import { TestFixtures, TestScenario } from './test-fixtures'

describe('Test Data Management Integration Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = global.__PRISMA__
    TestDataFactory.initialize(prisma)
    TestFixtures.initialize(prisma)
  })

  beforeEach(async () => {
    // Clean database before each test
    await prisma.migrationRecord.deleteMany()
    await prisma.auditLog.deleteMany()
    await prisma.session.deleteMany()
    await prisma.userRole.deleteMany()
    await prisma.document.deleteMany()
    await prisma.dueDiligenceItem.deleteMany()
    await prisma.valuation.deleteMany()
    await prisma.deal.deleteMany()
    await prisma.tenantApiKey.deleteMany()
    await prisma.tenantInvitation.deleteMany()
    await prisma.user.deleteMany()
    await prisma.role.deleteMany()
    await prisma.tenant.deleteMany()

    // Reset seed for consistent test data
    TestDataFactory.resetSeed()
  })

  afterEach(async () => {
    await TestFixtures.cleanupAll()
  })

  describe('Test Data Factory', () => {
    it('should create consistent tenant data', async () => {
      const tenant1 = await TestDataFactory.createTenant()
      const tenant2 = await TestDataFactory.createTenant()

      expect(tenant1.name).toContain('Test Tenant')
      expect(tenant2.name).toContain('Test Tenant')
      expect(tenant1.subdomain).toMatch(/^test\d+$/)
      expect(tenant2.subdomain).toMatch(/^test\d+$/)
      expect(tenant1.subdomain).not.toBe(tenant2.subdomain)

      // Verify default settings
      expect(tenant1.status).toBe('ACTIVE')
      expect(tenant1.plan).toBe('PROFESSIONAL')
      expect(tenant1.settings).toHaveProperty('timezone', 'UTC')
      expect(tenant1.features).toHaveProperty('apiAccess', true)
      expect(tenant1.limits).toHaveProperty('maxUsers', 50)

      await TestDataFactory.cleanupTenantData(tenant1.id)
      await TestDataFactory.cleanupTenantData(tenant2.id)
    })

    it('should create users with proper relationships', async () => {
      const tenant = await TestDataFactory.createTenant()
      const user1 = await TestDataFactory.createUser(tenant.id)
      const user2 = await TestDataFactory.createUser(tenant.id)

      expect(user1.tenantId).toBe(tenant.id)
      expect(user2.tenantId).toBe(tenant.id)
      expect(user1.email).not.toBe(user2.email)
      expect(user1.status).toBe('ACTIVE')
      expect(user1.emailVerified).toBe(true)

      // Verify users exist in database
      const dbUsers = await prisma.user.findMany({
        where: { tenantId: tenant.id }
      })
      expect(dbUsers).toHaveLength(2)

      await TestDataFactory.cleanupTenantData(tenant.id)
    })

    it('should create complete dataset with all relationships', async () => {
      const tenant = await TestDataFactory.createTenant()
      const dataset = await TestDataFactory.createCompleteDataset({
        tenantId: tenant.id,
        count: 5
      })

      // Verify all data was created
      expect(dataset.users).toHaveLength(5)
      expect(dataset.roles).toHaveLength(3) // Admin, Manager, User
      expect(dataset.deals).toHaveLength(5)
      expect(dataset.documents).toHaveLength(10) // 2 per deal
      expect(dataset.valuations).toHaveLength(5) // 1 per deal
      expect(dataset.dueDiligenceItems).toHaveLength(15) // 3 per deal
      expect(dataset.auditLogs).toHaveLength(25) // 5 per user
      expect(dataset.sessions.length).toBeGreaterThan(0)
      expect(dataset.invitations).toHaveLength(5)
      expect(dataset.apiKeys).toHaveLength(3)

      // Verify relationships
      const userWithRoles = await prisma.user.findFirst({
        where: { tenantId: tenant.id },
        include: { roles: { include: { role: true } } }
      })
      expect(userWithRoles?.roles.length).toBeGreaterThan(0)

      const dealWithDocuments = await prisma.deal.findFirst({
        where: { tenantId: tenant.id },
        include: { documents: true, valuations: true }
      })
      expect(dealWithDocuments?.documents.length).toBeGreaterThan(0)
      expect(dealWithDocuments?.valuations.length).toBeGreaterThan(0)

      await TestDataFactory.cleanupTenantData(tenant.id)
    })

    it('should create minimal dataset efficiently', async () => {
      const tenant = await TestDataFactory.createTenant()
      
      const startTime = Date.now()
      const dataset = await TestDataFactory.createMinimalDataset(tenant.id)
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(2000) // Should be fast
      expect(dataset.users).toHaveLength(1)
      expect(dataset.roles).toHaveLength(1)
      expect(dataset.deals).toHaveLength(1)
      expect(dataset.documents).toHaveLength(1)

      await TestDataFactory.cleanupTenantData(tenant.id)
    })

    it('should handle data overrides correctly', async () => {
      const tenant = await TestDataFactory.createTenant({
        name: 'Custom Tenant Name',
        plan: 'ENTERPRISE',
        settings: { timezone: 'America/New_York' }
      })

      expect(tenant.name).toBe('Custom Tenant Name')
      expect(tenant.plan).toBe('ENTERPRISE')
      expect(tenant.settings.timezone).toBe('America/New_York')

      const user = await TestDataFactory.createUser(tenant.id, {
        email: '<EMAIL>',
        firstName: 'Custom',
        status: 'INACTIVE'
      })

      expect(user.email).toBe('<EMAIL>')
      expect(user.firstName).toBe('Custom')
      expect(user.status).toBe('INACTIVE')

      await TestDataFactory.cleanupTenantData(tenant.id)
    })
  })

  describe('Test Fixtures and Scenarios', () => {
    it('should setup and cleanup basic tenant scenario', async () => {
      const scenario = TestFixtures.createBasicTenantScenario()
      
      const data = await scenario.setup()
      
      expect(data.tenant).toBeDefined()
      expect(data.users).toHaveLength(1)
      expect(data.roles).toHaveLength(1)
      expect(data.deals).toHaveLength(1)

      // Verify data exists in database
      const tenantCount = await prisma.tenant.count()
      expect(tenantCount).toBe(1)

      await scenario.cleanup()

      // Verify cleanup
      const tenantCountAfter = await prisma.tenant.count()
      expect(tenantCountAfter).toBe(0)
    })

    it('should setup multi-tenant scenario with isolation', async () => {
      const scenario = TestFixtures.createMultiTenantScenario()
      
      const data = await scenario.setup()
      
      expect(data.tenants).toHaveLength(3)
      expect(data.datasets).toHaveLength(3)

      // Verify tenant isolation
      const tenant1Data = await prisma.user.findMany({
        where: { tenantId: data.tenants[0].id }
      })
      const tenant2Data = await prisma.user.findMany({
        where: { tenantId: data.tenants[1].id }
      })

      expect(tenant1Data.length).toBeGreaterThan(0)
      expect(tenant2Data.length).toBeGreaterThan(0)
      expect(tenant1Data.every(u => u.tenantId === data.tenants[0].id)).toBe(true)
      expect(tenant2Data.every(u => u.tenantId === data.tenants[1].id)).toBe(true)

      await scenario.cleanup()
    })

    it('should setup authentication test scenario with proper roles', async () => {
      const scenario = TestFixtures.createAuthTestScenario()
      
      const data = await scenario.setup()
      
      expect(data.tenant).toBeDefined()
      expect(data.roles).toHaveLength(4) // Admin, Manager, User, Viewer
      expect(data.users).toHaveProperty('admin')
      expect(data.users).toHaveProperty('manager')
      expect(data.users).toHaveProperty('user')
      expect(data.users).toHaveProperty('viewer')
      expect(data.users).toHaveProperty('inactive')

      // Verify role assignments
      const adminWithRoles = await prisma.user.findUnique({
        where: { id: data.users.admin.id },
        include: { roles: { include: { role: true } } }
      })

      expect(adminWithRoles?.roles).toHaveLength(1)
      expect(adminWithRoles?.roles[0].role.name).toBe('Admin')
      expect(adminWithRoles?.roles[0].role.permissions).toContain('*')

      await scenario.cleanup()
    })

    it('should setup API test scenario with comprehensive data', async () => {
      const scenario = TestFixtures.createApiTestScenario()
      
      const data = await scenario.setup()
      
      expect(data.tenant).toBeDefined()
      expect(data.users.admin).toBeDefined()
      expect(data.users.user).toBeDefined()
      expect(data.deals).toHaveLength(4)
      expect(data.documents).toHaveLength(4)
      expect(data.valuations).toHaveLength(2)
      expect(data.dueDiligenceItems).toHaveLength(6)
      expect(data.apiKeys).toHaveLength(2)

      // Verify deal statuses
      const dealStatuses = data.deals.map(d => d.status)
      expect(dealStatuses).toContain('ACTIVE')
      expect(dealStatuses).toContain('PENDING')
      expect(dealStatuses).toContain('COMPLETED')
      expect(dealStatuses).toContain('CANCELLED')

      await scenario.cleanup()
    })

    it('should handle edge case scenario', async () => {
      const scenario = TestFixtures.createEdgeCaseScenario()
      
      const data = await scenario.setup()
      
      expect(data.tenant).toBeDefined()
      expect(data.edgeUser.email).toContain('+')
      expect(data.edgeUser.lastName).toContain('\'')
      expect(data.edgeDeal.dealValue).toBe(0)
      expect(data.largeDocument.size).toBe(1024 * 1024 * 1024)
      expect(data.expiredInvitation.status).toBe('EXPIRED')

      await scenario.cleanup()
    })

    it('should setup scenario by name', async () => {
      const data = await TestFixtures.setupScenario('Basic Tenant')
      
      expect(data.tenant).toBeDefined()
      expect(data.tenant.name).toBe('Basic Test Tenant')

      await TestFixtures.cleanupScenario('Basic Tenant')
    })

    it('should handle invalid scenario name', async () => {
      await expect(
        TestFixtures.setupScenario('Non-existent Scenario')
      ).rejects.toThrow('Scenario not found')
    })
  })

  describe('Performance and Scalability', () => {
    it('should create large datasets efficiently', async () => {
      const tenant = await TestDataFactory.createTenant()
      
      const startTime = Date.now()
      const dataset = await TestDataFactory.createCompleteDataset({
        tenantId: tenant.id,
        count: 50
      })
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(30000) // Should complete within 30 seconds
      expect(dataset.users).toHaveLength(50)
      expect(dataset.deals).toHaveLength(50)

      await TestDataFactory.cleanupTenantData(tenant.id)
    })

    it('should handle concurrent data creation', async () => {
      const tenant = await TestDataFactory.createTenant()
      
      const concurrentOperations = Array.from({ length: 10 }, () =>
        TestDataFactory.createUser(tenant.id)
      )

      const startTime = Date.now()
      const users = await Promise.all(concurrentOperations)
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds
      expect(users).toHaveLength(10)
      expect(new Set(users.map(u => u.email)).size).toBe(10) // All unique emails

      await TestDataFactory.cleanupTenantData(tenant.id)
    })

    it('should cleanup large datasets efficiently', async () => {
      const tenant = await TestDataFactory.createTenant()
      await TestDataFactory.createCompleteDataset({
        tenantId: tenant.id,
        count: 100
      })

      const startTime = Date.now()
      await TestDataFactory.cleanupTenantData(tenant.id)
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(10000) // Should complete within 10 seconds

      // Verify cleanup
      const remainingData = await prisma.tenant.findUnique({
        where: { id: tenant.id }
      })
      expect(remainingData).toBeNull()
    })
  })

  describe('Data Consistency and Integrity', () => {
    it('should maintain referential integrity', async () => {
      const tenant = await TestDataFactory.createTenant()
      const dataset = await TestDataFactory.createCompleteDataset({
        tenantId: tenant.id,
        count: 10
      })

      // Verify all foreign keys are valid
      const users = await prisma.user.findMany({
        where: { tenantId: tenant.id },
        include: { roles: { include: { role: true } } }
      })

      users.forEach(user => {
        expect(user.tenantId).toBe(tenant.id)
        user.roles.forEach(userRole => {
          expect(userRole.role.tenantId).toBe(tenant.id)
        })
      })

      const deals = await prisma.deal.findMany({
        where: { tenantId: tenant.id },
        include: { documents: true, valuations: true }
      })

      deals.forEach(deal => {
        expect(deal.tenantId).toBe(tenant.id)
        deal.documents.forEach(doc => {
          expect(doc.tenantId).toBe(tenant.id)
          expect(doc.dealId).toBe(deal.id)
        })
        deal.valuations.forEach(val => {
          expect(val.tenantId).toBe(tenant.id)
          expect(val.dealId).toBe(deal.id)
        })
      })

      await TestDataFactory.cleanupTenantData(tenant.id)
    })

    it('should generate unique identifiers', async () => {
      const tenant = await TestDataFactory.createTenant()
      
      const users = await Promise.all(
        Array.from({ length: 20 }, () => TestDataFactory.createUser(tenant.id))
      )

      const emails = users.map(u => u.email)
      const uniqueEmails = new Set(emails)
      expect(uniqueEmails.size).toBe(emails.length)

      const deals = await Promise.all(
        Array.from({ length: 20 }, () => TestDataFactory.createDeal(tenant.id))
      )

      const titles = deals.map(d => d.title)
      const uniqueTitles = new Set(titles)
      expect(uniqueTitles.size).toBe(titles.length)

      await TestDataFactory.cleanupTenantData(tenant.id)
    })

    it('should handle seed reset correctly', async () => {
      TestDataFactory.resetSeed()
      
      const tenant1 = await TestDataFactory.createTenant()
      const user1 = await TestDataFactory.createUser(tenant1.id)

      TestDataFactory.resetSeed()
      
      const tenant2 = await TestDataFactory.createTenant()
      const user2 = await TestDataFactory.createUser(tenant2.id)

      // After reset, should generate similar patterns
      expect(tenant1.name).toBe(tenant2.name)
      expect(user1.firstName).toBe(user2.firstName)

      await TestDataFactory.cleanupTenantData(tenant1.id)
      await TestDataFactory.cleanupTenantData(tenant2.id)
    })
  })
})
