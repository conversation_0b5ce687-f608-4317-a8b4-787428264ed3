import { PrismaClient } from '@prisma/client'
import { TestDataFactory } from './test-data-factory'

export interface TestScenario {
  name: string
  description: string
  setup: () => Promise<any>
  cleanup: () => Promise<void>
}

export class TestFixtures {
  private static prisma: PrismaClient
  private static createdTenants: string[] = []

  static initialize(prisma: PrismaClient) {
    this.prisma = prisma
    TestDataFactory.initialize(prisma)
  }

  // Track created tenants for cleanup
  private static trackTenant(tenantId: string): void {
    if (!this.createdTenants.includes(tenantId)) {
      this.createdTenants.push(tenantId)
    }
  }

  // Clean up all tracked tenants
  static async cleanupAll(): Promise<void> {
    for (const tenantId of this.createdTenants) {
      try {
        await TestDataFactory.cleanupTenantData(tenantId)
      } catch (error) {
        console.warn(`Failed to cleanup tenant ${tenantId}:`, error)
      }
    }
    this.createdTenants = []
  }

  // Scenario: Single tenant with basic data
  static createBasicTenantScenario(): TestScenario {
    let tenantId: string

    return {
      name: 'Basic Tenant',
      description: 'Single tenant with minimal test data',
      setup: async () => {
        const tenant = await TestDataFactory.createTenant({
          name: 'Basic Test Tenant',
          subdomain: 'basic',
          slug: 'basic-test'
        })
        tenantId = tenant.id
        this.trackTenant(tenantId)

        const dataset = await TestDataFactory.createMinimalDataset(tenantId)
        return { tenant, ...dataset }
      },
      cleanup: async () => {
        if (tenantId) {
          await TestDataFactory.cleanupTenantData(tenantId)
        }
      }
    }
  }

  // Scenario: Multi-tenant with isolated data
  static createMultiTenantScenario(): TestScenario {
    let tenantIds: string[] = []

    return {
      name: 'Multi-Tenant Isolation',
      description: 'Multiple tenants with isolated data for testing tenant isolation',
      setup: async () => {
        const tenants = await Promise.all([
          TestDataFactory.createTenant({
            name: 'Acme Corporation',
            subdomain: 'acme',
            slug: 'acme-corp',
            plan: 'ENTERPRISE'
          }),
          TestDataFactory.createTenant({
            name: 'Demo Company',
            subdomain: 'demo',
            slug: 'demo-company',
            plan: 'PROFESSIONAL'
          }),
          TestDataFactory.createTenant({
            name: 'Startup Inc',
            subdomain: 'startup',
            slug: 'startup-inc',
            plan: 'STARTER'
          })
        ])

        tenantIds = tenants.map(t => t.id)
        tenantIds.forEach(id => this.trackTenant(id))

        // Create different amounts of data for each tenant
        const datasets = await Promise.all([
          TestDataFactory.createCompleteDataset({ tenantId: tenants[0].id, count: 20 }),
          TestDataFactory.createCompleteDataset({ tenantId: tenants[1].id, count: 10 }),
          TestDataFactory.createMinimalDataset(tenants[2].id)
        ])

        return {
          tenants,
          datasets: datasets.map((dataset, i) => ({
            tenant: tenants[i],
            ...dataset
          }))
        }
      },
      cleanup: async () => {
        for (const tenantId of tenantIds) {
          await TestDataFactory.cleanupTenantData(tenantId)
        }
      }
    }
  }

  // Scenario: Performance testing with large dataset
  static createPerformanceTestScenario(): TestScenario {
    let tenantId: string

    return {
      name: 'Performance Test',
      description: 'Large dataset for performance testing',
      setup: async () => {
        const tenant = await TestDataFactory.createTenant({
          name: 'Performance Test Tenant',
          subdomain: 'performance',
          slug: 'performance-test',
          plan: 'ENTERPRISE'
        })
        tenantId = tenant.id
        this.trackTenant(tenantId)

        const dataset = await TestDataFactory.createCompleteDataset({
          tenantId,
          count: 100 // Large dataset
        })

        return { tenant, ...dataset }
      },
      cleanup: async () => {
        if (tenantId) {
          await TestDataFactory.cleanupTenantData(tenantId)
        }
      }
    }
  }

  // Scenario: Authentication and authorization testing
  static createAuthTestScenario(): TestScenario {
    let tenantId: string

    return {
      name: 'Authentication Test',
      description: 'Tenant with various user roles and permissions for auth testing',
      setup: async () => {
        const tenant = await TestDataFactory.createTenant({
          name: 'Auth Test Tenant',
          subdomain: 'authtest',
          slug: 'auth-test'
        })
        tenantId = tenant.id
        this.trackTenant(tenantId)

        // Create specific roles
        const adminRole = await TestDataFactory.createRole(tenantId, {
          name: 'Admin',
          description: 'Full system access',
          permissions: ['*'],
          isSystemRole: true
        })

        const managerRole = await TestDataFactory.createRole(tenantId, {
          name: 'Manager',
          description: 'Management access',
          permissions: ['deals:*', 'users:read', 'documents:*', 'reports:read'],
          isSystemRole: true
        })

        const userRole = await TestDataFactory.createRole(tenantId, {
          name: 'User',
          description: 'Basic user access',
          permissions: ['deals:read', 'deals:create', 'documents:read'],
          isSystemRole: true,
          isDefault: true
        })

        const viewerRole = await TestDataFactory.createRole(tenantId, {
          name: 'Viewer',
          description: 'Read-only access',
          permissions: ['deals:read', 'documents:read'],
          isSystemRole: true
        })

        // Create users with different roles
        const adminUser = await TestDataFactory.createUser(tenantId, {
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'User',
          status: 'ACTIVE'
        })

        const managerUser = await TestDataFactory.createUser(tenantId, {
          email: '<EMAIL>',
          firstName: 'Manager',
          lastName: 'User',
          status: 'ACTIVE'
        })

        const regularUser = await TestDataFactory.createUser(tenantId, {
          email: '<EMAIL>',
          firstName: 'Regular',
          lastName: 'User',
          status: 'ACTIVE'
        })

        const viewerUser = await TestDataFactory.createUser(tenantId, {
          email: '<EMAIL>',
          firstName: 'Viewer',
          lastName: 'User',
          status: 'ACTIVE'
        })

        const inactiveUser = await TestDataFactory.createUser(tenantId, {
          email: '<EMAIL>',
          firstName: 'Inactive',
          lastName: 'User',
          status: 'INACTIVE'
        })

        // Assign roles to users
        await Promise.all([
          this.prisma.userRole.create({
            data: { userId: adminUser.id, roleId: adminRole.id }
          }),
          this.prisma.userRole.create({
            data: { userId: managerUser.id, roleId: managerRole.id }
          }),
          this.prisma.userRole.create({
            data: { userId: regularUser.id, roleId: userRole.id }
          }),
          this.prisma.userRole.create({
            data: { userId: viewerUser.id, roleId: viewerRole.id }
          }),
          this.prisma.userRole.create({
            data: { userId: inactiveUser.id, roleId: userRole.id }
          })
        ])

        // Create some test data
        const deals = await Promise.all([
          TestDataFactory.createDeal(tenantId, { title: 'Public Deal', status: 'ACTIVE' }),
          TestDataFactory.createDeal(tenantId, { title: 'Confidential Deal', status: 'CONFIDENTIAL' }),
          TestDataFactory.createDeal(tenantId, { title: 'Completed Deal', status: 'COMPLETED' })
        ])

        const documents = await Promise.all(
          deals.map(deal => 
            TestDataFactory.createDocument(tenantId, deal.id, {
              name: `${deal.title} Document`
            })
          )
        )

        return {
          tenant,
          roles: [adminRole, managerRole, userRole, viewerRole],
          users: {
            admin: adminUser,
            manager: managerUser,
            user: regularUser,
            viewer: viewerUser,
            inactive: inactiveUser
          },
          deals,
          documents
        }
      },
      cleanup: async () => {
        if (tenantId) {
          await TestDataFactory.cleanupTenantData(tenantId)
        }
      }
    }
  }

  // Scenario: API testing with various data states
  static createApiTestScenario(): TestScenario {
    let tenantId: string

    return {
      name: 'API Test',
      description: 'Comprehensive data for API endpoint testing',
      setup: async () => {
        const tenant = await TestDataFactory.createTenant({
          name: 'API Test Tenant',
          subdomain: 'apitest',
          slug: 'api-test'
        })
        tenantId = tenant.id
        this.trackTenant(tenantId)

        // Create users
        const adminUser = await TestDataFactory.createUser(tenantId, {
          email: '<EMAIL>',
          firstName: 'API',
          lastName: 'Admin'
        })

        const regularUser = await TestDataFactory.createUser(tenantId, {
          email: '<EMAIL>',
          firstName: 'API',
          lastName: 'User'
        })

        // Create deals in various states
        const deals = await Promise.all([
          TestDataFactory.createDeal(tenantId, {
            title: 'Active Deal',
            status: 'ACTIVE',
            type: 'ACQUISITION'
          }),
          TestDataFactory.createDeal(tenantId, {
            title: 'Pending Deal',
            status: 'PENDING',
            type: 'MERGER'
          }),
          TestDataFactory.createDeal(tenantId, {
            title: 'Completed Deal',
            status: 'COMPLETED',
            type: 'ACQUISITION'
          }),
          TestDataFactory.createDeal(tenantId, {
            title: 'Cancelled Deal',
            status: 'CANCELLED',
            type: 'INVESTMENT'
          })
        ])

        // Create documents with different types
        const documents = await Promise.all([
          TestDataFactory.createDocument(tenantId, deals[0].id, {
            name: 'Contract Document',
            type: 'CONTRACT'
          }),
          TestDataFactory.createDocument(tenantId, deals[0].id, {
            name: 'Financial Document',
            type: 'FINANCIAL'
          }),
          TestDataFactory.createDocument(tenantId, deals[1].id, {
            name: 'Legal Document',
            type: 'LEGAL'
          }),
          TestDataFactory.createDocument(tenantId, undefined, {
            name: 'Standalone Document',
            type: 'OTHER'
          })
        ])

        // Create valuations
        const valuations = await Promise.all(
          deals.slice(0, 2).map(deal =>
            TestDataFactory.createValuation(deal.id, tenantId, {
              method: 'DCF',
              value: 10000000
            })
          )
        )

        // Create due diligence items
        const dueDiligenceItems = await Promise.all(
          deals.slice(0, 3).flatMap(deal => [
            TestDataFactory.createDueDiligenceItem(deal.id, tenantId, {
              category: 'FINANCIAL',
              status: 'COMPLETED',
              priority: 'HIGH'
            }),
            TestDataFactory.createDueDiligenceItem(deal.id, tenantId, {
              category: 'LEGAL',
              status: 'PENDING',
              priority: 'MEDIUM'
            })
          ])
        )

        // Create API keys
        const apiKeys = await Promise.all([
          TestDataFactory.createApiKey(tenantId, {
            name: 'Read-only API Key',
            permissions: ['read']
          }),
          TestDataFactory.createApiKey(tenantId, {
            name: 'Full Access API Key',
            permissions: ['read', 'write', 'delete']
          })
        ])

        return {
          tenant,
          users: { admin: adminUser, user: regularUser },
          deals,
          documents,
          valuations,
          dueDiligenceItems,
          apiKeys
        }
      },
      cleanup: async () => {
        if (tenantId) {
          await TestDataFactory.cleanupTenantData(tenantId)
        }
      }
    }
  }

  // Scenario: Edge cases and error conditions
  static createEdgeCaseScenario(): TestScenario {
    let tenantId: string

    return {
      name: 'Edge Cases',
      description: 'Data with edge cases and potential error conditions',
      setup: async () => {
        const tenant = await TestDataFactory.createTenant({
          name: 'Edge Case Tenant',
          subdomain: 'edgecase',
          slug: 'edge-case'
        })
        tenantId = tenant.id
        this.trackTenant(tenantId)

        // Create user with edge case data
        const edgeUser = await TestDataFactory.createUser(tenantId, {
          email: '<EMAIL>', // Email with plus sign
          firstName: 'Very Long First Name That Exceeds Normal Length',
          lastName: 'O\'Connor-Smith', // Name with special characters
          status: 'ACTIVE'
        })

        // Create deal with edge case values
        const edgeDeal = await TestDataFactory.createDeal(tenantId, {
          title: 'Deal with "Special" Characters & Symbols',
          dealValue: 0, // Zero value deal
          currency: 'EUR',
          expectedCloseDate: new Date('2030-12-31') // Far future date
        })

        // Create very large document
        const largeDocument = await TestDataFactory.createDocument(tenantId, edgeDeal.id, {
          name: 'Very Large Document.pdf',
          size: 1024 * 1024 * 1024, // 1GB
          type: 'OTHER'
        })

        // Create expired invitation
        const expiredInvitation = await TestDataFactory.createInvitation(tenantId, {
          email: '<EMAIL>',
          status: 'EXPIRED',
          expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // Expired yesterday
        })

        return {
          tenant,
          edgeUser,
          edgeDeal,
          largeDocument,
          expiredInvitation
        }
      },
      cleanup: async () => {
        if (tenantId) {
          await TestDataFactory.cleanupTenantData(tenantId)
        }
      }
    }
  }

  // Get all available scenarios
  static getAllScenarios(): TestScenario[] {
    return [
      this.createBasicTenantScenario(),
      this.createMultiTenantScenario(),
      this.createPerformanceTestScenario(),
      this.createAuthTestScenario(),
      this.createApiTestScenario(),
      this.createEdgeCaseScenario()
    ]
  }

  // Setup specific scenario by name
  static async setupScenario(scenarioName: string): Promise<any> {
    const scenario = this.getAllScenarios().find(s => s.name === scenarioName)
    if (!scenario) {
      throw new Error(`Scenario not found: ${scenarioName}`)
    }
    return await scenario.setup()
  }

  // Cleanup specific scenario by name
  static async cleanupScenario(scenarioName: string): Promise<void> {
    const scenario = this.getAllScenarios().find(s => s.name === scenarioName)
    if (!scenario) {
      throw new Error(`Scenario not found: ${scenarioName}`)
    }
    await scenario.cleanup()
  }
}
