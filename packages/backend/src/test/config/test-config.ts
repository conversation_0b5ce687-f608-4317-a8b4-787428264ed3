export interface TestConfig {
  database: {
    host: string
    port: number
    username: string
    password: string
    database: string
    url?: string
  }
  redis: {
    host: string
    port: number
    url?: string
  }
  api: {
    baseUrl: string
    timeout: number
  }
  auth: {
    jwtSecret: string
    encryptionKey: string
  }
  multiTenant: {
    defaultDomain: string
    testSubdomains: string[]
  }
  performance: {
    maxResponseTime: number
    maxConcurrentRequests: number
  }
  features: {
    enableRealDatabase: boolean
    enableRedis: boolean
    enableFileUploads: boolean
    enableEmailTesting: boolean
  }
}

export const testConfigs: Record<string, TestConfig> = {
  unit: {
    database: {
      host: 'localhost',
      port: 5433,
      username: 'test',
      password: 'test',
      database: 'ma_platform_test'
    },
    redis: {
      host: 'localhost',
      port: 6380
    },
    api: {
      baseUrl: 'http://localhost:3001',
      timeout: 5000
    },
    auth: {
      jwtSecret: 'test-jwt-secret-key-at-least-32-characters-long',
      encryptionKey: 'test-encryption-key-32-characters'
    },
    multiTenant: {
      defaultDomain: 'localhost',
      testSubdomains: ['test1', 'test2', 'acme', 'demo']
    },
    performance: {
      maxResponseTime: 1000,
      maxConcurrentRequests: 10
    },
    features: {
      enableRealDatabase: false,
      enableRedis: false,
      enableFileUploads: false,
      enableEmailTesting: false
    }
  },

  integration: {
    database: {
      host: 'localhost',
      port: 5433,
      username: 'test',
      password: 'test',
      database: 'ma_platform_integration_test'
    },
    redis: {
      host: 'localhost',
      port: 6380
    },
    api: {
      baseUrl: 'http://localhost:3001',
      timeout: 10000
    },
    auth: {
      jwtSecret: 'test-jwt-secret-key-at-least-32-characters-long',
      encryptionKey: 'test-encryption-key-32-characters'
    },
    multiTenant: {
      defaultDomain: 'localhost',
      testSubdomains: ['acme', 'demo', 'enterprise', 'startup']
    },
    performance: {
      maxResponseTime: 2000,
      maxConcurrentRequests: 50
    },
    features: {
      enableRealDatabase: true,
      enableRedis: true,
      enableFileUploads: true,
      enableEmailTesting: true
    }
  },

  e2e: {
    database: {
      host: 'localhost',
      port: 5433,
      username: 'test',
      password: 'test',
      database: 'ma_platform_e2e_test'
    },
    redis: {
      host: 'localhost',
      port: 6380
    },
    api: {
      baseUrl: 'http://localhost:3001',
      timeout: 30000
    },
    auth: {
      jwtSecret: 'test-jwt-secret-key-at-least-32-characters-long',
      encryptionKey: 'test-encryption-key-32-characters'
    },
    multiTenant: {
      defaultDomain: 'localhost',
      testSubdomains: ['acme', 'demo', 'enterprise', 'startup', 'test-company']
    },
    performance: {
      maxResponseTime: 5000,
      maxConcurrentRequests: 100
    },
    features: {
      enableRealDatabase: true,
      enableRedis: true,
      enableFileUploads: true,
      enableEmailTesting: true
    }
  }
}

export function getTestConfig(testType: string = 'unit'): TestConfig {
  const config = testConfigs[testType]
  if (!config) {
    throw new Error(`Unknown test type: ${testType}`)
  }

  // Build database URL if not provided
  if (!config.database.url) {
    config.database.url = `postgresql://${config.database.username}:${config.database.password}@${config.database.host}:${config.database.port}/${config.database.database}`
  }

  // Build Redis URL if not provided
  if (!config.redis.url) {
    config.redis.url = `redis://${config.redis.host}:${config.redis.port}`
  }

  return config
}

export function getCurrentTestType(): string {
  // Determine test type from Jest project name or environment
  const jestProjectName = process.env.JEST_PROJECT_NAME
  if (jestProjectName) {
    return jestProjectName
  }

  // Fallback to checking test file patterns
  const testFile = expect.getState().testPath
  if (testFile) {
    if (testFile.includes('.unit.test.')) return 'unit'
    if (testFile.includes('.integration.test.')) return 'integration'
    if (testFile.includes('.e2e.test.')) return 'e2e'
  }

  return 'unit'
}

export function setupTestEnvironment(testType?: string): TestConfig {
  const type = testType || getCurrentTestType()
  const config = getTestConfig(type)

  // Set environment variables
  process.env.NODE_ENV = 'test'
  process.env.DATABASE_URL = config.database.url
  process.env.REDIS_URL = config.redis.url
  process.env.JWT_SECRET = config.auth.jwtSecret
  process.env.ENCRYPTION_KEY = config.auth.encryptionKey
  process.env.CORS_ORIGIN = config.api.baseUrl
  process.env.MAIN_DOMAIN = config.multiTenant.defaultDomain
  process.env.API_DOMAIN = `api.${config.multiTenant.defaultDomain}`

  return config
}

// Test data generators
export const testData = {
  tenants: {
    acme: {
      name: 'Acme Corporation',
      slug: 'acme',
      subdomain: 'acme',
      plan: 'PROFESSIONAL',
      status: 'ACTIVE'
    },
    demo: {
      name: 'Demo Company',
      slug: 'demo',
      subdomain: 'demo',
      plan: 'STARTER',
      status: 'TRIAL'
    },
    enterprise: {
      name: 'Enterprise Corp',
      slug: 'enterprise',
      subdomain: 'enterprise',
      plan: 'ENTERPRISE',
      status: 'ACTIVE'
    }
  },

  users: {
    admin: {
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      roles: ['Admin']
    },
    manager: {
      firstName: 'Manager',
      lastName: 'User',
      email: '<EMAIL>',
      roles: ['Manager']
    },
    user: {
      firstName: 'Regular',
      lastName: 'User',
      email: '<EMAIL>',
      roles: ['User']
    }
  },

  deals: {
    acquisition: {
      title: 'Acme Acquisition',
      type: 'ACQUISITION',
      status: 'ACTIVE',
      targetCompany: 'Target Corp',
      dealValue: 10000000,
      currency: 'USD'
    },
    merger: {
      title: 'Strategic Merger',
      type: 'MERGER',
      status: 'ACTIVE',
      targetCompany: 'Merge Corp',
      dealValue: 50000000,
      currency: 'USD'
    }
  }
}

// Test assertions helpers
export const testAssertions = {
  // Assert response time is within limits
  assertResponseTime: (startTime: number, maxTime: number) => {
    const duration = Date.now() - startTime
    expect(duration).toBeLessThan(maxTime)
  },

  // Assert tenant isolation
  assertTenantIsolation: (data: any[], tenantId: string) => {
    data.forEach(item => {
      expect(item.tenantId).toBe(tenantId)
    })
  },

  // Assert API response structure
  assertApiResponse: (response: any, expectedStatus: number = 200) => {
    expect(response.status).toBe(expectedStatus)
    expect(response.body).toHaveProperty('success')
    if (response.body.success) {
      expect(response.body).toHaveProperty('data')
    } else {
      expect(response.body).toHaveProperty('error')
    }
  },

  // Assert pagination structure
  assertPagination: (response: any) => {
    expect(response.body.data).toHaveProperty('items')
    expect(response.body.data).toHaveProperty('pagination')
    expect(response.body.data.pagination).toHaveProperty('page')
    expect(response.body.data.pagination).toHaveProperty('limit')
    expect(response.body.data.pagination).toHaveProperty('total')
    expect(response.body.data.pagination).toHaveProperty('totalPages')
  }
}
