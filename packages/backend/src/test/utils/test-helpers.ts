import { PrismaClient } from '@prisma/client'
import { Request, Response } from 'express'
import jwt from 'jsonwebtoken'
import { hashPassword } from '@/shared/utils/encryption'
import { TenantProvisioningService } from '@/application/services/tenant-provisioning.service'
import { MigrationService } from '@/application/services/migration.service'

export interface TestTenant {
  id: string
  name: string
  slug: string
  subdomain: string
  status: string
  plan: string
  settings?: any
  features?: any
  limits?: any
  branding?: any
}

export interface TestUser {
  id: string
  email: string
  firstName: string
  lastName: string
  tenantId: string
  roles?: string[]
  token?: string
}

export interface TestContext {
  prisma: PrismaClient
  tenants: TestTenant[]
  users: TestUser[]
  cleanup: () => Promise<void>
}

export class TestHelpers {
  private static prisma: PrismaClient

  static initialize(prisma: PrismaClient) {
    this.prisma = prisma
  }

  // Create test tenant with all required setup
  static async createTestTenant(data: Partial<TestTenant> = {}): Promise<TestTenant> {
    const tenantData = {
      name: data.name || `Test Tenant ${Date.now()}`,
      slug: data.slug || `test-tenant-${Date.now()}`,
      subdomain: data.subdomain || `test${Date.now()}`,
      status: data.status || 'ACTIVE',
      plan: data.plan || 'STARTER',
      settings: data.settings || {},
      features: data.features || {
        apiAccess: false,
        customBranding: false,
        advancedReporting: false
      },
      limits: data.limits || {
        maxUsers: 5,
        maxDeals: 10,
        maxStorage: 1024 * 1024 * 1024
      },
      branding: data.branding || {}
    }

    const tenant = await this.prisma.tenant.create({
      data: tenantData
    })

    // Create default roles for the tenant
    await this.createDefaultRoles(tenant.id)

    return tenant as TestTenant
  }

  // Create test user with authentication token
  static async createTestUser(tenantId: string, data: Partial<TestUser> = {}): Promise<TestUser> {
    const userData = {
      email: data.email || `test${Date.now()}@example.com`,
      firstName: data.firstName || 'Test',
      lastName: data.lastName || 'User',
      password: await hashPassword('password123'),
      tenantId,
      emailVerified: true,
      status: 'ACTIVE'
    }

    const user = await this.prisma.user.create({
      data: userData
    })

    // Assign roles if specified
    if (data.roles && data.roles.length > 0) {
      await this.assignRolesToUser(user.id, tenantId, data.roles)
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        email: user.email,
        tenantId: user.tenantId
      },
      process.env.JWT_SECRET!,
      { expiresIn: '1h' }
    )

    return {
      ...user,
      token
    } as TestUser
  }

  // Create default roles for a tenant
  static async createDefaultRoles(tenantId: string): Promise<void> {
    const defaultRoles = [
      {
        name: 'Admin',
        description: 'Full access to all features',
        permissions: ['*'],
        tenantId,
        isDefault: false,
        isSystemRole: true
      },
      {
        name: 'Manager',
        description: 'Manage deals and users',
        permissions: ['deals:*', 'users:read', 'documents:*'],
        tenantId,
        isDefault: false,
        isSystemRole: true
      },
      {
        name: 'User',
        description: 'Basic user access',
        permissions: ['deals:read', 'deals:create', 'documents:read'],
        tenantId,
        isDefault: true,
        isSystemRole: true
      }
    ]

    for (const roleData of defaultRoles) {
      await this.prisma.role.create({ data: roleData })
    }
  }

  // Assign roles to user
  static async assignRolesToUser(userId: string, tenantId: string, roleNames: string[]): Promise<void> {
    for (const roleName of roleNames) {
      const role = await this.prisma.role.findFirst({
        where: { name: roleName, tenantId }
      })

      if (role) {
        await this.prisma.userRole.create({
          data: {
            userId,
            roleId: role.id
          }
        })
      }
    }
  }

  // Create mock Express request with tenant context
  static createMockRequest(options: {
    user?: TestUser
    tenant?: TestTenant
    headers?: Record<string, string>
    body?: any
    params?: any
    query?: any
  } = {}): Partial<Request> {
    const req: Partial<Request> = {
      headers: {
        'content-type': 'application/json',
        ...options.headers
      },
      body: options.body || {},
      params: options.params || {},
      query: options.query || {}
    }

    // Add user context if provided
    if (options.user) {
      (req as any).user = options.user
      req.headers!.authorization = `Bearer ${options.user.token}`
    }

    // Add tenant context if provided
    if (options.tenant) {
      (req as any).tenantContext = options.tenant
      req.headers!['x-tenant-id'] = options.tenant.id
      req.headers!['x-subdomain'] = options.tenant.subdomain
    }

    return req
  }

  // Create mock Express response
  static createMockResponse(): Partial<Response> {
    const res: Partial<Response> = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      setHeader: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis()
    }

    return res
  }

  // Setup complete test context with multiple tenants and users
  static async setupTestContext(): Promise<TestContext> {
    const tenants: TestTenant[] = []
    const users: TestUser[] = []

    // Create test tenants
    const tenant1 = await this.createTestTenant({
      name: 'Acme Corp',
      slug: 'acme',
      subdomain: 'acme',
      plan: 'PROFESSIONAL'
    })
    tenants.push(tenant1)

    const tenant2 = await this.createTestTenant({
      name: 'Demo Inc',
      slug: 'demo',
      subdomain: 'demo',
      plan: 'STARTER'
    })
    tenants.push(tenant2)

    // Create test users for each tenant
    const adminUser1 = await this.createTestUser(tenant1.id, {
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      roles: ['Admin']
    })
    users.push(adminUser1)

    const regularUser1 = await this.createTestUser(tenant1.id, {
      email: '<EMAIL>',
      firstName: 'Regular',
      lastName: 'User',
      roles: ['User']
    })
    users.push(regularUser1)

    const adminUser2 = await this.createTestUser(tenant2.id, {
      email: '<EMAIL>',
      firstName: 'Demo',
      lastName: 'Admin',
      roles: ['Admin']
    })
    users.push(adminUser2)

    // Store in global variables for easy access
    global.__TEST_TENANTS__ = tenants
    global.__TEST_USERS__ = users

    return {
      prisma: this.prisma,
      tenants,
      users,
      cleanup: async () => {
        // Cleanup is handled by the global beforeEach hook
      }
    }
  }

  // Get test tenant by subdomain
  static getTestTenantBySubdomain(subdomain: string): TestTenant | undefined {
    return global.__TEST_TENANTS__?.find(t => t.subdomain === subdomain)
  }

  // Get test user by email
  static getTestUserByEmail(email: string): TestUser | undefined {
    return global.__TEST_USERS__?.find(u => u.email === email)
  }

  // Create test deal for a tenant
  static async createTestDeal(tenantId: string, data: any = {}): Promise<any> {
    const dealData = {
      title: data.title || `Test Deal ${Date.now()}`,
      description: data.description || 'Test deal description',
      type: data.type || 'ACQUISITION',
      status: data.status || 'ACTIVE',
      targetCompany: data.targetCompany || 'Target Company',
      dealValue: data.dealValue || 1000000,
      currency: data.currency || 'USD',
      tenantId,
      ...data
    }

    return await this.prisma.deal.create({ data: dealData })
  }

  // Create test document for a tenant
  static async createTestDocument(tenantId: string, dealId?: string, data: any = {}): Promise<any> {
    const documentData = {
      name: data.name || `Test Document ${Date.now()}`,
      type: data.type || 'CONTRACT',
      size: data.size || 1024,
      mimeType: data.mimeType || 'application/pdf',
      path: data.path || '/test/documents/test.pdf',
      tenantId,
      dealId: dealId || null,
      ...data
    }

    return await this.prisma.document.create({ data: documentData })
  }

  // Simulate HTTP request with tenant context
  static async simulateRequest(
    method: string,
    path: string,
    options: {
      tenant?: TestTenant
      user?: TestUser
      body?: any
      headers?: Record<string, string>
    } = {}
  ): Promise<{ req: Partial<Request>; res: Partial<Response> }> {
    const req = this.createMockRequest({
      ...options,
      headers: {
        'x-original-host': options.tenant ? `${options.tenant.subdomain}.localhost` : 'localhost',
        ...options.headers
      }
    })

    const res = this.createMockResponse()

    return { req, res }
  }

  // Wait for async operations to complete
  static async waitFor(condition: () => boolean | Promise<boolean>, timeout = 5000): Promise<void> {
    const start = Date.now()
    
    while (Date.now() - start < timeout) {
      if (await condition()) {
        return
      }
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    throw new Error(`Condition not met within ${timeout}ms`)
  }

  // Generate random test data
  static generateRandomString(length = 8): string {
    return Math.random().toString(36).substring(2, 2 + length)
  }

  static generateRandomEmail(): string {
    return `test${this.generateRandomString()}@example.com`
  }

  static generateRandomSubdomain(): string {
    return `test${this.generateRandomString(6)}`
  }
}
