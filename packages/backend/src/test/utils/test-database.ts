import { PrismaClient } from '@prisma/client'
import { execSync } from 'child_process'
import { randomBytes } from 'crypto'

export class TestDatabase {
  private static instance: TestDatabase
  private prisma: PrismaClient
  private databaseName: string
  private originalDatabaseUrl: string

  private constructor() {
    this.originalDatabaseUrl = process.env.DATABASE_URL!
    this.databaseName = `ma_platform_test_${randomBytes(8).toString('hex')}`
    this.prisma = new PrismaClient()
  }

  static getInstance(): TestDatabase {
    if (!TestDatabase.instance) {
      TestDatabase.instance = new TestDatabase()
    }
    return TestDatabase.instance
  }

  // Create isolated test database
  async setup(): Promise<PrismaClient> {
    try {
      // Create test database
      await this.createTestDatabase()
      
      // Update DATABASE_URL to point to test database
      const testDatabaseUrl = this.originalDatabaseUrl.replace(
        /\/[^\/]+(\?|$)/,
        `/${this.databaseName}$1`
      )
      process.env.DATABASE_URL = testDatabaseUrl

      // Disconnect existing client and create new one with test database
      await this.prisma.$disconnect()
      this.prisma = new PrismaClient({
        datasources: {
          db: {
            url: testDatabaseUrl
          }
        }
      })

      // Run migrations on test database
      await this.runMigrations()

      // Store in global for access in tests
      global.__PRISMA__ = this.prisma

      console.log(`✅ Test database created: ${this.databaseName}`)
      return this.prisma

    } catch (error) {
      console.error('❌ Failed to setup test database:', error)
      throw error
    }
  }

  // Clean up test database
  async teardown(): Promise<void> {
    try {
      if (this.prisma) {
        await this.prisma.$disconnect()
      }

      // Drop test database
      await this.dropTestDatabase()

      // Restore original DATABASE_URL
      process.env.DATABASE_URL = this.originalDatabaseUrl

      console.log(`✅ Test database cleaned up: ${this.databaseName}`)

    } catch (error) {
      console.error('❌ Failed to teardown test database:', error)
      // Don't throw here to avoid masking test failures
    }
  }

  // Reset test database (clear all data but keep schema)
  async reset(): Promise<void> {
    try {
      // Clear all data in correct order (respecting foreign key constraints)
      const tableNames = [
        'MigrationRecord',
        'AuditLog',
        'Session',
        'UserRole',
        'Document',
        'DueDiligenceItem',
        'Valuation',
        'Deal',
        'TenantApiKey',
        'TenantInvitation',
        'User',
        'Role',
        'Tenant'
      ]

      for (const tableName of tableNames) {
        await this.prisma.$executeRawUnsafe(`DELETE FROM "${tableName}";`)
      }

      // Reset sequences
      for (const tableName of tableNames) {
        try {
          await this.prisma.$executeRawUnsafe(
            `SELECT setval(pg_get_serial_sequence('"${tableName}"', 'id'), 1, false);`
          )
        } catch (error) {
          // Ignore errors for tables without auto-increment IDs
        }
      }

      console.log('✅ Test database reset completed')

    } catch (error) {
      console.error('❌ Failed to reset test database:', error)
      throw error
    }
  }

  // Get Prisma client instance
  getPrisma(): PrismaClient {
    return this.prisma
  }

  // Get test database name
  getDatabaseName(): string {
    return this.databaseName
  }

  // Create test database
  private async createTestDatabase(): Promise<void> {
    const baseUrl = this.originalDatabaseUrl.split('/').slice(0, -1).join('/')
    const tempPrisma = new PrismaClient({
      datasources: {
        db: {
          url: baseUrl + '/postgres' // Connect to default postgres database
        }
      }
    })

    try {
      await tempPrisma.$executeRawUnsafe(`CREATE DATABASE "${this.databaseName}";`)
    } catch (error) {
      // Database might already exist, ignore error
    } finally {
      await tempPrisma.$disconnect()
    }
  }

  // Drop test database
  private async dropTestDatabase(): Promise<void> {
    const baseUrl = this.originalDatabaseUrl.split('/').slice(0, -1).join('/')
    const tempPrisma = new PrismaClient({
      datasources: {
        db: {
          url: baseUrl + '/postgres' // Connect to default postgres database
        }
      }
    })

    try {
      // Terminate active connections to the test database
      await tempPrisma.$executeRawUnsafe(`
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = '${this.databaseName}' AND pid <> pg_backend_pid();
      `)

      // Drop the database
      await tempPrisma.$executeRawUnsafe(`DROP DATABASE IF EXISTS "${this.databaseName}";`)
    } catch (error) {
      console.warn(`Warning: Could not drop test database ${this.databaseName}:`, error)
    } finally {
      await tempPrisma.$disconnect()
    }
  }

  // Run Prisma migrations on test database
  private async runMigrations(): Promise<void> {
    try {
      // Use Prisma CLI to run migrations
      execSync('npx prisma migrate deploy', {
        env: {
          ...process.env,
          DATABASE_URL: process.env.DATABASE_URL
        },
        stdio: 'pipe'
      })

      console.log('✅ Test database migrations completed')

    } catch (error) {
      console.error('❌ Failed to run migrations on test database:', error)
      throw error
    }
  }

  // Seed test database with initial data
  async seed(): Promise<void> {
    try {
      // Create system-level data that all tests might need
      console.log('✅ Test database seeding completed')

    } catch (error) {
      console.error('❌ Failed to seed test database:', error)
      throw error
    }
  }

  // Execute raw SQL for testing
  async executeRaw(sql: string, params: any[] = []): Promise<any> {
    return await this.prisma.$executeRawUnsafe(sql, ...params)
  }

  // Query raw SQL for testing
  async queryRaw(sql: string, params: any[] = []): Promise<any> {
    return await this.prisma.$queryRawUnsafe(sql, ...params)
  }

  // Check if database exists
  async databaseExists(): Promise<boolean> {
    const baseUrl = this.originalDatabaseUrl.split('/').slice(0, -1).join('/')
    const tempPrisma = new PrismaClient({
      datasources: {
        db: {
          url: baseUrl + '/postgres'
        }
      }
    })

    try {
      const result = await tempPrisma.$queryRawUnsafe(`
        SELECT 1 FROM pg_database WHERE datname = '${this.databaseName}';
      `)
      return Array.isArray(result) && result.length > 0
    } catch (error) {
      return false
    } finally {
      await tempPrisma.$disconnect()
    }
  }

  // Get database connection info
  getConnectionInfo(): {
    databaseName: string
    url: string
    originalUrl: string
  } {
    return {
      databaseName: this.databaseName,
      url: process.env.DATABASE_URL!,
      originalUrl: this.originalDatabaseUrl
    }
  }

  // Create database snapshot for faster test resets
  async createSnapshot(snapshotName: string): Promise<void> {
    try {
      const baseUrl = this.originalDatabaseUrl.split('/').slice(0, -1).join('/')
      const tempPrisma = new PrismaClient({
        datasources: {
          db: {
            url: baseUrl + '/postgres'
          }
        }
      })

      await tempPrisma.$executeRawUnsafe(`
        CREATE DATABASE "${snapshotName}" WITH TEMPLATE "${this.databaseName}";
      `)

      await tempPrisma.$disconnect()
      console.log(`✅ Database snapshot created: ${snapshotName}`)

    } catch (error) {
      console.error('❌ Failed to create database snapshot:', error)
      throw error
    }
  }

  // Restore from database snapshot
  async restoreSnapshot(snapshotName: string): Promise<void> {
    try {
      // Drop current test database
      await this.dropTestDatabase()

      // Recreate from snapshot
      const baseUrl = this.originalDatabaseUrl.split('/').slice(0, -1).join('/')
      const tempPrisma = new PrismaClient({
        datasources: {
          db: {
            url: baseUrl + '/postgres'
          }
        }
      })

      await tempPrisma.$executeRawUnsafe(`
        CREATE DATABASE "${this.databaseName}" WITH TEMPLATE "${snapshotName}";
      `)

      await tempPrisma.$disconnect()
      console.log(`✅ Database restored from snapshot: ${snapshotName}`)

    } catch (error) {
      console.error('❌ Failed to restore database snapshot:', error)
      throw error
    }
  }
}
