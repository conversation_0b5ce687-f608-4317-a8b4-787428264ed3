import { performance } from 'perf_hooks'

// Performance test utilities
declare global {
  namespace jest {
    interface Matchers<R> {
      toCompleteWithin(milliseconds: number): R
      toHaveAverageResponseTime(milliseconds: number): R
      toHandleConcurrentRequests(count: number, maxTime: number): R
    }
  }
}

// Custom Jest matchers for performance testing
expect.extend({
  toCompleteWithin(received: Promise<any>, milliseconds: number) {
    const startTime = performance.now()
    
    return received.then(
      () => {
        const duration = performance.now() - startTime
        const pass = duration <= milliseconds
        
        return {
          message: () =>
            pass
              ? `Expected operation to take more than ${milliseconds}ms, but it took ${duration.toFixed(2)}ms`
              : `Expected operation to complete within ${milliseconds}ms, but it took ${duration.toFixed(2)}ms`,
          pass
        }
      },
      (error) => {
        const duration = performance.now() - startTime
        return {
          message: () => `Operation failed after ${duration.toFixed(2)}ms: ${error.message}`,
          pass: false
        }
      }
    )
  },

  toHaveAverageResponseTime(received: number[], milliseconds: number) {
    const average = received.reduce((sum, time) => sum + time, 0) / received.length
    const pass = average <= milliseconds
    
    return {
      message: () =>
        pass
          ? `Expected average response time to be more than ${milliseconds}ms, but it was ${average.toFixed(2)}ms`
          : `Expected average response time to be within ${milliseconds}ms, but it was ${average.toFixed(2)}ms`,
      pass
    }
  },

  async toHandleConcurrentRequests(received: () => Promise<any>, count: number, maxTime: number) {
    const requests = Array.from({ length: count }, () => received())
    const startTime = performance.now()
    
    try {
      await Promise.all(requests)
      const duration = performance.now() - startTime
      const pass = duration <= maxTime
      
      return {
        message: () =>
          pass
            ? `Expected ${count} concurrent requests to take more than ${maxTime}ms, but they took ${duration.toFixed(2)}ms`
            : `Expected ${count} concurrent requests to complete within ${maxTime}ms, but they took ${duration.toFixed(2)}ms`,
        pass
      }
    } catch (error) {
      const duration = performance.now() - startTime
      return {
        message: () => `Concurrent requests failed after ${duration.toFixed(2)}ms: ${error.message}`,
        pass: false
      }
    }
  }
})

// Performance test helpers
export class PerformanceTestUtils {
  static async measureExecutionTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const startTime = performance.now()
    const result = await fn()
    const duration = performance.now() - startTime
    return { result, duration }
  }

  static async measureMultipleExecutions<T>(
    fn: () => Promise<T>,
    iterations: number
  ): Promise<{ results: T[]; durations: number[]; averageDuration: number }> {
    const results: T[] = []
    const durations: number[] = []

    for (let i = 0; i < iterations; i++) {
      const { result, duration } = await this.measureExecutionTime(fn)
      results.push(result)
      durations.push(duration)
    }

    const averageDuration = durations.reduce((sum, duration) => sum + duration, 0) / iterations

    return { results, durations, averageDuration }
  }

  static async measureConcurrentExecutions<T>(
    fn: () => Promise<T>,
    concurrency: number
  ): Promise<{ results: T[]; totalDuration: number; averageDuration: number }> {
    const startTime = performance.now()
    
    const promises = Array.from({ length: concurrency }, () => fn())
    const results = await Promise.all(promises)
    
    const totalDuration = performance.now() - startTime
    const averageDuration = totalDuration / concurrency

    return { results, totalDuration, averageDuration }
  }

  static createLoadTest(
    fn: () => Promise<any>,
    options: {
      duration: number // milliseconds
      concurrency: number
      rampUpTime?: number // milliseconds
    }
  ) {
    return async () => {
      const { duration, concurrency, rampUpTime = 0 } = options
      const results: any[] = []
      const errors: Error[] = []
      const startTime = performance.now()
      const endTime = startTime + duration

      // Ramp up
      if (rampUpTime > 0) {
        const rampUpInterval = rampUpTime / concurrency
        for (let i = 0; i < concurrency; i++) {
          setTimeout(() => {
            this.runContinuousLoad(fn, endTime, results, errors)
          }, i * rampUpInterval)
        }
      } else {
        // Start all concurrent workers immediately
        const workers = Array.from({ length: concurrency }, () =>
          this.runContinuousLoad(fn, endTime, results, errors)
        )
        await Promise.all(workers)
      }

      return {
        totalRequests: results.length,
        totalErrors: errors.length,
        successRate: (results.length / (results.length + errors.length)) * 100,
        duration: performance.now() - startTime,
        requestsPerSecond: results.length / ((performance.now() - startTime) / 1000)
      }
    }
  }

  private static async runContinuousLoad(
    fn: () => Promise<any>,
    endTime: number,
    results: any[],
    errors: Error[]
  ): Promise<void> {
    while (performance.now() < endTime) {
      try {
        const result = await fn()
        results.push(result)
      } catch (error) {
        errors.push(error as Error)
      }
    }
  }

  static createMemoryLeakTest(fn: () => Promise<any>, iterations: number = 100) {
    return async () => {
      const initialMemory = process.memoryUsage()
      
      for (let i = 0; i < iterations; i++) {
        await fn()
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc()
        }
      }

      const finalMemory = process.memoryUsage()
      const memoryIncrease = {
        heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
        heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
        external: finalMemory.external - initialMemory.external,
        rss: finalMemory.rss - initialMemory.rss
      }

      return {
        initialMemory,
        finalMemory,
        memoryIncrease,
        iterations
      }
    }
  }
}

// Global performance test configuration
export const PERFORMANCE_THRESHOLDS = {
  API_RESPONSE_TIME: 500, // milliseconds
  DATABASE_QUERY_TIME: 100, // milliseconds
  CONCURRENT_REQUESTS: 1000, // milliseconds for 10 concurrent requests
  MEMORY_LEAK_THRESHOLD: 50 * 1024 * 1024, // 50MB
  CPU_USAGE_THRESHOLD: 80 // percentage
}

// Setup performance monitoring
beforeEach(() => {
  // Clear any performance marks from previous tests
  performance.clearMarks()
  performance.clearMeasures()
})

afterEach(() => {
  // Log performance metrics if test failed
  if (expect.getState().currentTestName) {
    const marks = performance.getEntriesByType('mark')
    const measures = performance.getEntriesByType('measure')
    
    if (marks.length > 0 || measures.length > 0) {
      console.log(`Performance data for ${expect.getState().currentTestName}:`)
      marks.forEach(mark => console.log(`  Mark: ${mark.name} at ${mark.startTime}ms`))
      measures.forEach(measure => console.log(`  Measure: ${measure.name} took ${measure.duration}ms`))
    }
  }
})

// Global error handler for performance tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection in performance test:', reason)
  // Don't exit the process in tests, just log the error
})

export default PerformanceTestUtils
