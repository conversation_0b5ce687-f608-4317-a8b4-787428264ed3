import { BaseApiTest } from './base-api.test'
import { TestHelpers } from '../utils/test-helpers'

describe('Tenant Provisioning API Integration Tests', () => {
  let apiTest: BaseApiTest

  beforeAll(async () => {
    apiTest = new BaseApiTest()
    await apiTest.setup()
  })

  afterAll(async () => {
    await apiTest.cleanup()
  })

  describe('POST /api/tenant-provisioning/provision', () => {
    it('should provision new tenant with admin user', async () => {
      // This would typically require system admin permissions
      // For testing, we'll create a system admin user
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      const provisioningData = {
        tenantData: {
          name: 'New Test Company',
          subdomain: 'newtest',
          slug: 'new-test',
          plan: 'PROFESSIONAL',
          description: 'A new test company'
        },
        adminUser: {
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          password: 'securepassword123'
        },
        options: {
          skipMigrations: false,
          skipDefaultRoles: false,
          skipWelcomeEmail: true
        }
      }

      const response = await apiTest.makeRequest('post', '/api/tenant-provisioning/provision', {
        user: systemAdmin,
        body: provisioningData
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveProperty('provisioningId')
      expect(response.body.data).toHaveProperty('tenant')
      expect(response.body.data).toHaveProperty('adminUser')
      expect(response.body.data.status).toBe('success')

      const { tenant, adminUser } = response.body.data

      // Verify tenant was created
      expect(tenant.name).toBe(provisioningData.tenantData.name)
      expect(tenant.subdomain).toBe(provisioningData.tenantData.subdomain)
      expect(tenant.plan).toBe(provisioningData.tenantData.plan)

      // Verify admin user was created
      expect(adminUser.email).toBe(provisioningData.adminUser.email)
      expect(adminUser.firstName).toBe(provisioningData.adminUser.firstName)

      // Verify in database
      const dbTenant = await apiTest['prisma'].tenant.findUnique({
        where: { subdomain: provisioningData.tenantData.subdomain }
      })
      expect(dbTenant).toBeTruthy()

      const dbUser = await apiTest['prisma'].user.findUnique({
        where: { email: provisioningData.adminUser.email }
      })
      expect(dbUser).toBeTruthy()
      expect(dbUser?.tenantId).toBe(dbTenant?.id)

      // Verify default roles were created
      const roles = await apiTest['prisma'].role.findMany({
        where: { tenantId: dbTenant?.id }
      })
      expect(roles.length).toBeGreaterThanOrEqual(3) // Admin, Manager, User
      expect(roles.some(r => r.name === 'Admin')).toBe(true)
      expect(roles.some(r => r.name === 'Manager')).toBe(true)
      expect(roles.some(r => r.name === 'User')).toBe(true)
    })

    it('should reject duplicate subdomain', async () => {
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      const existingTenant = apiTest.getTenant('acme')

      const duplicateData = {
        tenantData: {
          name: 'Duplicate Company',
          subdomain: existingTenant.subdomain, // Duplicate subdomain
          slug: 'duplicate',
          plan: 'STARTER'
        },
        adminUser: {
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          password: 'password123'
        }
      }

      await apiTest.makeRequest('post', '/api/tenant-provisioning/provision', {
        user: systemAdmin,
        body: duplicateData,
        expectStatus: 409
      })
    })

    it('should validate provisioning data', async () => {
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      const invalidData = {
        tenantData: {
          name: '', // Empty name
          subdomain: 'invalid-subdomain!', // Invalid characters
          plan: 'INVALID_PLAN' // Invalid plan
        },
        adminUser: {
          firstName: '',
          lastName: '',
          email: 'invalid-email', // Invalid email
          password: '123' // Too short
        }
      }

      await apiTest.assertValidationError(
        'post', 
        '/api/tenant-provisioning/provision', 
        invalidData, 
        undefined as any, 
        systemAdmin
      )
    })

    it('should require system admin permissions', async () => {
      const regularUser = apiTest.getAdminUser(apiTest.getTenant('acme').id)

      const provisioningData = {
        tenantData: {
          name: 'Unauthorized Company',
          subdomain: 'unauthorized',
          plan: 'STARTER'
        },
        adminUser: {
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          password: 'password123'
        }
      }

      await apiTest.makeRequest('post', '/api/tenant-provisioning/provision', {
        user: regularUser,
        body: provisioningData,
        expectStatus: 403
      })
    })

    it('should handle provisioning with custom settings', async () => {
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      const customSettings = {
        general: {
          timezone: 'America/New_York',
          currency: 'EUR'
        },
        features: {
          apiAccess: true,
          customBranding: true
        },
        limits: {
          maxUsers: 50,
          maxDeals: 200
        }
      }

      const provisioningData = {
        tenantData: {
          name: 'Custom Settings Company',
          subdomain: 'customsettings',
          plan: 'ENTERPRISE',
          settings: customSettings.general,
          features: customSettings.features,
          limits: customSettings.limits
        },
        adminUser: {
          firstName: 'Custom',
          lastName: 'Admin',
          email: '<EMAIL>',
          password: 'password123'
        }
      }

      const response = await apiTest.makeRequest('post', '/api/tenant-provisioning/provision', {
        user: systemAdmin,
        body: provisioningData
      })

      expect(response.body.success).toBe(true)

      // Verify custom settings were applied
      const dbTenant = await apiTest['prisma'].tenant.findUnique({
        where: { subdomain: 'customsettings' }
      })

      expect(dbTenant?.settings).toMatchObject(customSettings.general)
      expect(dbTenant?.features).toMatchObject(customSettings.features)
      expect(dbTenant?.limits).toMatchObject(customSettings.limits)
    })
  })

  describe('POST /api/tenant-provisioning/deprovision', () => {
    it('should deprovision tenant and clean up data', async () => {
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      // First create a tenant to deprovision
      const tenant = await TestHelpers.createTestTenant({
        name: 'To Be Deleted',
        subdomain: 'tobedeleted',
        slug: 'to-be-deleted'
      })

      // Create some data in the tenant
      await TestHelpers.createTestUser(tenant.id, {
        email: '<EMAIL>'
      })

      await TestHelpers.createTestDeal(tenant.id, {
        title: 'Deal to be deleted'
      })

      const deprovisionData = {
        tenantId: tenant.id,
        options: {
          hardDelete: false,
          preserveData: false,
          notifyUsers: false
        }
      }

      const response = await apiTest.makeRequest('post', '/api/tenant-provisioning/deprovision', {
        user: systemAdmin,
        body: deprovisionData
      })

      expect(response.body.success).toBe(true)

      // Verify tenant was soft deleted
      const dbTenant = await apiTest['prisma'].tenant.findUnique({
        where: { id: tenant.id }
      })

      expect(dbTenant?.status).toBe('DELETED')
      expect(dbTenant?.deletedAt).toBeTruthy()
    })

    it('should require system admin permissions for deprovisioning', async () => {
      const regularUser = apiTest.getAdminUser(apiTest.getTenant('acme').id)
      const tenant = apiTest.getTenant('demo')

      const deprovisionData = {
        tenantId: tenant.id
      }

      await apiTest.makeRequest('post', '/api/tenant-provisioning/deprovision', {
        user: regularUser,
        body: deprovisionData,
        expectStatus: 403
      })
    })

    it('should validate tenant exists before deprovisioning', async () => {
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      const deprovisionData = {
        tenantId: 'non-existent-tenant-id'
      }

      await apiTest.makeRequest('post', '/api/tenant-provisioning/deprovision', {
        user: systemAdmin,
        body: deprovisionData,
        expectStatus: 404
      })
    })
  })

  describe('GET /api/tenant-provisioning/provision/templates', () => {
    it('should return provisioning templates', async () => {
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      const response = await apiTest.makeRequest('get', '/api/tenant-provisioning/provision/templates', {
        user: systemAdmin
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveProperty('starter')
      expect(response.body.data).toHaveProperty('professional')
      expect(response.body.data).toHaveProperty('enterprise')

      // Verify template structure
      const starterTemplate = response.body.data.starter
      expect(starterTemplate).toHaveProperty('name')
      expect(starterTemplate).toHaveProperty('description')
      expect(starterTemplate).toHaveProperty('tenantData')
      expect(starterTemplate).toHaveProperty('options')
      expect(starterTemplate.tenantData.plan).toBe('STARTER')
    })

    it('should require authentication', async () => {
      await apiTest.assertAuthenticationRequired('get', '/api/tenant-provisioning/provision/templates')
    })
  })

  describe('POST /api/tenant-provisioning/provision/bulk', () => {
    it('should provision multiple tenants', async () => {
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      const bulkData = {
        tenants: [
          {
            tenantData: {
              name: 'Bulk Tenant 1',
              subdomain: 'bulk1',
              plan: 'STARTER'
            },
            adminUser: {
              firstName: 'Admin',
              lastName: 'One',
              email: '<EMAIL>',
              password: 'password123'
            }
          },
          {
            tenantData: {
              name: 'Bulk Tenant 2',
              subdomain: 'bulk2',
              plan: 'PROFESSIONAL'
            },
            adminUser: {
              firstName: 'Admin',
              lastName: 'Two',
              email: '<EMAIL>',
              password: 'password123'
            }
          }
        ]
      }

      const response = await apiTest.makeRequest('post', '/api/tenant-provisioning/provision/bulk', {
        user: systemAdmin,
        body: bulkData
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data.total).toBe(2)
      expect(response.body.data.successful).toBe(2)
      expect(response.body.data.failed).toBe(0)
      expect(response.body.data.results).toHaveLength(2)

      // Verify both tenants were created
      const tenant1 = await apiTest['prisma'].tenant.findUnique({
        where: { subdomain: 'bulk1' }
      })
      const tenant2 = await apiTest['prisma'].tenant.findUnique({
        where: { subdomain: 'bulk2' }
      })

      expect(tenant1).toBeTruthy()
      expect(tenant2).toBeTruthy()
    })

    it('should handle partial failures in bulk provisioning', async () => {
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      const existingTenant = apiTest.getTenant('acme')

      const bulkData = {
        tenants: [
          {
            tenantData: {
              name: 'Valid Bulk Tenant',
              subdomain: 'validbulk',
              plan: 'STARTER'
            },
            adminUser: {
              firstName: 'Valid',
              lastName: 'Admin',
              email: '<EMAIL>',
              password: 'password123'
            }
          },
          {
            tenantData: {
              name: 'Invalid Bulk Tenant',
              subdomain: existingTenant.subdomain, // Duplicate subdomain
              plan: 'STARTER'
            },
            adminUser: {
              firstName: 'Invalid',
              lastName: 'Admin',
              email: '<EMAIL>',
              password: 'password123'
            }
          }
        ]
      }

      const response = await apiTest.makeRequest('post', '/api/tenant-provisioning/provision/bulk', {
        user: systemAdmin,
        body: bulkData
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data.total).toBe(2)
      expect(response.body.data.successful).toBe(1)
      expect(response.body.data.failed).toBe(1)
      expect(response.body.data.errors).toHaveLength(1)
    })
  })

  describe('Performance and Reliability Tests', () => {
    it('should handle provisioning under load', async () => {
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      const requests = Array.from({ length: 5 }, (_, i) => ({
        tenantData: {
          name: `Load Test Tenant ${i}`,
          subdomain: `loadtest${i}`,
          plan: 'STARTER'
        },
        adminUser: {
          firstName: 'Load',
          lastName: `Admin${i}`,
          email: `admin@loadtest${i}.com`,
          password: 'password123'
        },
        options: {
          skipWelcomeEmail: true
        }
      }))

      const startTime = Date.now()
      const responses = await Promise.all(
        requests.map(data =>
          apiTest.makeRequest('post', '/api/tenant-provisioning/provision', {
            user: systemAdmin,
            body: data
          })
        )
      )
      const duration = Date.now() - startTime

      // All requests should succeed
      responses.forEach(response => {
        expect(response.body.success).toBe(true)
      })

      // Should complete within reasonable time (adjust based on your requirements)
      expect(duration).toBeLessThan(30000) // 30 seconds for 5 tenants
    })

    it('should rollback on provisioning failure', async () => {
      const systemAdmin = await TestHelpers.createTestUser('system', {
        email: '<EMAIL>',
        roles: ['SystemAdmin']
      })

      // Create provisioning data that will fail (duplicate email)
      const existingUser = apiTest.getAdminUser(apiTest.getTenant('acme').id)

      const failingData = {
        tenantData: {
          name: 'Failing Tenant',
          subdomain: 'failing',
          plan: 'STARTER'
        },
        adminUser: {
          firstName: 'Failing',
          lastName: 'Admin',
          email: existingUser.email, // Duplicate email
          password: 'password123'
        }
      }

      await apiTest.makeRequest('post', '/api/tenant-provisioning/provision', {
        user: systemAdmin,
        body: failingData,
        expectStatus: 409
      })

      // Verify no partial data was left behind
      const tenant = await apiTest['prisma'].tenant.findUnique({
        where: { subdomain: 'failing' }
      })

      expect(tenant).toBeNull() // Should not exist due to rollback
    })
  })
})
