import { BaseApiTest } from './base-api.test'
import { TestHelpers } from '../utils/test-helpers'

describe('Authentication API Integration Tests', () => {
  let apiTest: BaseApiTest

  beforeAll(async () => {
    apiTest = new BaseApiTest()
    await apiTest.setup()
  })

  afterAll(async () => {
    await apiTest.cleanup()
  })

  describe('POST /api/auth/login', () => {
    it('should authenticate user with valid credentials', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const loginData = {
        email: user.email,
        password: 'password123'
      }

      const response = await apiTest.makeRequest('post', '/api/auth/login', {
        tenant,
        body: loginData
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveProperty('token')
      expect(response.body.data).toHaveProperty('user')
      expect(response.body.data.user.email).toBe(user.email)
      expect(response.body.data.user.tenantId).toBe(tenant.id)
      expect(response.body.data).toHaveProperty('tenant')
      expect(response.body.data.tenant.id).toBe(tenant.id)
    })

    it('should reject invalid credentials', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const invalidLoginData = {
        email: user.email,
        password: 'wrongpassword'
      }

      await apiTest.makeRequest('post', '/api/auth/login', {
        tenant,
        body: invalidLoginData,
        expectStatus: 401
      })
    })

    it('should enforce tenant isolation during login', async () => {
      const tenant1 = apiTest.getTenant('acme')
      const tenant2 = apiTest.getTenant('demo')
      const user1 = apiTest.getAdminUser(tenant1.id)

      // Try to login to tenant1 user from tenant2 context
      const loginData = {
        email: user1.email,
        password: 'password123'
      }

      await apiTest.makeRequest('post', '/api/auth/login', {
        tenant: tenant2, // Wrong tenant context
        body: loginData,
        expectStatus: 401
      })
    })

    it('should validate required fields', async () => {
      const tenant = apiTest.getTenant('acme')

      const invalidData = {
        email: '', // Empty email
        password: '' // Empty password
      }

      await apiTest.assertValidationError('post', '/api/auth/login', invalidData, tenant, undefined as any)
    })

    it('should handle non-existent user', async () => {
      const tenant = apiTest.getTenant('acme')

      const nonExistentUser = {
        email: '<EMAIL>',
        password: 'password123'
      }

      await apiTest.makeRequest('post', '/api/auth/login', {
        tenant,
        body: nonExistentUser,
        expectStatus: 401
      })
    })

    it('should include tenant information in response', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const loginData = {
        email: user.email,
        password: 'password123'
      }

      const response = await apiTest.makeRequest('post', '/api/auth/login', {
        tenant,
        body: loginData
      })

      expect(response.body.data.tenant).toMatchObject({
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        subdomain: tenant.subdomain,
        plan: tenant.plan,
        status: tenant.status
      })
    })

    it('should set secure session cookie', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const loginData = {
        email: user.email,
        password: 'password123'
      }

      const response = await apiTest.makeRequest('post', '/api/auth/login', {
        tenant,
        body: loginData
      })

      // Check for session cookie
      const cookies = response.headers['set-cookie']
      expect(cookies).toBeDefined()
      
      const sessionCookie = cookies?.find((cookie: string) => 
        cookie.startsWith('session=')
      )
      expect(sessionCookie).toBeDefined()
    })
  })

  describe('POST /api/auth/register', () => {
    it('should register new user with invitation token', async () => {
      const tenant = apiTest.getTenant('acme')
      const adminUser = apiTest.getAdminUser(tenant.id)

      // First create an invitation
      const inviteResponse = await apiTest.makeRequest('post', '/api/tenants/invite', {
        tenant,
        user: adminUser,
        body: {
          email: '<EMAIL>',
          role: 'User'
        }
      })

      const invitationToken = inviteResponse.body.data.token

      // Now register with the invitation token
      const registerData = {
        firstName: 'New',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'password123',
        invitationToken
      }

      const response = await apiTest.makeRequest('post', '/api/auth/register', {
        tenant,
        body: registerData
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveProperty('token')
      expect(response.body.data).toHaveProperty('user')
      expect(response.body.data.user.email).toBe(registerData.email)
      expect(response.body.data.user.tenantId).toBe(tenant.id)

      // Verify user was created in database
      const createdUser = await apiTest['prisma'].user.findUnique({
        where: { email: registerData.email }
      })

      expect(createdUser).toBeTruthy()
      expect(createdUser?.tenantId).toBe(tenant.id)
    })

    it('should reject registration without valid invitation', async () => {
      const tenant = apiTest.getTenant('acme')

      const registerData = {
        firstName: 'Invalid',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'password123',
        invitationToken: 'invalid-token'
      }

      await apiTest.makeRequest('post', '/api/auth/register', {
        tenant,
        body: registerData,
        expectStatus: 400
      })
    })

    it('should validate password strength', async () => {
      const tenant = apiTest.getTenant('acme')

      const weakPasswordData = {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: '123', // Too weak
        invitationToken: 'valid-token'
      }

      await apiTest.assertValidationError('post', '/api/auth/register', weakPasswordData, tenant, undefined as any)
    })

    it('should prevent duplicate email registration', async () => {
      const tenant = apiTest.getTenant('acme')
      const existingUser = apiTest.getAdminUser(tenant.id)

      const duplicateData = {
        firstName: 'Duplicate',
        lastName: 'User',
        email: existingUser.email, // Already exists
        password: 'password123',
        invitationToken: 'valid-token'
      }

      await apiTest.makeRequest('post', '/api/auth/register', {
        tenant,
        body: duplicateData,
        expectStatus: 409
      })
    })
  })

  describe('POST /api/auth/logout', () => {
    it('should logout authenticated user', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const response = await apiTest.makeRequest('post', '/api/auth/logout', {
        tenant,
        user
      })

      expect(response.body.success).toBe(true)

      // Check that session cookie is cleared
      const cookies = response.headers['set-cookie']
      const sessionCookie = cookies?.find((cookie: string) => 
        cookie.startsWith('session=')
      )
      
      if (sessionCookie) {
        expect(sessionCookie).toContain('Max-Age=0')
      }
    })

    it('should require authentication', async () => {
      const tenant = apiTest.getTenant('acme')
      
      await apiTest.assertAuthenticationRequired('post', '/api/auth/logout', tenant)
    })
  })

  describe('GET /api/auth/me', () => {
    it('should return current user information', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const response = await apiTest.makeRequest('get', '/api/auth/me', {
        tenant,
        user
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data).toMatchObject({
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        tenantId: tenant.id
      })
      expect(response.body.data).toHaveProperty('roles')
      expect(response.body.data).toHaveProperty('permissions')
    })

    it('should include tenant information', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const response = await apiTest.makeRequest('get', '/api/auth/me', {
        tenant,
        user
      })

      expect(response.body.data).toHaveProperty('tenant')
      expect(response.body.data.tenant.id).toBe(tenant.id)
    })

    it('should require authentication', async () => {
      const tenant = apiTest.getTenant('acme')
      
      await apiTest.assertAuthenticationRequired('get', '/api/auth/me', tenant)
    })

    it('should enforce tenant context', async () => {
      const user = apiTest.getAdminUser(apiTest.getTenant('acme').id)

      // Request without tenant context should fail
      await apiTest.makeRequest('get', '/api/auth/me', {
        user,
        expectStatus: 400
      })
    })
  })

  describe('POST /api/auth/refresh', () => {
    it('should refresh valid token', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const response = await apiTest.makeRequest('post', '/api/auth/refresh', {
        tenant,
        user
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveProperty('token')
      expect(response.body.data.token).not.toBe(user.token) // Should be a new token
    })

    it('should require valid authentication', async () => {
      const tenant = apiTest.getTenant('acme')
      
      await apiTest.assertAuthenticationRequired('post', '/api/auth/refresh', tenant)
    })
  })

  describe('Multi-tenant Authentication Isolation', () => {
    it('should prevent cross-tenant token usage', async () => {
      const tenant1 = apiTest.getTenant('acme')
      const tenant2 = apiTest.getTenant('demo')
      const user1 = apiTest.getAdminUser(tenant1.id)

      // Try to use tenant1 user token in tenant2 context
      await apiTest.makeRequest('get', '/api/auth/me', {
        tenant: tenant2,
        user: user1,
        expectStatus: 403
      })
    })

    it('should isolate user sessions by tenant', async () => {
      const tenant1 = apiTest.getTenant('acme')
      const tenant2 = apiTest.getTenant('demo')
      const user1 = apiTest.getAdminUser(tenant1.id)
      const user2 = apiTest.getAdminUser(tenant2.id)

      // Login to both tenants
      const response1 = await apiTest.makeRequest('get', '/api/auth/me', {
        tenant: tenant1,
        user: user1
      })

      const response2 = await apiTest.makeRequest('get', '/api/auth/me', {
        tenant: tenant2,
        user: user2
      })

      // Should return different user data
      expect(response1.body.data.id).not.toBe(response2.body.data.id)
      expect(response1.body.data.tenantId).toBe(tenant1.id)
      expect(response2.body.data.tenantId).toBe(tenant2.id)
    })

    it('should validate tenant membership during authentication', async () => {
      const tenant1 = apiTest.getTenant('acme')
      const tenant2 = apiTest.getTenant('demo')
      const user1 = apiTest.getAdminUser(tenant1.id)

      // Create login request for user1 but in tenant2 context
      const loginData = {
        email: user1.email,
        password: 'password123'
      }

      await apiTest.makeRequest('post', '/api/auth/login', {
        tenant: tenant2, // Wrong tenant
        body: loginData,
        expectStatus: 401
      })
    })
  })

  describe('Security Tests', () => {
    it('should rate limit login attempts', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const loginData = {
        email: user.email,
        password: 'wrongpassword'
      }

      // Make multiple failed login attempts
      const requests = Array.from({ length: 10 }, () =>
        apiTest.makeRequest('post', '/api/auth/login', {
          tenant,
          body: loginData,
          expectStatus: 401
        }).catch(() => {}) // Ignore errors for rate limited requests
      )

      await Promise.all(requests)

      // Next request should be rate limited
      await apiTest.makeRequest('post', '/api/auth/login', {
        tenant,
        body: loginData,
        expectStatus: 429
      })
    })

    it('should include security headers', async () => {
      const tenant = apiTest.getTenant('acme')

      const response = await apiTest.makeRequest('get', '/api/auth/me', {
        tenant,
        expectStatus: 401 // Will fail auth but we can check headers
      })

      // Check for security headers (these would be set by middleware)
      // Note: Actual header checking would depend on your security middleware implementation
    })

    it('should handle malformed tokens gracefully', async () => {
      const tenant = apiTest.getTenant('acme')

      await apiTest.makeRequest('get', '/api/auth/me', {
        tenant,
        headers: {
          'Authorization': 'Bearer invalid.malformed.token'
        },
        expectStatus: 401
      })
    })
  })
})
