import request from 'supertest'
import { Express } from 'express'
import { PrismaClient } from '@prisma/client'
import { TestHelpers, TestContext, TestTenant, TestUser } from '../utils/test-helpers'
import { getTestConfig, testAssertions } from '../config/test-config'
import { createApp } from '@/app'

export class BaseApiTest {
  protected app: Express
  protected prisma: PrismaClient
  protected testContext: TestContext
  protected config = getTestConfig('integration')

  async setup(): Promise<void> {
    // Create Express app
    this.app = createApp()
    this.prisma = global.__PRISMA__

    // Setup test context with tenants and users
    this.testContext = await TestHelpers.setupTestContext()
  }

  async cleanup(): Promise<void> {
    await this.testContext.cleanup()
  }

  // Helper method to make authenticated requests
  protected async makeRequest(
    method: 'get' | 'post' | 'put' | 'patch' | 'delete',
    path: string,
    options: {
      tenant?: TestTenant
      user?: TestUser
      body?: any
      headers?: Record<string, string>
      expectStatus?: number
    } = {}
  ) {
    const {
      tenant,
      user,
      body,
      headers = {},
      expectStatus = 200
    } = options

    let req = request(this.app)[method](path)

    // Add tenant headers
    if (tenant) {
      req = req
        .set('X-Tenant-ID', tenant.id)
        .set('X-Subdomain', tenant.subdomain)
        .set('X-Original-Host', `${tenant.subdomain}.localhost`)
    }

    // Add authentication
    if (user) {
      req = req.set('Authorization', `Bearer ${user.token}`)
    }

    // Add custom headers
    Object.entries(headers).forEach(([key, value]) => {
      req = req.set(key, value)
    })

    // Add body for POST/PUT/PATCH requests
    if (body && ['post', 'put', 'patch'].includes(method)) {
      req = req.send(body)
    }

    const startTime = Date.now()
    const response = await req.expect(expectStatus)

    // Assert response time
    testAssertions.assertResponseTime(startTime, this.config.performance.maxResponseTime)

    // Assert API response structure
    testAssertions.assertApiResponse(response, expectStatus)

    return response
  }

  // Helper method to get tenant by subdomain
  protected getTenant(subdomain: string): TestTenant {
    const tenant = this.testContext.tenants.find(t => t.subdomain === subdomain)
    if (!tenant) {
      throw new Error(`Test tenant not found: ${subdomain}`)
    }
    return tenant
  }

  // Helper method to get user by email
  protected getUser(email: string): TestUser {
    const user = this.testContext.users.find(u => u.email === email)
    if (!user) {
      throw new Error(`Test user not found: ${email}`)
    }
    return user
  }

  // Helper method to get admin user for a tenant
  protected getAdminUser(tenantId: string): TestUser {
    const user = this.testContext.users.find(u => 
      u.tenantId === tenantId && u.roles?.includes('Admin')
    )
    if (!user) {
      throw new Error(`Admin user not found for tenant: ${tenantId}`)
    }
    return user
  }

  // Helper method to get regular user for a tenant
  protected getRegularUser(tenantId: string): TestUser {
    const user = this.testContext.users.find(u => 
      u.tenantId === tenantId && u.roles?.includes('User')
    )
    if (!user) {
      throw new Error(`Regular user not found for tenant: ${tenantId}`)
    }
    return user
  }

  // Test tenant isolation by ensuring data doesn't leak between tenants
  protected async assertTenantIsolation(
    endpoint: string,
    tenant1: TestTenant,
    tenant2: TestTenant,
    user1: TestUser,
    user2: TestUser
  ): Promise<void> {
    // Create data in tenant1
    const createResponse = await this.makeRequest('post', endpoint, {
      tenant: tenant1,
      user: user1,
      body: { name: 'Test Data for Tenant 1' }
    })

    const createdId = createResponse.body.data.id

    // Try to access data from tenant2 - should not be visible
    const getResponse = await this.makeRequest('get', `${endpoint}/${createdId}`, {
      tenant: tenant2,
      user: user2,
      expectStatus: 404
    })

    expect(getResponse.body.success).toBe(false)
  }

  // Test authentication requirements
  protected async assertAuthenticationRequired(
    method: 'get' | 'post' | 'put' | 'patch' | 'delete',
    path: string,
    tenant?: TestTenant
  ): Promise<void> {
    await this.makeRequest(method, path, {
      tenant,
      expectStatus: 401
    })
  }

  // Test authorization requirements
  protected async assertAuthorizationRequired(
    method: 'get' | 'post' | 'put' | 'patch' | 'delete',
    path: string,
    tenant: TestTenant,
    user: TestUser
  ): Promise<void> {
    await this.makeRequest(method, path, {
      tenant,
      user,
      expectStatus: 403
    })
  }

  // Test pagination
  protected async assertPagination(
    endpoint: string,
    tenant: TestTenant,
    user: TestUser,
    totalItems: number
  ): Promise<void> {
    // Test first page
    const page1Response = await this.makeRequest('get', `${endpoint}?page=1&limit=5`, {
      tenant,
      user
    })

    testAssertions.assertPagination(page1Response)
    expect(page1Response.body.data.pagination.page).toBe(1)
    expect(page1Response.body.data.pagination.limit).toBe(5)
    expect(page1Response.body.data.pagination.total).toBe(totalItems)

    // Test second page if there are enough items
    if (totalItems > 5) {
      const page2Response = await this.makeRequest('get', `${endpoint}?page=2&limit=5`, {
        tenant,
        user
      })

      testAssertions.assertPagination(page2Response)
      expect(page2Response.body.data.pagination.page).toBe(2)
    }
  }

  // Test input validation
  protected async assertValidationError(
    method: 'post' | 'put' | 'patch',
    path: string,
    invalidData: any,
    tenant: TestTenant,
    user: TestUser
  ): Promise<void> {
    const response = await this.makeRequest(method, path, {
      tenant,
      user,
      body: invalidData,
      expectStatus: 400
    })

    expect(response.body.success).toBe(false)
    expect(response.body.error.code).toBe('VALIDATION_ERROR')
    expect(response.body.error.details).toBeDefined()
  }

  // Test rate limiting
  protected async assertRateLimit(
    endpoint: string,
    tenant: TestTenant,
    user: TestUser,
    maxRequests: number = 10
  ): Promise<void> {
    const requests = []

    // Make multiple requests rapidly
    for (let i = 0; i < maxRequests + 5; i++) {
      requests.push(
        request(this.app)
          .get(endpoint)
          .set('X-Tenant-ID', tenant.id)
          .set('X-Subdomain', tenant.subdomain)
          .set('Authorization', `Bearer ${user.token}`)
      )
    }

    const responses = await Promise.allSettled(requests)
    
    // Some requests should be rate limited (429 status)
    const rateLimitedResponses = responses.filter(
      result => result.status === 'fulfilled' && 
      (result.value as any).status === 429
    )

    expect(rateLimitedResponses.length).toBeGreaterThan(0)
  }

  // Test CORS headers
  protected async assertCorsHeaders(endpoint: string): Promise<void> {
    const response = await request(this.app)
      .options(endpoint)
      .set('Origin', 'http://localhost:3000')
      .set('Access-Control-Request-Method', 'GET')

    expect(response.headers['access-control-allow-origin']).toBeDefined()
    expect(response.headers['access-control-allow-methods']).toBeDefined()
    expect(response.headers['access-control-allow-headers']).toBeDefined()
  }

  // Test error handling
  protected async assertErrorHandling(
    endpoint: string,
    tenant: TestTenant,
    user: TestUser
  ): Promise<void> {
    // Test with invalid ID format
    const invalidIdResponse = await this.makeRequest('get', `${endpoint}/invalid-id`, {
      tenant,
      user,
      expectStatus: 400
    })

    expect(invalidIdResponse.body.success).toBe(false)
    expect(invalidIdResponse.body.error).toBeDefined()

    // Test with non-existent ID
    const nonExistentResponse = await this.makeRequest('get', `${endpoint}/non-existent-id`, {
      tenant,
      user,
      expectStatus: 404
    })

    expect(nonExistentResponse.body.success).toBe(false)
    expect(nonExistentResponse.body.error.code).toBe('NOT_FOUND')
  }

  // Test content type handling
  protected async assertContentTypeHandling(
    endpoint: string,
    tenant: TestTenant,
    user: TestUser
  ): Promise<void> {
    // Test with invalid content type
    const response = await request(this.app)
      .post(endpoint)
      .set('Content-Type', 'text/plain')
      .set('X-Tenant-ID', tenant.id)
      .set('Authorization', `Bearer ${user.token}`)
      .send('invalid data')
      .expect(400)

    expect(response.body.success).toBe(false)
  }
}
