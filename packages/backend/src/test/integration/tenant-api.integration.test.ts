import { BaseApiTest } from './base-api.test'
import { TestHelpers } from '../utils/test-helpers'

describe('Tenant API Integration Tests', () => {
  let apiTest: BaseApiTest

  beforeAll(async () => {
    apiTest = new BaseApiTest()
    await apiTest.setup()
  })

  afterAll(async () => {
    await apiTest.cleanup()
  })

  describe('GET /api/tenants/current', () => {
    it('should return current tenant information', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const response = await apiTest.makeRequest('get', '/api/tenants/current', {
        tenant,
        user
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data).toMatchObject({
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        subdomain: tenant.subdomain,
        status: tenant.status,
        plan: tenant.plan
      })
    })

    it('should require authentication', async () => {
      const tenant = apiTest.getTenant('acme')
      
      await apiTest.assertAuthenticationRequired('get', '/api/tenants/current', tenant)
    })

    it('should require tenant context', async () => {
      const user = apiTest.getUser('<EMAIL>')

      await apiTest.makeRequest('get', '/api/tenants/current', {
        user,
        expectStatus: 400
      })
    })

    it('should enforce tenant isolation', async () => {
      const tenant1 = apiTest.getTenant('acme')
      const tenant2 = apiTest.getTenant('demo')
      const user1 = apiTest.getAdminUser(tenant1.id)
      const user2 = apiTest.getAdminUser(tenant2.id)

      // User from tenant1 should only see tenant1 data
      const response1 = await apiTest.makeRequest('get', '/api/tenants/current', {
        tenant: tenant1,
        user: user1
      })

      expect(response1.body.data.id).toBe(tenant1.id)

      // User from tenant2 should only see tenant2 data
      const response2 = await apiTest.makeRequest('get', '/api/tenants/current', {
        tenant: tenant2,
        user: user2
      })

      expect(response2.body.data.id).toBe(tenant2.id)
    })
  })

  describe('PUT /api/tenants/current', () => {
    it('should update tenant information', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const updateData = {
        description: 'Updated description',
        website: 'https://updated.example.com'
      }

      const response = await apiTest.makeRequest('put', '/api/tenants/current', {
        tenant,
        user,
        body: updateData
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data.description).toBe(updateData.description)
      expect(response.body.data.website).toBe(updateData.website)

      // Verify in database
      const updatedTenant = await apiTest['prisma'].tenant.findUnique({
        where: { id: tenant.id }
      })

      expect(updatedTenant?.description).toBe(updateData.description)
      expect(updatedTenant?.website).toBe(updateData.website)
    })

    it('should require admin permissions', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getRegularUser(tenant.id)

      await apiTest.assertAuthorizationRequired('put', '/api/tenants/current', tenant, user)
    })

    it('should validate input data', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const invalidData = {
        name: '', // Empty name should be invalid
        website: 'invalid-url' // Invalid URL format
      }

      await apiTest.assertValidationError('put', '/api/tenants/current', invalidData, tenant, user)
    })

    it('should not allow updating protected fields', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const protectedData = {
        id: 'new-id',
        slug: 'new-slug',
        subdomain: 'new-subdomain',
        status: 'DELETED'
      }

      const response = await apiTest.makeRequest('put', '/api/tenants/current', {
        tenant,
        user,
        body: protectedData
      })

      // Should succeed but ignore protected fields
      expect(response.body.success).toBe(true)
      expect(response.body.data.id).toBe(tenant.id) // Should not change
      expect(response.body.data.slug).toBe(tenant.slug) // Should not change
      expect(response.body.data.subdomain).toBe(tenant.subdomain) // Should not change
    })
  })

  describe('GET /api/tenants/stats', () => {
    it('should return tenant statistics', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      // Create some test data
      await TestHelpers.createTestDeal(tenant.id, { title: 'Test Deal 1' })
      await TestHelpers.createTestDeal(tenant.id, { title: 'Test Deal 2' })
      await TestHelpers.createTestDocument(tenant.id, undefined, { name: 'Test Doc 1' })

      const response = await apiTest.makeRequest('get', '/api/tenants/stats', {
        tenant,
        user
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveProperty('totalDeals')
      expect(response.body.data).toHaveProperty('totalDocuments')
      expect(response.body.data).toHaveProperty('totalUsers')
      expect(response.body.data).toHaveProperty('storageUsed')
      expect(response.body.data.totalDeals).toBeGreaterThanOrEqual(2)
      expect(response.body.data.totalDocuments).toBeGreaterThanOrEqual(1)
    })

    it('should enforce tenant isolation in statistics', async () => {
      const tenant1 = apiTest.getTenant('acme')
      const tenant2 = apiTest.getTenant('demo')
      const user1 = apiTest.getAdminUser(tenant1.id)
      const user2 = apiTest.getAdminUser(tenant2.id)

      // Create data in tenant1
      await TestHelpers.createTestDeal(tenant1.id, { title: 'Tenant 1 Deal' })

      // Create data in tenant2
      await TestHelpers.createTestDeal(tenant2.id, { title: 'Tenant 2 Deal' })

      // Get stats for tenant1
      const response1 = await apiTest.makeRequest('get', '/api/tenants/stats', {
        tenant: tenant1,
        user: user1
      })

      // Get stats for tenant2
      const response2 = await apiTest.makeRequest('get', '/api/tenants/stats', {
        tenant: tenant2,
        user: user2
      })

      // Stats should be different and isolated
      expect(response1.body.data).not.toEqual(response2.body.data)
    })

    it('should require manager or admin permissions', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getRegularUser(tenant.id)

      await apiTest.assertAuthorizationRequired('get', '/api/tenants/stats', tenant, user)
    })
  })

  describe('POST /api/tenants/invite', () => {
    it('should create tenant invitation', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const inviteData = {
        email: '<EMAIL>',
        role: 'User'
      }

      const response = await apiTest.makeRequest('post', '/api/tenants/invite', {
        tenant,
        user,
        body: inviteData
      })

      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveProperty('id')
      expect(response.body.data).toHaveProperty('token')
      expect(response.body.data.email).toBe(inviteData.email)
      expect(response.body.data.role).toBe(inviteData.role)
      expect(response.body.data.status).toBe('PENDING')

      // Verify in database
      const invitation = await apiTest['prisma'].tenantInvitation.findFirst({
        where: {
          tenantId: tenant.id,
          email: inviteData.email
        }
      })

      expect(invitation).toBeTruthy()
      expect(invitation?.status).toBe('PENDING')
    })

    it('should not allow duplicate invitations', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const inviteData = {
        email: '<EMAIL>',
        role: 'User'
      }

      // First invitation should succeed
      await apiTest.makeRequest('post', '/api/tenants/invite', {
        tenant,
        user,
        body: inviteData
      })

      // Second invitation should fail
      await apiTest.makeRequest('post', '/api/tenants/invite', {
        tenant,
        user,
        body: inviteData,
        expectStatus: 409
      })
    })

    it('should validate email format', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const invalidData = {
        email: 'invalid-email',
        role: 'User'
      }

      await apiTest.assertValidationError('post', '/api/tenants/invite', invalidData, tenant, user)
    })

    it('should require admin permissions', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getRegularUser(tenant.id)

      const inviteData = {
        email: '<EMAIL>',
        role: 'User'
      }

      await apiTest.makeRequest('post', '/api/tenants/invite', {
        tenant,
        user,
        body: inviteData,
        expectStatus: 403
      })
    })
  })

  describe('Tenant Isolation Tests', () => {
    it('should prevent cross-tenant data access', async () => {
      const tenant1 = apiTest.getTenant('acme')
      const tenant2 = apiTest.getTenant('demo')
      const user1 = apiTest.getAdminUser(tenant1.id)
      const user2 = apiTest.getAdminUser(tenant2.id)

      // Create invitation in tenant1
      const inviteResponse = await apiTest.makeRequest('post', '/api/tenants/invite', {
        tenant: tenant1,
        user: user1,
        body: { email: '<EMAIL>', role: 'User' }
      })

      const invitationId = inviteResponse.body.data.id

      // Try to access invitation from tenant2 - should fail
      await apiTest.makeRequest('get', `/api/tenants/invitations/${invitationId}`, {
        tenant: tenant2,
        user: user2,
        expectStatus: 404
      })
    })

    it('should isolate tenant statistics', async () => {
      const tenant1 = apiTest.getTenant('acme')
      const tenant2 = apiTest.getTenant('demo')
      const user1 = apiTest.getAdminUser(tenant1.id)
      const user2 = apiTest.getAdminUser(tenant2.id)

      // Create different amounts of data in each tenant
      await TestHelpers.createTestDeal(tenant1.id, { title: 'T1 Deal 1' })
      await TestHelpers.createTestDeal(tenant1.id, { title: 'T1 Deal 2' })
      await TestHelpers.createTestDeal(tenant1.id, { title: 'T1 Deal 3' })

      await TestHelpers.createTestDeal(tenant2.id, { title: 'T2 Deal 1' })

      // Get stats for each tenant
      const stats1 = await apiTest.makeRequest('get', '/api/tenants/stats', {
        tenant: tenant1,
        user: user1
      })

      const stats2 = await apiTest.makeRequest('get', '/api/tenants/stats', {
        tenant: tenant2,
        user: user2
      })

      // Stats should reflect only each tenant's data
      expect(stats1.body.data.totalDeals).toBeGreaterThanOrEqual(3)
      expect(stats2.body.data.totalDeals).toBeGreaterThanOrEqual(1)
      expect(stats1.body.data.totalDeals).not.toBe(stats2.body.data.totalDeals)
    })
  })

  describe('Performance Tests', () => {
    it('should handle concurrent requests efficiently', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const requests = Array.from({ length: 10 }, () =>
        apiTest.makeRequest('get', '/api/tenants/current', { tenant, user })
      )

      const startTime = Date.now()
      const responses = await Promise.all(requests)
      const duration = Date.now() - startTime

      // All requests should succeed
      responses.forEach(response => {
        expect(response.body.success).toBe(true)
      })

      // Should complete within reasonable time
      expect(duration).toBeLessThan(5000) // 5 seconds for 10 requests
    })

    it('should respond within acceptable time limits', async () => {
      const tenant = apiTest.getTenant('acme')
      const user = apiTest.getAdminUser(tenant.id)

      const startTime = Date.now()
      await apiTest.makeRequest('get', '/api/tenants/current', { tenant, user })
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(1000) // Should respond within 1 second
    })
  })
})
