import request from 'supertest'
import { Express } from 'express'
import { PrismaClient } from '@prisma/client'
import { createApp } from '@/app'
import { PerformanceTestUtils, PERFORMANCE_THRESHOLDS } from '../setup/performance-setup'
import { TestHelpers } from '../utils/test-helpers'

describe('API Performance Tests', () => {
  let app: Express
  let prisma: PrismaClient
  let tenant: any
  let adminUser: any

  beforeAll(async () => {
    app = createApp()
    prisma = global.__PRISMA__
    
    // Create test tenant and user
    tenant = await TestHelpers.createTestTenant({
      name: 'Performance Test Tenant',
      subdomain: 'performance',
      slug: 'performance-test'
    })

    adminUser = await TestHelpers.createTestUser(tenant.id, {
      email: '<EMAIL>',
      firstName: 'Performance',
      lastName: 'Admin',
      roles: ['Admin']
    })

    // Create test data for performance testing
    await createPerformanceTestData()
  })

  afterAll(async () => {
    await TestHelpers.cleanupTestData()
  })

  describe('Authentication Performance', () => {
    it('should handle login requests within acceptable time', async () => {
      const loginRequest = () =>
        request(app)
          .post('/api/auth/login')
          .set('X-Tenant-ID', tenant.id)
          .set('X-Subdomain', tenant.subdomain)
          .send({
            email: adminUser.email,
            password: 'password123'
          })
          .expect(200)

      await expect(loginRequest()).toCompleteWithin(PERFORMANCE_THRESHOLDS.API_RESPONSE_TIME)
    })

    it('should handle concurrent login requests efficiently', async () => {
      const concurrentLogins = () =>
        request(app)
          .post('/api/auth/login')
          .set('X-Tenant-ID', tenant.id)
          .set('X-Subdomain', tenant.subdomain)
          .send({
            email: adminUser.email,
            password: 'password123'
          })
          .expect(200)

      await expect(concurrentLogins).toHandleConcurrentRequests(10, PERFORMANCE_THRESHOLDS.CONCURRENT_REQUESTS)
    })

    it('should not have memory leaks during repeated authentication', async () => {
      const authRequest = async () => {
        await request(app)
          .post('/api/auth/login')
          .set('X-Tenant-ID', tenant.id)
          .set('X-Subdomain', tenant.subdomain)
          .send({
            email: adminUser.email,
            password: 'password123'
          })
          .expect(200)
      }

      const memoryTest = PerformanceTestUtils.createMemoryLeakTest(authRequest, 50)
      const result = await memoryTest()

      expect(result.memoryIncrease.heapUsed).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_LEAK_THRESHOLD)
    })
  })

  describe('Deal API Performance', () => {
    it('should list deals within acceptable time', async () => {
      const listDealsRequest = () =>
        request(app)
          .get('/api/deals')
          .set('Authorization', `Bearer ${adminUser.token}`)
          .set('X-Tenant-ID', tenant.id)
          .set('X-Subdomain', tenant.subdomain)
          .expect(200)

      await expect(listDealsRequest()).toCompleteWithin(PERFORMANCE_THRESHOLDS.API_RESPONSE_TIME)
    })

    it('should handle paginated deal requests efficiently', async () => {
      const { durations } = await PerformanceTestUtils.measureMultipleExecutions(
        () =>
          request(app)
            .get('/api/deals?page=1&limit=20')
            .set('Authorization', `Bearer ${adminUser.token}`)
            .set('X-Tenant-ID', tenant.id)
            .set('X-Subdomain', tenant.subdomain)
            .expect(200),
        10
      )

      expect(durations).toHaveAverageResponseTime(PERFORMANCE_THRESHOLDS.API_RESPONSE_TIME)
    })

    it('should handle deal search efficiently', async () => {
      const searchRequest = () =>
        request(app)
          .get('/api/deals?search=Performance&type=ACQUISITION')
          .set('Authorization', `Bearer ${adminUser.token}`)
          .set('X-Tenant-ID', tenant.id)
          .set('X-Subdomain', tenant.subdomain)
          .expect(200)

      await expect(searchRequest()).toCompleteWithin(PERFORMANCE_THRESHOLDS.API_RESPONSE_TIME)
    })

    it('should create deals within acceptable time', async () => {
      const createDealRequest = () =>
        request(app)
          .post('/api/deals')
          .set('Authorization', `Bearer ${adminUser.token}`)
          .set('X-Tenant-ID', tenant.id)
          .set('X-Subdomain', tenant.subdomain)
          .send({
            title: `Performance Deal ${Date.now()}`,
            type: 'ACQUISITION',
            status: 'ACTIVE',
            targetCompany: 'Performance Target',
            dealValue: 1000000,
            currency: 'USD'
          })
          .expect(201)

      await expect(createDealRequest()).toCompleteWithin(PERFORMANCE_THRESHOLDS.API_RESPONSE_TIME)
    })
  })

  describe('Document API Performance', () => {
    it('should list documents within acceptable time', async () => {
      const listDocumentsRequest = () =>
        request(app)
          .get('/api/documents')
          .set('Authorization', `Bearer ${adminUser.token}`)
          .set('X-Tenant-ID', tenant.id)
          .set('X-Subdomain', tenant.subdomain)
          .expect(200)

      await expect(listDocumentsRequest()).toCompleteWithin(PERFORMANCE_THRESHOLDS.API_RESPONSE_TIME)
    })

    it('should handle document upload simulation efficiently', async () => {
      const uploadDocumentRequest = () =>
        request(app)
          .post('/api/documents')
          .set('Authorization', `Bearer ${adminUser.token}`)
          .set('X-Tenant-ID', tenant.id)
          .set('X-Subdomain', tenant.subdomain)
          .send({
            name: `Performance Document ${Date.now()}.pdf`,
            type: 'CONTRACT',
            size: 1024000,
            mimeType: 'application/pdf'
          })
          .expect(201)

      await expect(uploadDocumentRequest()).toCompleteWithin(PERFORMANCE_THRESHOLDS.API_RESPONSE_TIME)
    })
  })

  describe('Database Performance', () => {
    it('should execute tenant-filtered queries efficiently', async () => {
      const queryExecution = async () => {
        await prisma.deal.findMany({
          where: { tenantId: tenant.id },
          take: 50,
          include: {
            documents: true,
            valuations: true
          }
        })
      }

      await expect(queryExecution()).toCompleteWithin(PERFORMANCE_THRESHOLDS.DATABASE_QUERY_TIME)
    })

    it('should handle complex aggregation queries efficiently', async () => {
      const aggregationQuery = async () => {
        await prisma.deal.groupBy({
          by: ['status', 'type'],
          where: { tenantId: tenant.id },
          _count: { id: true },
          _sum: { dealValue: true },
          _avg: { dealValue: true }
        })
      }

      await expect(aggregationQuery()).toCompleteWithin(PERFORMANCE_THRESHOLDS.DATABASE_QUERY_TIME)
    })

    it('should handle concurrent database operations', async () => {
      const concurrentQuery = () =>
        prisma.deal.findMany({
          where: { tenantId: tenant.id },
          take: 10
        })

      await expect(concurrentQuery).toHandleConcurrentRequests(20, PERFORMANCE_THRESHOLDS.CONCURRENT_REQUESTS)
    })
  })

  describe('Load Testing', () => {
    it('should handle sustained load on health endpoint', async () => {
      const healthRequest = () =>
        request(app)
          .get('/health')
          .expect(200)

      const loadTest = PerformanceTestUtils.createLoadTest(healthRequest, {
        duration: 10000, // 10 seconds
        concurrency: 5,
        rampUpTime: 2000 // 2 seconds ramp up
      })

      const result = await loadTest()

      expect(result.successRate).toBeGreaterThan(95) // 95% success rate
      expect(result.requestsPerSecond).toBeGreaterThan(10) // At least 10 RPS
    })

    it('should handle sustained load on authenticated endpoints', async () => {
      const authenticatedRequest = () =>
        request(app)
          .get('/api/deals?limit=5')
          .set('Authorization', `Bearer ${adminUser.token}`)
          .set('X-Tenant-ID', tenant.id)
          .set('X-Subdomain', tenant.subdomain)
          .expect(200)

      const loadTest = PerformanceTestUtils.createLoadTest(authenticatedRequest, {
        duration: 5000, // 5 seconds
        concurrency: 3
      })

      const result = await loadTest()

      expect(result.successRate).toBeGreaterThan(90) // 90% success rate
      expect(result.requestsPerSecond).toBeGreaterThan(5) // At least 5 RPS
    })
  })

  // Helper function to create test data
  async function createPerformanceTestData() {
    // Create deals for performance testing
    const deals = Array.from({ length: 100 }, (_, i) => ({
      title: `Performance Deal ${i}`,
      type: i % 2 === 0 ? 'ACQUISITION' as const : 'MERGER' as const,
      status: 'ACTIVE' as const,
      targetCompany: `Performance Target ${i}`,
      dealValue: 1000000 + (i * 100000),
      currency: 'USD',
      tenantId: tenant.id
    }))

    await prisma.deal.createMany({ data: deals })

    // Create documents for performance testing
    const createdDeals = await prisma.deal.findMany({
      where: { tenantId: tenant.id },
      take: 50
    })

    const documents = createdDeals.flatMap((deal, dealIndex) =>
      Array.from({ length: 3 }, (_, docIndex) => ({
        name: `Performance Document ${dealIndex}-${docIndex}.pdf`,
        type: docIndex % 3 === 0 ? 'CONTRACT' as const : 
              docIndex % 3 === 1 ? 'FINANCIAL' as const : 'LEGAL' as const,
        size: 1024 * (docIndex + 1),
        mimeType: 'application/pdf',
        path: `/performance/docs/doc-${dealIndex}-${docIndex}.pdf`,
        tenantId: tenant.id,
        dealId: deal.id
      }))
    )

    await prisma.document.createMany({ data: documents })

    console.log(`Created ${deals.length} deals and ${documents.length} documents for performance testing`)
  }
})
