import { PrismaClient } from '@prisma/client'
import { DatabaseTestUtils } from './database-test-utils'
import { TestHelpers } from '../utils/test-helpers'

describe('Database Performance Integration Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = global.__PRISMA__
    DatabaseTestUtils.initialize(prisma)
  })

  beforeEach(async () => {
    // Clean database before each test
    await prisma.migrationRecord.deleteMany()
    await prisma.auditLog.deleteMany()
    await prisma.session.deleteMany()
    await prisma.userRole.deleteMany()
    await prisma.document.deleteMany()
    await prisma.dueDiligenceItem.deleteMany()
    await prisma.valuation.deleteMany()
    await prisma.deal.deleteMany()
    await prisma.tenantApiKey.deleteMany()
    await prisma.tenantInvitation.deleteMany()
    await prisma.user.deleteMany()
    await prisma.role.deleteMany()
    await prisma.tenant.deleteMany()
  })

  describe('Query Performance', () => {
    it('should perform tenant-filtered queries efficiently', async () => {
      const tenant = await TestHelpers.createTestTenant()
      
      // Create large dataset
      await DatabaseTestUtils.generateTestData(tenant.id, {
        users: 100,
        deals: 500,
        documents: 1000
      })

      // Test various tenant-filtered queries
      const queries = [
        {
          name: 'Find all deals for tenant',
          query: () => prisma.deal.findMany({ where: { tenantId: tenant.id } }),
          maxTime: 500
        },
        {
          name: 'Find deals with pagination',
          query: () => prisma.deal.findMany({
            where: { tenantId: tenant.id },
            take: 20,
            skip: 0,
            orderBy: { createdAt: 'desc' }
          }),
          maxTime: 200
        },
        {
          name: 'Find deals with status filter',
          query: () => prisma.deal.findMany({
            where: { 
              tenantId: tenant.id,
              status: 'ACTIVE'
            }
          }),
          maxTime: 300
        },
        {
          name: 'Find deals with documents',
          query: () => prisma.deal.findMany({
            where: { tenantId: tenant.id },
            include: { documents: true },
            take: 10
          }),
          maxTime: 400
        },
        {
          name: 'Count deals by status',
          query: () => prisma.deal.groupBy({
            by: ['status'],
            where: { tenantId: tenant.id },
            _count: { id: true }
          }),
          maxTime: 300
        }
      ]

      for (const { name, query, maxTime } of queries) {
        const startTime = Date.now()
        const result = await query()
        const duration = Date.now() - startTime

        console.log(`${name}: ${duration}ms`)
        expect(duration).toBeLessThan(maxTime)
        expect(result).toBeDefined()
      }
    })

    it('should handle complex joins efficiently', async () => {
      const tenant = await TestHelpers.createTestTenant()
      
      // Create test data with relationships
      const users = await Promise.all(
        Array.from({ length: 10 }, (_, i) =>
          TestHelpers.createTestUser(tenant.id, { email: `user${i}@test.com` })
        )
      )

      const deals = await Promise.all(
        Array.from({ length: 50 }, (_, i) =>
          TestHelpers.createTestDeal(tenant.id, { title: `Deal ${i}` })
        )
      )

      const documents = await Promise.all(
        deals.flatMap((deal, dealIndex) =>
          Array.from({ length: 5 }, (_, docIndex) =>
            TestHelpers.createTestDocument(tenant.id, deal.id, {
              name: `Doc ${dealIndex}-${docIndex}`
            })
          )
        )
      )

      // Test complex join query
      const startTime = Date.now()
      const result = await prisma.tenant.findUnique({
        where: { id: tenant.id },
        include: {
          users: {
            include: {
              roles: {
                include: {
                  role: true
                }
              }
            }
          },
          deals: {
            include: {
              documents: true,
              valuations: true
            },
            take: 10
          }
        }
      })
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(1000) // Should complete within 1 second
      expect(result).toBeTruthy()
      expect(result?.users).toHaveLength(10)
      expect(result?.deals).toHaveLength(10)
    })

    it('should optimize search queries', async () => {
      const tenant = await TestHelpers.createTestTenant()
      
      // Create searchable data
      const searchTerms = ['acquisition', 'merger', 'buyout', 'investment', 'partnership']
      const deals = await Promise.all(
        Array.from({ length: 200 }, (_, i) => {
          const term = searchTerms[i % searchTerms.length]
          return TestHelpers.createTestDeal(tenant.id, {
            title: `${term} deal ${i}`,
            targetCompany: `${term} target ${i}`,
            description: `This is a ${term} deal for testing search functionality`
          })
        })
      )

      // Test search queries
      const searchQueries = [
        {
          name: 'Search by title',
          query: () => prisma.deal.findMany({
            where: {
              tenantId: tenant.id,
              title: { contains: 'acquisition', mode: 'insensitive' }
            }
          }),
          maxTime: 300
        },
        {
          name: 'Search by target company',
          query: () => prisma.deal.findMany({
            where: {
              tenantId: tenant.id,
              targetCompany: { contains: 'merger', mode: 'insensitive' }
            }
          }),
          maxTime: 300
        },
        {
          name: 'Full text search simulation',
          query: () => prisma.deal.findMany({
            where: {
              tenantId: tenant.id,
              OR: [
                { title: { contains: 'investment', mode: 'insensitive' } },
                { targetCompany: { contains: 'investment', mode: 'insensitive' } },
                { description: { contains: 'investment', mode: 'insensitive' } }
              ]
            }
          }),
          maxTime: 500
        }
      ]

      for (const { name, query, maxTime } of searchQueries) {
        const startTime = Date.now()
        const result = await query()
        const duration = Date.now() - startTime

        console.log(`${name}: ${duration}ms, results: ${result.length}`)
        expect(duration).toBeLessThan(maxTime)
        expect(result.length).toBeGreaterThan(0)
      }
    })
  })

  describe('Bulk Operations Performance', () => {
    it('should handle bulk inserts efficiently', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Test bulk insert of deals
      const dealData = Array.from({ length: 1000 }, (_, i) => ({
        title: `Bulk Deal ${i}`,
        type: i % 2 === 0 ? 'ACQUISITION' as const : 'MERGER' as const,
        status: 'ACTIVE' as const,
        targetCompany: `Target ${i}`,
        dealValue: 1000000 + i,
        currency: 'USD',
        tenantId: tenant.id
      }))

      const startTime = Date.now()
      await prisma.deal.createMany({ data: dealData })
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds

      // Verify all records were created
      const count = await prisma.deal.count({ where: { tenantId: tenant.id } })
      expect(count).toBe(1000)
    })

    it('should handle bulk updates efficiently', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Create initial data
      await DatabaseTestUtils.generateTestData(tenant.id, { deals: 500 })

      // Test bulk update
      const startTime = Date.now()
      await prisma.deal.updateMany({
        where: { 
          tenantId: tenant.id,
          status: 'ACTIVE'
        },
        data: { 
          status: 'UNDER_REVIEW'
        }
      })
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(2000) // Should complete within 2 seconds

      // Verify updates were applied
      const updatedCount = await prisma.deal.count({
        where: { 
          tenantId: tenant.id,
          status: 'UNDER_REVIEW'
        }
      })
      expect(updatedCount).toBeGreaterThan(0)
    })

    it('should handle bulk deletes efficiently', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Create test data
      await DatabaseTestUtils.generateTestData(tenant.id, { 
        deals: 300,
        documents: 600
      })

      // Test bulk delete of documents
      const startTime = Date.now()
      await prisma.document.deleteMany({
        where: { tenantId: tenant.id }
      })
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(3000) // Should complete within 3 seconds

      // Verify deletion
      const remainingCount = await prisma.document.count({
        where: { tenantId: tenant.id }
      })
      expect(remainingCount).toBe(0)
    })
  })

  describe('Concurrent Access Performance', () => {
    it('should handle concurrent reads efficiently', async () => {
      const tenant = await TestHelpers.createTestTenant()
      
      // Create test data
      await DatabaseTestUtils.generateTestData(tenant.id, {
        deals: 100,
        documents: 200
      })

      // Simulate concurrent read operations
      const concurrentReads = Array.from({ length: 20 }, () =>
        prisma.deal.findMany({
          where: { tenantId: tenant.id },
          include: { documents: true },
          take: 10
        })
      )

      const startTime = Date.now()
      const results = await Promise.all(concurrentReads)
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(3000) // Should complete within 3 seconds
      expect(results).toHaveLength(20)
      results.forEach(result => {
        expect(result).toHaveLength(10)
      })
    })

    it('should handle concurrent writes efficiently', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Simulate concurrent write operations
      const concurrentWrites = Array.from({ length: 50 }, (_, i) =>
        TestHelpers.createTestDeal(tenant.id, {
          title: `Concurrent Deal ${i}`,
          targetCompany: `Target ${i}`
        })
      )

      const startTime = Date.now()
      const results = await Promise.all(concurrentWrites)
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(10000) // Should complete within 10 seconds
      expect(results).toHaveLength(50)

      // Verify all deals were created
      const count = await prisma.deal.count({ where: { tenantId: tenant.id } })
      expect(count).toBe(50)
    })

    it('should handle mixed read/write operations', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Create initial data
      await DatabaseTestUtils.generateTestData(tenant.id, { deals: 50 })

      // Mix of read and write operations
      const operations = [
        // Reads
        ...Array.from({ length: 10 }, () => 
          prisma.deal.findMany({ where: { tenantId: tenant.id }, take: 5 })
        ),
        // Writes
        ...Array.from({ length: 10 }, (_, i) =>
          TestHelpers.createTestDeal(tenant.id, { title: `Mixed Deal ${i}` })
        ),
        // Updates
        ...Array.from({ length: 5 }, (_, i) =>
          prisma.deal.updateMany({
            where: { 
              tenantId: tenant.id,
              title: { contains: 'Test Deal' }
            },
            data: { status: 'UNDER_REVIEW' }
          })
        )
      ]

      const startTime = Date.now()
      const results = await Promise.allSettled(operations)
      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(8000) // Should complete within 8 seconds
      
      // Most operations should succeed
      const successful = results.filter(r => r.status === 'fulfilled')
      expect(successful.length).toBeGreaterThan(20)
    })
  })

  describe('Memory and Resource Usage', () => {
    it('should handle large result sets efficiently', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Create large dataset
      await DatabaseTestUtils.generateTestData(tenant.id, {
        deals: 1000,
        documents: 2000
      })

      // Test streaming large result sets
      const startTime = Date.now()
      let count = 0
      
      // Use cursor-based pagination to handle large datasets
      let cursor: string | undefined
      const batchSize = 100

      do {
        const batch = await prisma.deal.findMany({
          where: { tenantId: tenant.id },
          take: batchSize,
          ...(cursor && { 
            cursor: { id: cursor },
            skip: 1 
          }),
          orderBy: { id: 'asc' }
        })

        count += batch.length
        cursor = batch.length === batchSize ? batch[batch.length - 1].id : undefined
      } while (cursor)

      const duration = Date.now() - startTime

      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds
      expect(count).toBe(1000)
    })

    it('should optimize memory usage for aggregations', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Create data for aggregation
      await DatabaseTestUtils.generateTestData(tenant.id, { deals: 500 })

      // Test various aggregation queries
      const aggregations = [
        {
          name: 'Count by status',
          query: () => prisma.deal.groupBy({
            by: ['status'],
            where: { tenantId: tenant.id },
            _count: { id: true }
          })
        },
        {
          name: 'Sum deal values',
          query: () => prisma.deal.aggregate({
            where: { tenantId: tenant.id },
            _sum: { dealValue: true },
            _avg: { dealValue: true },
            _count: { id: true }
          })
        },
        {
          name: 'Monthly deal counts',
          query: () => prisma.$queryRaw`
            SELECT 
              DATE_TRUNC('month', "createdAt") as month,
              COUNT(*) as count
            FROM "Deal" 
            WHERE "tenantId" = ${tenant.id}
            GROUP BY DATE_TRUNC('month', "createdAt")
            ORDER BY month
          `
        }
      ]

      for (const { name, query } of aggregations) {
        const startTime = Date.now()
        const result = await query()
        const duration = Date.now() - startTime

        console.log(`${name}: ${duration}ms`)
        expect(duration).toBeLessThan(1000) // Should complete within 1 second
        expect(result).toBeDefined()
      }
    })
  })

  describe('Index Effectiveness', () => {
    it('should use tenant indexes effectively', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Create large dataset to test index usage
      await DatabaseTestUtils.generateTestData(tenant.id, {
        users: 200,
        deals: 1000,
        documents: 2000
      })

      // Test queries that should use indexes
      const indexedQueries = [
        {
          name: 'Query by tenantId',
          query: () => prisma.deal.findMany({ where: { tenantId: tenant.id } })
        },
        {
          name: 'Query by tenantId and status',
          query: () => prisma.deal.findMany({ 
            where: { tenantId: tenant.id, status: 'ACTIVE' } 
          })
        },
        {
          name: 'Query by tenantId with ordering',
          query: () => prisma.deal.findMany({ 
            where: { tenantId: tenant.id },
            orderBy: { createdAt: 'desc' },
            take: 20
          })
        },
        {
          name: 'User by email and tenant',
          query: () => prisma.user.findFirst({
            where: { 
              tenantId: tenant.id,
              email: { contains: 'testuser1' }
            }
          })
        }
      ]

      for (const { name, query } of indexedQueries) {
        const startTime = Date.now()
        await query()
        const duration = Date.now() - startTime

        console.log(`${name}: ${duration}ms`)
        expect(duration).toBeLessThan(200) // Indexed queries should be very fast
      }
    })

    it('should identify slow queries without proper indexes', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Create test data
      await DatabaseTestUtils.generateTestData(tenant.id, { deals: 500 })

      // Test potentially slow queries (without proper indexes)
      const slowQueries = [
        {
          name: 'Search by description (no index)',
          query: () => prisma.deal.findMany({
            where: {
              tenantId: tenant.id,
              description: { contains: 'specific text' }
            }
          }),
          maxTime: 1000 // Allow more time for non-indexed queries
        }
      ]

      for (const { name, query, maxTime } of slowQueries) {
        const startTime = Date.now()
        await query()
        const duration = Date.now() - startTime

        console.log(`${name}: ${duration}ms`)
        expect(duration).toBeLessThan(maxTime)
      }
    })
  })
})
