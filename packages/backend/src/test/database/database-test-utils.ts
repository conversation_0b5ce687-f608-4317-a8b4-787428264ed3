import { PrismaClient } from '@prisma/client'
import { TestHelpers, TestTenant, TestUser } from '../utils/test-helpers'

export interface DatabaseTestContext {
  prisma: PrismaClient
  tenants: TestTenant[]
  users: TestUser[]
  cleanup: () => Promise<void>
}

export class DatabaseTestUtils {
  private static prisma: PrismaClient

  static initialize(prisma: PrismaClient) {
    this.prisma = prisma
  }

  // Test tenant data isolation
  static async testTenantDataIsolation(
    createDataFn: (tenantId: string) => Promise<any>,
    findDataFn: (tenantId: string) => Promise<any[]>
  ): Promise<void> {
    const tenant1 = await TestHelpers.createTestTenant({ subdomain: 'isolation1' })
    const tenant2 = await TestHelpers.createTestTenant({ subdomain: 'isolation2' })

    // Create data in tenant1
    const data1 = await createDataFn(tenant1.id)
    
    // Create data in tenant2
    const data2 = await createDataFn(tenant2.id)

    // Verify tenant1 only sees its data
    const tenant1Data = await findDataFn(tenant1.id)
    expect(tenant1Data.every(item => item.tenantId === tenant1.id)).toBe(true)
    expect(tenant1Data.some(item => item.id === data1.id)).toBe(true)
    expect(tenant1Data.some(item => item.id === data2.id)).toBe(false)

    // Verify tenant2 only sees its data
    const tenant2Data = await findDataFn(tenant2.id)
    expect(tenant2Data.every(item => item.tenantId === tenant2.id)).toBe(true)
    expect(tenant2Data.some(item => item.id === data2.id)).toBe(true)
    expect(tenant2Data.some(item => item.id === data1.id)).toBe(false)
  }

  // Test database constraints
  static async testForeignKeyConstraints(): Promise<void> {
    const tenant = await TestHelpers.createTestTenant()

    // Test that deleting a tenant cascades to related data
    const user = await TestHelpers.createTestUser(tenant.id)
    const deal = await TestHelpers.createTestDeal(tenant.id)

    // Delete tenant
    await this.prisma.tenant.delete({ where: { id: tenant.id } })

    // Verify related data was deleted
    const remainingUser = await this.prisma.user.findUnique({ where: { id: user.id } })
    const remainingDeal = await this.prisma.deal.findUnique({ where: { id: deal.id } })

    expect(remainingUser).toBeNull()
    expect(remainingDeal).toBeNull()
  }

  // Test unique constraints
  static async testUniqueConstraints(): Promise<void> {
    const tenant1 = await TestHelpers.createTestTenant({ subdomain: 'unique1' })
    
    // Try to create another tenant with same subdomain
    await expect(
      TestHelpers.createTestTenant({ subdomain: 'unique1' })
    ).rejects.toThrow()

    // Try to create user with same email in same tenant
    const user1 = await TestHelpers.createTestUser(tenant1.id, { email: '<EMAIL>' })
    
    await expect(
      TestHelpers.createTestUser(tenant1.id, { email: '<EMAIL>' })
    ).rejects.toThrow()
  }

  // Test database transactions
  static async testTransactionRollback(): Promise<void> {
    const tenant = await TestHelpers.createTestTenant()

    try {
      await this.prisma.$transaction(async (tx) => {
        // Create a user
        await tx.user.create({
          data: {
            email: '<EMAIL>',
            firstName: 'Transaction',
            lastName: 'Test',
            password: 'hashedpassword',
            tenantId: tenant.id
          }
        })

        // Create a deal
        await tx.deal.create({
          data: {
            title: 'Transaction Deal',
            type: 'ACQUISITION',
            status: 'ACTIVE',
            targetCompany: 'Target Corp',
            dealValue: 1000000,
            currency: 'USD',
            tenantId: tenant.id
          }
        })

        // Force an error to trigger rollback
        throw new Error('Forced rollback')
      })
    } catch (error) {
      // Expected error
    }

    // Verify no data was created due to rollback
    const users = await this.prisma.user.findMany({
      where: { email: '<EMAIL>' }
    })
    const deals = await this.prisma.deal.findMany({
      where: { title: 'Transaction Deal' }
    })

    expect(users).toHaveLength(0)
    expect(deals).toHaveLength(0)
  }

  // Test database performance with large datasets
  static async testPerformanceWithLargeDataset(recordCount: number = 1000): Promise<void> {
    const tenant = await TestHelpers.createTestTenant()

    // Create large dataset
    const startTime = Date.now()
    
    const deals = Array.from({ length: recordCount }, (_, i) => ({
      title: `Performance Deal ${i}`,
      type: 'ACQUISITION' as const,
      status: 'ACTIVE' as const,
      targetCompany: `Target ${i}`,
      dealValue: 1000000 + i,
      currency: 'USD',
      tenantId: tenant.id
    }))

    await this.prisma.deal.createMany({ data: deals })
    
    const createTime = Date.now() - startTime

    // Test query performance
    const queryStartTime = Date.now()
    const results = await this.prisma.deal.findMany({
      where: { tenantId: tenant.id },
      take: 100,
      orderBy: { createdAt: 'desc' }
    })
    const queryTime = Date.now() - queryStartTime

    // Assert performance expectations
    expect(createTime).toBeLessThan(10000) // 10 seconds for 1000 records
    expect(queryTime).toBeLessThan(1000) // 1 second for query
    expect(results).toHaveLength(100)
  }

  // Test database indexes effectiveness
  static async testIndexEffectiveness(): Promise<void> {
    const tenant = await TestHelpers.createTestTenant()

    // Create test data
    const deals = Array.from({ length: 100 }, (_, i) => ({
      title: `Index Test Deal ${i}`,
      type: 'ACQUISITION' as const,
      status: i % 2 === 0 ? 'ACTIVE' as const : 'COMPLETED' as const,
      targetCompany: `Target ${i}`,
      dealValue: 1000000 + i,
      currency: 'USD',
      tenantId: tenant.id
    }))

    await this.prisma.deal.createMany({ data: deals })

    // Test indexed queries (should be fast)
    const indexedQueries = [
      // Query by tenantId (should be indexed)
      () => this.prisma.deal.findMany({ where: { tenantId: tenant.id } }),
      
      // Query by tenantId + status (composite index)
      () => this.prisma.deal.findMany({ 
        where: { tenantId: tenant.id, status: 'ACTIVE' } 
      }),
      
      // Query by tenantId + createdAt (for pagination)
      () => this.prisma.deal.findMany({ 
        where: { tenantId: tenant.id },
        orderBy: { createdAt: 'desc' },
        take: 10
      })
    ]

    for (const query of indexedQueries) {
      const startTime = Date.now()
      await query()
      const duration = Date.now() - startTime
      
      // Indexed queries should be fast
      expect(duration).toBeLessThan(100) // 100ms
    }
  }

  // Test concurrent database operations
  static async testConcurrentOperations(): Promise<void> {
    const tenant = await TestHelpers.createTestTenant()

    // Create multiple concurrent operations
    const operations = Array.from({ length: 10 }, (_, i) => 
      this.prisma.deal.create({
        data: {
          title: `Concurrent Deal ${i}`,
          type: 'ACQUISITION',
          status: 'ACTIVE',
          targetCompany: `Target ${i}`,
          dealValue: 1000000 + i,
          currency: 'USD',
          tenantId: tenant.id
        }
      })
    )

    const startTime = Date.now()
    const results = await Promise.all(operations)
    const duration = Date.now() - startTime

    // All operations should succeed
    expect(results).toHaveLength(10)
    results.forEach(result => {
      expect(result.id).toBeDefined()
      expect(result.tenantId).toBe(tenant.id)
    })

    // Should complete within reasonable time
    expect(duration).toBeLessThan(5000) // 5 seconds
  }

  // Test data consistency across operations
  static async testDataConsistency(): Promise<void> {
    const tenant = await TestHelpers.createTestTenant()
    const user = await TestHelpers.createTestUser(tenant.id)

    // Create a deal
    const deal = await this.prisma.deal.create({
      data: {
        title: 'Consistency Test Deal',
        type: 'ACQUISITION',
        status: 'ACTIVE',
        targetCompany: 'Target Corp',
        dealValue: 1000000,
        currency: 'USD',
        tenantId: tenant.id
      }
    })

    // Create documents for the deal
    const documents = await Promise.all([
      this.prisma.document.create({
        data: {
          name: 'Document 1',
          type: 'CONTRACT',
          size: 1024,
          mimeType: 'application/pdf',
          path: '/test/doc1.pdf',
          tenantId: tenant.id,
          dealId: deal.id
        }
      }),
      this.prisma.document.create({
        data: {
          name: 'Document 2',
          type: 'FINANCIAL',
          size: 2048,
          mimeType: 'application/pdf',
          path: '/test/doc2.pdf',
          tenantId: tenant.id,
          dealId: deal.id
        }
      })
    ])

    // Verify relationships are consistent
    const dealWithDocuments = await this.prisma.deal.findUnique({
      where: { id: deal.id },
      include: { documents: true }
    })

    expect(dealWithDocuments?.documents).toHaveLength(2)
    expect(dealWithDocuments?.documents.every(doc => doc.dealId === deal.id)).toBe(true)
    expect(dealWithDocuments?.documents.every(doc => doc.tenantId === tenant.id)).toBe(true)
  }

  // Test soft delete functionality
  static async testSoftDelete(): Promise<void> {
    const tenant = await TestHelpers.createTestTenant()

    // Update tenant to be soft deleted
    await this.prisma.tenant.update({
      where: { id: tenant.id },
      data: { 
        status: 'DELETED',
        deletedAt: new Date()
      }
    })

    // Verify tenant still exists in database but marked as deleted
    const deletedTenant = await this.prisma.tenant.findUnique({
      where: { id: tenant.id }
    })

    expect(deletedTenant).toBeTruthy()
    expect(deletedTenant?.status).toBe('DELETED')
    expect(deletedTenant?.deletedAt).toBeTruthy()

    // Verify queries can filter out deleted tenants
    const activeTenants = await this.prisma.tenant.findMany({
      where: { 
        status: { not: 'DELETED' },
        deletedAt: null
      }
    })

    expect(activeTenants.some(t => t.id === tenant.id)).toBe(false)
  }

  // Test audit log functionality
  static async testAuditLogging(): Promise<void> {
    const tenant = await TestHelpers.createTestTenant()
    const user = await TestHelpers.createTestUser(tenant.id)

    // Create a deal (this should trigger audit logging)
    const deal = await this.prisma.deal.create({
      data: {
        title: 'Audit Test Deal',
        type: 'ACQUISITION',
        status: 'ACTIVE',
        targetCompany: 'Target Corp',
        dealValue: 1000000,
        currency: 'USD',
        tenantId: tenant.id
      }
    })

    // Manually create audit log entry (in real app, this would be automatic)
    await this.prisma.auditLog.create({
      data: {
        action: 'CREATE',
        entityType: 'Deal',
        entityId: deal.id,
        userId: user.id,
        tenantId: tenant.id,
        changes: {
          title: deal.title,
          type: deal.type,
          status: deal.status
        }
      }
    })

    // Verify audit log was created
    const auditLogs = await this.prisma.auditLog.findMany({
      where: { 
        entityType: 'Deal',
        entityId: deal.id,
        tenantId: tenant.id
      }
    })

    expect(auditLogs).toHaveLength(1)
    expect(auditLogs[0].action).toBe('CREATE')
    expect(auditLogs[0].userId).toBe(user.id)
    expect(auditLogs[0].tenantId).toBe(tenant.id)
  }

  // Test migration record tracking
  static async testMigrationTracking(): Promise<void> {
    const tenant = await TestHelpers.createTestTenant()

    // Create migration record
    const migrationRecord = await this.prisma.migrationRecord.create({
      data: {
        migrationId: 'test-migration-001',
        name: 'Test Migration',
        version: '1.0.0',
        tenantId: tenant.id,
        executedBy: 'test-system',
        success: true
      }
    })

    // Verify migration record
    expect(migrationRecord.tenantId).toBe(tenant.id)
    expect(migrationRecord.success).toBe(true)

    // Test querying migration records for tenant
    const tenantMigrations = await this.prisma.migrationRecord.findMany({
      where: { tenantId: tenant.id }
    })

    expect(tenantMigrations).toHaveLength(1)
    expect(tenantMigrations[0].migrationId).toBe('test-migration-001')
  }

  // Generate test data for performance testing
  static async generateTestData(tenantId: string, counts: {
    users?: number
    deals?: number
    documents?: number
  }): Promise<{
    users: any[]
    deals: any[]
    documents: any[]
  }> {
    const { users: userCount = 10, deals: dealCount = 50, documents: docCount = 100 } = counts

    // Create users
    const userData = Array.from({ length: userCount }, (_, i) => ({
      email: `testuser${i}@tenant.com`,
      firstName: `User${i}`,
      lastName: 'Test',
      password: 'hashedpassword',
      tenantId
    }))

    const users = await Promise.all(
      userData.map(data => this.prisma.user.create({ data }))
    )

    // Create deals
    const dealData = Array.from({ length: dealCount }, (_, i) => ({
      title: `Test Deal ${i}`,
      type: i % 2 === 0 ? 'ACQUISITION' as const : 'MERGER' as const,
      status: i % 3 === 0 ? 'COMPLETED' as const : 'ACTIVE' as const,
      targetCompany: `Target Company ${i}`,
      dealValue: 1000000 + (i * 100000),
      currency: 'USD',
      tenantId
    }))

    const deals = await Promise.all(
      dealData.map(data => this.prisma.deal.create({ data }))
    )

    // Create documents
    const documentData = Array.from({ length: docCount }, (_, i) => ({
      name: `Test Document ${i}`,
      type: i % 3 === 0 ? 'CONTRACT' as const : 'FINANCIAL' as const,
      size: 1024 * (i + 1),
      mimeType: 'application/pdf',
      path: `/test/documents/doc${i}.pdf`,
      tenantId,
      dealId: i < deals.length ? deals[i % deals.length].id : null
    }))

    const documents = await Promise.all(
      documentData.map(data => this.prisma.document.create({ data }))
    )

    return { users, deals, documents }
  }
}
