import { PrismaClient } from '@prisma/client'
import { DatabaseTestUtils } from './database-test-utils'
import { TestHelpers } from '../utils/test-helpers'

describe('Database Tenant Isolation Integration Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    prisma = global.__PRISMA__
    DatabaseTestUtils.initialize(prisma)
  })

  beforeEach(async () => {
    // Clean database before each test
    await prisma.migrationRecord.deleteMany()
    await prisma.auditLog.deleteMany()
    await prisma.session.deleteMany()
    await prisma.userRole.deleteMany()
    await prisma.document.deleteMany()
    await prisma.dueDiligenceItem.deleteMany()
    await prisma.valuation.deleteMany()
    await prisma.deal.deleteMany()
    await prisma.tenantApiKey.deleteMany()
    await prisma.tenantInvitation.deleteMany()
    await prisma.user.deleteMany()
    await prisma.role.deleteMany()
    await prisma.tenant.deleteMany()
  })

  describe('Tenant Data Isolation', () => {
    it('should isolate user data between tenants', async () => {
      await DatabaseTestUtils.testTenantDataIsolation(
        // Create user function
        async (tenantId: string) => {
          return await TestHelpers.createTestUser(tenantId, {
            email: `user@tenant${tenantId.slice(-4)}.com`
          })
        },
        // Find users function
        async (tenantId: string) => {
          return await prisma.user.findMany({
            where: { tenantId }
          })
        }
      )
    })

    it('should isolate deal data between tenants', async () => {
      await DatabaseTestUtils.testTenantDataIsolation(
        // Create deal function
        async (tenantId: string) => {
          return await TestHelpers.createTestDeal(tenantId, {
            title: `Deal for tenant ${tenantId.slice(-4)}`
          })
        },
        // Find deals function
        async (tenantId: string) => {
          return await prisma.deal.findMany({
            where: { tenantId }
          })
        }
      )
    })

    it('should isolate document data between tenants', async () => {
      await DatabaseTestUtils.testTenantDataIsolation(
        // Create document function
        async (tenantId: string) => {
          return await TestHelpers.createTestDocument(tenantId, undefined, {
            name: `Document for tenant ${tenantId.slice(-4)}`
          })
        },
        // Find documents function
        async (tenantId: string) => {
          return await prisma.document.findMany({
            where: { tenantId }
          })
        }
      )
    })

    it('should isolate role data between tenants', async () => {
      const tenant1 = await TestHelpers.createTestTenant({ subdomain: 'role1' })
      const tenant2 = await TestHelpers.createTestTenant({ subdomain: 'role2' })

      // Create roles in each tenant
      const role1 = await prisma.role.create({
        data: {
          name: 'Custom Role 1',
          description: 'Custom role for tenant 1',
          permissions: ['custom:permission'],
          tenantId: tenant1.id
        }
      })

      const role2 = await prisma.role.create({
        data: {
          name: 'Custom Role 2',
          description: 'Custom role for tenant 2',
          permissions: ['custom:permission'],
          tenantId: tenant2.id
        }
      })

      // Verify isolation
      const tenant1Roles = await prisma.role.findMany({
        where: { tenantId: tenant1.id }
      })

      const tenant2Roles = await prisma.role.findMany({
        where: { tenantId: tenant2.id }
      })

      expect(tenant1Roles.some(r => r.id === role1.id)).toBe(true)
      expect(tenant1Roles.some(r => r.id === role2.id)).toBe(false)
      expect(tenant2Roles.some(r => r.id === role2.id)).toBe(true)
      expect(tenant2Roles.some(r => r.id === role1.id)).toBe(false)
    })

    it('should isolate audit logs between tenants', async () => {
      const tenant1 = await TestHelpers.createTestTenant({ subdomain: 'audit1' })
      const tenant2 = await TestHelpers.createTestTenant({ subdomain: 'audit2' })
      const user1 = await TestHelpers.createTestUser(tenant1.id)
      const user2 = await TestHelpers.createTestUser(tenant2.id)

      // Create audit logs for each tenant
      const auditLog1 = await prisma.auditLog.create({
        data: {
          action: 'CREATE',
          entityType: 'Test',
          entityId: 'test1',
          userId: user1.id,
          tenantId: tenant1.id,
          changes: { test: 'data1' }
        }
      })

      const auditLog2 = await prisma.auditLog.create({
        data: {
          action: 'CREATE',
          entityType: 'Test',
          entityId: 'test2',
          userId: user2.id,
          tenantId: tenant2.id,
          changes: { test: 'data2' }
        }
      })

      // Verify isolation
      const tenant1Logs = await prisma.auditLog.findMany({
        where: { tenantId: tenant1.id }
      })

      const tenant2Logs = await prisma.auditLog.findMany({
        where: { tenantId: tenant2.id }
      })

      expect(tenant1Logs.some(l => l.id === auditLog1.id)).toBe(true)
      expect(tenant1Logs.some(l => l.id === auditLog2.id)).toBe(false)
      expect(tenant2Logs.some(l => l.id === auditLog2.id)).toBe(true)
      expect(tenant2Logs.some(l => l.id === auditLog1.id)).toBe(false)
    })
  })

  describe('Cross-Tenant Query Prevention', () => {
    it('should prevent accidental cross-tenant queries', async () => {
      const tenant1 = await TestHelpers.createTestTenant({ subdomain: 'prevent1' })
      const tenant2 = await TestHelpers.createTestTenant({ subdomain: 'prevent2' })

      // Create data in both tenants
      const deal1 = await TestHelpers.createTestDeal(tenant1.id, { title: 'Tenant 1 Deal' })
      const deal2 = await TestHelpers.createTestDeal(tenant2.id, { title: 'Tenant 2 Deal' })

      // Query without tenant filter (should return all deals - this is what we want to prevent)
      const allDeals = await prisma.deal.findMany()
      expect(allDeals).toHaveLength(2)

      // Query with proper tenant filter
      const tenant1Deals = await prisma.deal.findMany({
        where: { tenantId: tenant1.id }
      })

      const tenant2Deals = await prisma.deal.findMany({
        where: { tenantId: tenant2.id }
      })

      expect(tenant1Deals).toHaveLength(1)
      expect(tenant1Deals[0].id).toBe(deal1.id)
      expect(tenant2Deals).toHaveLength(1)
      expect(tenant2Deals[0].id).toBe(deal2.id)
    })

    it('should prevent cross-tenant joins', async () => {
      const tenant1 = await TestHelpers.createTestTenant({ subdomain: 'join1' })
      const tenant2 = await TestHelpers.createTestTenant({ subdomain: 'join2' })

      const user1 = await TestHelpers.createTestUser(tenant1.id)
      const user2 = await TestHelpers.createTestUser(tenant2.id)
      const deal1 = await TestHelpers.createTestDeal(tenant1.id)
      const deal2 = await TestHelpers.createTestDeal(tenant2.id)

      // Query deals with users - should only return matching tenant combinations
      const dealsWithUsers = await prisma.deal.findMany({
        where: { tenantId: tenant1.id },
        include: {
          tenant: {
            include: {
              users: true
            }
          }
        }
      })

      expect(dealsWithUsers).toHaveLength(1)
      expect(dealsWithUsers[0].tenant.users.every(u => u.tenantId === tenant1.id)).toBe(true)
      expect(dealsWithUsers[0].tenant.users.some(u => u.id === user1.id)).toBe(true)
      expect(dealsWithUsers[0].tenant.users.some(u => u.id === user2.id)).toBe(false)
    })
  })

  describe('Tenant Deletion and Cascade', () => {
    it('should cascade delete all tenant data when tenant is deleted', async () => {
      const tenant = await TestHelpers.createTestTenant({ subdomain: 'cascade' })
      
      // Create related data
      const user = await TestHelpers.createTestUser(tenant.id)
      const deal = await TestHelpers.createTestDeal(tenant.id)
      const document = await TestHelpers.createTestDocument(tenant.id, deal.id)
      
      const role = await prisma.role.create({
        data: {
          name: 'Test Role',
          description: 'Test role',
          permissions: ['test'],
          tenantId: tenant.id
        }
      })

      const userRole = await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: role.id
        }
      })

      // Delete tenant (should cascade)
      await prisma.tenant.delete({ where: { id: tenant.id } })

      // Verify all related data was deleted
      const remainingUser = await prisma.user.findUnique({ where: { id: user.id } })
      const remainingDeal = await prisma.deal.findUnique({ where: { id: deal.id } })
      const remainingDocument = await prisma.document.findUnique({ where: { id: document.id } })
      const remainingRole = await prisma.role.findUnique({ where: { id: role.id } })
      const remainingUserRole = await prisma.userRole.findUnique({ where: { id: userRole.id } })

      expect(remainingUser).toBeNull()
      expect(remainingDeal).toBeNull()
      expect(remainingDocument).toBeNull()
      expect(remainingRole).toBeNull()
      expect(remainingUserRole).toBeNull()
    })

    it('should handle soft delete without affecting data integrity', async () => {
      const tenant = await TestHelpers.createTestTenant({ subdomain: 'softdelete' })
      const user = await TestHelpers.createTestUser(tenant.id)
      const deal = await TestHelpers.createTestDeal(tenant.id)

      // Soft delete tenant
      await prisma.tenant.update({
        where: { id: tenant.id },
        data: {
          status: 'DELETED',
          deletedAt: new Date()
        }
      })

      // Verify tenant is marked as deleted but data still exists
      const deletedTenant = await prisma.tenant.findUnique({
        where: { id: tenant.id }
      })

      expect(deletedTenant?.status).toBe('DELETED')
      expect(deletedTenant?.deletedAt).toBeTruthy()

      // Verify related data still exists
      const existingUser = await prisma.user.findUnique({ where: { id: user.id } })
      const existingDeal = await prisma.deal.findUnique({ where: { id: deal.id } })

      expect(existingUser).toBeTruthy()
      expect(existingDeal).toBeTruthy()

      // Verify queries can filter out deleted tenants
      const activeTenantsOnly = await prisma.tenant.findMany({
        where: {
          status: { not: 'DELETED' },
          deletedAt: null
        }
      })

      expect(activeTenantsOnly.some(t => t.id === tenant.id)).toBe(false)
    })
  })

  describe('Database Constraints and Validation', () => {
    it('should enforce unique constraints', async () => {
      await DatabaseTestUtils.testUniqueConstraints()
    })

    it('should enforce foreign key constraints', async () => {
      await DatabaseTestUtils.testForeignKeyConstraints()
    })

    it('should handle transaction rollbacks correctly', async () => {
      await DatabaseTestUtils.testTransactionRollback()
    })

    it('should validate required fields', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Try to create user without required fields
      await expect(
        prisma.user.create({
          data: {
            // Missing email, firstName, lastName, password
            tenantId: tenant.id
          } as any
        })
      ).rejects.toThrow()

      // Try to create deal without required fields
      await expect(
        prisma.deal.create({
          data: {
            // Missing title, type, status, etc.
            tenantId: tenant.id
          } as any
        })
      ).rejects.toThrow()
    })

    it('should validate enum values', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Try to create deal with invalid enum values
      await expect(
        prisma.deal.create({
          data: {
            title: 'Test Deal',
            type: 'INVALID_TYPE' as any,
            status: 'INVALID_STATUS' as any,
            targetCompany: 'Target',
            dealValue: 1000000,
            currency: 'USD',
            tenantId: tenant.id
          }
        })
      ).rejects.toThrow()
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle large datasets efficiently', async () => {
      await DatabaseTestUtils.testPerformanceWithLargeDataset(500)
    })

    it('should use indexes effectively', async () => {
      await DatabaseTestUtils.testIndexEffectiveness()
    })

    it('should handle concurrent operations', async () => {
      await DatabaseTestUtils.testConcurrentOperations()
    })

    it('should maintain data consistency under load', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Create multiple concurrent operations that modify related data
      const operations = Array.from({ length: 20 }, async (_, i) => {
        const user = await TestHelpers.createTestUser(tenant.id, {
          email: `concurrent${i}@test.com`
        })

        const deal = await TestHelpers.createTestDeal(tenant.id, {
          title: `Concurrent Deal ${i}`
        })

        const document = await TestHelpers.createTestDocument(tenant.id, deal.id, {
          name: `Concurrent Doc ${i}`
        })

        return { user, deal, document }
      })

      const results = await Promise.all(operations)

      // Verify all operations completed successfully
      expect(results).toHaveLength(20)

      // Verify data consistency
      const users = await prisma.user.findMany({ where: { tenantId: tenant.id } })
      const deals = await prisma.deal.findMany({ where: { tenantId: tenant.id } })
      const documents = await prisma.document.findMany({ where: { tenantId: tenant.id } })

      expect(users).toHaveLength(20)
      expect(deals).toHaveLength(20)
      expect(documents).toHaveLength(20)

      // Verify all data belongs to the correct tenant
      expect(users.every(u => u.tenantId === tenant.id)).toBe(true)
      expect(deals.every(d => d.tenantId === tenant.id)).toBe(true)
      expect(documents.every(doc => doc.tenantId === tenant.id)).toBe(true)
    })
  })

  describe('Migration and Schema Evolution', () => {
    it('should track migration records per tenant', async () => {
      await DatabaseTestUtils.testMigrationTracking()
    })

    it('should handle schema changes gracefully', async () => {
      const tenant = await TestHelpers.createTestTenant()

      // Create data with current schema
      const deal = await TestHelpers.createTestDeal(tenant.id)

      // Verify data can be queried (simulating schema compatibility)
      const queriedDeal = await prisma.deal.findUnique({
        where: { id: deal.id }
      })

      expect(queriedDeal).toBeTruthy()
      expect(queriedDeal?.tenantId).toBe(tenant.id)
    })
  })

  describe('Audit and Compliance', () => {
    it('should maintain audit trails per tenant', async () => {
      await DatabaseTestUtils.testAuditLogging()
    })

    it('should support data export for compliance', async () => {
      const tenant = await TestHelpers.createTestTenant()
      
      // Generate test data
      const testData = await DatabaseTestUtils.generateTestData(tenant.id, {
        users: 5,
        deals: 10,
        documents: 15
      })

      // Export all tenant data (simulate compliance export)
      const exportData = {
        tenant: await prisma.tenant.findUnique({ where: { id: tenant.id } }),
        users: await prisma.user.findMany({ where: { tenantId: tenant.id } }),
        deals: await prisma.deal.findMany({ where: { tenantId: tenant.id } }),
        documents: await prisma.document.findMany({ where: { tenantId: tenant.id } }),
        auditLogs: await prisma.auditLog.findMany({ where: { tenantId: tenant.id } })
      }

      expect(exportData.users).toHaveLength(5)
      expect(exportData.deals).toHaveLength(10)
      expect(exportData.documents).toHaveLength(15)

      // Verify all exported data belongs to the tenant
      expect(exportData.users.every(u => u.tenantId === tenant.id)).toBe(true)
      expect(exportData.deals.every(d => d.tenantId === tenant.id)).toBe(true)
      expect(exportData.documents.every(doc => doc.tenantId === tenant.id)).toBe(true)
    })
  })
})
