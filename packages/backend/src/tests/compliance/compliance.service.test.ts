import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { PrismaClient } from '@prisma/client'
import { ComplianceService } from '@/services/compliance/compliance.service'
import { CacheService } from '@/services/cache.service'
import {
  ComplianceConfiguration,
  TransactionType,
  EntityType,
  ComplianceStatusType,
  RiskLevel
} from '@/shared/types/compliance'

// Mock dependencies
jest.mock('@prisma/client')
jest.mock('@/services/cache.service')

describe('ComplianceService', () => {
  let complianceService: ComplianceService
  let mockPrisma: jest.Mocked<PrismaClient>
  let mockCache: jest.Mocked<CacheService>

  beforeEach(() => {
    mockPrisma = new PrismaClient() as jest.Mocked<PrismaClient>
    mockCache = new CacheService() as jest.Mocked<CacheService>
    complianceService = new ComplianceService(mockPrisma, mockCache)

    // Mock Prisma methods
    mockPrisma.complianceStatus = {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn()
    } as any

    mockPrisma.complianceAlert = {
      create: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn()
    } as any

    // Mock Cache methods
    mockCache.get = jest.fn()
    mockCache.set = jest.fn()
    mockCache.delete = jest.fn()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('initializeCompliance', () => {
    it('should initialize compliance tracking for a deal', async () => {
      const config: ComplianceConfiguration = {
        dealId: 'deal-1',
        transactionType: TransactionType.ACQUISITION,
        transactionValue: *********,
        transactionCurrency: 'USD',
        jurisdictions: ['US'],
        industries: ['Technology'],
        entityTypes: [EntityType.PUBLIC_COMPANY],
        targetCompanyName: 'Target Corp',
        acquirerCompanyName: 'Acquirer Inc',
        expectedClosingDate: new Date('2024-06-01'),
        customRequirements: [],
        exemptionClaims: []
      }

      const userId = 'user-1'
      const tenantId = 'tenant-1'

      // Mock database responses
      mockPrisma.complianceStatus.create.mockResolvedValue({
        id: 'status-1',
        dealId: config.dealId,
        frameworkId: 'sec-ma-rules',
        requirementId: 'sec-8k-filing',
        status: ComplianceStatusType.NOT_STARTED,
        completionPercentage: 0,
        lastUpdated: new Date(),
        updatedBy: userId,
        dueDate: new Date('2024-05-28'),
        isOverdue: false,
        daysRemaining: 30,
        submittedDocuments: [],
        missingDocuments: ['Form 8-K'],
        riskLevel: RiskLevel.HIGH,
        notes: [],
        tenantId,
        createdAt: new Date(),
        updatedAt: new Date()
      } as any)

      const result = await complianceService.initializeCompliance(config, userId, tenantId)

      expect(result).toBeDefined()
      expect(result.dealId).toBe(config.dealId)
      expect(result.applicableFrameworks).toBeDefined()
      expect(result.complianceStatuses).toBeDefined()
      expect(mockPrisma.complianceStatus.create).toHaveBeenCalled()
    })

    it('should handle errors during initialization', async () => {
      const config: ComplianceConfiguration = {
        dealId: 'deal-1',
        transactionType: TransactionType.ACQUISITION,
        transactionValue: *********,
        transactionCurrency: 'USD',
        jurisdictions: ['US'],
        industries: ['Technology'],
        entityTypes: [EntityType.PUBLIC_COMPANY],
        targetCompanyName: 'Target Corp',
        acquirerCompanyName: 'Acquirer Inc',
        customRequirements: [],
        exemptionClaims: []
      }

      mockPrisma.complianceStatus.create.mockRejectedValue(new Error('Database error'))

      await expect(
        complianceService.initializeCompliance(config, 'user-1', 'tenant-1')
      ).rejects.toThrow('Database error')
    })
  })

  describe('updateComplianceStatus', () => {
    it('should update compliance status successfully', async () => {
      const statusId = 'status-1'
      const updates = {
        status: ComplianceStatusType.IN_PROGRESS,
        completionPercentage: 50
      }
      const userId = 'user-1'

      // Mock current status
      mockPrisma.complianceStatus.findUnique.mockResolvedValue({
        id: statusId,
        status: ComplianceStatusType.NOT_STARTED,
        completionPercentage: 0,
        submittedDocuments: [],
        missingDocuments: ['Document 1']
      } as any)

      // Mock update
      mockPrisma.complianceStatus.update.mockResolvedValue({
        id: statusId,
        status: ComplianceStatusType.IN_PROGRESS,
        completionPercentage: 50,
        lastUpdated: new Date(),
        updatedBy: userId
      } as any)

      const result = await complianceService.updateComplianceStatus(statusId, updates, userId)

      expect(result).toBeDefined()
      expect(result.status).toBe(ComplianceStatusType.IN_PROGRESS)
      expect(mockPrisma.complianceStatus.update).toHaveBeenCalledWith({
        where: { id: statusId },
        data: expect.objectContaining({
          status: ComplianceStatusType.IN_PROGRESS,
          completionPercentage: 50,
          lastUpdated: expect.any(Date),
          updatedBy: userId,
          updatedAt: expect.any(Date)
        })
      })
    })

    it('should throw error if status not found', async () => {
      mockPrisma.complianceStatus.findUnique.mockResolvedValue(null)

      await expect(
        complianceService.updateComplianceStatus('invalid-id', {}, 'user-1')
      ).rejects.toThrow('Compliance status not found')
    })
  })

  describe('getComplianceAssessment', () => {
    it('should return cached assessment if available', async () => {
      const dealId = 'deal-1'
      const tenantId = 'tenant-1'
      const cachedAssessment = {
        dealId,
        applicableFrameworks: [],
        complianceStatuses: [],
        overallRiskLevel: RiskLevel.LOW,
        criticalDeadlines: [],
        openIssues: [],
        completionPercentage: 100
      }

      mockCache.get.mockResolvedValue(cachedAssessment)

      const result = await complianceService.getComplianceAssessment(dealId, tenantId)

      expect(result).toEqual(cachedAssessment)
      expect(mockCache.get).toHaveBeenCalledWith(`compliance:assessment:${dealId}`)
      expect(mockPrisma.complianceStatus.findMany).not.toHaveBeenCalled()
    })

    it('should fetch and cache assessment if not cached', async () => {
      const dealId = 'deal-1'
      const tenantId = 'tenant-1'

      mockCache.get.mockResolvedValue(null)
      mockPrisma.complianceStatus.findMany.mockResolvedValue([
        {
          id: 'status-1',
          dealId,
          frameworkId: 'sec-ma-rules',
          requirementId: 'sec-8k-filing',
          status: ComplianceStatusType.COMPLETED,
          riskLevel: RiskLevel.LOW,
          isOverdue: false,
          issues: []
        }
      ] as any)

      const result = await complianceService.getComplianceAssessment(dealId, tenantId)

      expect(result).toBeDefined()
      expect(result.dealId).toBe(dealId)
      expect(mockPrisma.complianceStatus.findMany).toHaveBeenCalledWith({
        where: { dealId, tenantId }
      })
      expect(mockCache.set).toHaveBeenCalledWith(
        `compliance:assessment:${dealId}`,
        expect.any(Object),
        900
      )
    })
  })

  describe('monitorDeadlines', () => {
    it('should monitor deadlines and generate alerts', async () => {
      const tenantId = 'tenant-1'
      const upcomingDeadlines = [
        {
          id: 'status-1',
          dealId: 'deal-1',
          dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
          status: ComplianceStatusType.IN_PROGRESS,
          tenantId
        }
      ]

      mockPrisma.complianceStatus.findMany
        .mockResolvedValueOnce(upcomingDeadlines as any) // For upcoming deadlines
        .mockResolvedValueOnce([]) // For overdue items

      await complianceService.monitorDeadlines(tenantId)

      expect(mockPrisma.complianceStatus.findMany).toHaveBeenCalledTimes(2)
    })

    it('should handle monitoring errors gracefully', async () => {
      const tenantId = 'tenant-1'

      mockPrisma.complianceStatus.findMany.mockRejectedValue(new Error('Database error'))

      // Should not throw error
      await expect(complianceService.monitorDeadlines(tenantId)).resolves.toBeUndefined()
    })
  })

  describe('generateComplianceReport', () => {
    it('should generate compliance report successfully', async () => {
      const dealId = 'deal-1'
      const tenantId = 'tenant-1'
      const reportType = 'SUMMARY'

      // Mock assessment data
      const mockAssessment = {
        dealId,
        applicableFrameworks: [],
        complianceStatuses: [
          {
            id: 'status-1',
            status: ComplianceStatusType.COMPLETED,
            isOverdue: false
          }
        ],
        overallRiskLevel: RiskLevel.LOW,
        criticalDeadlines: [],
        openIssues: [],
        completionPercentage: 100
      }

      mockCache.get.mockResolvedValue(mockAssessment)

      const result = await complianceService.generateComplianceReport(dealId, tenantId, reportType)

      expect(result).toBeDefined()
      expect(result.dealId).toBe(dealId)
      expect(result.reportType).toBe(reportType)
      expect(result.summary).toBeDefined()
      expect(result.summary.totalRequirements).toBe(1)
      expect(result.summary.completedRequirements).toBe(1)
      expect(result.summary.overdueRequirements).toBe(0)
    })
  })

  describe('Risk Assessment', () => {
    it('should calculate risk score correctly', async () => {
      const complianceStatuses = [
        { status: ComplianceStatusType.COMPLETED, isOverdue: false, riskLevel: RiskLevel.LOW },
        { status: ComplianceStatusType.IN_PROGRESS, isOverdue: true, riskLevel: RiskLevel.HIGH },
        { status: ComplianceStatusType.NOT_STARTED, isOverdue: false, riskLevel: RiskLevel.MEDIUM }
      ]

      const alerts = [
        { severity: 'CRITICAL' },
        { severity: 'WARNING' }
      ]

      // This would test the private calculateRiskScore method
      // In a real implementation, you might expose this as a public method for testing
      // or test it indirectly through public methods
    })
  })

  describe('Event Handling', () => {
    it('should emit events on status changes', async () => {
      const statusId = 'status-1'
      const updates = { status: ComplianceStatusType.COMPLETED }
      const userId = 'user-1'

      mockPrisma.complianceStatus.findUnique.mockResolvedValue({
        id: statusId,
        status: ComplianceStatusType.IN_PROGRESS
      } as any)

      mockPrisma.complianceStatus.update.mockResolvedValue({
        id: statusId,
        status: ComplianceStatusType.COMPLETED
      } as any)

      const eventSpy = jest.fn()
      complianceService.on('compliance:requirement_completed', eventSpy)

      await complianceService.updateComplianceStatus(statusId, updates, userId)

      // In a real implementation, you would verify that the event was emitted
      // This depends on how the event emission is implemented
    })
  })

  describe('Integration with Regulatory Frameworks', () => {
    it('should apply correct frameworks based on transaction characteristics', async () => {
      const config: ComplianceConfiguration = {
        dealId: 'deal-1',
        transactionType: TransactionType.MERGER,
        transactionValue: *********, // Above HSR threshold
        transactionCurrency: 'USD',
        jurisdictions: ['US'],
        industries: ['Technology'],
        entityTypes: [EntityType.PUBLIC_COMPANY],
        targetCompanyName: 'Target Corp',
        acquirerCompanyName: 'Acquirer Inc',
        customRequirements: [],
        exemptionClaims: []
      }

      mockPrisma.complianceStatus.create.mockResolvedValue({} as any)

      const result = await complianceService.initializeCompliance(config, 'user-1', 'tenant-1')

      // Should include SEC and HSR frameworks for large public company merger
      expect(result.applicableFrameworks).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ id: 'sec-ma-rules' }),
          expect.objectContaining({ id: 'hsr-act' })
        ])
      )
    })

    it('should exclude frameworks that do not apply', async () => {
      const config: ComplianceConfiguration = {
        dealId: 'deal-1',
        transactionType: TransactionType.ACQUISITION,
        transactionValue: 50000000, // Below HSR threshold
        transactionCurrency: 'USD',
        jurisdictions: ['US'],
        industries: ['Technology'],
        entityTypes: [EntityType.PRIVATE_COMPANY],
        targetCompanyName: 'Target Corp',
        acquirerCompanyName: 'Acquirer Inc',
        customRequirements: [],
        exemptionClaims: []
      }

      mockPrisma.complianceStatus.create.mockResolvedValue({} as any)

      const result = await complianceService.initializeCompliance(config, 'user-1', 'tenant-1')

      // Should not include HSR for small transaction
      expect(result.applicableFrameworks).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({ id: 'hsr-act' })
        ])
      )
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      mockPrisma.complianceStatus.findMany.mockRejectedValue(new Error('Connection failed'))

      await expect(
        complianceService.getComplianceAssessment('deal-1', 'tenant-1')
      ).rejects.toThrow('Connection failed')
    })

    it('should handle cache errors gracefully', async () => {
      mockCache.get.mockRejectedValue(new Error('Cache error'))
      mockPrisma.complianceStatus.findMany.mockResolvedValue([])

      // Should fall back to database even if cache fails
      const result = await complianceService.getComplianceAssessment('deal-1', 'tenant-1')
      expect(result).toBeDefined()
    })
  })
})
