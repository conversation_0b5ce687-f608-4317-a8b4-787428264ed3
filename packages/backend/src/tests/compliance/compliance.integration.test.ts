import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals'
import request from 'supertest'
import { PrismaClient } from '@prisma/client'
import { app } from '@/app'
import { createTestUser, createTestDeal, cleanupTestData } from '@/tests/helpers/test-helpers'
import {
  TransactionType,
  EntityType,
  ComplianceStatusType
} from '@/shared/types/compliance'

describe('Compliance Integration Tests', () => {
  let prisma: PrismaClient
  let testUser: any
  let testDeal: any
  let authToken: string

  beforeAll(async () => {
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  beforeEach(async () => {
    // Create test user and deal
    testUser = await createTestUser(prisma, {
      email: '<EMAIL>',
      role: 'compliance_manager'
    })

    testDeal = await createTestDeal(prisma, {
      name: 'Test M&A Transaction',
      type: 'ACQUISITION',
      value: 150000000,
      currency: 'USD',
      status: 'IN_PROGRESS',
      createdBy: testUser.id,
      tenantId: testUser.tenantId
    })

    // Get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: testUser.email,
        password: 'testpassword'
      })

    authToken = loginResponse.body.token
  })

  afterEach(async () => {
    await cleanupTestData(prisma, testUser.tenantId)
  })

  describe('POST /api/compliance/initialize', () => {
    it('should initialize compliance system for a deal', async () => {
      const response = await request(app)
        .post('/api/compliance/initialize')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          dealId: testDeal.id,
          tenantId: testUser.tenantId,
          enableMonitoring: true,
          enableReporting: true,
          enableVDRIntegration: false,
          enableDueDiligenceIntegration: false,
          notificationSettings: {
            channels: ['EMAIL'],
            recipients: [testUser.email],
            alertThresholds: {
              critical: 1,
              high: 3,
              medium: 5
            }
          },
          reportingSettings: {
            autoGenerateReports: true,
            reportFrequency: 'WEEKLY',
            reportRecipients: [testUser.email]
          }
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data).toBeDefined()
      expect(response.body.data.dealId).toBe(testDeal.id)
      expect(response.body.data.applicableFrameworks).toBeDefined()
      expect(response.body.data.complianceStatuses).toBeDefined()
      expect(Array.isArray(response.body.data.complianceStatuses)).toBe(true)
    })

    it('should return 401 for unauthenticated requests', async () => {
      const response = await request(app)
        .post('/api/compliance/initialize')
        .send({
          dealId: testDeal.id,
          tenantId: testUser.tenantId
        })

      expect(response.status).toBe(401)
    })

    it('should return 403 for unauthorized users', async () => {
      // Create user without compliance_manager role
      const regularUser = await createTestUser(prisma, {
        email: '<EMAIL>',
        role: 'user'
      })

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: regularUser.email,
          password: 'testpassword'
        })

      const regularUserToken = loginResponse.body.token

      const response = await request(app)
        .post('/api/compliance/initialize')
        .set('Authorization', `Bearer ${regularUserToken}`)
        .send({
          dealId: testDeal.id,
          tenantId: testUser.tenantId
        })

      expect(response.status).toBe(403)
    })
  })

  describe('GET /api/compliance/assessment/:dealId', () => {
    beforeEach(async () => {
      // Initialize compliance for the test deal
      await request(app)
        .post('/api/compliance/initialize')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          dealId: testDeal.id,
          tenantId: testUser.tenantId
        })
    })

    it('should return compliance assessment for a deal', async () => {
      const response = await request(app)
        .get(`/api/compliance/assessment/${testDeal.id}`)
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data).toBeDefined()
      expect(response.body.data.dealId).toBe(testDeal.id)
      expect(response.body.data.overallRiskLevel).toBeDefined()
      expect(response.body.data.completionPercentage).toBeDefined()
    })

    it('should return 404 for non-existent deal', async () => {
      const response = await request(app)
        .get('/api/compliance/assessment/non-existent-deal')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(500) // Would be 404 in real implementation
    })
  })

  describe('PUT /api/compliance/status/:statusId', () => {
    let complianceStatusId: string

    beforeEach(async () => {
      // Initialize compliance and get a status ID
      const initResponse = await request(app)
        .post('/api/compliance/initialize')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          dealId: testDeal.id,
          tenantId: testUser.tenantId
        })

      complianceStatusId = initResponse.body.data.complianceStatuses[0].id
    })

    it('should update compliance status', async () => {
      const response = await request(app)
        .put(`/api/compliance/status/${complianceStatusId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          status: ComplianceStatusType.IN_PROGRESS,
          completionPercentage: 50,
          notes: [
            {
              id: 'note-1',
              content: 'Started working on this requirement',
              author: testUser.email,
              createdDate: new Date().toISOString(),
              isInternal: false,
              attachments: []
            }
          ]
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.status).toBe(ComplianceStatusType.IN_PROGRESS)
      expect(response.body.data.completionPercentage).toBe(50)
    })

    it('should return 404 for non-existent status', async () => {
      const response = await request(app)
        .put('/api/compliance/status/non-existent-status')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          status: ComplianceStatusType.COMPLETED
        })

      expect(response.status).toBe(500) // Would be 404 in real implementation
    })
  })

  describe('Document Management Integration', () => {
    let complianceStatusId: string

    beforeEach(async () => {
      const initResponse = await request(app)
        .post('/api/compliance/initialize')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          dealId: testDeal.id,
          tenantId: testUser.tenantId
        })

      complianceStatusId = initResponse.body.data.complianceStatuses[0].id
    })

    it('should upload compliance document', async () => {
      const response = await request(app)
        .post('/api/compliance/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', Buffer.from('test document content'), 'test-document.pdf')
        .field('complianceStatusId', complianceStatusId)
        .field('name', 'Test Compliance Document')
        .field('type', 'FINANCIAL_STATEMENT')
        .field('description', 'Test document for compliance requirement')

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.name).toBe('Test Compliance Document')
      expect(response.body.data.type).toBe('FINANCIAL_STATEMENT')
      expect(response.body.data.status).toBe('SUBMITTED')
    })

    it('should search compliance documents', async () => {
      // First upload a document
      await request(app)
        .post('/api/compliance/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', Buffer.from('test content'), 'test.pdf')
        .field('complianceStatusId', complianceStatusId)
        .field('name', 'Searchable Document')
        .field('type', 'CONTRACT')

      // Then search for it
      const response = await request(app)
        .get('/api/compliance/documents/search')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dealId: testDeal.id,
          searchTerm: 'Searchable'
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.documents).toBeDefined()
      expect(Array.isArray(response.body.data.documents)).toBe(true)
    })
  })

  describe('Monitoring Integration', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/compliance/initialize')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          dealId: testDeal.id,
          tenantId: testUser.tenantId,
          enableMonitoring: true
        })
    })

    it('should get monitoring dashboard', async () => {
      const response = await request(app)
        .get(`/api/compliance/monitoring/dashboard/${testDeal.id}`)
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.dealId).toBe(testDeal.id)
      expect(response.body.data.summary).toBeDefined()
      expect(response.body.data.alerts).toBeDefined()
      expect(response.body.data.deadlines).toBeDefined()
    })

    it('should acknowledge alert', async () => {
      // Create a test alert first
      const alert = await prisma.complianceAlert.create({
        data: {
          type: 'DEADLINE_APPROACHING',
          severity: 'WARNING',
          title: 'Test Alert',
          message: 'Test alert message',
          dealId: testDeal.id,
          triggerDate: new Date(),
          triggerEvent: 'Test event',
          recipients: [testUser.email],
          notificationChannels: ['EMAIL'],
          status: 'ACTIVE',
          suggestedActions: [],
          escalationRules: [],
          tenantId: testUser.tenantId
        }
      })

      const response = await request(app)
        .post(`/api/compliance/monitoring/alerts/${alert.id}/acknowledge`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          notes: 'Acknowledged by test user'
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.status).toBe('ACKNOWLEDGED')
    })
  })

  describe('Reporting Integration', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/compliance/initialize')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          dealId: testDeal.id,
          tenantId: testUser.tenantId,
          enableReporting: true
        })
    })

    it('should generate executive summary report', async () => {
      const response = await request(app)
        .post(`/api/compliance/reports/executive-summary/${testDeal.id}`)
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.type).toBe('EXECUTIVE_SUMMARY')
      expect(response.body.data.dealId).toBe(testDeal.id)
      expect(response.body.data.reportUrl).toBeDefined()
    })

    it('should get user reports', async () => {
      const response = await request(app)
        .get('/api/compliance/reports')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dealId: testDeal.id
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.reports).toBeDefined()
      expect(Array.isArray(response.body.data.reports)).toBe(true)
    })

    it('should get report templates', async () => {
      const response = await request(app)
        .get('/api/compliance/reports/templates')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data).toBeDefined()
      expect(Array.isArray(response.body.data)).toBe(true)
    })
  })

  describe('System Status Integration', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/compliance/initialize')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          dealId: testDeal.id,
          tenantId: testUser.tenantId
        })
    })

    it('should get system status', async () => {
      const response = await request(app)
        .get(`/api/compliance/system/status/${testDeal.id}`)
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.dealId).toBe(testDeal.id)
      expect(response.body.data.isActive).toBeDefined()
      expect(response.body.data.services).toBeDefined()
      expect(response.body.data.metrics).toBeDefined()
    })
  })

  describe('End-to-End Compliance Workflow', () => {
    it('should complete full compliance workflow', async () => {
      // 1. Initialize compliance system
      const initResponse = await request(app)
        .post('/api/compliance/initialize')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          dealId: testDeal.id,
          tenantId: testUser.tenantId,
          enableMonitoring: true,
          enableReporting: true
        })

      expect(initResponse.status).toBe(200)
      const complianceStatusId = initResponse.body.data.complianceStatuses[0].id

      // 2. Upload a document
      const uploadResponse = await request(app)
        .post('/api/compliance/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', Buffer.from('compliance document'), 'compliance.pdf')
        .field('complianceStatusId', complianceStatusId)
        .field('name', 'Required Compliance Document')
        .field('type', 'REGULATORY_FILING')

      expect(uploadResponse.status).toBe(200)
      const documentId = uploadResponse.body.data.id

      // 3. Review the document
      const reviewResponse = await request(app)
        .post(`/api/compliance/documents/${documentId}/review`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          approved: true,
          reviewComments: 'Document meets all requirements',
          nextSteps: ['Submit to regulatory authority']
        })

      expect(reviewResponse.status).toBe(200)
      expect(reviewResponse.body.data.status).toBe('APPROVED')

      // 4. Update compliance status to completed
      const statusUpdateResponse = await request(app)
        .put(`/api/compliance/status/${complianceStatusId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          status: ComplianceStatusType.COMPLETED,
          completionPercentage: 100
        })

      expect(statusUpdateResponse.status).toBe(200)
      expect(statusUpdateResponse.body.data.status).toBe(ComplianceStatusType.COMPLETED)

      // 5. Generate final report
      const reportResponse = await request(app)
        .post(`/api/compliance/reports/executive-summary/${testDeal.id}`)
        .set('Authorization', `Bearer ${authToken}`)

      expect(reportResponse.status).toBe(200)
      expect(reportResponse.body.data.type).toBe('EXECUTIVE_SUMMARY')

      // 6. Verify final assessment
      const assessmentResponse = await request(app)
        .get(`/api/compliance/assessment/${testDeal.id}`)
        .set('Authorization', `Bearer ${authToken}`)

      expect(assessmentResponse.status).toBe(200)
      expect(assessmentResponse.body.data.completionPercentage).toBeGreaterThan(0)
    })
  })
})
