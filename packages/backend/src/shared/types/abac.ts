/**
 * Attribute-Based Access Control (ABAC) Type Definitions
 * 
 * This file defines the comprehensive type system for ABAC including:
 * - Attribute definitions and structures
 * - Dynamic policy evaluation
 * - Context-aware access control
 * - Attribute providers and resolvers
 */

// Core attribute structure
export interface Attribute {
  id: string
  name: string
  type: AttributeType
  category: AttributeCategory
  description?: string
  isRequired: boolean
  isMultiValue: boolean
  defaultValue?: any
  validation?: AttributeValidation
  metadata?: AttributeMetadata
}

export enum AttributeType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  TIME = 'time',
  ARRAY = 'array',
  OBJECT = 'object',
  JSON = 'json',
  ENUM = 'enum'
}

export enum AttributeCategory {
  SUBJECT = 'subject',     // User attributes
  RESOURCE = 'resource',   // Resource attributes
  ACTION = 'action',       // Action attributes
  ENVIRONMENT = 'environment', // Environmental attributes
  CONTEXT = 'context'      // Request context attributes
}

export interface AttributeValidation {
  pattern?: string
  minLength?: number
  maxLength?: number
  minimum?: number
  maximum?: number
  enum?: any[]
  customValidator?: string
}

export interface AttributeMetadata {
  source?: string
  provider?: string
  cacheable?: boolean
  cacheTimeout?: number
  sensitive?: boolean
  pii?: boolean
}

// Attribute value with context
export interface AttributeValue {
  attributeId: string
  value: any
  source: string
  timestamp: Date
  confidence?: number
  metadata?: Record<string, any>
}

// ABAC policy structure
export interface ABACPolicy {
  id: string
  name: string
  description?: string
  version: string
  isActive: boolean
  priority: number
  
  // ABAC-specific fields
  target: ABACTarget
  rules: ABACRule[]
  obligations?: ABACObligation[]
  advice?: ABACAdvice[]
  
  // Standard policy fields
  tenantId: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

// Policy target - defines when policy applies
export interface ABACTarget {
  subjects?: AttributeExpression[]
  resources?: AttributeExpression[]
  actions?: AttributeExpression[]
  environments?: AttributeExpression[]
}

// Attribute expression for matching
export interface AttributeExpression {
  attributeId: string
  operator: AttributeOperator
  value: any
  function?: AttributeFunction
}

export enum AttributeOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  CONTAINS = 'contains',
  IN = 'in',
  MATCHES = 'matches',
  EXISTS = 'exists',
  IS_SUBSET_OF = 'is_subset_of',
  INTERSECTS = 'intersects'
}

// Attribute functions for dynamic evaluation
export interface AttributeFunction {
  name: string
  parameters: any[]
}

// ABAC rule structure
export interface ABACRule {
  id: string
  effect: 'permit' | 'deny'
  condition: ABACCondition
  description?: string
}

// Complex condition structure
export interface ABACCondition {
  type: 'simple' | 'composite'
  operator?: LogicalOperator
  expressions?: AttributeExpression[]
  conditions?: ABACCondition[]
}

export enum LogicalOperator {
  AND = 'and',
  OR = 'or',
  NOT = 'not'
}

// Obligations - actions that must be performed
export interface ABACObligation {
  id: string
  type: 'log' | 'notify' | 'transform' | 'custom'
  parameters: Record<string, any>
  fulfillOn: 'permit' | 'deny' | 'both'
}

// Advice - recommendations for policy decisions
export interface ABACAdvice {
  id: string
  type: 'warning' | 'info' | 'recommendation'
  message: string
  parameters?: Record<string, any>
}

// ABAC evaluation context
export interface ABACContext {
  requestId: string
  timestamp: Date
  
  // Subject attributes (user)
  subject: AttributeSet
  
  // Resource attributes
  resource: AttributeSet
  
  // Action attributes
  action: AttributeSet
  
  // Environment attributes
  environment: AttributeSet
  
  // Additional context
  metadata?: Record<string, any>
}

// Set of attributes for an entity
export interface AttributeSet {
  entityId: string
  entityType: string
  attributes: Record<string, AttributeValue>
}

// ABAC evaluation request
export interface ABACRequest {
  subjectId: string
  resourceId?: string
  resourceType: string
  action: string
  environment?: Record<string, any>
  context?: Record<string, any>
}

// ABAC evaluation result
export interface ABACResult {
  decision: 'permit' | 'deny' | 'not_applicable' | 'indeterminate'
  matchedPolicies: ABACPolicyMatch[]
  obligations: ABACObligation[]
  advice: ABACAdvice[]
  evaluationTime: number
  errors?: ABACError[]
  attributesUsed: string[]
}

export interface ABACPolicyMatch {
  policyId: string
  policyName: string
  ruleId: string
  effect: 'permit' | 'deny'
  confidence: number
}

export interface ABACError {
  code: string
  message: string
  attributeId?: string
  policyId?: string
}

// Attribute providers for dynamic attribute resolution
export interface AttributeProvider {
  id: string
  name: string
  type: AttributeProviderType
  config: AttributeProviderConfig
  supportedAttributes: string[]
  isActive: boolean
}

export enum AttributeProviderType {
  DATABASE = 'database',
  LDAP = 'ldap',
  API = 'api',
  STATIC = 'static',
  COMPUTED = 'computed',
  EXTERNAL = 'external'
}

export interface AttributeProviderConfig {
  endpoint?: string
  credentials?: Record<string, any>
  mapping?: Record<string, string>
  cacheTimeout?: number
  retryPolicy?: RetryPolicy
}

export interface RetryPolicy {
  maxRetries: number
  backoffMultiplier: number
  maxBackoffTime: number
}

// Attribute resolver for fetching attribute values
export interface AttributeResolver {
  resolveSubjectAttributes(subjectId: string, requiredAttributes: string[]): Promise<AttributeSet>
  resolveResourceAttributes(resourceId: string, resourceType: string, requiredAttributes: string[]): Promise<AttributeSet>
  resolveEnvironmentAttributes(context: Record<string, any>): Promise<AttributeSet>
  resolveActionAttributes(action: string): Promise<AttributeSet>
}

// Dynamic attribute computation
export interface ComputedAttribute {
  id: string
  name: string
  expression: string
  dependencies: string[]
  cacheTimeout?: number
}

// Attribute cache for performance
export interface AttributeCache {
  get(key: string): Promise<AttributeValue | null>
  set(key: string, value: AttributeValue, ttl: number): Promise<void>
  invalidate(pattern: string): Promise<void>
  getStats(): Promise<CacheStats>
}

export interface CacheStats {
  hits: number
  misses: number
  hitRate: number
  size: number
}

// ABAC policy templates
export interface ABACPolicyTemplate {
  id: string
  name: string
  description: string
  category: string
  template: Partial<ABACPolicy>
  parameters: ABACTemplateParameter[]
}

export interface ABACTemplateParameter {
  name: string
  type: string
  description: string
  required: boolean
  defaultValue?: any
}

// ABAC configuration
export interface ABACConfig {
  enableAttributeCaching: boolean
  defaultCacheTimeout: number
  maxEvaluationTime: number
  enableObligations: boolean
  enableAdvice: boolean
  attributeProviders: AttributeProvider[]
  computedAttributes: ComputedAttribute[]
}

// Attribute discovery and introspection
export interface AttributeDiscovery {
  discoverSubjectAttributes(subjectId: string): Promise<Attribute[]>
  discoverResourceAttributes(resourceType: string): Promise<Attribute[]>
  getAttributeSchema(attributeId: string): Promise<Attribute>
  validateAttributeValue(attributeId: string, value: any): Promise<boolean>
}

// ABAC audit and monitoring
export interface ABACEvaluationLog {
  id: string
  requestId: string
  subjectId: string
  resourceId?: string
  resourceType: string
  action: string
  decision: string
  evaluationTime: number
  attributesUsed: string[]
  policiesEvaluated: string[]
  timestamp: Date
  metadata?: Record<string, any>
}

// Attribute lineage for compliance
export interface AttributeLineage {
  attributeId: string
  value: any
  source: string
  derivedFrom?: string[]
  transformations?: AttributeTransformation[]
  timestamp: Date
}

export interface AttributeTransformation {
  type: string
  function: string
  parameters: any[]
  timestamp: Date
}

// ABAC testing and simulation
export interface ABACTestCase {
  id: string
  name: string
  description?: string
  request: ABACRequest
  expectedDecision: 'permit' | 'deny'
  expectedPolicies?: string[]
  mockAttributes?: Record<string, any>
}

export interface ABACSimulation {
  testCases: ABACTestCase[]
  policies: string[]
  attributeOverrides?: Record<string, any>
}

export interface ABACSimulationResult {
  testResults: ABACTestResult[]
  coverage: ABACCoverage
  performance: ABACPerformanceMetrics
}

export interface ABACTestResult {
  testCaseId: string
  passed: boolean
  actualDecision: string
  expectedDecision: string
  evaluationResult: ABACResult
}

export interface ABACCoverage {
  totalPolicies: number
  coveredPolicies: number
  totalRules: number
  coveredRules: number
  totalAttributes: number
  usedAttributes: number
}

export interface ABACPerformanceMetrics {
  averageEvaluationTime: number
  maxEvaluationTime: number
  attributeResolutionTime: number
  policyEvaluationTime: number
}

// Utility types
export type AttributeMap = Record<string, AttributeValue>
export type PolicyEvaluator = (context: ABACContext, policy: ABACPolicy) => Promise<ABACResult>
export type AttributeExtractor = (entity: any) => AttributeMap

// Constants
export const ABAC_VERSION = '1.0.0'
export const MAX_EVALUATION_TIME = 5000 // 5 seconds
export const DEFAULT_CACHE_TIMEOUT = 300 // 5 minutes
export const MAX_ATTRIBUTE_DEPTH = 10
