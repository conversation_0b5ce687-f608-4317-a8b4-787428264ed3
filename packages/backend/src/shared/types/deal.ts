/**
 * Deal Pipeline Management Type Definitions
 * 
 * This file defines the comprehensive type system for deal pipeline management including:
 * - Deal entities and their relationships
 * - Pipeline stages and workflow
 * - Activities and task management
 * - Team collaboration and contacts
 * - Forecasting and analytics
 */

import { 
  DealStatus, 
  DealPriority, 
  DealType, 
  DealSource, 
  ForecastCategory, 
  RiskLevel,
  ActivityType,
  TaskStatus,
  TaskPriority,
  MilestoneStatus
} from '@prisma/client'

// Core Deal Types
export interface Deal {
  id: string
  title: string
  description?: string
  status: DealStatus
  stage: string
  dealValue?: number
  currency: string
  priority: DealPriority
  
  // Enhanced deal information
  dealType: DealType
  dealSource: DealSource
  confidentiality?: string
  tags: string[]
  
  // Target company information
  targetCompany: string
  targetIndustry?: string
  targetRevenue?: number
  targetEmployees?: number
  targetLocation?: string
  targetWebsite?: string
  targetDescription?: string

  // Financial information
  enterpriseValue?: number
  equityValue?: number
  ebitda?: number
  revenue?: number
  multiple?: number
  
  // Timeline and milestones
  expectedCloseDate?: Date
  actualCloseDate?: Date
  firstContactDate?: Date
  loiSignedDate?: Date
  ddStartDate?: Date
  ddEndDate?: Date

  // Pipeline tracking
  currentStageId?: string
  stageEnteredAt?: Date
  daysInCurrentStage?: number
  totalDaysInPipeline?: number
  
  // Probability and forecasting
  probability?: number
  weightedValue?: number
  forecastCategory: ForecastCategory
  
  // Deal health and scoring
  healthScore?: number
  riskLevel: RiskLevel
  lastActivityDate?: Date
  
  // Competitive information
  competitiveProcess: boolean
  competitors: string[]
  
  // Internal tracking
  internalNotes?: string
  nextSteps?: string
  keyRisks: string[]
  keyOpportunities: string[]

  // Metadata
  tenantId: string
  createdBy: string
  assignedTo?: string
  createdAt: Date
  updatedAt: Date

  // Relations
  currentStage?: DealStage
  stageHistory?: DealStageHistory[]
  activities?: DealActivity[]
  contacts?: DealContact[]
  team?: DealTeamMember[]
  tasks?: DealTask[]
  notes?: DealNote[]
  milestones?: DealMilestone[]
}

// Deal Stage Management
export interface DealStage {
  id: string
  name: string
  description?: string
  order: number
  isActive: boolean
  color?: string
  
  // Stage configuration
  isDefault: boolean
  isClosing: boolean
  
  // Automation settings
  autoAdvance: boolean
  requiredFields: string[]
  
  // Metadata
  tenantId: string
  createdAt: Date
  updatedAt: Date
}

export interface DealStageHistory {
  id: string
  dealId: string
  stageId: string
  enteredAt: Date
  exitedAt?: Date
  daysInStage?: number
  
  // Change tracking
  changedBy: string
  reason?: string
  notes?: string
  
  // Relations
  deal?: Deal
  stage?: DealStage
}

// Deal Activities
export interface DealActivity {
  id: string
  dealId: string
  type: ActivityType
  subject: string
  description?: string
  
  // Activity details
  startTime: Date
  endTime?: Date
  location?: string
  isCompleted: boolean
  
  // Participants
  createdBy: string
  attendees: string[]
  
  // External participants
  externalAttendees?: any[]
  
  // Follow-up
  followUpDate?: Date
  followUpNotes?: string
  
  // Metadata
  createdAt: Date
  updatedAt: Date
  
  // Relations
  deal?: Deal
}

// Deal Contacts
export interface DealContact {
  id: string
  dealId: string
  
  // Contact information
  firstName: string
  lastName: string
  email?: string
  phone?: string
  title?: string
  company?: string
  
  // Contact role
  role?: string
  isPrimary: boolean
  isDecisionMaker: boolean
  
  // Contact details
  notes?: string
  linkedInUrl?: string
  
  // Metadata
  createdAt: Date
  updatedAt: Date
}

// Deal Team Management
export interface DealTeamMember {
  id: string
  dealId: string
  userId: string
  role: string
  
  // Permissions
  canEdit: boolean
  canView: boolean
  
  // Metadata
  createdAt: Date
  
  // Relations
  user?: any // User type from auth system
}

// Deal Tasks
export interface DealTask {
  id: string
  dealId: string
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  
  // Task details
  dueDate?: Date
  completedAt?: Date
  estimatedHours?: number
  actualHours?: number
  
  // Assignment
  assignedTo?: string
  createdBy: string
  
  // Task dependencies
  dependsOn: string[]
  blockedBy: string[]
  
  // Metadata
  createdAt: Date
  updatedAt: Date
  
  // Relations
  deal?: Deal
  assignee?: any
  creator?: any
}

// Deal Notes
export interface DealNote {
  id: string
  dealId: string
  content: string
  isPrivate: boolean
  
  // Note metadata
  tags: string[]
  
  // Relations
  createdBy: string
  
  // Metadata
  createdAt: Date
  updatedAt: Date
}

// Deal Milestones
export interface DealMilestone {
  id: string
  dealId: string
  name: string
  description?: string
  status: MilestoneStatus
  
  // Milestone timing
  targetDate: Date
  actualDate?: Date
  
  // Milestone details
  isRequired: boolean
  order: number
  
  // Metadata
  createdAt: Date
  updatedAt: Date
}

// Deal Pipeline Configuration
export interface DealPipeline {
  id: string
  name: string
  description?: string
  isDefault: boolean
  isActive: boolean
  
  // Pipeline configuration
  stages: any[] // Array of stage configurations
  
  // Metadata
  tenantId: string
  createdAt: Date
  updatedAt: Date
}

// API Request/Response Types
export interface CreateDealRequest {
  title: string
  description?: string
  dealType: DealType
  dealSource: DealSource
  targetCompany: string
  targetIndustry?: string
  dealValue?: number
  currency?: string
  priority?: DealPriority
  expectedCloseDate?: Date
  assignedTo?: string
  tags?: string[]
  confidentiality?: string
}

export interface UpdateDealRequest {
  title?: string
  description?: string
  status?: DealStatus
  stage?: string
  dealValue?: number
  priority?: DealPriority
  expectedCloseDate?: Date
  assignedTo?: string
  tags?: string[]
  probability?: number
  healthScore?: number
  riskLevel?: RiskLevel
  nextSteps?: string
  internalNotes?: string
}

export interface DealFilters {
  status?: DealStatus[]
  stage?: string[]
  assignedTo?: string[]
  priority?: DealPriority[]
  dealType?: DealType[]
  dealSource?: DealSource[]
  riskLevel?: RiskLevel[]
  minValue?: number
  maxValue?: number
  expectedCloseDateFrom?: Date
  expectedCloseDateTo?: Date
  tags?: string[]
  search?: string
}

export interface DealSortOptions {
  field: 'title' | 'dealValue' | 'expectedCloseDate' | 'createdAt' | 'updatedAt' | 'priority' | 'healthScore'
  direction: 'asc' | 'desc'
}

// Analytics and Reporting Types
export interface DealAnalytics {
  totalDeals: number
  totalValue: number
  averageDealSize: number
  averageDaysInPipeline: number
  conversionRate: number
  
  // By status
  dealsByStatus: Record<DealStatus, number>
  valueByStatus: Record<DealStatus, number>
  
  // By stage
  dealsByStage: Record<string, number>
  valueByStage: Record<string, number>
  
  // By time period
  dealsCreatedThisMonth: number
  dealsClosedThisMonth: number
  valueClosedThisMonth: number
  
  // Forecasting
  forecastedValue: number
  weightedPipelineValue: number
  
  // Performance metrics
  averageHealthScore: number
  dealsAtRisk: number
  overdueDeals: number
}

export interface PipelineMetrics {
  stageId: string
  stageName: string
  dealCount: number
  totalValue: number
  averageDaysInStage: number
  conversionRate: number
  dropOffRate: number
}

export interface DealForecast {
  period: string // e.g., "2024-Q1"
  bestCase: number
  commit: number
  pipeline: number
  closed: number
  confidence: number
}

// Utility Types
export type DealWithRelations = Deal & {
  currentStage: DealStage
  activities: DealActivity[]
  contacts: DealContact[]
  team: DealTeamMember[]
  tasks: DealTask[]
  notes: DealNote[]
  milestones: DealMilestone[]
}

export type DealSummary = Pick<Deal, 
  'id' | 'title' | 'status' | 'stage' | 'dealValue' | 'currency' | 
  'priority' | 'expectedCloseDate' | 'assignedTo' | 'healthScore' | 'probability'
>

// Constants
export const DEAL_HEALTH_THRESHOLDS = {
  EXCELLENT: 80,
  GOOD: 60,
  FAIR: 40,
  POOR: 20
} as const

export const DEAL_RISK_COLORS = {
  LOW: '#10B981',
  MEDIUM: '#F59E0B', 
  HIGH: '#EF4444',
  CRITICAL: '#DC2626'
} as const

export const DEAL_STATUS_COLORS = {
  PIPELINE: '#6B7280',
  DUE_DILIGENCE: '#3B82F6',
  NEGOTIATION: '#F59E0B',
  CLOSING: '#10B981',
  CLOSED: '#059669',
  CANCELLED: '#EF4444'
} as const
