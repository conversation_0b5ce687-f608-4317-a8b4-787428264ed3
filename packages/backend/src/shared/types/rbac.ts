/**
 * Role-Based Access Control (RBAC) Type Definitions
 * 
 * This file defines the comprehensive type system for RBAC including:
 * - Permission definitions
 * - Role structures
 * - Access control patterns
 * - Resource-based permissions
 */

// Core permission structure
export interface Permission {
  id: string
  resource: string
  action: string
  conditions?: PermissionCondition[]
  metadata?: Record<string, any>
}

// Permission conditions for fine-grained access control
export interface PermissionCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than'
  value: any
}

// Role definition with hierarchical support
export interface Role {
  id: string
  name: string
  description?: string
  permissions: string[] // Array of permission strings
  isSystem: boolean
  isDefault: boolean
  tenantId: string
  parentRoleId?: string // For role hierarchy
  metadata?: RoleMetadata
  createdAt: Date
  updatedAt: Date
}

export interface RoleMetadata {
  color?: string
  icon?: string
  priority?: number
  category?: string
  tags?: string[]
}

// User role assignment
export interface UserRole {
  id: string
  userId: string
  roleId: string
  assignedBy?: string
  assignedAt: Date
  expiresAt?: Date
  isActive: boolean
  conditions?: UserRoleCondition[]
}

export interface UserRoleCondition {
  type: 'time_based' | 'location_based' | 'ip_based' | 'custom'
  configuration: Record<string, any>
}

// Permission string format: "resource:action:scope"
// Examples:
// - "deals:read:own" - Read own deals
// - "deals:write:all" - Write all deals
// - "users:delete:tenant" - Delete users in tenant
// - "settings:admin:*" - All admin settings permissions

export type PermissionString = string

// Resource definitions for the M&A platform
export enum Resource {
  // Core entities
  DEALS = 'deals',
  DOCUMENTS = 'documents',
  USERS = 'users',
  TENANTS = 'tenants',
  ROLES = 'roles',
  
  // Deal-specific resources
  VALUATIONS = 'valuations',
  DUE_DILIGENCE = 'due_diligence',
  DEAL_TEAMS = 'deal_teams',
  DEAL_STAGES = 'deal_stages',
  
  // Document-specific resources
  DOCUMENT_VERSIONS = 'document_versions',
  DOCUMENT_COMMENTS = 'document_comments',
  DOCUMENT_APPROVALS = 'document_approvals',
  
  // Administrative resources
  SETTINGS = 'settings',
  AUDIT_LOGS = 'audit_logs',
  REPORTS = 'reports',
  INTEGRATIONS = 'integrations',
  API_KEYS = 'api_keys',
  
  // Communication resources
  NOTIFICATIONS = 'notifications',
  MESSAGES = 'messages',
  ANNOUNCEMENTS = 'announcements'
}

// Action definitions
export enum Action {
  // CRUD operations
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  
  // Special actions
  APPROVE = 'approve',
  REJECT = 'reject',
  ASSIGN = 'assign',
  UNASSIGN = 'unassign',
  SHARE = 'share',
  EXPORT = 'export',
  IMPORT = 'import',
  
  // Administrative actions
  ADMIN = 'admin',
  MANAGE = 'manage',
  CONFIGURE = 'configure',
  
  // Deal-specific actions
  CLOSE_DEAL = 'close_deal',
  REOPEN_DEAL = 'reopen_deal',
  ARCHIVE_DEAL = 'archive_deal',
  
  // Document-specific actions
  UPLOAD = 'upload',
  DOWNLOAD = 'download',
  SIGN = 'sign',
  REVIEW = 'review'
}

// Scope definitions for permission granularity
export enum Scope {
  OWN = 'own',           // Only resources owned by the user
  TEAM = 'team',         // Resources within user's team
  TENANT = 'tenant',     // All resources within the tenant
  ALL = 'all',           // All resources (super admin)
  NONE = 'none'          // No access
}

// Predefined system roles
export enum SystemRole {
  SUPER_ADMIN = 'super_admin',
  TENANT_ADMIN = 'tenant_admin',
  DEAL_MANAGER = 'deal_manager',
  SENIOR_ANALYST = 'senior_analyst',
  ANALYST = 'analyst',
  VIEWER = 'viewer',
  GUEST = 'guest'
}

// Permission sets for different roles
export const PERMISSION_SETS = {
  [SystemRole.SUPER_ADMIN]: [
    '*:*:*' // All permissions
  ],
  
  [SystemRole.TENANT_ADMIN]: [
    'deals:*:tenant',
    'documents:*:tenant',
    'users:*:tenant',
    'roles:*:tenant',
    'settings:*:tenant',
    'reports:*:tenant',
    'audit_logs:read:tenant',
    'integrations:*:tenant',
    'api_keys:*:tenant'
  ],
  
  [SystemRole.DEAL_MANAGER]: [
    'deals:*:team',
    'deals:read:tenant',
    'documents:*:team',
    'documents:read:tenant',
    'valuations:*:team',
    'due_diligence:*:team',
    'deal_teams:manage:team',
    'users:read:tenant',
    'reports:read:team'
  ],
  
  [SystemRole.SENIOR_ANALYST]: [
    'deals:create:tenant',
    'deals:read:tenant',
    'deals:update:own',
    'documents:*:own',
    'documents:read:team',
    'valuations:*:own',
    'due_diligence:*:own',
    'reports:read:own'
  ],
  
  [SystemRole.ANALYST]: [
    'deals:read:team',
    'deals:update:own',
    'documents:read:team',
    'documents:create:own',
    'documents:update:own',
    'valuations:read:team',
    'valuations:create:own',
    'due_diligence:read:team',
    'due_diligence:update:own'
  ],
  
  [SystemRole.VIEWER]: [
    'deals:read:team',
    'documents:read:team',
    'valuations:read:team',
    'due_diligence:read:team'
  ],
  
  [SystemRole.GUEST]: [
    'deals:read:own',
    'documents:read:own'
  ]
} as const

// Permission validation utilities
export interface PermissionCheck {
  resource: Resource | string
  action: Action | string
  scope?: Scope | string
  resourceId?: string
  conditions?: Record<string, any>
}

export interface AccessContext {
  userId: string
  tenantId: string
  roles: Role[]
  teamIds?: string[]
  metadata?: Record<string, any>
}

// Role hierarchy and inheritance
export interface RoleHierarchy {
  roleId: string
  parentRoleId?: string
  children: RoleHierarchy[]
  inheritedPermissions: string[]
}

// Permission evaluation result
export interface PermissionResult {
  granted: boolean
  reason?: string
  conditions?: PermissionCondition[]
  metadata?: Record<string, any>
}

// Audit trail for permission changes
export interface PermissionAudit {
  id: string
  userId: string
  tenantId: string
  action: 'grant' | 'revoke' | 'modify'
  resourceType: 'role' | 'permission' | 'user_role'
  resourceId: string
  changes: Record<string, any>
  performedBy: string
  timestamp: Date
  reason?: string
}

// Dynamic permission evaluation
export interface DynamicPermission {
  id: string
  name: string
  description: string
  evaluator: string // Function name or script
  parameters: Record<string, any>
  cacheTimeout?: number
}

// Permission templates for common scenarios
export interface PermissionTemplate {
  id: string
  name: string
  description: string
  category: string
  permissions: string[]
  variables?: Record<string, any>
}

// Role assignment rules
export interface RoleAssignmentRule {
  id: string
  name: string
  description: string
  conditions: RoleAssignmentCondition[]
  targetRoleId: string
  isActive: boolean
  priority: number
}

export interface RoleAssignmentCondition {
  type: 'user_attribute' | 'tenant_attribute' | 'time_based' | 'custom'
  field: string
  operator: string
  value: any
}

// Export utility types
export type PermissionMatrix = Record<string, Record<string, boolean>>
export type RolePermissionMap = Record<string, string[]>
export type UserPermissionCache = Record<string, PermissionResult>

// Constants for permission validation
export const PERMISSION_SEPARATOR = ':'
export const WILDCARD = '*'
export const PERMISSION_CACHE_TTL = 300 // 5 minutes in seconds

// Helper type for permission string parsing
export interface ParsedPermission {
  resource: string
  action: string
  scope: string
  isWildcard: boolean
  specificity: number
}
