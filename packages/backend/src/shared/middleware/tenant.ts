import { CacheService } from '@/application/services/cache.service'
import { prisma } from '@/infrastructure/database/prisma'
import { NextFunction, Response } from 'express'
import { AuthenticatedRequest } from './auth'
import { ForbiddenError, NotFoundError } from './error-handler'

export interface TenantRequest extends AuthenticatedRequest {
  tenantContext?: {
    id: string
    name: string
    slug: string
    domain?: string
    subdomain: string
    status: string
    plan: string
    features: any
    limits: any
    settings: any
    branding: any
  }
}

// Extract tenant from subdomain
export async function extractTenantFromSubdomain(
  req: TenantRequest,
  res: Response,
  next: NextFunction
) {
  try {
    const host = req.get('host')
    if (!host) {
      return next()
    }

    // Extract subdomain (e.g., "demo.ma-platform.com" -> "demo")
    const parts = host.split('.')
    if (parts.length < 3) {
      return next() // No subdomain
    }

    const subdomain = parts[0]
    if (subdomain === 'www' || subdomain === 'api') {
      return next() // Skip common subdomains
    }

    // Try to get tenant from cache first
    let tenant = await CacheService.getCachedTenant(`subdomain:${subdomain}`)
    
    if (!tenant) {
      // Fetch from database
      tenant = await prisma.tenant.findUnique({
        where: { subdomain }
      })

      if (tenant) {
        // Cache for 1 hour
        await CacheService.cacheTenant(`subdomain:${subdomain}`, tenant, 3600)
      }
    }

    if (tenant && (tenant.status === 'ACTIVE' || tenant.status === 'TRIAL')) {
      req.tenantContext = {
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        domain: tenant.domain,
        subdomain: tenant.subdomain,
        status: tenant.status,
        plan: tenant.plan,
        features: tenant.features || {},
        limits: tenant.limits || {},
        settings: tenant.settings || {},
        branding: tenant.branding || {}
      }
    }

    next()
  } catch (error) {
    next(error)
  }
}

// Extract tenant from custom domain
export async function extractTenantFromDomain(
  req: TenantRequest,
  res: Response,
  next: NextFunction
) {
  try {
    const host = req.get('host')
    if (!host || req.tenantContext) {
      return next() // Already have tenant context or no host
    }

    // Try to get tenant from cache first
    let tenant = await CacheService.getCachedTenant(`domain:${host}`)
    
    if (!tenant) {
      // Fetch from database
      tenant = await prisma.tenant.findUnique({
        where: { domain: host }
      })

      if (tenant) {
        // Cache for 1 hour
        await CacheService.cacheTenant(`domain:${host}`, tenant, 3600)
      }
    }

    if (tenant && tenant.status === 'ACTIVE') {
      req.tenantContext = {
        id: tenant.id,
        name: tenant.name,
        domain: tenant.domain,
        subdomain: tenant.subdomain,
        settings: tenant.settings
      }
    }

    next()
  } catch (error) {
    next(error)
  }
}

// Require tenant context
export function requireTenant(req: TenantRequest, res: Response, next: NextFunction) {
  if (!req.tenantContext) {
    return next(new NotFoundError('Tenant not found'))
  }

  if (req.user && req.user.tenantId !== req.tenantContext.id) {
    return next(new ForbiddenError('User does not belong to this tenant'))
  }

  next()
}

// Ensure all database queries are tenant-scoped
export function enforceTenantIsolation(
  req: TenantRequest,
  res: Response,
  next: NextFunction
) {
  if (!req.user) {
    return next()
  }

  // Add tenant filter to Prisma queries
  const originalPrisma = prisma
  
  // Override Prisma methods to automatically include tenant filter
  const tenantId = req.user.tenantId

  // This is a simplified example - in practice, you'd want to use Prisma middleware
  // or a more sophisticated approach to ensure tenant isolation
  req.prisma = new Proxy(originalPrisma, {
    get(target, prop) {
      const value = target[prop as keyof typeof target]
      
      if (typeof value === 'object' && value !== null) {
        // For model delegates (user, deal, etc.)
        return new Proxy(value, {
          get(modelTarget, modelProp) {
            const modelValue = modelTarget[modelProp as keyof typeof modelTarget]
            
            if (typeof modelValue === 'function') {
              return function(...args: any[]) {
                // Add tenant filter to queries
                if (args[0] && typeof args[0] === 'object') {
                  if (args[0].where) {
                    args[0].where.tenantId = tenantId
                  } else {
                    args[0].where = { tenantId }
                  }
                }
                
                return modelValue.apply(modelTarget, args)
              }
            }
            
            return modelValue
          }
        })
      }
      
      return value
    }
  })

  next()
}

// Validate tenant access for specific resources
export function validateTenantResource(resourceTenantIdField = 'tenantId') {
  return async (req: TenantRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next()
    }

    const resourceId = req.params.id
    if (!resourceId) {
      return next()
    }

    try {
      // This would need to be implemented per resource type
      // For now, we'll assume the resource has a tenantId field
      const resource = await prisma.$queryRaw`
        SELECT ${resourceTenantIdField} as tenantId 
        FROM ${req.route.path.split('/')[1]} 
        WHERE id = ${resourceId}
      `

      if (resource && resource[0]?.tenantId !== req.user.tenantId) {
        return next(new ForbiddenError('Access denied to this resource'))
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// Tenant-aware rate limiting
export function tenantRateLimit(req: TenantRequest, res: Response, next: NextFunction) {
  if (!req.tenantContext) {
    return next()
  }

  // Implement tenant-specific rate limiting
  const tenantId = req.tenantContext.id
  const key = `rate_limit:tenant:${tenantId}:${req.ip}`
  
  // This would integrate with your rate limiting service
  // For now, we'll just pass through
  next()
}

// Middleware to check tenant features
export function requireTenantFeature(feature: string) {
  return (req: TenantRequest, res: Response, next: NextFunction) => {
    if (!req.tenantContext) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'TENANT_REQUIRED',
          message: 'Tenant identification required'
        }
      })
    }

    const features = req.tenantContext.features || {}
    if (!features[feature]) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FEATURE_NOT_AVAILABLE',
          message: `Feature '${feature}' is not available for this tenant`
        }
      })
    }

    next()
  }
}

// Middleware to check tenant limits
export function checkTenantLimit(limitType: string) {
  return async (req: TenantRequest, res: Response, next: NextFunction) => {
    if (!req.tenantContext) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'TENANT_REQUIRED',
          message: 'Tenant identification required'
        }
      })
    }

    const limits = req.tenantContext.limits || {}
    const limit = limits[limitType]

    // -1 means unlimited
    if (limit === -1) {
      return next()
    }

    // If no limit is set, allow
    if (typeof limit !== 'number') {
      return next()
    }

    // Get current usage
    const usage = await getCurrentUsage(req.tenantContext.id, limitType)

    if (usage >= limit) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'LIMIT_EXCEEDED',
          message: `Tenant limit exceeded for ${limitType}`,
          details: {
            limit,
            current: usage
          }
        }
      })
    }

    next()
  }
}

// Helper function to get current usage
async function getCurrentUsage(tenantId: string, limitType: string): Promise<number> {
  try {
    // Cache usage data for performance
    const cacheKey = `tenant:usage:${tenantId}:${limitType}`
    let usage = await CacheService.get(cacheKey)

    if (usage !== null) {
      return usage
    }

    // Calculate usage based on limit type
    switch (limitType) {
      case 'maxUsers':
        usage = await getUserCount(tenantId)
        break
      case 'maxDeals':
        usage = await getDealCount(tenantId)
        break
      case 'maxStorage':
        usage = await getStorageUsage(tenantId)
        break
      default:
        usage = 0
    }

    // Cache for 5 minutes
    await CacheService.set(cacheKey, usage, 300)

    return usage
  } catch (error) {
    console.error('Error getting current usage:', error)
    return 0
  }
}

async function getUserCount(tenantId: string): Promise<number> {
  const count = await prisma.user.count({
    where: {
      tenantId,
      deletedAt: null
    }
  })
  return count
}

async function getDealCount(tenantId: string): Promise<number> {
  const count = await prisma.deal.count({
    where: { tenantId }
  })
  return count
}

async function getStorageUsage(tenantId: string): Promise<number> {
  const result = await prisma.document.aggregate({
    where: { tenantId },
    _sum: { size: true }
  })
  return result._sum.size || 0
}

declare global {
  namespace Express {
    interface Request {
      prisma?: typeof prisma
    }
  }
}
