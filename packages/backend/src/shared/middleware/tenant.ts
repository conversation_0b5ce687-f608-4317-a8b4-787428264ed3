import { Request, Response, NextFunction } from 'express'
import { prisma } from '@/infrastructure/database/prisma'
import { CacheService } from '@/application/services/cache.service'
import { NotFoundError, ForbiddenError } from './error-handler'
import { AuthenticatedRequest } from './auth'

export interface TenantRequest extends AuthenticatedRequest {
  tenantContext?: {
    id: string
    name: string
    domain: string
    subdomain: string
    settings: any
  }
}

// Extract tenant from subdomain
export async function extractTenantFromSubdomain(
  req: TenantRequest,
  res: Response,
  next: NextFunction
) {
  try {
    const host = req.get('host')
    if (!host) {
      return next()
    }

    // Extract subdomain (e.g., "demo.ma-platform.com" -> "demo")
    const parts = host.split('.')
    if (parts.length < 3) {
      return next() // No subdomain
    }

    const subdomain = parts[0]
    if (subdomain === 'www' || subdomain === 'api') {
      return next() // Skip common subdomains
    }

    // Try to get tenant from cache first
    let tenant = await CacheService.getCachedTenant(`subdomain:${subdomain}`)
    
    if (!tenant) {
      // Fetch from database
      tenant = await prisma.tenant.findUnique({
        where: { subdomain }
      })

      if (tenant) {
        // Cache for 1 hour
        await CacheService.cacheTenant(`subdomain:${subdomain}`, tenant, 3600)
      }
    }

    if (tenant && tenant.status === 'ACTIVE') {
      req.tenantContext = {
        id: tenant.id,
        name: tenant.name,
        domain: tenant.domain,
        subdomain: tenant.subdomain,
        settings: tenant.settings
      }
    }

    next()
  } catch (error) {
    next(error)
  }
}

// Extract tenant from custom domain
export async function extractTenantFromDomain(
  req: TenantRequest,
  res: Response,
  next: NextFunction
) {
  try {
    const host = req.get('host')
    if (!host || req.tenantContext) {
      return next() // Already have tenant context or no host
    }

    // Try to get tenant from cache first
    let tenant = await CacheService.getCachedTenant(`domain:${host}`)
    
    if (!tenant) {
      // Fetch from database
      tenant = await prisma.tenant.findUnique({
        where: { domain: host }
      })

      if (tenant) {
        // Cache for 1 hour
        await CacheService.cacheTenant(`domain:${host}`, tenant, 3600)
      }
    }

    if (tenant && tenant.status === 'ACTIVE') {
      req.tenantContext = {
        id: tenant.id,
        name: tenant.name,
        domain: tenant.domain,
        subdomain: tenant.subdomain,
        settings: tenant.settings
      }
    }

    next()
  } catch (error) {
    next(error)
  }
}

// Require tenant context
export function requireTenant(req: TenantRequest, res: Response, next: NextFunction) {
  if (!req.tenantContext) {
    return next(new NotFoundError('Tenant not found'))
  }

  if (req.user && req.user.tenantId !== req.tenantContext.id) {
    return next(new ForbiddenError('User does not belong to this tenant'))
  }

  next()
}

// Ensure all database queries are tenant-scoped
export function enforceTenantIsolation(
  req: TenantRequest,
  res: Response,
  next: NextFunction
) {
  if (!req.user) {
    return next()
  }

  // Add tenant filter to Prisma queries
  const originalPrisma = prisma
  
  // Override Prisma methods to automatically include tenant filter
  const tenantId = req.user.tenantId

  // This is a simplified example - in practice, you'd want to use Prisma middleware
  // or a more sophisticated approach to ensure tenant isolation
  req.prisma = new Proxy(originalPrisma, {
    get(target, prop) {
      const value = target[prop as keyof typeof target]
      
      if (typeof value === 'object' && value !== null) {
        // For model delegates (user, deal, etc.)
        return new Proxy(value, {
          get(modelTarget, modelProp) {
            const modelValue = modelTarget[modelProp as keyof typeof modelTarget]
            
            if (typeof modelValue === 'function') {
              return function(...args: any[]) {
                // Add tenant filter to queries
                if (args[0] && typeof args[0] === 'object') {
                  if (args[0].where) {
                    args[0].where.tenantId = tenantId
                  } else {
                    args[0].where = { tenantId }
                  }
                }
                
                return modelValue.apply(modelTarget, args)
              }
            }
            
            return modelValue
          }
        })
      }
      
      return value
    }
  })

  next()
}

// Validate tenant access for specific resources
export function validateTenantResource(resourceTenantIdField = 'tenantId') {
  return async (req: TenantRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next()
    }

    const resourceId = req.params.id
    if (!resourceId) {
      return next()
    }

    try {
      // This would need to be implemented per resource type
      // For now, we'll assume the resource has a tenantId field
      const resource = await prisma.$queryRaw`
        SELECT ${resourceTenantIdField} as tenantId 
        FROM ${req.route.path.split('/')[1]} 
        WHERE id = ${resourceId}
      `

      if (resource && resource[0]?.tenantId !== req.user.tenantId) {
        return next(new ForbiddenError('Access denied to this resource'))
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// Tenant-aware rate limiting
export function tenantRateLimit(req: TenantRequest, res: Response, next: NextFunction) {
  if (!req.tenantContext) {
    return next()
  }

  // Implement tenant-specific rate limiting
  const tenantId = req.tenantContext.id
  const key = `rate_limit:tenant:${tenantId}:${req.ip}`
  
  // This would integrate with your rate limiting service
  // For now, we'll just pass through
  next()
}

declare global {
  namespace Express {
    interface Request {
      prisma?: typeof prisma
    }
  }
}
