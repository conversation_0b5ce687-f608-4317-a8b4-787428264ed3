import { Request, Response, NextFunction } from 'express'
import rateLimit from 'express-rate-limit'
import { env } from '@/shared/config/env'
import { CacheService } from '@/application/services/cache.service'

// Security headers middleware
export function securityHeaders(req: Request, res: Response, next: NextFunction) {
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY')
  
  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff')
  
  // XSS protection
  res.setHeader('X-XSS-Protection', '1; mode=block')
  
  // Referrer policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  // Permissions policy
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
  
  // HSTS (only in production with HTTPS)
  if (env.NODE_ENV === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
  }
  
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Adjust as needed
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self'",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ')
  
  res.setHeader('Content-Security-Policy', csp)
  
  next()
}

// Rate limiting configuration
export const createRateLimit = (options: {
  windowMs?: number
  max?: number
  message?: string
  keyGenerator?: (req: Request) => string
}) => {
  return rateLimit({
    windowMs: options.windowMs || env.RATE_LIMIT_WINDOW_MS,
    max: options.max || env.RATE_LIMIT_MAX_REQUESTS,
    message: {
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: options.message || 'Too many requests, please try again later'
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: options.keyGenerator || ((req) => req.ip),
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/health' || req.path === '/api/health'
    }
  })
}

// Different rate limits for different endpoints
export const authRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many authentication attempts, please try again later'
})

export const apiRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  message: 'Too many API requests, please try again later'
})

export const uploadRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 uploads per hour
  message: 'Too many file uploads, please try again later'
})

// IP whitelist middleware
export function ipWhitelist(allowedIPs: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress
    
    if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'IP_NOT_ALLOWED',
          message: 'Access denied from this IP address'
        }
      })
    }
    
    next()
  }
}

// Request size limiting
export function requestSizeLimit(maxSize: string = '10mb') {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = req.get('content-length')
    
    if (contentLength) {
      const sizeInBytes = parseInt(contentLength, 10)
      const maxSizeInBytes = parseSize(maxSize)
      
      if (sizeInBytes > maxSizeInBytes) {
        return res.status(413).json({
          success: false,
          error: {
            code: 'REQUEST_TOO_LARGE',
            message: `Request size exceeds limit of ${maxSize}`
          }
        })
      }
    }
    
    next()
  }
}

// Helper function to parse size strings
function parseSize(size: string): number {
  const units: { [key: string]: number } = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  }
  
  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/)
  if (!match) {
    throw new Error(`Invalid size format: ${size}`)
  }
  
  const value = parseFloat(match[1])
  const unit = match[2] || 'b'
  
  return value * units[unit]
}

// CORS configuration
export function corsConfig(req: Request, res: Response, next: NextFunction) {
  const origin = req.get('origin')
  const allowedOrigins = [
    env.FRONTEND_URL,
    'http://localhost:3000',
    'https://app.ma-platform.com',
    'https://staging.ma-platform.com'
  ]
  
  // Allow tenant-specific domains
  if (origin && origin.includes('.ma-platform.com')) {
    allowedOrigins.push(origin)
  }
  
  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin)
  }
  
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Tenant-ID')
  res.setHeader('Access-Control-Allow-Credentials', 'true')
  res.setHeader('Access-Control-Max-Age', '86400') // 24 hours
  
  if (req.method === 'OPTIONS') {
    return res.status(200).end()
  }
  
  next()
}

// Request logging for security monitoring
export function securityLogger(req: Request, res: Response, next: NextFunction) {
  const startTime = Date.now()
  
  res.on('finish', () => {
    const duration = Date.now() - startTime
    const logData = {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('user-agent'),
      statusCode: res.statusCode,
      duration,
      timestamp: new Date().toISOString()
    }
    
    // Log suspicious activity
    if (res.statusCode === 401 || res.statusCode === 403 || res.statusCode === 429) {
      console.warn('Security event:', logData)
    }
    
    // Log slow requests
    if (duration > 5000) {
      console.warn('Slow request:', logData)
    }
  })
  
  next()
}

// Honeypot middleware to detect bots
export function honeypot(req: Request, res: Response, next: NextFunction) {
  // Check for common bot patterns
  const userAgent = req.get('user-agent')?.toLowerCase() || ''
  const suspiciousPatterns = [
    'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget'
  ]
  
  const isSuspicious = suspiciousPatterns.some(pattern => 
    userAgent.includes(pattern)
  )
  
  if (isSuspicious && !req.path.startsWith('/api/public')) {
    // Log the attempt
    console.warn('Suspicious bot activity:', {
      ip: req.ip,
      userAgent,
      path: req.path,
      timestamp: new Date().toISOString()
    })
    
    // Return fake response to waste bot's time
    return res.status(200).json({ message: 'OK' })
  }
  
  next()
}
