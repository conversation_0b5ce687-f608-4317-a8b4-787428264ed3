import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { prisma } from '@/infrastructure/database/prisma'
import { CacheService } from '@/application/services/cache.service'
import { UnauthorizedError, ForbiddenError } from './error-handler'
import { env } from '@/shared/config/env'

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string
    email: string
    tenantId: string
    roles: string[]
    permissions: string[]
  }
  tenant?: {
    id: string
    name: string
    domain: string
    status: string
  }
}

export interface JWTPayload {
  userId: string
  tenantId: string
  sessionId?: string
  iat?: number
  exp?: number
}

export async function authenticateToken(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    if (!token) {
      throw new UnauthorizedError('Access token required')
    }

    // Verify JWT token
    const payload = jwt.verify(token, env.JWT_SECRET) as JWTPayload

    // Check if session exists in cache (for logout functionality)
    if (payload.sessionId) {
      const session = await CacheService.getSession(payload.sessionId)
      if (!session) {
        throw new UnauthorizedError('Session expired or invalid')
      }
    }

    // Get user with tenant and roles
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        tenant: true,
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    if (user.status !== 'ACTIVE') {
      throw new UnauthorizedError('User account is not active')
    }

    if (user.tenant.status !== 'ACTIVE') {
      throw new UnauthorizedError('Tenant account is not active')
    }

    // Extract permissions from roles
    const permissions = new Set<string>()
    const roles = user.userRoles.map(ur => {
      const rolePermissions = ur.role.permissions as string[]
      rolePermissions.forEach(permission => permissions.add(permission))
      return ur.role.name
    })

    // Attach user and tenant to request
    req.user = {
      id: user.id,
      email: user.email,
      tenantId: user.tenantId,
      roles,
      permissions: Array.from(permissions)
    }

    req.tenant = {
      id: user.tenant.id,
      name: user.tenant.name,
      domain: user.tenant.domain,
      status: user.tenant.status
    }

    next()
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new UnauthorizedError('Invalid token'))
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new UnauthorizedError('Token expired'))
    } else {
      next(error)
    }
  }
}

export function requirePermissions(...requiredPermissions: string[]) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new UnauthorizedError('Authentication required'))
    }

    const hasPermission = requiredPermissions.every(permission =>
      req.user!.permissions.includes(permission)
    )

    if (!hasPermission) {
      return next(new ForbiddenError('Insufficient permissions'))
    }

    next()
  }
}

export function requireRoles(...requiredRoles: string[]) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new UnauthorizedError('Authentication required'))
    }

    const hasRole = requiredRoles.some(role =>
      req.user!.roles.includes(role)
    )

    if (!hasRole) {
      return next(new ForbiddenError('Insufficient role privileges'))
    }

    next()
  }
}

export function requireTenantAccess(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  if (!req.user || !req.tenant) {
    return next(new UnauthorizedError('Authentication required'))
  }

  // Check if tenant ID in request matches user's tenant
  const tenantIdFromHeader = req.headers['x-tenant-id']
  const tenantIdFromParams = req.params.tenantId
  const tenantIdFromQuery = req.query.tenantId

  const requestedTenantId = tenantIdFromHeader || tenantIdFromParams || tenantIdFromQuery

  if (requestedTenantId && requestedTenantId !== req.user.tenantId) {
    return next(new ForbiddenError('Access denied to this tenant'))
  }

  next()
}

// Optional authentication - doesn't fail if no token provided
export async function optionalAuth(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    if (token) {
      await authenticateToken(req, res, next)
    } else {
      next()
    }
  } catch (error) {
    // For optional auth, we don't fail on auth errors
    next()
  }
}

// Generate JWT token
export function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, env.JWT_SECRET, {
    expiresIn: env.JWT_EXPIRES_IN
  })
}

// Verify and decode token without throwing
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, env.JWT_SECRET) as JWTPayload
  } catch {
    return null
  }
}
