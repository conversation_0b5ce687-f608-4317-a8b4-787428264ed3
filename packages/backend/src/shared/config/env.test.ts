import { validateEnv } from './env'

describe('Environment Configuration', () => {
  const originalEnv = process.env

  beforeEach(() => {
    jest.resetModules()
    process.env = { ...originalEnv }
  })

  afterAll(() => {
    process.env = originalEnv
  })

  it('should validate valid environment variables', () => {
    process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test'
    process.env.REDIS_URL = 'redis://localhost:6379'
    process.env.JWT_SECRET = 'test-jwt-secret-key-at-least-32-characters-long'
    process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters'

    expect(() => validateEnv()).not.toThrow()
  })

  it('should throw error for missing required variables', () => {
    delete process.env.DATABASE_URL

    expect(() => validateEnv()).toThrow('Missing or invalid environment variables')
  })

  it('should throw error for invalid JWT secret length', () => {
    process.env.JWT_SECRET = 'short'

    expect(() => validateEnv()).toThrow('Missing or invalid environment variables')
  })

  it('should use default values for optional variables', () => {
    process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test'
    process.env.REDIS_URL = 'redis://localhost:6379'
    process.env.JWT_SECRET = 'test-jwt-secret-key-at-least-32-characters-long'
    process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters'

    const env = validateEnv()

    expect(env.NODE_ENV).toBe('development')
    expect(env.PORT).toBe('3001')
    expect(env.FRONTEND_URL).toBe('http://localhost:3000')
  })
})
