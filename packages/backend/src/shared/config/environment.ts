import { config } from 'dotenv'
import { resolve } from 'path'
import { existsSync } from 'fs'
import { logger } from '@/shared/logger'

export function loadEnvironment() {
  const nodeEnv = process.env.NODE_ENV || 'development'
  
  // Load environment-specific config file
  const envFile = resolve(process.cwd(), `config/${nodeEnv}.env`)
  
  if (existsSync(envFile)) {
    logger.info(`Loading environment config from: ${envFile}`)
    config({ path: envFile })
  } else {
    logger.warn(`Environment config file not found: ${envFile}`)
  }
  
  // Load local .env file (overrides environment-specific config)
  const localEnvFile = resolve(process.cwd(), '.env')
  if (existsSync(localEnvFile)) {
    logger.info(`Loading local environment config from: ${localEnvFile}`)
    config({ path: localEnvFile, override: true })
  }
  
  // Load .env.local file (overrides everything, for local development)
  const localOverrideFile = resolve(process.cwd(), '.env.local')
  if (existsSync(localOverrideFile)) {
    logger.info(`Loading local override config from: ${localOverrideFile}`)
    config({ path: localOverrideFile, override: true })
  }
}

export interface EnvironmentConfig {
  nodeEnv: 'development' | 'staging' | 'production' | 'test'
  port: number
  
  // Database
  databaseUrl: string
  
  // Redis
  redisUrl: string
  
  // JWT
  jwtSecret: string
  jwtExpiresIn: string
  
  // Frontend
  frontendUrl: string
  
  // Email
  smtpHost?: string
  smtpPort?: number
  smtpUser?: string
  smtpPass?: string
  
  // File Storage
  storageProvider: 'local' | 's3' | 'gcs'
  localStoragePath?: string
  awsAccessKeyId?: string
  awsSecretAccessKey?: string
  awsRegion?: string
  awsS3Bucket?: string
  cdnUrl?: string
  
  // Encryption
  encryptionKey: string
  
  // Rate Limiting
  rateLimitWindowMs: number
  rateLimitMaxRequests: number
  
  // Logging
  logLevel: string
  
  // Feature Flags
  enableSwaggerDocs: boolean
  enableDebugRoutes: boolean
  enableSeedData: boolean
  
  // Security
  corsOrigin?: string
  secureCookies: boolean
  trustProxy: boolean
  
  // Monitoring
  sentryDsn?: string
  newRelicLicenseKey?: string
  
  // Performance
  clusterMode: boolean
  maxWorkers: number
}

export function getEnvironmentConfig(): EnvironmentConfig {
  return {
    nodeEnv: (process.env.NODE_ENV as any) || 'development',
    port: parseInt(process.env.PORT || '3001', 10),
    
    // Database
    databaseUrl: process.env.DATABASE_URL!,
    
    // Redis
    redisUrl: process.env.REDIS_URL!,
    
    // JWT
    jwtSecret: process.env.JWT_SECRET!,
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
    
    // Frontend
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
    
    // Email
    smtpHost: process.env.SMTP_HOST,
    smtpPort: process.env.SMTP_PORT ? parseInt(process.env.SMTP_PORT, 10) : undefined,
    smtpUser: process.env.SMTP_USER,
    smtpPass: process.env.SMTP_PASS,
    
    // File Storage
    storageProvider: (process.env.STORAGE_PROVIDER as any) || 'local',
    localStoragePath: process.env.LOCAL_STORAGE_PATH,
    awsAccessKeyId: process.env.AWS_ACCESS_KEY_ID,
    awsSecretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    awsRegion: process.env.AWS_REGION,
    awsS3Bucket: process.env.AWS_S3_BUCKET,
    cdnUrl: process.env.CDN_URL,
    
    // Encryption
    encryptionKey: process.env.ENCRYPTION_KEY!,
    
    // Rate Limiting
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10),
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
    
    // Logging
    logLevel: process.env.LOG_LEVEL || 'info',
    
    // Feature Flags
    enableSwaggerDocs: process.env.ENABLE_SWAGGER_DOCS === 'true',
    enableDebugRoutes: process.env.ENABLE_DEBUG_ROUTES === 'true',
    enableSeedData: process.env.ENABLE_SEED_DATA === 'true',
    
    // Security
    corsOrigin: process.env.CORS_ORIGIN,
    secureCookies: process.env.SECURE_COOKIES === 'true',
    trustProxy: process.env.TRUST_PROXY === 'true',
    
    // Monitoring
    sentryDsn: process.env.SENTRY_DSN,
    newRelicLicenseKey: process.env.NEW_RELIC_LICENSE_KEY,
    
    // Performance
    clusterMode: process.env.CLUSTER_MODE === 'true',
    maxWorkers: parseInt(process.env.MAX_WORKERS || '1', 10),
  }
}
