import crypto from 'crypto'
import bcrypt from 'bcryptjs'
import { env } from '@/shared/config/env'

const ALGORITHM = 'aes-256-gcm'
const IV_LENGTH = 16
const SALT_LENGTH = 64
const TAG_LENGTH = 16
const ITERATIONS = 100000

// Derive key from password using PBKDF2
function deriveKey(password: string, salt: Buffer): Buffer {
  return crypto.pbkdf2Sync(password, salt, ITERATIONS, 32, 'sha512')
}

// Encrypt data using AES-256-GCM
export function encrypt(text: string, password?: string): string {
  try {
    const key = password ? 
      deriveKey(password, crypto.randomBytes(SALT_LENGTH)) : 
      Buffer.from(env.ENCRYPTION_KEY, 'hex')
    
    const iv = crypto.randomBytes(IV_LENGTH)
    const cipher = crypto.createCipher(ALGORITHM, key)
    cipher.setAAD(Buffer.from('ma-platform', 'utf8'))
    
    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    const tag = cipher.getAuthTag()
    
    // Combine salt (if used), iv, tag, and encrypted data
    const result = password ? 
      Buffer.concat([
        deriveKey(password, crypto.randomBytes(SALT_LENGTH)),
        iv,
        tag,
        Buffer.from(encrypted, 'hex')
      ]).toString('base64') :
      Buffer.concat([
        iv,
        tag,
        Buffer.from(encrypted, 'hex')
      ]).toString('base64')
    
    return result
  } catch (error) {
    throw new Error('Encryption failed')
  }
}

// Decrypt data using AES-256-GCM
export function decrypt(encryptedData: string, password?: string): string {
  try {
    const data = Buffer.from(encryptedData, 'base64')
    
    let offset = 0
    let key: Buffer
    
    if (password) {
      const salt = data.slice(offset, offset + SALT_LENGTH)
      offset += SALT_LENGTH
      key = deriveKey(password, salt)
    } else {
      key = Buffer.from(env.ENCRYPTION_KEY, 'hex')
    }
    
    const iv = data.slice(offset, offset + IV_LENGTH)
    offset += IV_LENGTH
    
    const tag = data.slice(offset, offset + TAG_LENGTH)
    offset += TAG_LENGTH
    
    const encrypted = data.slice(offset)
    
    const decipher = crypto.createDecipher(ALGORITHM, key)
    decipher.setAuthTag(tag)
    decipher.setAAD(Buffer.from('ma-platform', 'utf8'))
    
    let decrypted = decipher.update(encrypted, undefined, 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  } catch (error) {
    throw new Error('Decryption failed')
  }
}

// Hash password using bcrypt
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12
  return bcrypt.hash(password, saltRounds)
}

// Verify password against hash
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

// Generate secure random token
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex')
}

// Generate cryptographically secure random string
export function generateSecureString(length: number = 16): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, chars.length)
    result += chars[randomIndex]
  }
  
  return result
}

// Hash data using SHA-256
export function hashData(data: string): string {
  return crypto.createHash('sha256').update(data).digest('hex')
}

// Create HMAC signature
export function createSignature(data: string, secret: string): string {
  return crypto.createHmac('sha256', secret).update(data).digest('hex')
}

// Verify HMAC signature
export function verifySignature(data: string, signature: string, secret: string): boolean {
  const expectedSignature = createSignature(data, secret)
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  )
}

// Encrypt sensitive fields in database records
export function encryptSensitiveFields<T extends Record<string, any>>(
  data: T,
  sensitiveFields: (keyof T)[]
): T {
  const result = { ...data }
  
  for (const field of sensitiveFields) {
    if (result[field] && typeof result[field] === 'string') {
      result[field] = encrypt(result[field] as string)
    }
  }
  
  return result
}

// Decrypt sensitive fields in database records
export function decryptSensitiveFields<T extends Record<string, any>>(
  data: T,
  sensitiveFields: (keyof T)[]
): T {
  const result = { ...data }
  
  for (const field of sensitiveFields) {
    if (result[field] && typeof result[field] === 'string') {
      try {
        result[field] = decrypt(result[field] as string)
      } catch (error) {
        // If decryption fails, leave the field as is
        console.warn(`Failed to decrypt field ${String(field)}:`, error)
      }
    }
  }
  
  return result
}

// Generate API key
export function generateApiKey(): string {
  const prefix = 'map_' // M&A Platform prefix
  const key = generateSecureToken(32)
  return `${prefix}${key}`
}

// Validate API key format
export function isValidApiKey(apiKey: string): boolean {
  const pattern = /^map_[a-f0-9]{64}$/
  return pattern.test(apiKey)
}

// Create password reset token
export function createPasswordResetToken(userId: string): {
  token: string
  hashedToken: string
  expiresAt: Date
} {
  const token = generateSecureToken(32)
  const hashedToken = hashData(token)
  const expiresAt = new Date(Date.now() + 60 * 60 * 1000) // 1 hour
  
  return {
    token,
    hashedToken,
    expiresAt
  }
}

// Verify password reset token
export function verifyPasswordResetToken(
  token: string,
  hashedToken: string,
  expiresAt: Date
): boolean {
  if (new Date() > expiresAt) {
    return false
  }
  
  const tokenHash = hashData(token)
  return crypto.timingSafeEqual(
    Buffer.from(tokenHash, 'hex'),
    Buffer.from(hashedToken, 'hex')
  )
}

// Mask sensitive data for logging
export function maskSensitiveData(data: any): any {
  if (typeof data !== 'object' || data === null) {
    return data
  }
  
  const sensitiveKeys = [
    'password', 'token', 'secret', 'key', 'authorization',
    'ssn', 'creditcard', 'email', 'phone'
  ]
  
  const masked = { ...data }
  
  for (const key in masked) {
    if (sensitiveKeys.some(sensitive => 
      key.toLowerCase().includes(sensitive)
    )) {
      if (typeof masked[key] === 'string') {
        masked[key] = '***MASKED***'
      }
    } else if (typeof masked[key] === 'object') {
      masked[key] = maskSensitiveData(masked[key])
    }
  }
  
  return masked
}
