{"name": "@ma-platform/backend", "version": "1.0.0", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint . --ext .ts --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/scripts/seed.ts", "migrate": "tsx src/scripts/run-migrations.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --selectProjects unit", "test:integration": "jest --selectProjects integration", "test:e2e": "jest --selectProjects e2e", "test:all": "jest --selectProjects unit integration e2e"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "zod": "^3.22.4", "@prisma/client": "^5.7.0", "redis": "^4.6.10", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "winston": "^3.11.0", "uuid": "^9.0.1", "@auth/core": "^0.18.6", "@auth/express": "^0.5.6", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "otplib": "^12.0.1", "express-session": "^1.17.3", "connect-redis": "^7.1.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "typescript": "^5.3.2", "tsx": "^4.6.0", "prisma": "^5.7.0", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "@testcontainers/postgresql": "^10.4.0", "testcontainers": "^10.4.0", "nock": "^13.4.0", "faker": "^6.6.6", "@types/faker": "^6.6.9", "@types/speakeasy": "^2.0.10", "@types/qrcode": "^1.5.5", "@types/express-session": "^1.17.10"}}