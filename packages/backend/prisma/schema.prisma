// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Tenant model for multi-tenancy
model Tenant {
  id        String       @id @default(cuid())
  name      String
  domain    String       @unique
  subdomain String       @unique
  status    TenantStatus @default(ACTIVE)
  settings  Json?
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt

  // Relations
  users     User[]
  deals     Deal[]
  documents Document[]
  roles     Role[]

  @@map("tenants")
}

enum TenantStatus {
  ACTIVE
  SUSPENDED
  INACTIVE
}

// User model
model User {
  id          String     @id @default(cuid())
  email       String     @unique
  firstName   String
  lastName    String
  avatar      String?
  status      UserStatus @default(ACTIVE)
  lastLoginAt DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Authentication
  password      String?
  emailVerified Boolean @default(false)

  // Relations
  userRoles        UserRole[]
  sessions         Session[]
  createdDeals     Deal[]             @relation("DealCreator")
  assignedDeals    Deal[]             @relation("DealAssignee")
  documents        Document[]
  auditLogs        AuditLog[]
  DueDiligenceItem DueDiligenceItem[]

  @@map("users")
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

// Role-based access control
model Role {
  id          String   @id @default(cuid())
  name        String
  description String?
  permissions Json // Store permissions as JSON array
  isSystem    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  userRoles UserRole[]

  @@unique([tenantId, name])
  @@map("roles")
}

model UserRole {
  id        String   @id @default(cuid())
  userId    String
  roleId    String
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

// Session management
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Deal management
model Deal {
  id          String       @id @default(cuid())
  title       String
  description String?
  status      DealStatus   @default(PIPELINE)
  stage       String
  value       Decimal?
  currency    String       @default("USD")
  priority    DealPriority @default(MEDIUM)

  // Target company information
  targetCompany  String
  targetIndustry String?
  targetRevenue  Decimal?

  // Timeline
  expectedCloseDate DateTime?
  actualCloseDate   DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  createdById String
  createdBy   User   @relation("DealCreator", fields: [createdById], references: [id])

  assignedToId String?
  assignedTo   User?   @relation("DealAssignee", fields: [assignedToId], references: [id])

  documents         Document[]
  dueDiligenceItems DueDiligenceItem[]
  valuations        Valuation[]

  @@map("deals")
}

enum DealStatus {
  PIPELINE
  DUE_DILIGENCE
  NEGOTIATION
  CLOSING
  CLOSED
  CANCELLED
}

enum DealPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// Document management (VDR)
model Document {
  id           String         @id @default(cuid())
  name         String
  originalName String
  mimeType     String
  size         Int
  path         String
  version      Int            @default(1)
  status       DocumentStatus @default(ACTIVE)

  // Security
  isEncrypted Boolean             @default(false)
  accessLevel DocumentAccessLevel @default(RESTRICTED)

  // Metadata
  tags        String[]
  description String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  uploadedById String
  uploadedBy   User   @relation(fields: [uploadedById], references: [id])

  dealId String?
  deal   Deal?   @relation(fields: [dealId], references: [id])

  parentId String?
  parent   Document?  @relation("DocumentVersions", fields: [parentId], references: [id])
  versions Document[] @relation("DocumentVersions")

  @@map("documents")
}

enum DocumentStatus {
  ACTIVE
  ARCHIVED
  DELETED
}

enum DocumentAccessLevel {
  PUBLIC
  INTERNAL
  RESTRICTED
  CONFIDENTIAL
}

// Due Diligence Management
model DueDiligenceItem {
  id          String       @id @default(cuid())
  title       String
  description String?
  category    String
  status      DDItemStatus @default(PENDING)
  priority    DealPriority @default(MEDIUM)

  // Assignment
  assignedToId String?
  assignedTo   User?   @relation(fields: [assignedToId], references: [id])

  dueDate     DateTime?
  completedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  dealId String
  deal   Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("due_diligence_items")
}

enum DDItemStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  BLOCKED
  NOT_APPLICABLE
}

// Financial modeling and valuation
model Valuation {
  id          String        @id @default(cuid())
  name        String
  type        ValuationType
  methodology String

  // Financial data
  assumptions  Json
  calculations Json
  results      Json

  // DCF specific
  discountRate       Decimal?
  terminalGrowthRate Decimal?

  // Comparable analysis
  multiples Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  dealId String
  deal   Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("valuations")
}

enum ValuationType {
  DCF
  COMPARABLE_COMPANY
  PRECEDENT_TRANSACTION
  ASSET_BASED
  SUM_OF_PARTS
}

// Audit logging
model AuditLog {
  id        String   @id @default(cuid())
  action    String
  entity    String
  entityId  String
  oldValues Json?
  newValues Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}
