// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Tenant model for multi-tenancy
model Tenant {
  id        String       @id @default(cuid())
  name      String
  slug      String       @unique // URL-friendly identifier
  domain    String?      @unique // Custom domain (optional)
  subdomain String       @unique // Subdomain for multi-tenant routing

  // Tenant metadata
  description String?
  logo        String?
  website     String?
  industry    String?
  size        TenantSize?  @default(SMALL)

  // Status and lifecycle
  status      TenantStatus @default(ACTIVE)
  plan        TenantPlan   @default(STARTER)
  trialEndsAt DateTime?

  // Configuration
  settings    Json?        // Tenant-specific settings
  features    Json?        // Enabled features
  limits      Json?        // Usage limits and quotas
  branding    Json?        // Custom branding configuration

  // Billing and subscription
  billingEmail     String?
  subscriptionId   String?
  customerId       String?

  // Timestamps
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
  deletedAt DateTime?    // Soft delete

  // Relations
  users                User[]
  deals                Deal[]
  documents            Document[]
  roles                Role[]
  invitations          TenantInvitation[]
  apiKeys              TenantApiKey[]
  permissionAudits     PermissionAudit[]
  roleAssignmentRules  RoleAssignmentRule[]
  permissionTemplates  PermissionTemplate[]

  @@map("tenants")
}

enum TenantStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  TRIAL
  EXPIRED
  DELETED
}

enum TenantSize {
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

enum TenantPlan {
  STARTER
  PROFESSIONAL
  ENTERPRISE
  CUSTOM
}

// User model
model User {
  id          String     @id @default(cuid())
  email       String     @unique
  firstName   String
  lastName    String
  avatar      String?
  status      UserStatus @default(ACTIVE)
  lastLoginAt DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Authentication
  password      String?
  emailVerified Boolean @default(false)

  // Relations
  userRoles              UserRole[]
  assignedUserRoles      UserRole[]           @relation("UserRoleAssignedBy")
  sessions               Session[]
  createdDeals           Deal[]               @relation("DealCreator")
  assignedDeals          Deal[]               @relation("DealAssignee")
  documents              Document[]
  auditLogs              AuditLog[]
  DueDiligenceItem       DueDiligenceItem[]
  mfa                    UserMfa?
  sentInvitations        TenantInvitation[]   @relation("TenantInviter")
  permissionAuditsUser   PermissionAudit[]    @relation("PermissionAuditUser")
  permissionAuditsPerformer PermissionAudit[] @relation("PermissionAuditPerformer")
  createdRoleAssignmentRules RoleAssignmentRule[]
  createdPermissionTemplates PermissionTemplate[]

  @@map("users")
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

// Role-based access control
model Role {
  id          String   @id @default(cuid())
  name        String
  description String?
  permissions Json     // Store permissions as JSON array
  isSystem    Boolean  @default(false)
  isDefault   Boolean  @default(false)
  priority    Int      @default(0)
  color       String?
  icon        String?
  category    String?
  tags        Json?    // Array of tags
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Role hierarchy support
  parentRoleId String?
  parentRole   Role?  @relation("RoleHierarchy", fields: [parentRoleId], references: [id])
  childRoles   Role[] @relation("RoleHierarchy")

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  userRoles           UserRole[]
  permissionAudits    PermissionAudit[]
  roleAssignmentRules RoleAssignmentRule[]

  @@unique([tenantId, name])
  @@index([tenantId, isSystem])
  @@index([tenantId, isDefault])
  @@index([parentRoleId])
  @@map("roles")
}

model UserRole {
  id          String    @id @default(cuid())
  userId      String
  roleId      String
  assignedBy  String?   // User ID who assigned this role
  assignedAt  DateTime  @default(now())
  expiresAt   DateTime? // Optional expiration
  isActive    Boolean   @default(true)
  conditions  Json?     // Conditional access rules
  metadata    Json?     // Additional metadata
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  user       User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role       Role @relation(fields: [roleId], references: [id], onDelete: Cascade)
  assignedByUser User? @relation("UserRoleAssignedBy", fields: [assignedBy], references: [id])

  @@unique([userId, roleId])
  @@index([userId, isActive])
  @@index([roleId, isActive])
  @@index([expiresAt])
  @@map("user_roles")
}

// Session management
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Deal management
model Deal {
  id          String       @id @default(cuid())
  title       String
  description String?
  status      DealStatus   @default(PIPELINE)
  stage       String
  value       Decimal?
  currency    String       @default("USD")
  priority    DealPriority @default(MEDIUM)

  // Target company information
  targetCompany  String
  targetIndustry String?
  targetRevenue  Decimal?

  // Timeline
  expectedCloseDate DateTime?
  actualCloseDate   DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  createdById String
  createdBy   User   @relation("DealCreator", fields: [createdById], references: [id])

  assignedToId String?
  assignedTo   User?   @relation("DealAssignee", fields: [assignedToId], references: [id])

  documents         Document[]
  dueDiligenceItems DueDiligenceItem[]
  valuations        Valuation[]

  @@map("deals")
}

enum DealStatus {
  PIPELINE
  DUE_DILIGENCE
  NEGOTIATION
  CLOSING
  CLOSED
  CANCELLED
}

enum DealPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// Document management (VDR)
model Document {
  id           String         @id @default(cuid())
  name         String
  originalName String
  mimeType     String
  size         Int
  path         String
  version      Int            @default(1)
  status       DocumentStatus @default(ACTIVE)

  // Security
  isEncrypted Boolean             @default(false)
  accessLevel DocumentAccessLevel @default(RESTRICTED)

  // Metadata
  tags        String[]
  description String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  uploadedById String
  uploadedBy   User   @relation(fields: [uploadedById], references: [id])

  dealId String?
  deal   Deal?   @relation(fields: [dealId], references: [id])

  parentId String?
  parent   Document?  @relation("DocumentVersions", fields: [parentId], references: [id])
  versions Document[] @relation("DocumentVersions")

  @@map("documents")
}

enum DocumentStatus {
  ACTIVE
  ARCHIVED
  DELETED
}

enum DocumentAccessLevel {
  PUBLIC
  INTERNAL
  RESTRICTED
  CONFIDENTIAL
}

// Due Diligence Management
model DueDiligenceItem {
  id          String       @id @default(cuid())
  title       String
  description String?
  category    String
  status      DDItemStatus @default(PENDING)
  priority    DealPriority @default(MEDIUM)

  // Assignment
  assignedToId String?
  assignedTo   User?   @relation(fields: [assignedToId], references: [id])

  dueDate     DateTime?
  completedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  dealId String
  deal   Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("due_diligence_items")
}

enum DDItemStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  BLOCKED
  NOT_APPLICABLE
}

// Financial modeling and valuation
model Valuation {
  id          String        @id @default(cuid())
  name        String
  type        ValuationType
  methodology String

  // Financial data
  assumptions  Json
  calculations Json
  results      Json

  // DCF specific
  discountRate       Decimal?
  terminalGrowthRate Decimal?

  // Comparable analysis
  multiples Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  dealId String
  deal   Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("valuations")
}

enum ValuationType {
  DCF
  COMPARABLE_COMPANY
  PRECEDENT_TRANSACTION
  ASSET_BASED
  SUM_OF_PARTS
}

// Multi-Factor Authentication
model UserMfa {
  id           String   @id @default(cuid())
  userId       String   @unique
  secret       String   // Encrypted TOTP secret
  backupCodes  Json     // Array of encrypted backup codes
  enabled      Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_mfa")
}

// Audit logging
model AuditLog {
  id        String   @id @default(cuid())
  action    String
  entity    String
  entityId  String
  oldValues Json?
  newValues Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

// Tenant invitations
model TenantInvitation {
  id        String   @id @default(cuid())
  tenantId  String
  email     String
  role      String   @default("User")
  token     String   @unique
  status    InvitationStatus @default(PENDING)
  expiresAt DateTime
  invitedBy String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  inviter   User   @relation("TenantInviter", fields: [invitedBy], references: [id])

  @@unique([tenantId, email])
  @@map("tenant_invitations")
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  CANCELLED
}

// Tenant API keys
model TenantApiKey {
  id          String   @id @default(cuid())
  tenantId    String
  name        String
  keyHash     String   @unique
  permissions Json?    // API permissions
  lastUsedAt  DateTime?
  expiresAt   DateTime?
  isActive    Boolean  @default(true)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant      Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("tenant_api_keys")
}

// Migration tracking
model MigrationRecord {
  id          String    @id @default(cuid())
  migrationId String    // ID of the migration script
  name        String    // Human-readable name
  version     String    // Migration version
  tenantId    String?   // Null for global migrations
  executedAt  DateTime  @default(now())
  executedBy  String    // User who executed the migration
  success     Boolean   @default(false)
  error       String?   // Error message if failed
  rollbackAt  DateTime? // When migration was rolled back
  rollbackBy  String?   // User who rolled back the migration

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@unique([migrationId, tenantId])
  @@map("migration_records")
}

// Permission audit trail
model PermissionAudit {
  id           String   @id @default(cuid())
  userId       String
  tenantId     String
  action       String   // 'grant', 'revoke', 'modify'
  resourceType String   // 'role', 'permission', 'user_role'
  resourceId   String
  changes      Json     // What changed
  performedBy  String   // User ID who performed the action
  reason       String?  // Optional reason for the change
  metadata     Json?    // Additional context
  createdAt    DateTime @default(now())

  // Relations
  user      User   @relation("PermissionAuditUser", fields: [userId], references: [id])
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  performer User   @relation("PermissionAuditPerformer", fields: [performedBy], references: [id])
  role      Role?  @relation(fields: [resourceId], references: [id])

  @@index([userId, tenantId])
  @@index([tenantId, action])
  @@index([resourceType, resourceId])
  @@index([performedBy])
  @@index([createdAt])
  @@map("permission_audits")
}

// Role assignment rules for automatic role assignment
model RoleAssignmentRule {
  id          String   @id @default(cuid())
  name        String
  description String?
  conditions  Json     // Array of conditions
  targetRoleId String
  isActive    Boolean  @default(true)
  priority    Int      @default(0)
  tenantId    String
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant     Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  targetRole Role   @relation(fields: [targetRoleId], references: [id], onDelete: Cascade)
  creator    User   @relation(fields: [createdBy], references: [id])

  @@unique([tenantId, name])
  @@index([tenantId, isActive])
  @@index([priority])
  @@map("role_assignment_rules")
}

// Permission templates for common permission sets
model PermissionTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String
  permissions Json     // Array of permission strings
  variables   Json?    // Template variables
  isSystem    Boolean  @default(false)
  tenantId    String?  // Null for system templates
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant  Tenant? @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  creator User    @relation(fields: [createdBy], references: [id])

  @@unique([tenantId, name])
  @@index([category])
  @@index([isSystem])
  @@map("permission_templates")
}
