export interface ComplianceFramework {
  id: string
  name: string
  description: string
  jurisdiction: string
  category: ComplianceCategory
  applicability: ComplianceApplicability
  requirements: ComplianceRequirement[]
  penalties: CompliancePenalty[]
  lastUpdated: Date
  version: string
  isActive: boolean
}

export enum ComplianceCategory {
  SECURITIES = 'SECURITIES',
  ANTITRUST = 'ANTITRUST',
  BANKING = 'BANKING',
  INSURANCE = 'INSURANCE',
  HEALTHCARE = 'HEALTHCARE',
  TELECOMMUNICATIONS = 'TELECOMMUNICATIONS',
  ENERGY = 'ENERGY',
  ENVIRONMENTAL = 'ENVIRONMENTAL',
  DATA_PRIVACY = 'DATA_PRIVACY',
  EMPLOYMENT = 'EMPLOYMENT',
  TAX = 'TAX',
  FOREIGN_INVESTMENT = 'FOREIGN_INVESTMENT',
  INDUSTRY_SPECIFIC = 'INDUSTRY_SPECIFIC'
}

export interface ComplianceApplicability {
  transactionTypes: TransactionType[]
  transactionSizeThresholds: TransactionThreshold[]
  geographicScope: string[]
  industryScope: string[]
  entityTypes: EntityType[]
  timeframes: ComplianceTimeframe[]
}

export enum TransactionType {
  MERGER = 'MERGER',
  ACQUISITION = 'ACQUISITION',
  JOINT_VENTURE = 'JOINT_VENTURE',
  ASSET_PURCHASE = 'ASSET_PURCHASE',
  STOCK_PURCHASE = 'STOCK_PURCHASE',
  SPIN_OFF = 'SPIN_OFF',
  DIVESTITURE = 'DIVESTITURE',
  TENDER_OFFER = 'TENDER_OFFER',
  PROXY_CONTEST = 'PROXY_CONTEST'
}

export interface TransactionThreshold {
  type: 'VALUE' | 'MARKET_SHARE' | 'REVENUE' | 'ASSETS'
  amount: number
  currency?: string
  percentage?: number
  description: string
}

export enum EntityType {
  PUBLIC_COMPANY = 'PUBLIC_COMPANY',
  PRIVATE_COMPANY = 'PRIVATE_COMPANY',
  PARTNERSHIP = 'PARTNERSHIP',
  LLC = 'LLC',
  TRUST = 'TRUST',
  FUND = 'FUND',
  GOVERNMENT_ENTITY = 'GOVERNMENT_ENTITY',
  NON_PROFIT = 'NON_PROFIT',
  FOREIGN_ENTITY = 'FOREIGN_ENTITY'
}

export interface ComplianceTimeframe {
  phase: CompliancePhase
  description: string
  daysFromTrigger: number
  triggerEvent: string
  isHardDeadline: boolean
  extensions: ComplianceExtension[]
}

export enum CompliancePhase {
  PRE_SIGNING = 'PRE_SIGNING',
  PRE_CLOSING = 'PRE_CLOSING',
  POST_CLOSING = 'POST_CLOSING',
  ONGOING = 'ONGOING'
}

export interface ComplianceExtension {
  maxDays: number
  conditions: string[]
  approvalRequired: boolean
  fee?: number
}

export interface ComplianceRequirement {
  id: string
  name: string
  description: string
  type: RequirementType
  priority: CompliancePriority
  mandatory: boolean
  
  // Filing requirements
  filingRequired: boolean
  filingDeadline?: ComplianceTimeframe
  filingFee?: number
  filingDocuments: RequiredDocument[]
  
  // Approval requirements
  approvalRequired: boolean
  approvalAuthority?: string
  approvalTimeframe?: ComplianceTimeframe
  approvalCriteria: string[]
  
  // Notification requirements
  notificationRequired: boolean
  notificationParties: string[]
  notificationTimeframe?: ComplianceTimeframe
  notificationMethod: string[]
  
  // Documentation requirements
  documentationRequired: RequiredDocument[]
  retentionPeriod: number // years
  
  // Monitoring requirements
  ongoingMonitoring: boolean
  reportingFrequency?: ReportingFrequency
  
  // Dependencies
  dependencies: string[] // other requirement IDs
  exemptions: ComplianceExemption[]
}

export enum RequirementType {
  FILING = 'FILING',
  APPROVAL = 'APPROVAL',
  NOTIFICATION = 'NOTIFICATION',
  DISCLOSURE = 'DISCLOSURE',
  DOCUMENTATION = 'DOCUMENTATION',
  MONITORING = 'MONITORING',
  REPORTING = 'REPORTING',
  WAITING_PERIOD = 'WAITING_PERIOD',
  DIVESTITURE = 'DIVESTITURE',
  CONDITION = 'CONDITION'
}

export enum CompliancePriority {
  CRITICAL = 'CRITICAL',
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW'
}

export interface RequiredDocument {
  name: string
  description: string
  template?: string
  format: string[]
  maxSize?: number
  required: boolean
  examples: string[]
}

export enum ReportingFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  ANNUALLY = 'ANNUALLY',
  AS_NEEDED = 'AS_NEEDED'
}

export interface ComplianceExemption {
  name: string
  description: string
  conditions: string[]
  documentation: RequiredDocument[]
}

export interface CompliancePenalty {
  type: PenaltyType
  description: string
  monetaryPenalty?: MonetaryPenalty
  nonMonetaryPenalty?: string[]
  severity: PenaltySeverity
}

export enum PenaltyType {
  MONETARY = 'MONETARY',
  INJUNCTION = 'INJUNCTION',
  DIVESTITURE_ORDER = 'DIVESTITURE_ORDER',
  CRIMINAL = 'CRIMINAL',
  ADMINISTRATIVE = 'ADMINISTRATIVE',
  REPUTATIONAL = 'REPUTATIONAL'
}

export interface MonetaryPenalty {
  baseAmount?: number
  percentage?: number
  dailyAmount?: number
  maxAmount?: number
  currency: string
  calculation: string
}

export enum PenaltySeverity {
  MINOR = 'MINOR',
  MODERATE = 'MODERATE',
  MAJOR = 'MAJOR',
  SEVERE = 'SEVERE',
  CRITICAL = 'CRITICAL'
}

// Compliance Status and Tracking
export interface ComplianceStatus {
  id: string
  dealId: string
  frameworkId: string
  requirementId: string
  status: ComplianceStatusType
  
  // Progress tracking
  completionPercentage: number
  lastUpdated: Date
  updatedBy: string
  
  // Deadlines
  dueDate?: Date
  completedDate?: Date
  isOverdue: boolean
  daysRemaining?: number
  
  // Documentation
  submittedDocuments: SubmittedDocument[]
  missingDocuments: string[]
  
  // Approvals
  approvalStatus?: ApprovalStatus
  approvalDate?: Date
  approvalReference?: string
  
  // Issues and risks
  issues: ComplianceIssue[]
  riskLevel: RiskLevel
  
  // Comments and notes
  notes: ComplianceNote[]
  
  // Metadata
  tenantId: string
  createdAt: Date
  updatedAt: Date
}

export enum ComplianceStatusType {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING_REVIEW = 'PENDING_REVIEW',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED',
  OVERDUE = 'OVERDUE',
  EXEMPTED = 'EXEMPTED',
  NOT_APPLICABLE = 'NOT_APPLICABLE'
}

export interface SubmittedDocument {
  id: string
  name: string
  type: string
  fileUrl: string
  fileSize: number
  submittedDate: Date
  submittedBy: string
  version: number
  status: DocumentStatus
}

export enum DocumentStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  REQUIRES_REVISION = 'REQUIRES_REVISION'
}

export enum ApprovalStatus {
  PENDING = 'PENDING',
  UNDER_REVIEW = 'UNDER_REVIEW',
  APPROVED = 'APPROVED',
  APPROVED_WITH_CONDITIONS = 'APPROVED_WITH_CONDITIONS',
  REJECTED = 'REJECTED',
  WITHDRAWN = 'WITHDRAWN'
}

export interface ComplianceIssue {
  id: string
  type: IssueType
  severity: IssueSeverity
  description: string
  impact: string
  recommendation: string
  assignedTo?: string
  dueDate?: Date
  status: IssueStatus
  createdDate: Date
  resolvedDate?: Date
}

export enum IssueType {
  MISSING_DOCUMENT = 'MISSING_DOCUMENT',
  DEADLINE_RISK = 'DEADLINE_RISK',
  APPROVAL_DELAY = 'APPROVAL_DELAY',
  REGULATORY_CHANGE = 'REGULATORY_CHANGE',
  INTERPRETATION_QUESTION = 'INTERPRETATION_QUESTION',
  PROCESS_VIOLATION = 'PROCESS_VIOLATION',
  SYSTEM_ERROR = 'SYSTEM_ERROR'
}

export enum IssueSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum IssueStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED',
  ESCALATED = 'ESCALATED'
}

export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export interface ComplianceNote {
  id: string
  content: string
  author: string
  createdDate: Date
  isInternal: boolean
  attachments: string[]
}

// Compliance Monitoring and Alerts
export interface ComplianceAlert {
  id: string
  type: AlertType
  severity: AlertSeverity
  title: string
  message: string
  dealId: string
  complianceStatusId?: string
  
  // Trigger information
  triggerDate: Date
  triggerEvent: string
  
  // Recipients
  recipients: string[]
  notificationChannels: NotificationChannel[]
  
  // Status
  status: AlertStatus
  acknowledgedBy?: string
  acknowledgedDate?: Date
  resolvedDate?: Date
  
  // Actions
  suggestedActions: string[]
  escalationRules: EscalationRule[]
  
  // Metadata
  tenantId: string
  createdAt: Date
  updatedAt: Date
}

export enum AlertType {
  DEADLINE_APPROACHING = 'DEADLINE_APPROACHING',
  DEADLINE_MISSED = 'DEADLINE_MISSED',
  APPROVAL_REQUIRED = 'APPROVAL_REQUIRED',
  DOCUMENT_MISSING = 'DOCUMENT_MISSING',
  REGULATORY_CHANGE = 'REGULATORY_CHANGE',
  COMPLIANCE_VIOLATION = 'COMPLIANCE_VIOLATION',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  MANUAL_REVIEW_REQUIRED = 'MANUAL_REVIEW_REQUIRED'
}

export enum AlertSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

export enum NotificationChannel {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  IN_APP = 'IN_APP',
  SLACK = 'SLACK',
  TEAMS = 'TEAMS',
  WEBHOOK = 'WEBHOOK'
}

export enum AlertStatus {
  ACTIVE = 'ACTIVE',
  ACKNOWLEDGED = 'ACKNOWLEDGED',
  RESOLVED = 'RESOLVED',
  DISMISSED = 'DISMISSED',
  ESCALATED = 'ESCALATED'
}

export interface EscalationRule {
  level: number
  triggerAfterHours: number
  recipients: string[]
  actions: string[]
}

// Compliance Reporting
export interface ComplianceReport {
  id: string
  name: string
  type: ReportType
  dealId?: string
  frameworkIds: string[]
  
  // Report configuration
  dateRange: DateRange
  includeCompleted: boolean
  includeInProgress: boolean
  includeOverdue: boolean
  groupBy: ReportGrouping[]
  
  // Generated report
  generatedDate?: Date
  reportUrl?: string
  format: ReportFormat
  
  // Scheduling
  isScheduled: boolean
  schedule?: ReportSchedule
  
  // Access control
  visibility: ReportVisibility
  allowedUsers: string[]
  
  // Metadata
  createdBy: string
  tenantId: string
  createdAt: Date
  updatedAt: Date
}

export enum ReportType {
  COMPLIANCE_STATUS = 'COMPLIANCE_STATUS',
  DEADLINE_TRACKER = 'DEADLINE_TRACKER',
  APPROVAL_STATUS = 'APPROVAL_STATUS',
  DOCUMENT_STATUS = 'DOCUMENT_STATUS',
  RISK_ASSESSMENT = 'RISK_ASSESSMENT',
  AUDIT_TRAIL = 'AUDIT_TRAIL',
  REGULATORY_SUMMARY = 'REGULATORY_SUMMARY',
  EXECUTIVE_SUMMARY = 'EXECUTIVE_SUMMARY'
}

export interface DateRange {
  startDate: Date
  endDate: Date
}

export enum ReportGrouping {
  FRAMEWORK = 'FRAMEWORK',
  CATEGORY = 'CATEGORY',
  STATUS = 'STATUS',
  PRIORITY = 'PRIORITY',
  ASSIGNEE = 'ASSIGNEE',
  DUE_DATE = 'DUE_DATE'
}

export enum ReportFormat {
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  CSV = 'CSV',
  HTML = 'HTML',
  JSON = 'JSON'
}

export interface ReportSchedule {
  frequency: ReportingFrequency
  dayOfWeek?: number
  dayOfMonth?: number
  time: string
  timezone: string
  recipients: string[]
  isActive: boolean
}

export enum ReportVisibility {
  PRIVATE = 'PRIVATE',
  TEAM = 'TEAM',
  ORGANIZATION = 'ORGANIZATION',
  PUBLIC = 'PUBLIC'
}
