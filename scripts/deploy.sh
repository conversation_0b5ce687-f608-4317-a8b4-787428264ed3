#!/bin/bash

# Deployment script for M&A Platform
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
SKIP_TESTS=false
SKIP_BUILD=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -e|--environment)
      ENVIRONMENT="$2"
      shift 2
      ;;
    --skip-tests)
      SKIP_TESTS=true
      shift
      ;;
    --skip-build)
      SKIP_BUILD=true
      shift
      ;;
    -h|--help)
      echo "Usage: $0 [OPTIONS]"
      echo "Options:"
      echo "  -e, --environment ENV    Set environment (development, staging, production)"
      echo "  --skip-tests            Skip running tests"
      echo "  --skip-build            Skip building applications"
      echo "  -h, --help              Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option $1"
      exit 1
      ;;
  esac
done

echo -e "${GREEN}🚀 Starting deployment for environment: ${ENVIRONMENT}${NC}"

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
  echo -e "${RED}❌ Invalid environment: $ENVIRONMENT${NC}"
  echo "Valid environments: development, staging, production"
  exit 1
fi

# Check if required files exist
if [[ ! -f "config/${ENVIRONMENT}.env" ]]; then
  echo -e "${RED}❌ Environment config file not found: config/${ENVIRONMENT}.env${NC}"
  exit 1
fi

# Load environment variables
source "config/${ENVIRONMENT}.env"

echo -e "${YELLOW}📋 Environment: $ENVIRONMENT${NC}"
echo -e "${YELLOW}📋 Node Environment: $NODE_ENV${NC}"

# Install dependencies
echo -e "${GREEN}📦 Installing dependencies...${NC}"
pnpm install --frozen-lockfile

# Run tests (unless skipped)
if [[ "$SKIP_TESTS" == false ]]; then
  echo -e "${GREEN}🧪 Running tests...${NC}"
  pnpm test
fi

# Build applications (unless skipped)
if [[ "$SKIP_BUILD" == false ]]; then
  echo -e "${GREEN}🔨 Building applications...${NC}"
  pnpm build
fi

# Run database migrations
echo -e "${GREEN}🗄️ Running database migrations...${NC}"
pnpm --filter backend prisma migrate deploy

# Deploy based on environment
case $ENVIRONMENT in
  development)
    echo -e "${GREEN}🏃 Starting development environment...${NC}"
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    ;;
  staging)
    echo -e "${GREEN}🚀 Deploying to staging...${NC}"
    docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
    ;;
  production)
    echo -e "${GREEN}🚀 Deploying to production...${NC}"
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    ;;
esac

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"

# Show service status
echo -e "${YELLOW}📊 Service Status:${NC}"
docker-compose ps
