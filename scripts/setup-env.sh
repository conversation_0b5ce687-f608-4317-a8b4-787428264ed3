#!/bin/bash

# Environment setup script for M&A Platform
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 M&A Platform Environment Setup${NC}"
echo "=================================="

# Check if .env file exists
if [[ -f ".env" ]]; then
  echo -e "${YELLOW}⚠️  .env file already exists${NC}"
  read -p "Do you want to overwrite it? (y/N): " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Skipping .env file creation${NC}"
    exit 0
  fi
fi

# Copy example env file
echo -e "${GREEN}📄 Creating .env file from template...${NC}"
cp .env.example .env

# Generate secure secrets
echo -e "${GREEN}🔐 Generating secure secrets...${NC}"

# Generate JWT secret (64 characters)
JWT_SECRET=$(openssl rand -hex 32)
echo "Generated JWT_SECRET: ${JWT_SECRET:0:10}..."

# Generate encryption key (32 characters)
ENCRYPTION_KEY=$(openssl rand -hex 16)
echo "Generated ENCRYPTION_KEY: ${ENCRYPTION_KEY:0:10}..."

# Update .env file with generated secrets
if [[ "$OSTYPE" == "darwin"* ]]; then
  # macOS
  sed -i '' "s/your-super-secret-jwt-key-at-least-32-characters-long/$JWT_SECRET/g" .env
  sed -i '' "s/your-super-secret-encryption-key-32-chars/$ENCRYPTION_KEY/g" .env
else
  # Linux
  sed -i "s/your-super-secret-jwt-key-at-least-32-characters-long/$JWT_SECRET/g" .env
  sed -i "s/your-super-secret-encryption-key-32-chars/$ENCRYPTION_KEY/g" .env
fi

echo -e "${GREEN}✅ Environment file created successfully!${NC}"

# Check for required tools
echo -e "${BLUE}🔍 Checking required tools...${NC}"

check_tool() {
  if command -v $1 &> /dev/null; then
    echo -e "${GREEN}✅ $1 is installed${NC}"
  else
    echo -e "${RED}❌ $1 is not installed${NC}"
    return 1
  fi
}

MISSING_TOOLS=0

check_tool "node" || MISSING_TOOLS=$((MISSING_TOOLS + 1))
check_tool "pnpm" || MISSING_TOOLS=$((MISSING_TOOLS + 1))
check_tool "docker" || MISSING_TOOLS=$((MISSING_TOOLS + 1))
check_tool "docker-compose" || MISSING_TOOLS=$((MISSING_TOOLS + 1))

if [[ $MISSING_TOOLS -gt 0 ]]; then
  echo -e "${RED}❌ Please install missing tools before continuing${NC}"
  exit 1
fi

# Install dependencies
echo -e "${BLUE}📦 Installing dependencies...${NC}"
pnpm install

# Setup database
echo -e "${BLUE}🗄️ Setting up database...${NC}"
echo "Starting PostgreSQL and Redis containers..."
docker-compose up -d postgres redis

# Wait for database to be ready
echo "Waiting for database to be ready..."
sleep 10

# Generate Prisma client
echo -e "${GREEN}🔧 Generating Prisma client...${NC}"
pnpm --filter backend prisma generate

# Run database migrations
echo -e "${GREEN}🗄️ Running database migrations...${NC}"
pnpm --filter backend prisma migrate dev --name init

# Seed database
echo -e "${GREEN}🌱 Seeding database...${NC}"
pnpm --filter backend db:seed

echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Start the development servers:"
echo "   ${YELLOW}pnpm dev${NC}"
echo ""
echo "2. Open your browser:"
echo "   Frontend: ${YELLOW}http://localhost:3000${NC}"
echo "   Backend API: ${YELLOW}http://localhost:3001${NC}"
echo ""
echo "3. Access the database:"
echo "   Prisma Studio: ${YELLOW}pnpm --filter backend db:studio${NC}"
echo ""
echo -e "${GREEN}Happy coding! 🚀${NC}"
