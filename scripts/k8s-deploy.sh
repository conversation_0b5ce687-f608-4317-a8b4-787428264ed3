#!/bin/bash

# Kubernetes Deployment Script for M&A Platform
# Usage: ./scripts/k8s-deploy.sh [environment] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-production}
NAMESPACE="ma-platform-${ENVIRONMENT}"
SKIP_BUILD=${SKIP_BUILD:-false}
SKIP_MIGRATIONS=${SKIP_MIGRATIONS:-false}
DRY_RUN=${DRY_RUN:-false}

# Configuration
DOCKER_REGISTRY=${DOCKER_REGISTRY:-"your-registry.com"}
IMAGE_TAG=${IMAGE_TAG:-"latest"}

echo -e "${BLUE}🚀 Starting Kubernetes deployment for ${ENVIRONMENT} environment${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    print_status "kubectl is available"
}

# Check if we can connect to the cluster
check_cluster_connection() {
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    print_status "Connected to Kubernetes cluster"
}

# Build and push Docker images
build_and_push_images() {
    if [ "$SKIP_BUILD" = "true" ]; then
        print_info "Skipping Docker image build"
        return
    fi

    print_info "Building and pushing Docker images..."
    
    # Build backend image
    print_info "Building backend image..."
    docker build -t "${DOCKER_REGISTRY}/ma-platform/backend:${IMAGE_TAG}" ./packages/backend
    
    if [ "$DRY_RUN" != "true" ]; then
        docker push "${DOCKER_REGISTRY}/ma-platform/backend:${IMAGE_TAG}"
    fi
    
    # Build frontend image
    print_info "Building frontend image..."
    docker build -t "${DOCKER_REGISTRY}/ma-platform/frontend:${IMAGE_TAG}" ./packages/frontend
    
    if [ "$DRY_RUN" != "true" ]; then
        docker push "${DOCKER_REGISTRY}/ma-platform/frontend:${IMAGE_TAG}"
    fi
    
    print_status "Docker images built and pushed successfully"
}

# Create namespace if it doesn't exist
create_namespace() {
    print_info "Creating namespace: $NAMESPACE"
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply --dry-run=client -f k8s/namespace.yaml
    else
        kubectl apply -f k8s/namespace.yaml
    fi
    
    print_status "Namespace created/updated"
}

# Deploy secrets
deploy_secrets() {
    print_info "Deploying secrets..."
    
    # Check if secrets file exists
    if [ ! -f "k8s/secrets.yaml" ]; then
        print_warning "Secrets file not found. Please create k8s/secrets.yaml with your secrets."
        return
    fi
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply --dry-run=client -f k8s/secrets.yaml -n "$NAMESPACE"
    else
        kubectl apply -f k8s/secrets.yaml -n "$NAMESPACE"
    fi
    
    print_status "Secrets deployed"
}

# Deploy ConfigMaps
deploy_configmaps() {
    print_info "Deploying ConfigMaps..."
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply --dry-run=client -f k8s/configmap.yaml -n "$NAMESPACE"
    else
        kubectl apply -f k8s/configmap.yaml -n "$NAMESPACE"
    fi
    
    print_status "ConfigMaps deployed"
}

# Deploy database
deploy_database() {
    print_info "Deploying PostgreSQL database..."
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply --dry-run=client -f k8s/postgres.yaml -n "$NAMESPACE"
    else
        kubectl apply -f k8s/postgres.yaml -n "$NAMESPACE"
    fi
    
    print_status "Database deployment initiated"
}

# Deploy Redis
deploy_redis() {
    print_info "Deploying Redis cache..."
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply --dry-run=client -f k8s/redis.yaml -n "$NAMESPACE"
    else
        kubectl apply -f k8s/redis.yaml -n "$NAMESPACE"
    fi
    
    print_status "Redis deployment initiated"
}

# Deploy backend
deploy_backend() {
    print_info "Deploying backend API..."
    
    # Update image tag in deployment
    sed -i.bak "s|ma-platform/backend:latest|${DOCKER_REGISTRY}/ma-platform/backend:${IMAGE_TAG}|g" k8s/backend.yaml
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply --dry-run=client -f k8s/backend.yaml -n "$NAMESPACE"
    else
        kubectl apply -f k8s/backend.yaml -n "$NAMESPACE"
    fi
    
    # Restore original file
    mv k8s/backend.yaml.bak k8s/backend.yaml
    
    print_status "Backend deployment initiated"
}

# Deploy frontend
deploy_frontend() {
    print_info "Deploying frontend application..."
    
    # Update image tag in deployment
    sed -i.bak "s|ma-platform/frontend:latest|${DOCKER_REGISTRY}/ma-platform/frontend:${IMAGE_TAG}|g" k8s/frontend.yaml
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply --dry-run=client -f k8s/frontend.yaml -n "$NAMESPACE"
    else
        kubectl apply -f k8s/frontend.yaml -n "$NAMESPACE"
    fi
    
    # Restore original file
    mv k8s/frontend.yaml.bak k8s/frontend.yaml
    
    print_status "Frontend deployment initiated"
}

# Deploy nginx proxy
deploy_nginx() {
    print_info "Deploying Nginx proxy..."
    
    if [ "$DRY_RUN" = "true" ]; then
        kubectl apply --dry-run=client -f k8s/nginx.yaml -n "$NAMESPACE"
    else
        kubectl apply -f k8s/nginx.yaml -n "$NAMESPACE"
    fi
    
    print_status "Nginx deployment initiated"
}

# Wait for deployments to be ready
wait_for_deployments() {
    if [ "$DRY_RUN" = "true" ]; then
        print_info "Skipping deployment wait (dry run)"
        return
    fi

    print_info "Waiting for deployments to be ready..."
    
    # Wait for database
    kubectl wait --for=condition=available --timeout=300s deployment/postgres -n "$NAMESPACE"
    
    # Wait for Redis
    kubectl wait --for=condition=available --timeout=300s deployment/redis -n "$NAMESPACE"
    
    # Wait for backend
    kubectl wait --for=condition=available --timeout=300s deployment/ma-platform-backend -n "$NAMESPACE"
    
    # Wait for frontend
    kubectl wait --for=condition=available --timeout=300s deployment/ma-platform-frontend -n "$NAMESPACE"
    
    # Wait for nginx
    kubectl wait --for=condition=available --timeout=300s deployment/nginx -n "$NAMESPACE"
    
    print_status "All deployments are ready"
}

# Run database migrations
run_migrations() {
    if [ "$SKIP_MIGRATIONS" = "true" ]; then
        print_info "Skipping database migrations"
        return
    fi

    if [ "$DRY_RUN" = "true" ]; then
        print_info "Skipping migrations (dry run)"
        return
    fi

    print_info "Running database migrations..."
    
    # Get a backend pod name
    BACKEND_POD=$(kubectl get pods -n "$NAMESPACE" -l app=ma-platform-backend -o jsonpath='{.items[0].metadata.name}')
    
    if [ -z "$BACKEND_POD" ]; then
        print_error "No backend pod found"
        return 1
    fi
    
    # Run global migrations
    kubectl exec -n "$NAMESPACE" "$BACKEND_POD" -- npm run migrate run global
    
    print_status "Database migrations completed"
}

# Show deployment status
show_deployment_status() {
    print_info "Deployment status:"
    
    echo -e "\n${BLUE}📋 Pods:${NC}"
    kubectl get pods -n "$NAMESPACE"
    
    echo -e "\n${BLUE}🔧 Services:${NC}"
    kubectl get services -n "$NAMESPACE"
    
    echo -e "\n${BLUE}🌐 Ingresses:${NC}"
    kubectl get ingress -n "$NAMESPACE"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        echo -e "\n${BLUE}🔗 Access URLs:${NC}"
        echo -e "Application: ${YELLOW}https://app.maplatform.com${NC}"
        echo -e "API: ${YELLOW}https://api.maplatform.com${NC}"
    fi
}

# Cleanup function
cleanup() {
    if [ $? -ne 0 ]; then
        print_error "Deployment failed"
        if [ "$DRY_RUN" != "true" ]; then
            print_info "Check logs with: kubectl logs -n $NAMESPACE -l app=ma-platform-backend"
        fi
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main deployment flow
main() {
    echo -e "${BLUE}🔍 Pre-deployment checks${NC}"
    check_kubectl
    check_cluster_connection
    
    echo -e "${BLUE}🏗️  Building and pushing images${NC}"
    build_and_push_images
    
    echo -e "${BLUE}🚀 Deploying to Kubernetes${NC}"
    create_namespace
    deploy_secrets
    deploy_configmaps
    deploy_database
    deploy_redis
    deploy_backend
    deploy_frontend
    deploy_nginx
    
    echo -e "${BLUE}⏳ Waiting for deployments${NC}"
    wait_for_deployments
    
    echo -e "${BLUE}🗄️  Database setup${NC}"
    run_migrations
    
    echo -e "${BLUE}📊 Deployment status${NC}"
    show_deployment_status
    
    print_status "Kubernetes deployment completed successfully!"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-migrations)
            SKIP_MIGRATIONS=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --registry)
            DOCKER_REGISTRY="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [environment] [options]"
            echo ""
            echo "Environments:"
            echo "  production (default)"
            echo "  staging"
            echo ""
            echo "Options:"
            echo "  --skip-build        Skip Docker image building"
            echo "  --skip-migrations   Skip database migrations"
            echo "  --dry-run          Show what would be deployed"
            echo "  --image-tag TAG    Docker image tag (default: latest)"
            echo "  --registry URL     Docker registry URL"
            echo "  --help             Show this help message"
            exit 0
            ;;
        *)
            if [ -z "$ENVIRONMENT" ]; then
                ENVIRONMENT=$1
            fi
            shift
            ;;
    esac
done

# Update namespace based on environment
NAMESPACE="ma-platform-${ENVIRONMENT}"

# Run main deployment
main
