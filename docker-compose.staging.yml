version: '3.8'

services:
  postgres:
    environment:
      POSTGRES_DB: ma_platform_staging
      POSTGRES_USER: staging_user
      POSTGRES_PASSWORD: staging_pass
    volumes:
      - postgres_staging_data:/var/lib/postgresql/data

  redis:
    volumes:
      - redis_staging_data:/data

  backend:
    build:
      target: production
    environment:
      NODE_ENV: staging
    env_file:
      - config/staging.env
    restart: always

  frontend:
    build:
      target: production
    environment:
      NODE_ENV: staging
      VITE_API_URL: https://staging-api.ma-platform.com
    restart: always

  nginx:
    profiles: []  # Enable nginx in staging

volumes:
  postgres_staging_data:
  redis_staging_data:
