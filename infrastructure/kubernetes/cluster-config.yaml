# High-Availability Kubernetes Cluster Configuration
# This configuration sets up a production-grade Kubernetes cluster with multi-master setup

apiVersion: v1
kind: Namespace
metadata:
  name: kube-system
---
apiVersion: v1
kind: Namespace
metadata:
  name: development
  labels:
    environment: dev
    tier: application
---
apiVersion: v1
kind: Namespace
metadata:
  name: staging
  labels:
    environment: staging
    tier: application
---
apiVersion: v1
kind: Namespace
metadata:
  name: production
  labels:
    environment: prod
    tier: application
---
apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
  labels:
    tier: infrastructure
---
apiVersion: v1
kind: Namespace
metadata:
  name: logging
  labels:
    tier: infrastructure
---
apiVersion: v1
kind: Namespace
metadata:
  name: security
  labels:
    tier: infrastructure
---
# Resource Quotas for Development Environment
apiVersion: v1
kind: ResourceQuota
metadata:
  name: dev-resource-quota
  namespace: development
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    pods: "20"
    services: "10"
    secrets: "10"
    configmaps: "10"
---
# Resource Quotas for Staging Environment
apiVersion: v1
kind: ResourceQuota
metadata:
  name: staging-resource-quota
  namespace: staging
spec:
  hard:
    requests.cpu: "8"
    requests.memory: 16Gi
    limits.cpu: "16"
    limits.memory: 32Gi
    persistentvolumeclaims: "20"
    pods: "40"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
# Resource Quotas for Production Environment
apiVersion: v1
kind: ResourceQuota
metadata:
  name: prod-resource-quota
  namespace: production
spec:
  hard:
    requests.cpu: "16"
    requests.memory: 32Gi
    limits.cpu: "32"
    limits.memory: 64Gi
    persistentvolumeclaims: "50"
    pods: "100"
    services: "50"
    secrets: "50"
    configmaps: "50"
---
# Network Policy for Development Environment
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: dev-network-policy
  namespace: development
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          environment: dev
    - namespaceSelector:
        matchLabels:
          tier: infrastructure
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          environment: dev
    - namespaceSelector:
        matchLabels:
          tier: infrastructure
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
---
# Network Policy for Production Environment
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: prod-network-policy
  namespace: production
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          environment: prod
    - namespaceSelector:
        matchLabels:
          tier: infrastructure
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          environment: prod
    - namespaceSelector:
        matchLabels:
          tier: infrastructure
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
---
# Priority Classes
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: high-priority
value: 1000
globalDefault: false
description: "High priority class for critical workloads"
---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: medium-priority
value: 500
globalDefault: true
description: "Medium priority class for standard workloads"
---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: low-priority
value: 100
globalDefault: false
description: "Low priority class for batch workloads"
---
# Pod Security Policy
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: restricted-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
---
# Cluster Role for Pod Security Policy
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: restricted-psp-user
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - restricted-psp
---
# Cluster Role Binding for Pod Security Policy
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: restricted-psp-all-serviceaccounts
roleRef:
  kind: ClusterRole
  name: restricted-psp-user
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: Group
  name: system:serviceaccounts
  apiGroup: rbac.authorization.k8s.io
---
# Horizontal Pod Autoscaler for cluster-autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cluster-autoscaler-hpa
  namespace: kube-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cluster-autoscaler
  minReplicas: 1
  maxReplicas: 3
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
# Service Account for cluster operations
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cluster-admin-sa
  namespace: kube-system
---
# Cluster Role Binding for cluster admin
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cluster-admin-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: cluster-admin-sa
  namespace: kube-system
---
# ConfigMap for cluster configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-config
  namespace: kube-system
data:
  cluster.name: "mna-platform-cluster"
  cluster.region: "us-west-2"
  cluster.environment: "production"
  monitoring.enabled: "true"
  logging.enabled: "true"
  security.enabled: "true"
  backup.enabled: "true"
  autoscaling.enabled: "true"
  networking.cni: "calico"
  storage.class: "gp3"
  ingress.controller: "nginx"
---
# Limit Ranges for Development
apiVersion: v1
kind: LimitRange
metadata:
  name: dev-limit-range
  namespace: development
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - max:
      cpu: "2"
      memory: "4Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
    type: Container
---
# Limit Ranges for Production
apiVersion: v1
kind: LimitRange
metadata:
  name: prod-limit-range
  namespace: production
spec:
  limits:
  - default:
      cpu: "1"
      memory: "1Gi"
    defaultRequest:
      cpu: "200m"
      memory: "256Mi"
    type: Container
  - max:
      cpu: "4"
      memory: "8Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
    type: Container
