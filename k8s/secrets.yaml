apiVersion: v1
kind: Secret
metadata:
  name: ma-platform-secrets
  namespace: ma-platform-production
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  DATABASE_URL: ************************************************************
  JWT_SECRET: eW91ci1zdXBlci1zZWNyZXQtand0LWtleQ==
  ENCRYPTION_KEY: eW91ci0zMi1jaGFyLWVuY3J5cHRpb24ta2V5
  REDIS_URL: cmVkaXM6Ly9yZWRpcy1zZXJ2aWNlOjYzNzk=
  
---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: ma-platform-production
type: Opaque
data:
  POSTGRES_DB: bWFfcGxhdGZvcm0=
  POSTGRES_USER: cG9zdGdyZXM=
  POSTGRES_PASSWORD: c2VjdXJlLXBhc3N3b3Jk
  
---
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: ma-platform-production
type: kubernetes.io/tls
data:
  # Replace with actual TLS certificate and key
  tls.crt: LS0tLS1CRUdJTi...
  tls.key: LS0tLS1CRUdJTi...
