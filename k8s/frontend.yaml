apiVersion: apps/v1
kind: Deployment
metadata:
  name: ma-platform-frontend
  namespace: ma-platform-production
  labels:
    app: ma-platform-frontend
    component: web
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ma-platform-frontend
  template:
    metadata:
      labels:
        app: ma-platform-frontend
        component: web
    spec:
      containers:
      - name: frontend
        image: ma-platform/frontend:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: ma-platform-config
              key: NODE_ENV
        - name: PORT
          value: "3000"
        - name: REACT_APP_API_URL
          value: "https://api.maplatform.com"
        - name: REACT_APP_MAIN_DOMAIN
          valueFrom:
            configMapKeyRef:
              name: ma-platform-config
              key: MAIN_DOMAIN
        - name: REACT_APP_API_DOMAIN
          valueFrom:
            configMapKeyRef:
              name: ma-platform-config
              key: API_DOMAIN
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3

---
apiVersion: v1
kind: Service
metadata:
  name: ma-platform-frontend
  namespace: ma-platform-production
  labels:
    app: ma-platform-frontend
    component: web
spec:
  selector:
    app: ma-platform-frontend
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ma-platform-frontend-hpa
  namespace: ma-platform-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ma-platform-frontend
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
