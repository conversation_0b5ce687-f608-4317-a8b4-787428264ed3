apiVersion: apps/v1
kind: Deployment
metadata:
  name: ma-platform-backend
  namespace: ma-platform-production
  labels:
    app: ma-platform-backend
    component: api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ma-platform-backend
  template:
    metadata:
      labels:
        app: ma-platform-backend
        component: api
    spec:
      containers:
      - name: backend
        image: ma-platform/backend:latest
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: ma-platform-config
              key: NODE_ENV
        - name: PORT
          value: "3001"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ma-platform-secrets
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: ma-platform-secrets
              key: REDIS_URL
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ma-platform-secrets
              key: JWT_SECRET
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: ma-platform-secrets
              key: ENCRYPTION_KEY
        - name: CORS_ORIGIN
          valueFrom:
            configMapKeyRef:
              name: ma-platform-config
              key: CORS_ORIGIN
        - name: MAIN_DOMAIN
          valueFrom:
            configMapKeyRef:
              name: ma-platform-config
              key: MAIN_DOMAIN
        - name: API_DOMAIN
          valueFrom:
            configMapKeyRef:
              name: ma-platform-config
              key: API_DOMAIN
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30

---
apiVersion: v1
kind: Service
metadata:
  name: ma-platform-backend
  namespace: ma-platform-production
  labels:
    app: ma-platform-backend
    component: api
spec:
  selector:
    app: ma-platform-backend
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ma-platform-backend-hpa
  namespace: ma-platform-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ma-platform-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
