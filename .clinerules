# Rules of engagements

## Startup Focus
- Ship functional MVPs
- Apply First Principles & 80/20 rule
- Concise docs & Mermaid charts
- modular monolith

##Tech Stack

###frontend
- React, vite, Tailwind CSS, TypeScript, tanstack (query, table , form), zod, authjs, shadcn/ui

###backend
- express, authjs, prisma, postgresql, zod, redis, bullmq, typescript

###deployment
- Dockerised deployment (cloud agnostic)

## UI/UX
- Modular, card-based design  
- Clear visual hierarchy, responsive, mobile-first  
- Micro-interactions, micro-animations, dark/light mode  
- Skeleton loading, progressive disclosure  
- Consistent visuals, effective data display  

### Component Design
- Atomic Design methodology  
- Composition over inheritance  
- Consistent, responsive, accessible  
- Reusable components  

## Code & Architecture
- DDD, SOLID, KISS, DRY, YAGNI  
- Self-documenting code, clear naming  
- Defined module boundaries for scalability

## Security (Minimal)
- Auth and Authz(RBAC)
- Authenticated routes  
- Input validation  
- Sanitization  
- Error handling  
- Rate limiting  

## Observability (Minimal)
- Essential debugging logs  
- Basic structured logging (requests, errors)  
