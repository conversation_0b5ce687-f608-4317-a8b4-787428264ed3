# Dependencies
node_modules/
.pnpm-store/
.pnpm-debug.log*

# Build outputs
dist/
build/
.next/
.nuxt/
.output/
.vercel/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
dev-debug.log

# Coverage and testing
coverage/
*.lcov
.nyc_output/

# TypeScript cache
*.tsbuildinfo

# Cache directories
.npm
.eslintcache
.stylelintcache
.cache
.parcel-cache

# Database
*.db
*.sqlite
*.sqlite3

# Docker
.dockerignore

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# TaskMaster (keep tasks but ignore reports)
.taskmaster/reports/
.taskmaster/cache/