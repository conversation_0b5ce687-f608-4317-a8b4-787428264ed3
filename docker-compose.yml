version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ma-platform-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ma_platform_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./packages/backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ma-platform-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ma-platform-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - ma-platform-network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: packages/backend/Dockerfile
      target: development
    container_name: ma-platform-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ********************************************/ma_platform_dev
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-at-least-32-characters-long
      JWT_EXPIRES_IN: 7d
      FRONTEND_URL: http://localhost:3000
      ENCRYPTION_KEY: your-super-secret-encryption-key-32-chars
    ports:
      - "3001:3001"
    volumes:
      - ./packages/backend:/app/packages/backend
      - ./packages/shared:/app/packages/shared
      - /app/packages/backend/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - ma-platform-network
    command: pnpm dev

  # Frontend Application
  frontend:
    build:
      context: .
      dockerfile: packages/frontend/Dockerfile
      target: development
    container_name: ma-platform-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      VITE_API_URL: http://localhost:3001/api
    ports:
      - "3000:3000"
    volumes:
      - ./packages/frontend:/app/packages/frontend
      - ./packages/shared:/app/packages/shared
      - /app/packages/frontend/node_modules
    depends_on:
      - backend
    networks:
      - ma-platform-network
    command: pnpm dev

  # Nginx Reverse Proxy (Optional for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: ma-platform-nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend
    networks:
      - ma-platform-network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  ma-platform-network:
    driver: bridge
